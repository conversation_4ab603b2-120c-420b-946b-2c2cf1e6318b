package com.sankuai.algoplatform.matchops.api.request.algoPackage;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.util.Map;

@ThriftStruct
@Data
public class TAddEditAlgoBizCodeRequest {
    @FieldDoc(description = "algoBizCode", example = {})
    @ThriftField(1)
    public String algoBizCode;

    @FieldDoc(description = "流量策略", example = {})
    @ThriftField(2)
    public Map<String,String> strategy;

    @FieldDoc(description = "note", example = {})
    @ThriftField(3)
    public String note;

    @FieldDoc(description = "ab实验名称", example = {})
    @ThriftField(4)
    public String abTestName;

    @FieldDoc(description = "业务线id", example = {})
    @ThriftField(5)
    public Long bizLineId;

    @FieldDoc(description = "algoBizCodeId", example = {})
    @ThriftField(6)
    public Long algoBizCodeId;
}
