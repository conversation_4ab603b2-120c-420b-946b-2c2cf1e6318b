package com.sankuai.algoplatform.matchops.api.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.algoplatform.matchops.api.request.resourceGroup.TAddEditResourceGroupRequest;
import com.sankuai.algoplatform.matchops.api.request.resourceGroup.TDeleteResourceGroupRequest;
import com.sankuai.algoplatform.matchops.api.request.resourceGroup.TResourceGroupListRequest;
import com.sankuai.algoplatform.matchops.api.response.resourceGroup.TAddResourceGroupResponse;
import com.sankuai.algoplatform.matchops.api.response.resourceGroup.TDeleteResourceGroupResponse;
import com.sankuai.algoplatform.matchops.api.response.resourceGroup.TEditResourceGroupResponse;
import com.sankuai.algoplatform.matchops.api.response.resourceGroup.TRSourceGroupListResponse;

@ThriftService
@InterfaceDoc(type = "octo.thrift.annotation", description = "测试场景接口", scenarios = "")
public interface TResourceGroupService {
    @ThriftMethod
    @MethodDoc(description = "资源组列表")
    TRSourceGroupListResponse getResourceGroupList(TResourceGroupListRequest request);

    @ThriftMethod
    @MethodDoc(description = "新增资源组")
    TAddResourceGroupResponse addResourceGroupList(TAddEditResourceGroupRequest request);

//    @ThriftMethod
//    @MethodDoc(description = "根据名称获取资源组")
//    TRSourceGroupListResponse getResourceGroup(TAddEditResourceGroupRequest request);

    @ThriftMethod
    @MethodDoc(description = "编辑资源组")
    TEditResourceGroupResponse editResourceGroupList(TAddEditResourceGroupRequest request);

    @ThriftMethod
    @MethodDoc(description = "删除资源组")
    TDeleteResourceGroupResponse delResourceGroupList(TDeleteResourceGroupRequest request);

}
