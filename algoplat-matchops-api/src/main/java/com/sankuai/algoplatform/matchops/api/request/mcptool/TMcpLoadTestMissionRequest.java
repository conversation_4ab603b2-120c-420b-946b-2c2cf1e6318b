package com.sankuai.algoplatform.matchops.api.request.mcptool;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * @program: algoplat-matchops
 * <AUTHOR>
 * @Date 2025/5/23
 */

@TypeDoc(name = "TMcpLoadTestMissionRequest", description = "匹配测试工具 模型压测")
@ThriftStruct
public class TMcpLoadTestMissionRequest implements Serializable {

    @FieldDoc(name = "industryType", description = "产业类型。1到餐 2到综 3住宿", requiredness = Requiredness.REQUIRED)
    private String industryType;

    @FieldDoc(name = "buType", description = "业务类型。全部 美容美体 境内竞对", requiredness = Requiredness.REQUIRED)
    private String bu;

    @FieldDoc(name = "matchStandard", description = "匹配标准。精准匹配 商品匹配 房型匹配", requiredness = Requiredness.REQUIRED)
    private String matchStandard;

    @FieldDoc(name = "modelType", description = "模型类型。枚举值：tf、bge、llm", requiredness = Requiredness.REQUIRED)
    private String modelType;

    @FieldDoc(name = "modelName", description = "模型名称", requiredness = Requiredness.REQUIRED)
    private String modelName;

    @FieldDoc(name = "modelVersion", description = "模型版本", requiredness = Requiredness.REQUIRED)
    private String modelVersion;

    @FieldDoc(name = "misId", description = "用户mis号", requiredness = Requiredness.REQUIRED)
    private String misId;

    @FieldDoc(name = "testDataSet", description = "测试数据集s3链接", requiredness = Requiredness.OPTIONAL)
    private String testDataSet;

    @FieldDoc(name= "extraInfo",description = "extraInfo",requiredness = Requiredness.OPTIONAL)
    private Map<String,String> extraInfo = new HashMap<>();

    public String getIndustryType() {
        return industryType;
    }

    public void setIndustryType(String industryType) {
        this.industryType = industryType;
    }

    public String getBu() {
        return bu;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }

    public String getMatchStandard() {
        return matchStandard;
    }

    public void setMatchStandard(String matchStandard) {
        this.matchStandard = matchStandard;
    }

    public String getModelType() {
        return modelType;
    }

    public void setModelType(String modelType) {
        this.modelType = modelType;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public String getModelVersion() {
        return modelVersion;
    }

    public void setModelVersion(String modelVersion) {
        this.modelVersion = modelVersion;
    }

    public String getMisId() {
        return misId;
    }

    public void setMisId(String misId) {
        this.misId = misId;
    }

    public String getTestDataSet() {
        return testDataSet;
    }

    public void setTestDataSet(String testDataSet) {
        this.testDataSet = testDataSet;
    }

    public Map<String, String> getExtraInfo() {
        return extraInfo;
    }

    public void setExtraInfo(Map<String, String> extraInfo) {
        this.extraInfo = extraInfo;
    }
}
