package com.sankuai.algoplatform.matchops.api.request.matchStrategy;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.Data;

@ThriftStruct
@Data
public class TQueryMatchStrategyRequest {
    @FieldDoc(description = "策略id", example = {})
    @ThriftField(1)
    public Long matchStrategyId;

    @FieldDoc(description = "策略名称", example = {}, requiredness = Requiredness.OPTIONAL)
    @ThriftField(2)
    public String matchStrategyName;
}
