package com.sankuai.algoplatform.matchops.api.response.resourceGroup;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.algoplatform.matchops.api.response.TBaseResponse;
import lombok.Data;

import java.util.List;

@ThriftStruct
@Data
public class TTestTaskDetailResponse extends TBaseResponse {

    @FieldDoc(description = "返回结果", example = {})
    @ThriftField(3)
    public Data data;

    @lombok.Data
    static public class Data {
        private Long taskId;
        private String taskName;
        private String matchStrategyName;
        private String sceneName;
        private List<AlgoCodeInfo> algoCodeInfo;
        private PredictorCache predictorCache;
        private List<LlmInfo> llmInfo;
        private List<OctoInfo> octoInfo;

        private String resourceGroupName;
        private List<ResourceInfo> cpuResourceInfo;
        private List<MlpResourceInfo> mlpResourceInfo;
        private List<LlmResourceInfo> llmResourceInfo;
        private List<Long> reporterTemplateIdList;
        private String missionResult;
        private String missionStatus;

        @lombok.Data
        public static class AlgoCodeInfo {
            private String algoBizCode;
            private List<AlgoCodeStrategy> strategies;
        }

        @lombok.Data
        public static class AlgoCodeStrategy {
            private String strategyName;
            private String version;
            private String quota;
        }

        @lombok.Data
        public static class LlmStrategy {
            private String strategyName;
            private String modelName;
            private String modelServiceName;
            private String prompt;
            private String quota;
        }

        @lombok.Data
        public static class PredictorCache {
            private String setName;
            private String cache1;
            private String cache2;
            private String cache3;
            private String cache4;
        }

        @lombok.Data
        public static class LlmInfo {
            private String llmBizCode;
            private List<LlmStrategy> strategies;
        }

        @lombok.Data
        public static class OctoInfo {
            private String appkey;
            private String setName;
            private String lionConfig;
            private String branch;
        }

        @lombok.Data
        public static class ResourceInfo {
            private String appkey;
            private String set;
            private String instanceNum;
        }

        @lombok.Data
        public static class MlpResourceInfo {
            private String wxProject;

            private String appkey;

            private String modelName;

            private String modelType;

            private String modelVersion;

            private String instanceNum;

            private String retainTime;

            private List<String> ququeName;

            private String groupName;

            private String serverImage;

            private Integer rpcOutTime;

            private Boolean batching;

            private Integer maxBatchSize;

            private Integer batchTimeoutMicros;

            private String batchingExtra = "";

            private String env;

            private String set;

            private int vcores;
            private int memory;
            private int gcores;
            private String gcoresType;
        }

        @lombok.Data
        public static class LlmResourceInfo {
            private String serviceName;
            private String instanceNum;

            // 添加新字段
            private String gpuPerInstance;
            private String modelName;
            private String batchSize;
            private String useTemplate;
            private String promptTemplate;
            private String modelBaseName;
            private String trainMethod;
            private String trainingTaskId;

            // 模型参数
            private String topP;
            private String maxNewTokens;
            private String promptPrefix;
            private String topK;
            private String promptSuffix;
            private String temperature;
            private String maxSeqLength;
        }
    }
}
