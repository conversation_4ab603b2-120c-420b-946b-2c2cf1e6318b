package com.sankuai.algoplatform.matchops.api.request.general;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

@ThriftStruct
@Data
public class TDropdownRequest {
    @FieldDoc(description = "type", example = {})
    @ThriftField(1)
    public String type;

    @FieldDoc(description = "业务线id", example = {})
    @ThriftField(2)
    public Long BizLineId;

    @FieldDoc(description = "环境", example = {})
    @ThriftField(3)
    public String env;
}
