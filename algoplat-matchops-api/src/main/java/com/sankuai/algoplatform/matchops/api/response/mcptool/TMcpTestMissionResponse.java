package com.sankuai.algoplatform.matchops.api.response.mcptool;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.algoplatform.matchops.api.response.TBaseResponse;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * @program: algoplat-matchops
 * <AUTHOR>
 * @Date 2025/5/23
 */
@ThriftStruct
@Data
public class TMcpTestMissionResponse extends TBaseResponse {
    @FieldDoc(description = "返回结果", example = {})
    @ThriftField(3)
    public Map<String,String> data;

    @FieldDoc(description = "extra",example = {})
    @ThriftField(4)
    public Map<String,String> extra;
}
