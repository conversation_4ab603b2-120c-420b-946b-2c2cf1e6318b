package com.sankuai.algoplatform.matchops.api.request.resourceGroup;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

@ThriftStruct
@Data
public class TDeleteResourceGroupRequest {
    @FieldDoc(description = "资源组id", example = {})
    @ThriftField(1)
    public Long resourceGroupId;
}
