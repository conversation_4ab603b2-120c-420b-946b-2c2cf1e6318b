package com.sankuai.algoplatform.matchops.api.request.mcptool;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.util.List;

/**
 * @program: algoplat-matchops
 * <AUTHOR>
 * @Date 2025/5/23
 */
@TypeDoc(name = "AlgoPackageInfo", description = "算法包")
@ThriftStruct
public class AlgoPackageInfo {
    /**
     * 迭代后的算法包commitId。选填，如果发起的此次测试并没有修改算法包则不用填
     */
    @FieldDoc(description = "commitId", example = {})
    private String commitId;

    /**
     * 模型信息
     */
    @FieldDoc(description = "模型信息", example = {})
    private List<ModelDetail> models = Lists.newArrayList();

    /**
     * 获取commitId
     * @return commitId
     */
    @ThriftField(1)
    public String getCommitId() {
        return commitId;
    }

    /**
     * 设置commitId
     * @param commitId 算法包commitId
     */
    @ThriftField()
    public void setCommitId(String commitId) {
        this.commitId = commitId;
    }

    /**
     * 获取模型信息列表
     * @return 模型信息列表
     */
    @ThriftField(2)
    public List<ModelDetail> getModels() {
        return models;
    }

    /**
     * 设置模型信息列表
     * @param models 模型信息列表
     */
    @ThriftField()
    public void setModels(List<ModelDetail> models) {
        this.models = models;
    }

    public AlgoPackageInfo() {
        // 无参构造
    }

    public AlgoPackageInfo(String commitId, List<ModelDetail> models) {
        this.commitId = commitId;
        this.models = models;
    }
}
