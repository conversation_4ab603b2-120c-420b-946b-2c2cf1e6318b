package com.sankuai.algoplatform.matchops.api.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.algoplatform.matchops.api.request.mcptool.TMcpLoadTestMissionRequest;
import com.sankuai.algoplatform.matchops.api.request.mcptool.TMcpTestMissionRequest;
import com.sankuai.algoplatform.matchops.api.response.mcptool.TMcpTestMissionResponse;

/**
 * @program: algoplat-matchops
 * <AUTHOR>
 * @Date 2025/5/23
 */

@ThriftService
@InterfaceDoc(
        type = "octo.thrift.annotation",
        displayName = "测试工具Mcp任务服务",
        description = "为匹配Agent提供模型测试和效果评估能力,相关文档：https://km.sankuai.com/collabpage/2710265173#b-e846ce05eac94dd3aa434566291b45c3",
        scenarios = "适用于BadCase率验证、模型压测、GSB测试、一致性测试等场景")
public interface TMcpTestMissionService {

    @ThriftMethod
    @MethodDoc(description = "发起测试任务")
    TMcpTestMissionResponse submitMcpTestTask(TMcpTestMissionRequest request);

    @ThriftMethod
    @MethodDoc(description = "查询测试任务")
    TMcpTestMissionResponse queryMcpTestTaskByMissionId(Long missionId);

    @ThriftMethod
    @MethodDoc(description = "发起压测任务")
    TMcpTestMissionResponse submitMcpLoadTestTask(TMcpTestMissionRequest request);

}
