package com.sankuai.algoplatform.matchops.api.request.algoPackage;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

@ThriftStruct
@Data
public class TAddEditAlgoStrategyRequest {
    @FieldDoc(description = "策略名称", example = {})
    @ThriftField(1)
    public String algoCodeStrategyName;

    @FieldDoc(description = "策略id", example = {})
    @ThriftField(2)
    public Long algoCodeStrategyId;

    @FieldDoc(description = "模型路径", example = {})
    @ThriftField(3)
    public String modulePath;

    @FieldDoc(description = "文件路径", example = {})
    @ThriftField(4)
    public String entrancePath;

    @FieldDoc(description = "算法方法", example = {})
    @ThriftField(5)
    public String entranceMethod;

    @FieldDoc(description = "算法环境", example = {})
    @ThriftField(6)
    public String algoRuntime;

    @FieldDoc(description = "version", example = {})
    @ThriftField(7)
    public String version;

    @FieldDoc(description = "note", example = {})
    @ThriftField(8)
    public String note;

    @FieldDoc(description = "bizLineId", example = {})
    @ThriftField(9)
    public Long bizLineId;

    @FieldDoc(description = "codeRepo", example = {})
    @ThriftField(10)
    public String codeRepo;
    @FieldDoc(description = "convertEntrancePath", example = {})
    @ThriftField(11)
    public String convertEntrancePath;
    @FieldDoc(description = "convertEntranceMethod", example = {})
    @ThriftField(12)
    public String convertEntranceMethod;

}
