package com.sankuai.algoplatform.matchops.api.request.testreprotertemplate;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

@ThriftStruct
@Data
public class TDeleteTestReporterTemplateRequest {
    @FieldDoc(description = "templateId", example = {})
    @ThriftField(1)
    public Long templateId;
}
