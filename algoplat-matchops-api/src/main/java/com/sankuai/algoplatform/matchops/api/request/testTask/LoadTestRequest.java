package com.sankuai.algoplatform.matchops.api.request.testTask;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 模型配置实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LoadTestRequest {
    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 模型路径
     */
    private String modelPath;

    /**
     * 模型类型（仅支持：BGE、TF）
     */
    private String modelType;

//    /**
//     * 缓存类型。
//     * 0: 模型缓存值内容类型为Float, 1: 为string，不做处理直接返回
//     */
//    private String cacheType;

//    /**
//     * 特征缓存分组ID
//     */
//    private String groupId;

    /**
     * 模型最大输入长度
     */
    private String maxSeqLen;

    /**
     * 返回模型输出指定下标的值，不传默认返回第二个（index=1）
     */

    private String modelIndex;

    /**
     * 输出类型
     * 0: 非矩阵任务
     * 1: 矩阵任务
     */
    private String preHandleStructType;

    /**
     * 从tokens转化为id的函数类型
     */
    private String funcType;

    /**
     * 压测数据地址（S3链接）
     */
    private String testDataUrl;

    /**
     * prompt
     */
//    private List<String> processEdInput;

    /**
     * batch数，如 "1,10"，会分别按照1、10压测
     */
    private String batchSizes;

    /**
     * 初始线程
     */
    private Integer initialWorkers;

    /**
     * misID
     */
    private String misId;

    private boolean similarityComparison;

}