package com.sankuai.algoplatform.matchops.api.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.algoplatform.matchops.api.request.testScene.TAddEditTestSceneRequest;
import com.sankuai.algoplatform.matchops.api.request.testScene.TDeleteTestSceneRequest;
import com.sankuai.algoplatform.matchops.api.request.testScene.TTestSceneListRequest;
import com.sankuai.algoplatform.matchops.api.response.testScene.TAddTestSceneResponse;
import com.sankuai.algoplatform.matchops.api.response.testScene.TDeleteTestSceneResponse;
import com.sankuai.algoplatform.matchops.api.response.testScene.TEditTestSceneResponse;
import com.sankuai.algoplatform.matchops.api.response.testScene.TTestSceneListResponse;

@ThriftService
@InterfaceDoc(type = "octo.thrift.annotation", description = "测试场景接口", scenarios = "")
public interface TTestSceneService {
    @ThriftMethod
    @MethodDoc(description = "测试场景列表")
    TTestSceneListResponse getTestSceneList(TTestSceneListRequest request);

    @ThriftMethod
    @MethodDoc(description = "新增测试场景")
    TAddTestSceneResponse addTestSceneList(TAddEditTestSceneRequest request);

    @ThriftMethod
    @MethodDoc(description = "编辑测试场景")
    TEditTestSceneResponse editTestSceneList(TAddEditTestSceneRequest request);

    @ThriftMethod
    @MethodDoc(description = "删除测试场景")
    TDeleteTestSceneResponse delTestSceneList(TDeleteTestSceneRequest request);

}
