package com.sankuai.algoplatform.matchops.api.request.matchStrategy;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

@ThriftStruct
@Data
public class TDeleteMatchStrategyRequest {
    @FieldDoc(description = "策略id", example = {})
    @ThriftField(1)
    public Long matchStrategyId;
}
