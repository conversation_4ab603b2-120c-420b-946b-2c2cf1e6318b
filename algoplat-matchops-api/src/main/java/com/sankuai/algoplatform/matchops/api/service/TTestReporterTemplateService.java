package com.sankuai.algoplatform.matchops.api.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.algoplatform.matchops.api.request.testreprotertemplate.TAddEditTestReporterTemplateRequest;
import com.sankuai.algoplatform.matchops.api.request.testreprotertemplate.TDeleteTestReporterTemplateRequest;
import com.sankuai.algoplatform.matchops.api.request.testreprotertemplate.TTestReporterTemplateListRequest;
import com.sankuai.algoplatform.matchops.api.response.testreportertemplate.TAddTestReporterTemplateResponse;
import com.sankuai.algoplatform.matchops.api.response.testreportertemplate.TDeleteTestReporterTemplateResponse;
import com.sankuai.algoplatform.matchops.api.response.testreportertemplate.TEditTestReporterTemplateResponse;
import com.sankuai.algoplatform.matchops.api.response.testreportertemplate.TTestReporterTemplateListResponse;

@ThriftService
@InterfaceDoc(type = "octo.thrift.annotation", description = "测试模板接口", scenarios = "")
public interface TTestReporterTemplateService {
    @ThriftMethod
    @MethodDoc(description = "测试场景列表")
    TTestReporterTemplateListResponse getTestReporterTemplateList(TTestReporterTemplateListRequest request);

    @ThriftMethod
    @MethodDoc(description = "新增测试模板")
    TAddTestReporterTemplateResponse addTestReporterTemplate(TAddEditTestReporterTemplateRequest request);

    @ThriftMethod
    @MethodDoc(description = "编辑测试模板")
    TEditTestReporterTemplateResponse editTestReporterTemplate(TAddEditTestReporterTemplateRequest request);

    @ThriftMethod
    @MethodDoc(description = "删除测试模板")
    TDeleteTestReporterTemplateResponse delTestReporterTemplate(TDeleteTestReporterTemplateRequest request);

}
