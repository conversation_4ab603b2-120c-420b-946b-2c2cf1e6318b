package com.sankuai.algoplatform.matchops.api.request.testTask;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;
import org.apache.logging.log4j.core.config.plugins.validation.constraints.Required;

@ThriftStruct
@Data
public class TTestSubTaskListRequest {
    @FieldDoc(description = "任务Id", example = {})
    @ThriftField(1)
    @Required
    public Long testTaskId;

}
