package com.sankuai.algoplatform.matchops.api.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.algoplatform.matchops.api.request.testTask.*;
import com.sankuai.algoplatform.matchops.api.response.TBaseResponse;
import com.sankuai.algoplatform.matchops.api.response.resourceGroup.TTestTaskDetailResponse;
import com.sankuai.algoplatform.matchops.api.response.testTask.*;
import org.apache.logging.log4j.core.config.plugins.validation.constraints.Required;

@ThriftService
@InterfaceDoc(type = "octo.thrift.annotation", description = "测试任务接口", scenarios = "")
public interface TTestTaskService {

    @ThriftMethod
    @MethodDoc(description = "测试列表")
    TTestTaskListResponse getTestTaskList(TTestTaskListRequest request);

    @ThriftMethod
    @MethodDoc(description = "新增测试任务")
    TAddOrEditTestTaskResponse addTestTask(TAddOrEditTestTaskRequest request);

    @ThriftMethod
    @MethodDoc(description = "编辑测试任务")
    TAddOrEditTestTaskResponse editTestTask(TAddOrEditTestTaskRequest request);


    @ThriftMethod
    @MethodDoc(description = "任务执行")
    TRunTaskResponse runTask(TRunTaskRequest request);


    @ThriftMethod
    @MethodDoc(description = "环境准备")
    TBaseResponse prepareEnv(@Required String taskId);


    @ThriftMethod
    @MethodDoc(description = "查看任务详情")
    TTestTaskDetailResponse getTestTaskDetail(@Required String taskId, @Required String type);

    @ThriftMethod
    @MethodDoc(description = "通过任务名称查询测试任务")
    TTestTaskDetailResponse getTestTaskDetailByTaskName(@Required String taskName, @Required Long bizLineId);

    @ThriftMethod
    @MethodDoc(description = "查看任务记录详情")
    TTestSubTaskListResponse getTestSubTaskList(TTestSubTaskListRequest request);

    @ThriftMethod
    @MethodDoc(description = "任务执行")
    TRunTaskResponse runSubTask(@Required String testSubTaskId);

    @ThriftMethod
    @MethodDoc(description = "任务停止")
    TBaseResponse stopSubTask(@Required String testSubTaskId);

    @ThriftMethod
    @MethodDoc(description = "任务完成")
    TBaseResponse doneSubTask(@Required String testSubTaskId);


    @ThriftMethod
    @MethodDoc(description = "生成测试报告")
    TBaseResponse generateReporter(@Required String testSubTaskId);

}
