package com.sankuai.algoplatform.matchops.api.request.prompt;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

@ThriftStruct
@Data
public class TPromptListRequest {
    @FieldDoc(description = "开始页数", example = {})
    @ThriftField(1)
    public Integer current;

    @FieldDoc(description = "业务线id", example = {})
    @ThriftField(2)
    public Long BizLineId;
}
