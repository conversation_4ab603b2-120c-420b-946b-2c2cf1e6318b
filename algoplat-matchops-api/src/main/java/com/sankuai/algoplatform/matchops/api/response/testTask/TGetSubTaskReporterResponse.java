package com.sankuai.algoplatform.matchops.api.response.testTask;

import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.algoplatform.matchops.api.response.TBaseResponse;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class TGetSubTaskReporterResponse extends TBaseResponse {

    @FieldDoc(description = "返回结果", example = {})
    @ThriftField(3)
    public List<Map<String, String>> data;
}
