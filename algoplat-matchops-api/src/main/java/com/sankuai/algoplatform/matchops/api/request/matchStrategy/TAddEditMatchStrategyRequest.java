package com.sankuai.algoplatform.matchops.api.request.matchStrategy;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.util.List;
import java.util.Map;

@ThriftStruct
@Data
public class TAddEditMatchStrategyRequest {
    @FieldDoc(description = "匹配策略名称", example = {})
    @ThriftField(1)
    public String matchStrategyName;

    @FieldDoc(description = "匹配策略id", example = {})
    @ThriftField(2)
    public Long matchStrategyId;

    @FieldDoc(description = "测试场景id", example = {})
    @ThriftField(3)
    public Long sceneId;

    @FieldDoc(description = "业务线id", example = {})
    @ThriftField(4)
    public Long bizLineId;

    @FieldDoc(description = "算法代码bizcode", example = {})
    @ThriftField(5)
    public List<Map<String,String>> algoCodeInfo;

    @FieldDoc(description = "缓存策略", example = {})
    @ThriftField(6)
    public Map<String,String> predictorCache;

    @FieldDoc(description = "大模型bizcode", example = {})
    @ThriftField(7)
    public List<Map<String,String>> llmInfo;

    @FieldDoc(description = "octo信息", example = {})
    @ThriftField(8)
    public List<Map<String,String>> octoInfo;

}
