package com.sankuai.algoplatform.matchops.api.request.mcptool;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import org.apache.curator.shaded.com.google.common.collect.Lists;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: algoplat-matchops
 * <AUTHOR>
 * @Date 2025/5/23
 */

@TypeDoc(name = "TMcpTestMissionRequest", description = "匹配测试工具 Badcase率测试请求")
@ThriftStruct
public class TMcpTestMissionRequest implements Serializable {

    @FieldDoc(name = "testType", description = "测试类型。1:业务回测（badcase率测试） 2.一致性测试  3.GSB测试  4.整体性能压测 5.单模型压测", requiredness = Requiredness.REQUIRED)
    private String testType;

    @FieldDoc(name = "bu", description = "产业类型。全部 美容美体 境内竞对", requiredness = Requiredness.REQUIRED)
    private String bu;

    @FieldDoc(name = "industryType", description = "业务类型代号。1到餐 2到综 3住宿", requiredness = Requiredness.REQUIRED)
    private String industryType;

    @FieldDoc(name = "matchStandard", description = "匹配标准。精准匹配 商品匹配 房型匹配", requiredness = Requiredness.REQUIRED)
    private String matchStandard;

    @FieldDoc(name = "testScene", description = "餐场景，1货架*货架、2货架*直播、3直播*直播、4直播*货架", requiredness = Requiredness.OPTIONAL)
    private String testScene;

    @FieldDoc(name = "postXtVersion", description = "发起XT链接。需要人工提前修改配置的hour、线上线下等信息", requiredness = Requiredness.OPTIONAL)
    private String postXtVersion;

    @FieldDoc(name = "parseXtVersion", description = "解析XT链接", requiredness = Requiredness.OPTIONAL)
    private String parseXtVersion;

    @FieldDoc(name = "testDataDate", description = "测试数据日期", requiredness = Requiredness.OPTIONAL)
    private String testDataDate;

    @FieldDoc(name = "algoPackageInfos", description = "算法包信息 选填，如果commitId未变动可以不填", requiredness = Requiredness.OPTIONAL)
    private List<AlgoPackageInfo> algoPackageInfos = Lists.newArrayList();

    @FieldDoc(name = "predictorClearCache", description = "是否清除缓存,默认不清除", requiredness = Requiredness.OPTIONAL)
    private Boolean predictorClearCache = false;

    @FieldDoc(name = "misId", description = "用户mis号", requiredness = Requiredness.REQUIRED)
    private String misId;

    @FieldDoc(name = "cpuInfos", description = "cpu信息", requiredness = Requiredness.OPTIONAL)
    private List<CpuInfo> cpuInfos = Lists.newArrayList();

    @FieldDoc(name = "sessionId", description = "会话ID", requiredness = Requiredness.REQUIRED)
    private String sessionId;

    @FieldDoc(name = "llmInfos", description = "LLM详情列表", requiredness = Requiredness.OPTIONAL)
    private List<LlmDetail> llmInfos = Lists.newArrayList();

    @FieldDoc(name = "hour", description = "小时分区", requiredness = Requiredness.OPTIONAL)
    private String hour = "04";

    @FieldDoc(name = "testDataSet", description = "测试数据集s3链接", requiredness = Requiredness.OPTIONAL)
    private String testDataSet;


    @FieldDoc(name= "extraInfo",description = "extraInfo",requiredness = Requiredness.OPTIONAL)
    private Map<String,String> extraInfo = new HashMap<>();


    //以下为模型压测专用参数
    @FieldDoc(name = "modelType", description = "模型类型。枚举值：tf、bge、llm", requiredness = Requiredness.OPTIONAL)
    private String modelType;

    @FieldDoc(name = "modelName", description = "模型名称", requiredness = Requiredness.OPTIONAL)
    private String modelName;

    @FieldDoc(name = "modelPath", description = "模型路径", requiredness = Requiredness.OPTIONAL)
    private String modelPath;

    @FieldDoc(name = "modelVersion", description = "模型版本", requiredness = Requiredness.OPTIONAL)
    private String modelVersion;

    @FieldDoc(name = "instanceNum", description = "实例数", requiredness = Requiredness.OPTIONAL)
    private String instanceNum;


    @ThriftField(1)
    public String getTestType() {
        return testType;
    }

    @ThriftField()
    public void setTestType(String testType) {
        this.testType = testType;
    }

    @ThriftField(2)
    public String getBu() {
        return bu;
    }

    @ThriftField()
    public void setBu(String bu) {
        this.bu = bu;
    }

    @ThriftField(3)
    public String getIndustryType() {
        return industryType;
    }

    @ThriftField()
    public void setIndustryType(String industryType) {
        this.industryType = industryType;
    }

    @ThriftField(4)
    public String getMatchStandard() {
        return matchStandard;
    }

    @ThriftField()
    public void setMatchStandard(String matchStandard) {
        this.matchStandard = matchStandard;
    }

    @ThriftField(5)
    public String getTestScene() {
        return testScene;
    }

    @ThriftField()
    public void setTestScene(String testScene) {
        this.testScene = testScene;
    }

    @ThriftField(6)
    public String getPostXtVersion() {
        return postXtVersion;
    }

    @ThriftField()
    public void setPostXtVersion(String postXtVersion) {
        this.postXtVersion = postXtVersion;
    }

    @ThriftField(7)
    public String getParseXtVersion() {
        return parseXtVersion;
    }

    @ThriftField()
    public void setParseXtVersion(String parseXtVersion) {
        this.parseXtVersion = parseXtVersion;
    }

    @ThriftField(8)
    public String getTestDataDate() {
        return testDataDate;
    }

    @ThriftField()
    public void setTestDataDate(String testDataDate) {
        this.testDataDate = testDataDate;
    }

    @ThriftField(9)
    public List<AlgoPackageInfo> getAlgoPackageInfos() {
        return algoPackageInfos;
    }

    @ThriftField()
    public void setAlgoPackageInfos(List<AlgoPackageInfo> algoPackageInfos) {
        this.algoPackageInfos = algoPackageInfos;
    }

    @ThriftField(10)
    public Boolean getPredictorClearCache() {
        return predictorClearCache;
    }

    @ThriftField()
    public void setPredictorClearCache(Boolean predictorClearCache) {
        this.predictorClearCache = predictorClearCache;
    }

    @ThriftField(11)
    public String getMisId() {
        return misId;
    }

    @ThriftField()
    public void setMisId(String misId) {
        this.misId = misId;
    }

    @ThriftField(12)
    public List<CpuInfo> getCpuInfos() {
        return cpuInfos;
    }

    @ThriftField()
    public void setCpuInfos(List<CpuInfo> cpuInfos) {
        this.cpuInfos = cpuInfos;
    }

    @ThriftField(13)
    public String getSessionId() {
        return sessionId;
    }

    @ThriftField()
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    @ThriftField(14)
    public List<LlmDetail> getLlmInfos() {
        return llmInfos;
    }

    @ThriftField()
    public void setLlmInfos(List<LlmDetail> llmInfos) {
        this.llmInfos = llmInfos;
    }

    @ThriftField(15)
    public String getHour() {
        return hour;
    }

    @ThriftField()
    public void setHour(String hour) {
        this.hour = hour;
    }

    @ThriftField(16)
    public String getTestDataSet() {
        return testDataSet;
    }

    @ThriftField()
    public void setTestDataSet(String testDataSet) {
        this.testDataSet = testDataSet;
    }

    @ThriftField(17)
    public Map<String, String> getExtraInfo() {
        return extraInfo;
    }

    @ThriftField()
    public void setExtraInfo(Map<String, String> extraInfo) {
        this.extraInfo = extraInfo;
    }

    @ThriftField(18)
    public String getModelType() {
        return modelType;
    }

    @ThriftField()
    public void setModelType(String modelType) {
        this.modelType = modelType;
    }

    @ThriftField(19)
    public String getModelName() {
        return modelName;
    }

    @ThriftField()
    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    @ThriftField(20)
    public String getModelPath() {
        return modelPath;
    }

    @ThriftField()
    public void setModelPath(String modelPath) {
        this.modelPath = modelPath;
    }

    @ThriftField(21)
    public String getModelVersion() {
        return modelVersion;
    }

    @ThriftField()
    public void setModelVersion(String modelVersion) {
        this.modelVersion = modelVersion;
    }

    @ThriftField(22)
    public String getInstanceNum() {
        return instanceNum;
    }

    @ThriftField()
    public void setInstanceNum(String instanceNum) {
        this.instanceNum = instanceNum;
    }

    public TMcpTestMissionRequest() {

    }

    public TMcpTestMissionRequest(String testType, String bu, String industryType, String matchStandard,
                                  String testScene, String postXtVersion, String parseXtVersion,
                                  String testDataDate, List<AlgoPackageInfo> algoPackageInfos,
                                  Boolean predictorClearCache, String misId, List<CpuInfo> cpuInfos, String sessionId, List<LlmDetail> llmInfos, String hour, String testDataSet, Map<String, String> extraInfo) {
        this.testType = testType;
        this.bu = bu;
        this.industryType = industryType;
        this.matchStandard = matchStandard;
        this.testScene = testScene;
        this.postXtVersion = postXtVersion;
        this.parseXtVersion = parseXtVersion;
        this.testDataDate = testDataDate;
        this.algoPackageInfos = algoPackageInfos;
        this.predictorClearCache = predictorClearCache;
        this.misId = misId;
        this.cpuInfos = cpuInfos;
        this.sessionId = sessionId;
        this.llmInfos = llmInfos;
        this.hour = hour;
        this.testDataSet = testDataSet;
        this.extraInfo = extraInfo;
    }

}
