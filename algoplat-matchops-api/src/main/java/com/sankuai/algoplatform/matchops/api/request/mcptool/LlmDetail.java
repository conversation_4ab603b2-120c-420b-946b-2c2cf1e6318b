package com.sankuai.algoplatform.matchops.api.request.mcptool;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @program: algoplat-matchops
 * <AUTHOR>
 * @Date 2025/5/24
 */


@TypeDoc(name = "LlmDetail", description = "大模型参数")
@ThriftStruct
public class LlmDetail {

    @NotBlank(message = "modelName模型名称不能为空")
    @FieldDoc(description = "模型名称", example = {})
    private String modelName;

    @NotNull(message = "batchSize不能为空")
    @Min(value = 1, message = "batchSize必须大于0")
    @FieldDoc(description = "批次大小", example = {})
    private Integer batchSize;

    @NotNull(message = "useTemplate是否使用模版不能为空")
    @FieldDoc(description = "是否使用模版", example = {})
    private Boolean useTemplate;

    @FieldDoc(description = "prompt模版", example = {})
    private String promptTemplate;
    @NotNull(message = "modelBaseName模型基础名称不能为空")

    @FieldDoc(description = "模型基础名称", example = {})
    private String modelBaseName;

    @NotNull(message = "trainMethod训练方法不能为空")
    @FieldDoc(description = "训练方法", example = {})
    private String trainMethod;

    @Valid
    @NotNull(message = "modelParameters模型参数不能为空")
    @FieldDoc(description = "模型参数", example = {})
    private modelParam modelParam;

    @FieldDoc(description = "实例数量", example = {})
    private String instanceNum = "1";

    @ThriftField(1)
    public String getModelName() {
        return modelName;
    }
    @ThriftField()
    public void setModelName(String modelName) {
        this.modelName = modelName;
    }
    @ThriftField(2)
    public Integer getBatchSize() {
        return batchSize;
    }
    @ThriftField()
    public void setBatchSize(Integer batchSize) {
        this.batchSize = batchSize;
    }
    @ThriftField(3)
    public Boolean getUseTemplate() {
        return useTemplate;
    }
    @ThriftField()
    public void setUseTemplate(Boolean useTemplate) {
        this.useTemplate = useTemplate;
    }
    @ThriftField(4)
    public String getPromptTemplate() {
        return promptTemplate;
    }
    @ThriftField()
    public void setPromptTemplate(String promptTemplate) {
        this.promptTemplate = promptTemplate;
    }
    @ThriftField(5)
    public String getModelBaseName() {
        return modelBaseName;
    }
    @ThriftField()
    public void setModelBaseName(String modelBaseName) {
        this.modelBaseName = modelBaseName;
    }
    @ThriftField(6)
    public String getTrainMethod() {
        return trainMethod;
    }
    @ThriftField()
    public void setTrainMethod(String trainMethod) {
        this.trainMethod = trainMethod;
    }
    @ThriftField(7)
    public modelParam getModelParam() {
        return modelParam;
    }
    @ThriftField()
    public void setModelParam(modelParam modelParam) {
        this.modelParam = modelParam;
    }
    @ThriftField(8)
    public String getInstanceNum() {
        return instanceNum;
    }
    @ThriftField()
    public void setInstanceNum(String instanceNum) {
        this.instanceNum = instanceNum;
    }

    public LlmDetail() {
    }


    public LlmDetail(String modelName, Integer batchSize, Boolean useTemplate, String promptTemplate, String modelBaseName, String trainMethod, modelParam modelParam, String instanceNum) {
        this.modelName = modelName;
        this.batchSize = batchSize;
        this.useTemplate = useTemplate;
        this.promptTemplate = promptTemplate;
        this.modelBaseName = modelBaseName;
        this.trainMethod = trainMethod;
        this.modelParam = modelParam;
        this.instanceNum = instanceNum;
    }

}
