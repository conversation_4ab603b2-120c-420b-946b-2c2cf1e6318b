package com.sankuai.algoplatform.matchops.api.request.testreprotertemplate;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

@ThriftStruct
@Data
public class TAddEditTestReporterTemplateRequest {

    @FieldDoc(description = "模版id", example = {})
    @ThriftField(1)
    public Long templateId;
    @FieldDoc(description = "模版名称", example = {})
    @ThriftField(2)
    public String templateName;

    @FieldDoc(description = "模版地址", example = {})
    @ThriftField(3)
    public String templateAddr;

    @FieldDoc(description = "业务线id", example = {})
    @ThriftField(5)
    public Long bizLineId;
}
