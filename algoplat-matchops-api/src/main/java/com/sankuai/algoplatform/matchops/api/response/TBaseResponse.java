
package com.sankuai.algoplatform.matchops.api.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

@ThriftStruct
@Data
public class TBaseResponse {

    @FieldDoc(description = "状态码", example = {})
    @ThriftField(1)
    public Integer code;

    @FieldDoc(description = "错误信息", example = {})
    @ThriftField(2)
    public String message;


}
