package com.sankuai.algoplatform.matchops.api.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.algoplatform.matchops.api.request.algoPackage.TAddEditAlgoBizCodeRequest;
import com.sankuai.algoplatform.matchops.api.request.algoPackage.TAddEditAlgoStrategyRequest;
import com.sankuai.algoplatform.matchops.api.request.algoPackage.TAlgoCodePackageListRequest;
import com.sankuai.algoplatform.matchops.api.request.algoPackage.TDeleteAlgoBizCodeRequest;
import com.sankuai.algoplatform.matchops.api.request.prompt.TAddEditPromptBizCodeRequest;
import com.sankuai.algoplatform.matchops.api.request.prompt.TAddEditPromptStrategyRequest;
import com.sankuai.algoplatform.matchops.api.request.prompt.TDeletePromptBizCodeRequest;
import com.sankuai.algoplatform.matchops.api.request.prompt.TPromptListRequest;
import com.sankuai.algoplatform.matchops.api.response.algoPackage.*;
import com.sankuai.algoplatform.matchops.api.response.prompt.*;

@ThriftService
@InterfaceDoc(type = "octo.thrift.annotation", description = "Prompt管理", scenarios = "")
public interface TPromptStrategyService {
    @ThriftMethod
    @MethodDoc(description = "LLM列表")
    TPromptListResponse getPromptList(TPromptListRequest request);

    @ThriftMethod
    @MethodDoc(description = "新增LLM策略")
    TAddPromptStrategyResponse addPromptStrategy(TAddEditPromptStrategyRequest request);

    @ThriftMethod
    @MethodDoc(description = "修改LLM策略")
    TEditPromptStrategyResponse editPromptStrategy(TAddEditPromptStrategyRequest request);


    @ThriftMethod
    @MethodDoc(description = "新增LLM bizCode")
    TAddPromptBizCodeResponse addPromptBizCode(TAddEditPromptBizCodeRequest request);

    @ThriftMethod
    @MethodDoc(description = "修改LLM bizCode")
    TEditPromptBizCodeResponse editPromptBizCode(TAddEditPromptBizCodeRequest request);

    @ThriftMethod
    @MethodDoc(description = "删除LLM bizCode")
    TDeletePromptBizCodeResponse deletePromptBizCode(TDeletePromptBizCodeRequest request);

}
