package com.sankuai.algoplatform.matchops.api.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.algoplatform.matchops.api.request.algoPackage.TAddEditAlgoBizCodeRequest;
import com.sankuai.algoplatform.matchops.api.request.algoPackage.TAddEditAlgoStrategyRequest;
import com.sankuai.algoplatform.matchops.api.request.algoPackage.TAlgoCodePackageListRequest;
import com.sankuai.algoplatform.matchops.api.request.algoPackage.TDeleteAlgoBizCodeRequest;
import com.sankuai.algoplatform.matchops.api.response.algoPackage.*;

@ThriftService
@InterfaceDoc(type = "octo.thrift.annotation", description = "算法代码包接口", scenarios = "")
public interface TAlgoPackageService {
    @ThriftMethod
    @MethodDoc(description = "算法代码包列表")
    TAlgoPackageListResponse getAlgoCodePackageList(TAlgoCodePackageListRequest request);

    @ThriftMethod
    @MethodDoc(description = "新增算法代码策略")
    TAddAlgoStrategyResponse addAlgoStrategy(TAddEditAlgoStrategyRequest request);

    @ThriftMethod
    @MethodDoc(description = "修改算法代码策略")
    TEditAlgoStrategyResponse editAlgoStrategy(TAddEditAlgoStrategyRequest request);


    @ThriftMethod
    @MethodDoc(description = "新增算法代码bizCode")
    TAddAlgoBizCodeResponse addAlgoBizCode(TAddEditAlgoBizCodeRequest request);

    @ThriftMethod
    @MethodDoc(description = "修改算法代码bizCode")
    TEditAlgoBizCodeResponse editAlgoBizCode(TAddEditAlgoBizCodeRequest request);

    @ThriftMethod
    @MethodDoc(description = "删除算法代码bizCode")
    TDeleteAlgoBizCodeResponse deleteAlgoBizCode(TDeleteAlgoBizCodeRequest request);

}
