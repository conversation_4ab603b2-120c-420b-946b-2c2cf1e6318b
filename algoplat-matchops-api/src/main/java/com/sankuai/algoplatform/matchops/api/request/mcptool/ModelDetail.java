package com.sankuai.algoplatform.matchops.api.request.mcptool;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * @program: algoplat-matchops
 * <AUTHOR>
 * @Date 2025/5/27
 */
@TypeDoc(name = "ModelDetail", description = "模型详情")
@ThriftStruct
public class ModelDetail {

    /**
     * 模型类型。枚举值：bge、tf。必填
     */
    @FieldDoc(description = "模型类型", example = {})
    private String modelType;

    /**
     * 注册的模型名称。必填，如果没迭代，写1个之前已有的
     */
    @FieldDoc(description = "模型名称", example = {})
    private String name;

    /**
     * 需要的实例数。必填
     */
    @FieldDoc(description = "实例数量", example = {})
    private String instanceNum;

    @FieldDoc(description = "模型版本", example = {})
    private String modelVersion;
    /**
     * 获取模型类型
     * @return 模型类型
     */
    @ThriftField(1)
    public String getModelType() {
        return modelType;
    }

    /**
     * 设置模型类型
     * @param modelType 模型类型，枚举值：bge、tf
     */
    @ThriftField()
    public void setModelType(String modelType) {
        this.modelType = modelType;
    }

    /**
     * 获取模型名称
     * @return 模型名称
     */
    @ThriftField(2)
    public String getName() {
        return name;
    }

    /**
     * 设置模型名称
     * @param name 模型名称
     */
    @ThriftField()
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取实例数量
     * @return 实例数量
     */
    @ThriftField(3)
    public String getInstanceNum() {
        return instanceNum;
    }

    /**
     * 设置实例数量
     * @param instanceNum 实例数量
     */
    @ThriftField()
    public void setInstanceNum(String instanceNum) {
        this.instanceNum = instanceNum;
    }

    /**
     * 获取模型版本
     * @return 模型版本
     */
    @ThriftField(4)
    public String getModelVersion() {
        return modelVersion;
    }

    /**
     * 设置模型版本
     * @param modelVersion 模型版本
     */
    @ThriftField()
    public void setModelVersion(String modelVersion) {
        this.modelVersion = modelVersion;
    }

    public ModelDetail() {
    }

    public ModelDetail(String modelType, String name, String instanceNum, String modelVersion) {
        this.modelType = modelType;
        this.name = name;
        this.instanceNum = instanceNum;
        this.modelVersion = modelVersion;
    }

}