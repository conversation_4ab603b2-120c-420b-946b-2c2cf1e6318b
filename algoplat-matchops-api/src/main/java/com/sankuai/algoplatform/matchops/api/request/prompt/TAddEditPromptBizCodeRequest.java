package com.sankuai.algoplatform.matchops.api.request.prompt;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.util.Map;

@ThriftStruct
@Data
public class TAddEditPromptBizCodeRequest {
    @FieldDoc(description = "大模型策略id", example = {})
    @ThriftField(1)
    public Long llmBizCodeId;

    @FieldDoc(description = "bizcode", example = {})
    @ThriftField(2)
    public String llmBizCode;

    @FieldDoc(description = "模型路径", example = {})
    @ThriftField(3)
    public Map<String,String> strategy;

    @FieldDoc(description = "说明", example = {})
    @ThriftField(4)
    public String note;

    @FieldDoc(description = "bizLineId", example = {})
    @ThriftField(5)
    public Long bizLineId;
}
