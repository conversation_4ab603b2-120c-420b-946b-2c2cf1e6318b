package com.sankuai.algoplatform.matchops.api.request.prompt;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

@ThriftStruct
@Data
public class TDeletePromptBizCodeRequest {
    @FieldDoc(description = "llmbizstrategyid", example = {})
    @ThriftField(1)
    public Long llmStrategyId;

    @FieldDoc(description = "onwer", example = {})
    @ThriftField(2)
    public String owner;
}
