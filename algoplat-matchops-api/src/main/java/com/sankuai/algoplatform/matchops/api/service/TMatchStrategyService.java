package com.sankuai.algoplatform.matchops.api.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.algoplatform.matchops.api.request.matchStrategy.*;
import com.sankuai.algoplatform.matchops.api.response.TBaseResponse;
import com.sankuai.algoplatform.matchops.api.response.matchStrategy.*;
import org.apache.thrift.TBase;

@ThriftService
@InterfaceDoc(type = "octo.thrift.annotation", description = "匹配策略接口", scenarios = "")
public interface TMatchStrategyService {
    @ThriftMethod
    @MethodDoc(description = "匹配策略列表")
    TMatchStrategyListResponse getMatchStrategyList(TMatchStrategyListRequest request);

    @ThriftMethod
    @MethodDoc(description = "新增匹配策略列表")
    TAddMatchStrategyResponse addMatchStrategy(TAddEditMatchStrategyRequest request);

    @ThriftMethod
    @MethodDoc(description = "更新匹配策略状态")
    void updateMatchStrategyStatus(Long matchStrategyId,Integer status);

    @ThriftMethod
    @MethodDoc(description = "编辑匹配策略列表")
    TEditMatchStrategyResponse editMatchStrategy(TAddEditMatchStrategyRequest request);

    @ThriftMethod
    @MethodDoc(description = "查看匹配策略")
    TQueryMatchStrategyResponse queryMatchStrategy(TQueryMatchStrategyRequest request);

    @ThriftMethod
    @MethodDoc(description = "删除匹配策略")
    TDeleteMatchStrategyResponse deleteMatchStrategy(TDeleteMatchStrategyRequest request);

    @ThriftMethod
    @MethodDoc(description = "查看匹配策略")
    TQueryMatchStrategyResponse queryDeployInfo(TQueryMatchStrategyRequest request);

    @ThriftMethod
    @MethodDoc(description = "查看匹配策略")
    TQueryMatchStrategyResponse queryRollbackInfo(TQueryMatchStrategyRequest request);

    @ThriftMethod
    @MethodDoc(description = "上线匹配策略")
    TBaseResponse deployMatchStrategy(TDeployRollbackMatchStrategyRequest request);

    @ThriftMethod
    @MethodDoc(description = "回滚匹配策略")
    TBaseResponse rollbackMatchStrategy(TDeployRollbackMatchStrategyRequest request);

    @ThriftMethod
    @MethodDoc(description = "通过匹配策略名称获取匹配策略详情")
    TQueryMatchStrategyResponse queryMatchStrategyDetail(TQueryMatchStrategyRequest request);

}
