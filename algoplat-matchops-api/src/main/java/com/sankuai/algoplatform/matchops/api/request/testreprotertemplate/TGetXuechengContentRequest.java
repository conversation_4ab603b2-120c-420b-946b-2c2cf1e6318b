
package com.sankuai.algoplatform.matchops.api.request.testreprotertemplate;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

@ThriftStruct
@Data
public class TGetXuechengContentRequest {

    @FieldDoc(description = "学城链接", example = {})
    @ThriftField(1)
    public String link;

    @FieldDoc(description = "ssoId", example = {})
    @ThriftField(2)
    public String ssoId;
}
