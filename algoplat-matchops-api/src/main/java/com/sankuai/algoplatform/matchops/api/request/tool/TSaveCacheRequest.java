package com.sankuai.algoplatform.matchops.api.request.tool;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 保存缓存请求
 *
 * <AUTHOR>
 * @date 2025/4/30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
public class TSaveCacheRequest {

    @FieldDoc(description = "缓存Key前缀")
    @ThriftField(1)
    private String cacheKeyPrefix;

    @FieldDoc(description = "缓存数据")
    @ThriftField(2)
    private String value;
}