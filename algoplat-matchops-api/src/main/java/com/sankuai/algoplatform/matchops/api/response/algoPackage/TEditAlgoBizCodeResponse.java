
package com.sankuai.algoplatform.matchops.api.response.algoPackage;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.algoplatform.matchops.api.response.TBaseResponse;
import lombok.Data;

import java.util.Map;

@ThriftStruct
@Data
public class TEditAlgoBizCodeResponse extends TBaseResponse {
    @FieldDoc(description = "返回结果", example = {})
    @ThriftField(3)
    public Map<String,String> data;
}
