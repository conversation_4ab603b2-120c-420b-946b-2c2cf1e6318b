package com.sankuai.algoplatform.matchops.api.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.algoplatform.matchops.api.request.testreprotertemplate.TGetXuechengContentRequest;
import com.sankuai.algoplatform.matchops.api.response.testreportertemplate.TGetXuechengContentResponse;

@ThriftService
@InterfaceDoc(type = "octo.thrift.annotation", description = "读学城文档", scenarios = "")
public interface TXuechengService {


    @ThriftMethod
    @MethodDoc(description = "读学城链接内容")
    TGetXuechengContentResponse getXuechengContent(TGetXuechengContentRequest request);

}
