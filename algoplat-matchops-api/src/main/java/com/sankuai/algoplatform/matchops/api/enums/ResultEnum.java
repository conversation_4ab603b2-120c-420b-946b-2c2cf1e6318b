package com.sankuai.algoplatform.matchops.api.enums;


public enum ResultEnum {
    SUCCESS(0, "请求成功"),

    PARAMS_FAIL(102, "参数错误"),

    SYS_ERROR(9999, "系统异常"),

    ;

    private int code;
    private String msg;

    ResultEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }


    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
