package com.sankuai.algoplatform.matchops.api.request.testScene;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

@ThriftStruct
@Data
public class TAddEditTestSceneRequest {

    @FieldDoc(description = "场景id", example = {})
    @ThriftField(1)

    public Long sceneId;
    @FieldDoc(description = "场景名称", example = {})
    @ThriftField(2)
    public String sceneName;

    @FieldDoc(description = "跑数类型", example = {})
    @ThriftField(3)
    public Integer runType;

    @FieldDoc(description = "跑数地址", example = {})
    @ThriftField(4)
    public String runAddress;

    @FieldDoc(description = "业务线id", example = {})
    @ThriftField(5)
    public Long bizLineId;


    @FieldDoc(description = "其他信息", example = {})
    @ThriftField(6)
    public String extra;

}
