package com.sankuai.algoplatform.matchops.api.request.algoPackage;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

@ThriftStruct
@Data
public class TDeleteAlgoBizCodeRequest {
    @FieldDoc(description = "算法bizcodeId", example = {})
    @ThriftField(1)
    public Long algoBizCodeId;

    @FieldDoc(description = "onwer", example = {})
    @ThriftField(2)
    public String owner;
}
