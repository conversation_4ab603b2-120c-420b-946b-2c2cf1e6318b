package com.sankuai.algoplatform.matchops.api.request.mcptool;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * @program: algoplat-matchops
 * <AUTHOR>
 * @Date 2025/5/23
 */

@TypeDoc(name = "CpuInfo", description = "CpuInfo")
@ThriftStruct
public class CpuInfo {

    /**
     * 二级品类名称
     */

    @FieldDoc(description = "二级品类名称", example = {})
    private String secondCateName;

    /**
     * 实例数量
     */
    @FieldDoc(description = "实例数量", example = {})
    private Integer instanceNum = 1;

    // 无参构造方法
    public CpuInfo() {
    }

    // 有参构造方法
    public CpuInfo(String secondCateName, Integer instanceNum) {
        this.secondCateName = secondCateName;
        this.instanceNum = instanceNum;
    }

    @ThriftField(1)
    public String getSecondCateName() {
        return secondCateName;
    }
    @ThriftField()
    public void setSecondCateName(String secondCateName) {
        this.secondCateName = secondCateName;
    }
    @ThriftField(2)
    public Integer getInstanceNum() {
        return instanceNum;
    }
    @ThriftField()
    public void setInstanceNum(Integer instanceNum) {
        this.instanceNum = instanceNum;
    }
}
