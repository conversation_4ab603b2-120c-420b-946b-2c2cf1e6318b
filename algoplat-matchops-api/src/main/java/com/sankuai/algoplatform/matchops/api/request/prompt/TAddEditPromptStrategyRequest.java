package com.sankuai.algoplatform.matchops.api.request.prompt;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;
import org.apache.logging.log4j.core.config.plugins.validation.constraints.Required;

import java.util.Map;

@ThriftStruct
@Data
public class TAddEditPromptStrategyRequest {
    @FieldDoc(description = "llm策略id", example = {})
    @ThriftField(1)
    public Long llmStrategyId;

    @FieldDoc(description = "策略名称", example = {})
    @ThriftField(2)
    public String llmStrategyName;

    @FieldDoc(description = "批次大小", example = {})
    @ThriftField(3)
    public Long batchSize;

    @FieldDoc(description = "使用使用模板", example = {})
    @ThriftField(4)
    public Integer isTemplate;

    @FieldDoc(description = "appId", example = {})
    @ThriftField(5)
    public String appId;

    @FieldDoc(description = "是否开启debug模式", example = {})
    @ThriftField(6)
    public Integer isDebug;

    @FieldDoc(description = "maxTokens", example = {})
    @ThriftField(7)
    public Long maxTokens;

    @FieldDoc(description = "topP", example = {})
    @ThriftField(8)
    public Double topP;

    @FieldDoc(description = "topK", example = {})
    @ThriftField(9)
    public Double topK;

    @FieldDoc(description = "temperature", example = {})
    @ThriftField(10)
    public Double temperature;

    @FieldDoc(description = "prompt", example = {})
    @ThriftField(11)
    public String prompt;

    @FieldDoc(description = "note", example = {})
    @ThriftField(12)
    public String note;

    @FieldDoc(description = "llmModelName", example = {})
    @ThriftField(13)
    public String llmModelName;

    @FieldDoc(description = "llmServiceName", example = {})
    @ThriftField(14)
    public String llmServiceName;

    @FieldDoc(description = "llmStrategyBizCode", example = {})
    @ThriftField(15)
    public String llmStrategyBizCode;

    @FieldDoc(description = "bizLineId", example = {})
    @ThriftField(16)
    @Required
    public String bizLineId;

}
