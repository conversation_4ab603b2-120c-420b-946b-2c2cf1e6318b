package com.sankuai.algoplatform.matchops.api.request.testTask;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.Data;

import java.util.List;
import java.util.Map;

@ThriftStruct
@Data
public class TAddOrEditTestTaskRequest {

    @FieldDoc(description = "任务ID", example = {})
    @ThriftField(1)
    public Long testTaskId;

    @FieldDoc(description = "任务名称", example = {})
    @ThriftField(2)
    public String testTaskName;

    @FieldDoc(description = "业务线id", example = {})
    @ThriftField(3)
    public Long bizLineId;

    @FieldDoc(description = "匹配策略id", example = {})
    @ThriftField(4)
    public Long matchStrategyId;


    @FieldDoc(description = "资源组id", example = {})
    @ThriftField(5)
    public Long resourceGroupId;

    @FieldDoc(description = "测试报告模板ids", example = {})
    @ThriftField(6)
    public List<Long> testTemplates;

    @FieldDoc(description = "任务执行状态", example = {}, requiredness = Requiredness.OPTIONAL)
    @ThriftField(7)
    public Integer status;

    @FieldDoc(description = "extra", example = {}, requiredness = Requiredness.OPTIONAL)
    @ThriftField(8)
    public Map<String,String> extra;

}
