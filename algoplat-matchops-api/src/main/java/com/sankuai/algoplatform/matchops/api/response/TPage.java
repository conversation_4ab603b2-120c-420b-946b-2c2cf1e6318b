package com.sankuai.algoplatform.matchops.api.response;


import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.util.List;

@ThriftStruct
@Data
public class TPage<T> {

    @FieldDoc(description = "当前页", example = {})
    @ThriftField(1)
    public Integer current;

    @FieldDoc(description = "总条数", example = {})
    @ThriftField(2)
    public Long total;

    @FieldDoc(description = "分页列表", example = {})
    @ThriftField(3)
    public List<T> list;
}
