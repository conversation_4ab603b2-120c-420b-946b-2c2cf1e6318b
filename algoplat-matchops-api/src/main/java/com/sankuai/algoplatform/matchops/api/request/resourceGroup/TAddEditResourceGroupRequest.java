package com.sankuai.algoplatform.matchops.api.request.resourceGroup;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.util.List;
import java.util.Map;

@ThriftStruct
@Data
public class TAddEditResourceGroupRequest {
    @FieldDoc(description = "资源组id", example = {})
    @ThriftField(1)
    public Long resourceGroupId;

    @FieldDoc(description = "资源组名称", example = {})
    @ThriftField(2)
    public String resourceGroupName;

    @FieldDoc(description = "cpu资源", example = {})
    @ThriftField(3)
    public List<Map<String,String>> cpuResourceInfo;

    @FieldDoc(description = "mlp资源", example = {})
    @ThriftField(4)
    public List<Map<String,String>> mlpResourceInfo;

    @FieldDoc(description = "大模型资源", example = {})
    @ThriftField(5)
    public List<Map<String,String>> llmResourceInfo;

    @FieldDoc(description = "业务线id", example = {})
    @ThriftField(6)
    public Long bizLineId;
}
