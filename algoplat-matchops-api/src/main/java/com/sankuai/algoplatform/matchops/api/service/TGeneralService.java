package com.sankuai.algoplatform.matchops.api.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.algoplatform.matchops.api.request.general.TDropdownRequest;
import com.sankuai.algoplatform.matchops.api.response.general.TAppResponse;
import com.sankuai.algoplatform.matchops.api.response.general.TDropdownResponse;
import com.sankuai.algoplatform.matchops.api.response.general.TUploadSignResponse;

@ThriftService
@InterfaceDoc(type = "octo.thrift.annotation", description = "通用接口", scenarios = "")
public interface TGeneralService {

    @ThriftMethod
    @MethodDoc(description = "获取业务线下拉框")
    TDropdownResponse getDropdown(TDropdownRequest request);

    @ThriftMethod
    @MethodDoc(description = "获取apps")
    TAppResponse getApps(String keyword);


    @ThriftMethod
    @MethodDoc(description = "获取cells")
    TAppResponse getCells(String appkey);

    @ThriftMethod
    @MethodDoc(description = "获取cells")
    TUploadSignResponse getUploadSign(String fileName);

}
