package com.sankuai.algoplatform.matchops.api.request.mcptool;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @program: algoplat-matchops
 * <AUTHOR>
 * @Date 2025/5/25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TestSubTaskContext implements Serializable {
    private Integer status;

    private String statusName;

    private List<Reporter> reporters;

    private String taskResultMessage;

    private String sessionId;

    private String missionId;

    private String taskId;

    private Map<String,String> extra;

    @Data
    public static class Reporter {
        private String reporterName;
        private String reporterAddr;
    }

}
