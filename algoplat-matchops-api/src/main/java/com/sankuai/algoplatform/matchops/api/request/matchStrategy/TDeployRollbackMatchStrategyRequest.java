package com.sankuai.algoplatform.matchops.api.request.matchStrategy;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

@ThriftStruct
@Data
public class TDeployRollbackMatchStrategyRequest {
    @FieldDoc(description = "策略id", example = {})
    @ThriftField(1)
    public Long matchStrategyId;

    @FieldDoc(description = "type", example = {})
    @ThriftField(2)
    public int type;
}
