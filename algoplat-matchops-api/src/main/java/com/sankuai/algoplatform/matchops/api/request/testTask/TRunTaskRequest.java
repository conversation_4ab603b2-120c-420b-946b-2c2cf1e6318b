package com.sankuai.algoplatform.matchops.api.request.testTask;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.Data;
import org.apache.logging.log4j.core.config.plugins.validation.constraints.Required;

@ThriftStruct
@Data
public class TRunTaskRequest {

    @FieldDoc(description = "任务ID", example = {})
    @ThriftField(1)
    @Required
    public Long testTaskId;

    @FieldDoc(description = "任务入参", example = {})
    @ThriftField(2)
    @Required
    public String param;

    @FieldDoc(description = "文件名", example = {})
    @ThriftField(3)
    public String fileName;

    @FieldDoc(description = "文件流", example = {})
    @ThriftField(4)
    public String fileStream;

    @FieldDoc(description = "文件路径", example = {})
    @ThriftField(5)
    public String filePath;


    @FieldDoc(description = "是否清除预测服务缓存", example = {})
    @ThriftField(6)
    public String clearCache;

    @FieldDoc(description = "missionId", example = {}, requiredness = Requiredness.OPTIONAL)
    @ThriftField(7)
    public String missionId;

}
