package com.sankuai.algoplatform.matchops.api.response.testTask;

import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.algoplatform.matchops.api.response.TPage;
import com.sankuai.algoplatform.matchops.api.response.TBaseResponse;
import lombok.Data;

import java.util.Map;

@Data
public class TTestSubTaskListResponse extends TBaseResponse {

    @FieldDoc(description = "返回结果", example = {})
    @ThriftField(3)
    public TPage<Map<String, String>> data;

}
