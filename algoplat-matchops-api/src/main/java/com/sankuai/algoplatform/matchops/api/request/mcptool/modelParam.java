package com.sankuai.algoplatform.matchops.api.request.mcptool;

import com.alibaba.fastjson.annotation.JSONField;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * @program: algoplat-matchops
 * <AUTHOR>
 * @Date 2025/5/24
 */

@TypeDoc(name = "modelParam", description = "大模型参数,模型微调")
@ThriftStruct
public class modelParam {


    @NotNull(message = "temperature不能为空")
    @FieldDoc(description = "topP", example = {})
    private String topP;

    @NotNull(message = "topK不能为空")
    @FieldDoc(description = "topk", example = {})
    private String topK;

    @NotNull(message = "temperature不能为空")
    @FieldDoc(description = "temperature", example = {})
    private String temperature;

    @NotNull(message = "maxTokens不能为空")
    @FieldDoc(description = "maxTokens", example = {})
    private String maxTokens;

    @NotNull(message = "max_seq_length不能为空")
    @FieldDoc(description = "max_seq_length", example = {})
    private String maxSeqLength;

    @FieldDoc(description = "promptSuffix", example = {})
    private String promptSuffix = "";

    @FieldDoc(description = "promptPrefix", example = {})
    private String promptPrefix = "";


    public modelParam() {

    }

    public modelParam(String topP, String topK, String temperature, String maxTokens, String maxSeqLength, String promptSuffix, String promptPrefix) {
        this.topP = topP;
        this.topK = topK;
        this.temperature = temperature;
        this.maxTokens = maxTokens;
        this.maxSeqLength = maxSeqLength;
        this.promptSuffix = promptSuffix;
        this.promptPrefix = promptPrefix;
    }

    @ThriftField(1)
    public @NotNull(message = "temperature不能为空") String getTopP() {
        return topP;
    }

    @ThriftField(2)
    public @NotNull(message = "topK不能为空") String getTopK() {
        return topK;
    }

    @ThriftField(3)
    public @NotNull(message = "temperature不能为空") String getTemperature() {
        return temperature;
    }

    @ThriftField(4)
    public @NotNull(message = "maxTokens不能为空") String getMaxTokens() {
        return maxTokens;
    }

    @ThriftField(5)
    public @NotNull(message = "max_seq_length不能为空") String getMaxSeqLength() {
        return maxSeqLength;
    }

    @ThriftField(6)
    public String getPromptSuffix() {
        return promptSuffix;
    }

    @ThriftField(7)
    public String getPromptPrefix() {
        return promptPrefix;
    }

    @ThriftField()
    public void setTopP(@NotNull(message = "temperature不能为空") String topP) {
        this.topP = topP;
    }

    @ThriftField()
    public void setTopK(@NotNull(message = "topK不能为空") String topK) {
        this.topK = topK;
    }

    @ThriftField()
    public void setTemperature(@NotNull(message = "temperature不能为空") String temperature) {
        this.temperature = temperature;
    }

    @ThriftField()
    public void setMaxTokens(@NotNull(message = "maxTokens不能为空") String maxTokens) {
        this.maxTokens = maxTokens;
    }

    @ThriftField()
    public void setMaxSeqLength(@NotNull(message = "maxseqlength不能为空") String maxSeqLength) {
        this.maxSeqLength = maxSeqLength;
    }

    @ThriftField()
    public void setPromptSuffix(String promptSuffix) {
        this.promptSuffix = promptSuffix;
    }

    @ThriftField()
    public void setPromptPrefix(String promptPrefix) {
        this.promptPrefix = promptPrefix;
    }
}
