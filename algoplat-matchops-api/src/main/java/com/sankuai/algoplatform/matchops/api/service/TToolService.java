package com.sankuai.algoplatform.matchops.api.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.algoplatform.matchops.api.request.tool.TSaveCacheRequest;
import com.sankuai.algoplatform.matchops.api.response.TBaseResponse;

/**
 * <AUTHOR>
 * @date 2025/4/30
 */
@ThriftService
@InterfaceDoc(type = "octo.thrift.annotation", description = "工具接口", scenarios = "")
public interface TToolService {

    /**
     * 保存缓存
     *
     * @param request 请求对象，包含map数据
     * @return 是否保存成功
     */
    @ThriftMethod
    @MethodDoc(description = "保存缓存")
    TBaseResponse saveCache(TSaveCacheRequest request);
}
