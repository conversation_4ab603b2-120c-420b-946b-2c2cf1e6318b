package com.sankuai.algoplatform.matchops.api.request.testTask;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;
import org.apache.logging.log4j.core.config.plugins.validation.constraints.Required;

import java.util.List;

@ThriftStruct
@Data
public class TGetSubTaskReporterRequest {

    @FieldDoc(description = "子任务ID", example = {})
    @ThriftField(1)
    @Required
    public Long testSubTaskId;

}
