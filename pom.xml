<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.meituan.mdp</groupId>
        <artifactId>mdp-basic-parent</artifactId>
        <version>1.8.7.6</version>
        <relativePath/>
    </parent>

    <groupId>com.sankuai.algoplatform</groupId>
    <artifactId>algoplat-matchops</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>algoplat-matchops</name>

    <modules>
        <module>algoplat-matchops-api</module>
        <module>algoplat-matchops-starter</module>
        <module>algoplat-matchops-application</module>
        <module>algoplat-matchops-domain</module>
        <module>algoplat-matchops-infrastructure</module>
    </modules>

    <properties>
        <api-revision>0.0.3</api-revision>
        <revision>0.0.1</revision>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!--mockito-->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>4.1.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>2.0.9</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>2.0.9</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.sankuai.algoplatform</groupId>
                <artifactId>algoplat-matchops-api</artifactId>
                <version>${api-revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.algoplatform</groupId>
                <artifactId>algoplat-matchops-starter</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.algoplatform</groupId>
                <artifactId>algoplat-matchops-domain</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.algoplatform</groupId>
                <artifactId>algoplat-matchops-application</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.algoplatform</groupId>
                <artifactId>algoplat-matchops-infrastructure</artifactId>
                <version>${revision}</version>
            </dependency>
            <!-- llm https://km.sankuai.com/collabpage/2498708689-->
            <dependency>
                <groupId>com.sankuai.ai</groupId>
                <artifactId>friday-llm-training-registry-thrift</artifactId>
                <version>0.0.18</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.xm</groupId>
                <artifactId>openplatform-client</artifactId>
                <version>1.1.32-RELEASE</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.ead</groupId>
                <artifactId>citadel-client</artifactId>
                <version>3.0.55</version>
            </dependency>

            <dependency>
                <groupId>com.meituan.talostwo</groupId>
                <artifactId>talostwo-sdk-java</artifactId>
                <version>2.2.2-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83_noneautotype</version>
            </dependency>
            <!-- https://km.sankuai.com/collabpage/1332146795 -->
            <dependency>
                <groupId>com.sankuai.mlp.ml</groupId>
                <artifactId>k8sweb-thrift-sdk</artifactId>
                <version>1.1.13</version>
            </dependency>

            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>mss-java-sdk-s3</artifactId>
                <version>1.10.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sankuai.xm</groupId>
                <artifactId>xm-pub-api-client</artifactId>
                <version>1.5.8</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>com.sankuai.xm</groupId>
                <artifactId>udb-common</artifactId>
                <version>1.2.5</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>aifree-thrift</artifactId>
                <version>1.0.0.16</version>
            </dependency>
            <dependency>
                <groupId>org.apache.thrift</groupId>
                <artifactId>libthrift</artifactId>
                <version>0.9.3-mt2</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.org</groupId>
                <artifactId>open-sdk</artifactId>
                <version>5.0.5</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.meituan.ai</groupId>
                <artifactId>friday-java-sdk</artifactId>
                <version>0.1.0</version>
                <exclusions>
                    <exclusion>
                            <groupId>javax.validation</groupId>
                            <artifactId>validation-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meishi.stgy.algoplatform</groupId>
                <artifactId>predictor-client</artifactId>
                <version>1.0.3</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>
                            <flattenMode>defaults</flattenMode>
                            <pomElements>
                                <parent>expand</parent>
                                <profiles>expand</profiles>
                                <dependencies>keep</dependencies>
                                <build>keep</build>
                            </pomElements>
                            <updatePomFile>true</updatePomFile>
                        </configuration>
                    </execution>
                    <execution>
                        <id>flatten-clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
