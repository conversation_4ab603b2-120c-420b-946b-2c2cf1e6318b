package com.sankuai.algoplatform.matchops.application.service.testtool;

import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.MatchStrategy;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;
import java.util.Map;

public interface MatchStrategyService {

    Map<String, String> getMatchStrategyList(Integer current, Long bizLineId);

    List<Map<String, String>> getDropdown(String type, Long bizLineId, String env);

    Long addMatchStrategy(MatchStrategy matchStrategy);

    void editMatchStrategy(MatchStrategy matchStrategy);

    Integer deleteMatchStrategy(Long matchStrategyId,String owner);

    Map<String, String> queryMatchStrategy(Long matchStrategyId);

    void updateMatchStrategyStatus(Long matchStrategyId, Integer status);

    Map<String, String> queryMatchStrategyByName(String matchStrategyName);

    Pair<Boolean, String> deployMatchStrategy(Long matchStrategyId, int type);

    Pair<<PERSON>olean, String> rollMatchStrategy(Long matchStrategyId, int type);
}
