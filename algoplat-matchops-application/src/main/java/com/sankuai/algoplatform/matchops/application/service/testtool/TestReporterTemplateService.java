package com.sankuai.algoplatform.matchops.application.service.testtool;

import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ReporterTemplate;

import java.util.List;
import java.util.Map;

public interface TestReporterTemplateService {

    Map<String, String> getTestReporterTemplateList(Integer current, Long bizLineId);

    Long addTestReporterTemplate(ReporterTemplate reporterTemplate);

    void editTestReporterTemplate(ReporterTemplate reporterTemplate);

    Integer delTestReporterTemplate(Long reporterTemplateId, String mis);

    List<Map<String, String>> getDropdown(String type, Long bizLineId);

}
