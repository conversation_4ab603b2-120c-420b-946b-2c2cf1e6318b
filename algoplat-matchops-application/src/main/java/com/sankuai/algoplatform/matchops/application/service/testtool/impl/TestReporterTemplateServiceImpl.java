package com.sankuai.algoplatform.matchops.application.service.testtool.impl;

import com.alibaba.fastjson.JSON;
import com.sankuai.algoplatform.matchops.application.service.testtool.TestReporterTemplateService;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.TestReporterTemplateDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ReporterTemplate;
import com.sankuai.algoplatform.matchops.infrastructure.util.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class TestReporterTemplateServiceImpl implements TestReporterTemplateService {
    @Autowired
    private TestReporterTemplateDao testReporterTemplateDao;

    @Override
    public Map<String, String> getTestReporterTemplateList(Integer current, Long bizLineId) {
        List<ReporterTemplate> reporterTemplates = testReporterTemplateDao.selectByPageAndBizLineId(current, 20, bizLineId);
        Long count = testReporterTemplateDao.selectCountByBizLineId(bizLineId);
        Map<String, String> result = new HashMap<>();
        List<Map<String, String>> reporterTemplateList = new ArrayList<>();
        reporterTemplates.forEach(reporterTemplate -> {
            Map<String, String> temp = new HashMap<>();
            temp.put("templateId", String.valueOf(reporterTemplate.getId()));
            temp.put("templateName", reporterTemplate.getName());
            temp.put("templateAddr", reporterTemplate.getTemplateAddr());
            temp.put("createTime", DateUtil.toDateTimeString(reporterTemplate.getCreateTime()));
            temp.put("updateTime", DateUtil.toDateTimeString(reporterTemplate.getUpdateTime()));
            reporterTemplateList.add(temp);
        });
        result.put("total", String.valueOf(count));
        result.put("list", JSON.toJSONString(reporterTemplateList));
        result.put("current", String.valueOf(current));
        return result;
    }

    @Override
    public Long addTestReporterTemplate(ReporterTemplate reporterTemplate) {
        return testReporterTemplateDao.insert(reporterTemplate);
    }

    @Override
    public void editTestReporterTemplate(ReporterTemplate reporterTemplate) {
        testReporterTemplateDao.update(reporterTemplate);
    }

    @Override
    public Integer delTestReporterTemplate(Long reporterTemplateId, String mis) {
        return testReporterTemplateDao.softDelete(reporterTemplateId, mis);
    }

    @Override
    public List<Map<String, String>> getDropdown(String type, Long bizLineId) {
        List<Map<String, String>> dropdownList = new ArrayList<>();
        List<ReporterTemplate> reporterTemplates = testReporterTemplateDao.selectAllByBizLineId(bizLineId);
        reporterTemplates.forEach(resourceGroup -> {
            Map<String, String> temp = new HashMap<>();
            temp.put("id", String.valueOf(resourceGroup.getId()));
            temp.put("name", resourceGroup.getName());
            dropdownList.add(temp);
        });
        return dropdownList;
    }
}
