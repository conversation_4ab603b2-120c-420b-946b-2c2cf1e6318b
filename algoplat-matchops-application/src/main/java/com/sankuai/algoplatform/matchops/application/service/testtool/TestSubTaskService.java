package com.sankuai.algoplatform.matchops.application.service.testtool;

import com.sankuai.algoplatform.matchops.application.model.testtool.TestSubTaskPo;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;
import java.util.Map;

public interface TestSubTaskService {

    TestSubTaskPo getTestTaskDetailBySubTaskId(Long subTaskId);

    List<Map<String, String>> getTestSubTaskList(Long taskId);

    Pair<Boolean, String> runSubTask(Long subTaskId);

    Pair<Boolean, String> stopSubTask(Long subTaskId);

    Pair<Boolean, String> doneSubTask(Long subTaskId);

    Pair<Boolean, String> generateReporter(Long subTaskId);

}
