package com.sankuai.algoplatform.matchops.application.model.testtool.matchstrategy;

import com.sankuai.algoplatform.matchops.infrastructure.enums.RunTimeStatus;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.AlgoPackage;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
public class AlgoPackagePo {
    private Long id;

    private String note;

    private String ownerMis;

    private RunTimeStatus runtime;

    private String modulePath;

    private String version;

    private String codeRepo;

    private Integer status;

    private Date addTime;

    private Date updateTime;

    public AlgoPackagePo(AlgoPackage algoPackage) {
        this.id = algoPackage.getId();
        this.note = algoPackage.getNote();
        this.ownerMis = algoPackage.getOwnerMis();
        this.runtime = RunTimeStatus.fromDesc(algoPackage.getRuntime());
        this.modulePath = algoPackage.getModulePath();
        this.version = algoPackage.getVersion();
        this.codeRepo = algoPackage.getCodeRepo();
        this.status = algoPackage.getStatus();
        this.addTime = algoPackage.getAddTime();
        this.updateTime = algoPackage.getUpdateTime();
    }
}
