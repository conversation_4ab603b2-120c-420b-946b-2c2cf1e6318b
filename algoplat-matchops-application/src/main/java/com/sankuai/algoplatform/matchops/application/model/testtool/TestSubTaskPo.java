package com.sankuai.algoplatform.matchops.application.model.testtool;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.sankuai.algoplatform.matchops.api.response.resourceGroup.TTestTaskDetailResponse;
import com.sankuai.algoplatform.matchops.application.model.testtool.matchstrategy.*;
import com.sankuai.algoplatform.matchops.application.model.testtool.resourcegroup.FridayResource;
import com.sankuai.algoplatform.matchops.application.model.testtool.resourcegroup.MlpResource;
import com.sankuai.algoplatform.matchops.application.model.testtool.resourcegroup.OctoResource;
import com.sankuai.algoplatform.matchops.domain.constant.TestToolConstants;
import com.sankuai.algoplatform.matchops.domain.enums.TestSubTaskStatusEnum;
import com.sankuai.algoplatform.matchops.domain.model.testtool.RunTime;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestSubTask;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestTask;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
public class TestSubTaskPo {

    private Long id;

    private TestTask testTask;

    private Map<String, String> runParam;

    private List<AlgoCodeInfo> algoCodeInfo;

    private PredictorCache predictorCache;

    private List<LlmInfo> llmInfo;

    private List<OctoServiceConfigInfo> octoInfo;

    private List<OctoResource> octoResources;

    private List<MlpResource> mlpModelResources;

    private List<FridayResource> fridayModelResources;

    private List<ReporterTemplatePo> reporterInfo;

    private String taskResult;

    private TestSubTaskStatusEnum status;

    private Map<String, String> extraInfo;

    private String creator;

    private RunTime runTime;

    private Date createTime;

    private Date updateTime;

    public TestSubTaskPo(TestSubTask testSubTask, TestTask testTask) {
        this.id = testSubTask.getId();
        this.testTask = testTask;
        this.runParam = JSONObject.parseObject(testSubTask.getRunParam(), new TypeReference<Map<String, String>>() {
        });
        this.algoCodeInfo = JSON.parseArray(testSubTask.getAlgoCodeInfo(), AlgoCodeInfo.class);
        this.predictorCache = JSON.parseObject(testSubTask.getPredictorCacheInfo(), PredictorCache.class);
        this.llmInfo = JSON.parseArray(testSubTask.getLlmInfo(), LlmInfo.class);
        this.octoInfo = JSON.parseArray(testSubTask.getOctoInfo(), OctoServiceConfigInfo.class);
        this.octoResources = JSON.parseArray(testSubTask.getOctoResources(), OctoResource.class);
        this.mlpModelResources = JSON.parseArray(testSubTask.getMlpModelResources(), MlpResource.class);
        this.fridayModelResources = JSON.parseArray(testSubTask.getFridayModelResources(), FridayResource.class);
        this.reporterInfo = JSON.parseArray(testSubTask.getReporterInfo(), ReporterTemplatePo.class);
        this.taskResult = testSubTask.getTaskResult();
        this.status = TestSubTaskStatusEnum.getByCode(testSubTask.getStatus());
        this.extraInfo = JSON.parseObject(testSubTask.getExtraInfo(), new TypeReference<Map<String, String>>() {
        });
        this.creator = testSubTask.getCreator();
        this.runTime = JSON.parseObject(testSubTask.getRunTime(), RunTime.class);
        this.createTime = testSubTask.getCreateTime();
        this.updateTime = testSubTask.getUpdateTime();
    }

    public TTestTaskDetailResponse.Data trans2TData() {
        TTestTaskDetailResponse.Data data = new TTestTaskDetailResponse.Data();
        data.setTaskName(testTask.getName());
        data.setMatchStrategyName(this.getExtraInfo().getOrDefault(TestToolConstants.MATCH_STRATEGY_NAME, ""));
        data.setSceneName(this.getExtraInfo().getOrDefault(TestToolConstants.SCENE_NAME, ""));
        if (CollectionUtils.isNotEmpty(this.getAlgoCodeInfo())) {
            List<TTestTaskDetailResponse.Data.AlgoCodeInfo> algoCodeInfos = this.getAlgoCodeInfo().stream().map(AlgoCodeInfo::trans2TData).collect(Collectors.toList());
            data.setAlgoCodeInfo(algoCodeInfos);
        }
        if (predictorCache != null) {
            data.setPredictorCache(predictorCache.trans2TData());
        }
        if (CollectionUtils.isNotEmpty(this.getLlmInfo())) {
            List<TTestTaskDetailResponse.Data.LlmInfo> llmInfos = this.getLlmInfo().stream().map(LlmInfo::trans2TData).collect(Collectors.toList());
            data.setLlmInfo(llmInfos);
        }
        if (CollectionUtils.isNotEmpty(octoInfo)) {
            List<TTestTaskDetailResponse.Data.OctoInfo> octoInfos = octoInfo.stream().map(OctoServiceConfigInfo::trans2TData).collect(Collectors.toList());
            data.setOctoInfo(octoInfos);
        }
        data.setResourceGroupName(this.getExtraInfo().getOrDefault(TestToolConstants.RESOURCE_GROUP_NAME, ""));
        if (CollectionUtils.isNotEmpty(octoResources)) {
            List<TTestTaskDetailResponse.Data.ResourceInfo> resources = octoResources.stream().map(OctoResource::trans2TData).collect(Collectors.toList());
            data.setCpuResourceInfo(resources);
        }
        if (CollectionUtils.isNotEmpty(mlpModelResources)) {
            List<TTestTaskDetailResponse.Data.MlpResourceInfo> resources = mlpModelResources.stream().map(MlpResource::trans2TData).collect(Collectors.toList());
            data.setMlpResourceInfo(resources);
        }
        if (CollectionUtils.isNotEmpty(fridayModelResources)) {
            List<TTestTaskDetailResponse.Data.LlmResourceInfo> resources = fridayModelResources.stream().map(FridayResource::trans2TData).collect(Collectors.toList());
            data.setLlmResourceInfo(resources);
        }
        return data;
    }
}
