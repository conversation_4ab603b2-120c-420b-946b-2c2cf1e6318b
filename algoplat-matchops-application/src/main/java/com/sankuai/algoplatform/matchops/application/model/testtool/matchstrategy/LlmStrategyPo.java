package com.sankuai.algoplatform.matchops.application.model.testtool.matchstrategy;

import com.alibaba.fastjson.annotation.JSONField;
import com.sankuai.algoplatform.matchops.api.response.resourceGroup.TTestTaskDetailResponse;
import com.sankuai.algoplatform.matchops.domain.enums.EnvStatus;
import lombok.Data;

import java.util.Date;

@Data
public class LlmStrategyPo {
    private Long id;

    @JSONField(name = "modelBizCode")
    private String bizCode;

    @JSONField(name = "note")
    private String note;

    private LlmModelConfig modelConfig;

    private LlmPromptTemplatePo llmPromptTemplate;

    private EnvStatus env;

    private Long bizLineId;

    private Boolean status;

    private String owner;

    private Date addTime;

    private Date updateTime;

    @JSONField(name = "quota")
    private int quota;


    public TTestTaskDetailResponse.Data.LlmStrategy trans2TData() {
        TTestTaskDetailResponse.Data.LlmStrategy algoCodeStrategy = new TTestTaskDetailResponse.Data.LlmStrategy();
        algoCodeStrategy.setStrategyName(note);
        algoCodeStrategy.setModelName(modelConfig.getModelName());
        algoCodeStrategy.setModelServiceName(modelConfig.getModelServiceName());
        algoCodeStrategy.setPrompt(llmPromptTemplate.getPromptTemplate());
        algoCodeStrategy.setQuota(String.valueOf(quota));
        return algoCodeStrategy;
    }
}
