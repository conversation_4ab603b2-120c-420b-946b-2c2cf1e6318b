package com.sankuai.algoplatform.matchops.application.service.testtool.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.util.CollectionUtils;
import com.sankuai.algoplatform.matchops.application.service.testtool.ResourceGroupService;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.*;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ResourceGroup;
import com.sankuai.algoplatform.matchops.infrastructure.util.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ResourceGroupServiceImpl implements ResourceGroupService {
    @Autowired
    private ResourceGroupDao resourceGroupDao;



    @Override
    public Map<String, String> getResourceGroupList(Integer current, Long bizLineId) {
        List<ResourceGroup> resourceGroups = resourceGroupDao.selectByPageAndBizLineId(current, 20, bizLineId);
        Long count = resourceGroupDao.selectCountByBizLineId(bizLineId);
        Map<String, String> result = new HashMap<>();
        List<Map<String, String>> resourceGroupList = new ArrayList<>();
        resourceGroups.forEach(resourceGroup -> {
            Map<String, String> temp = new HashMap<>();
            temp.put("resourceGroupId", String.valueOf(resourceGroup.getId()));
            temp.put("resourceGroupName", resourceGroup.getName());
            temp.put("cpuResourceInfo", resourceGroup.getOctoResources());
            temp.put("mlpResourceInfo", resourceGroup.getMlpResources());
            temp.put("llmResourceInfo", resourceGroup.getFridayResources());
            temp.put("owner", resourceGroup.getCreator());
            temp.put("createTime", DateUtil.toDateTimeString(resourceGroup.getCreateTime()));
            temp.put("updateTime", DateUtil.toDateTimeString(resourceGroup.getUpdateTime()));
            resourceGroupList.add(temp);
        });
        result.put("total", String.valueOf(count));
        result.put("list", JSON.toJSONString(resourceGroupList));
        result.put("current", String.valueOf(current));
        return result;
    }

    @Override
    public ResourceGroup getResourceGroup(Long bizLineId, Long resourceGroupId) {
        List<ResourceGroup> resourceGroups = resourceGroupDao.selectByResourceGroupNameAndBizLineId(bizLineId, resourceGroupId);
        if(CollectionUtils.isEmpty(resourceGroups)){
            return null;
        }
        ResourceGroup resourceGroup = resourceGroups.get(0);
        return resourceGroup;
    }

    @Override
    public Long addResourceGroupList(ResourceGroup resourceGroup) {
        return resourceGroupDao.insert(resourceGroup);
    }

    @Override
    public void editResourceGroupList(ResourceGroup resourceGroup) {
        resourceGroupDao.update(resourceGroup);
    }

    @Override
    public Integer delResourceGroupList(Long resourceGroupId, String mis) {
        return resourceGroupDao.softDelete(resourceGroupId, mis);
    }

    @Override
    public List<Map<String, String>> getDropdown(String type, Long bizLineId) {
        List<Map<String, String>> dropdownList = new ArrayList<>();
        List<ResourceGroup> resourceGroups = resourceGroupDao.selectAllByBizLineId(bizLineId);
        resourceGroups.forEach(resourceGroup -> {
            Map<String, String> temp = new HashMap<>();
            temp.put("id", String.valueOf(resourceGroup.getId()));
            temp.put("name", resourceGroup.getName());
            dropdownList.add(temp);
        });
        return dropdownList;
    }
}
