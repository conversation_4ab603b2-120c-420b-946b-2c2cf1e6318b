package com.sankuai.algoplatform.matchops.application.service.testtool;

import com.sankuai.algoplatform.matchops.infrastructure.util.ParserUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class ParamHelper {


    public static Map<String, String> parseXTParam(String runParam) {
        return ParserUtil.getValue(ParserUtil.splitCommandLine(runParam));
    }

    public static Map<String, String> parseHttpParam(String runParam) {
        return ParserUtil.getValue(ParserUtil.splitCommandLine(runParam));
    }
}
