package com.sankuai.algoplatform.matchops.application.service.testtool;

import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestScene;

import java.util.List;
import java.util.Map;

public interface TestSceneService {

    Map<String, String> getTestSceneList(Integer current, Long bizLineId);

    Long addTestScene(TestScene testScene);

    void editTestScene(TestScene testScene);

    Integer delTestScene(Long sceneId, String mis);

    List<Map<String, String>> getDropdown(String type, Long bizLineId);

    TestScene queryTestSceneByIdAndBizLineId(Long sceneId);

}
