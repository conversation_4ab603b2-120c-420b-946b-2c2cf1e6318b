package com.sankuai.algoplatform.matchops.application.model.testtool.resourcegroup;

import com.sankuai.algoplatform.matchops.api.response.resourceGroup.TTestTaskDetailResponse;
import com.sankuai.algoplatform.matchops.domain.model.testtool.DFridayModelParam;
import com.sankuai.algoplatform.matchops.domain.model.testtool.DFridayResource;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FridayResource {
    private int instanceNum;
    private String gpuPerInstance;
    private String modelName;
    private int batchSize;
    private boolean useTemplate;
    private String promptTemplate;
    private String modelBaseName;
    private String trainMethod;
    private String trainingTaskId;

    private DFridayModelParam modelParam;

    public TTestTaskDetailResponse.Data.LlmResourceInfo trans2TData() {
        TTestTaskDetailResponse.Data.LlmResourceInfo resourceInfo = new TTestTaskDetailResponse.Data.LlmResourceInfo();
        resourceInfo.setServiceName(this.modelName);
        resourceInfo.setInstanceNum(String.valueOf(this.instanceNum));

        // 添加其他属性转换
        resourceInfo.setGpuPerInstance(this.gpuPerInstance);
        resourceInfo.setModelName(this.modelName);
        resourceInfo.setBatchSize(String.valueOf(this.batchSize));
        resourceInfo.setUseTemplate(String.valueOf(this.useTemplate));
        resourceInfo.setPromptTemplate(this.promptTemplate);
        resourceInfo.setModelBaseName(this.modelBaseName);
        resourceInfo.setTrainMethod(this.trainMethod);
        resourceInfo.setTrainingTaskId(this.trainingTaskId);

        // 转换模型参数
        if (this.modelParam != null) {
            resourceInfo.setTopP(this.modelParam.getTopP());
            resourceInfo.setMaxNewTokens(this.modelParam.getMaxNewTokens());
            resourceInfo.setPromptPrefix(this.modelParam.getPromptPrefix());
            resourceInfo.setTopK(this.modelParam.getTopK());
            resourceInfo.setPromptSuffix(this.modelParam.getPromptSuffix());
            resourceInfo.setTemperature(this.modelParam.getTemperature());
            resourceInfo.setMaxSeqLength(this.modelParam.getMaxSeqLength());
        }

        return resourceInfo;
    }

    public DFridayResource trans() {
        DFridayResource resource = new DFridayResource();
        resource.setInstanceNum(instanceNum);
        resource.setGpuPerInstance(gpuPerInstance);
        resource.setModelName(modelName);
        resource.setBatchSize(batchSize);
        resource.setUseTemplate(useTemplate);
        resource.setPromptTemplate(promptTemplate);
        resource.setModelBaseName(modelBaseName);
        resource.setTrainMethod(trainMethod);
        resource.setTrainingTaskId(trainingTaskId);
        resource.setModelParam(modelParam);
        return resource;
    }
}
