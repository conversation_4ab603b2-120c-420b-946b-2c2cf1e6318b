package com.sankuai.algoplatform.matchops.application.service;

public interface GpuScheduleFlowHandlerService {
    /**
     * 生成任务实例
     */
    void generateTaskInstance();

    /**
     * 调用任务实例
     */
    void invokeTaskInstance();

    /**
     * 更新任务实例
     */
    void updateTaskInstance();

    /**
     * 生成资源监控任务实例
     */
    void generateResourceMonitorTaskInstance();

    /**
     * 资源监控
     */
    void resourceMonitor();
}
