package com.sankuai.algoplatform.matchops.application.model.testtool.matchstrategy;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class LlmModelConfig {

    @J<PERSON>NField(name = "modelName")
    private String modelName;

    @J<PERSON><PERSON>ield(name = "modelServiceName")
    private String modelServiceName;

    @JSONField(name = "batchSize")
    private int batchSize;

    @JSONField(name = "useTemplate")
    private int useTemplate;

    @JSONField(name = "appId")
    private String appId;

    @JSONField(name = "debug")
    private boolean debug;

    @J<PERSON><PERSON>ield(name = "modelParms")
    private ModelParms modelParms;

    @Data
    public static class ModelParms {

        @J<PERSON><PERSON>ield(name = "maxTokens")
        private int maxTokens;

        @JSONField(name = "top_p")
        private double topP;

        @JSONField(name = "top_k")
        private int topK;

        @JSONField(name = "temperature")
        private double temperature;

        @JSONField(name = "seed")
        private int seed;
    }

}
