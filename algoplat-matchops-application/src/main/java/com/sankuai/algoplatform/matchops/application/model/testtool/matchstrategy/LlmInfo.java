package com.sankuai.algoplatform.matchops.application.model.testtool.matchstrategy;

import com.alibaba.fastjson.annotation.JSONField;
import com.sankuai.algoplatform.matchops.api.response.resourceGroup.TTestTaskDetailResponse;
import com.sankuai.algoplatform.matchops.domain.enums.EnvStatus;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class LlmInfo {

    private Long id;

    private Long bizLineId;

    @JSONField(name = "llmBizCode")
    private String llmBizCode;

    @JSONField(name = "prodLlmBizCode")
    private String prodLlmBizCode;

    private String note;

    private LlmStrategyAbtest llmStrategyAbtest;

    private EnvStatus env;

    private Boolean status;

    private String owner;

    private Date addTime;

    private Date updateTime;

    public TTestTaskDetailResponse.Data.LlmInfo trans2TData(){
        TTestTaskDetailResponse.Data.LlmInfo llmInfo = new TTestTaskDetailResponse.Data.LlmInfo();
        llmInfo.setLlmBizCode(this.llmBizCode);
        List<LlmStrategyPo> distributions = llmStrategyAbtest.getDistributions();
        List<TTestTaskDetailResponse.Data.LlmStrategy> strategies = distributions.stream().map(LlmStrategyPo::trans2TData).collect(Collectors.toList());
        llmInfo.setStrategies(strategies);;
        return llmInfo;
    }

}
