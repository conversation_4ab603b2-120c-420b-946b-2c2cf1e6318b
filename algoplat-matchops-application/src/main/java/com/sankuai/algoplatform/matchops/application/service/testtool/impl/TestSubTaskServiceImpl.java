package com.sankuai.algoplatform.matchops.application.service.testtool.impl;

import com.alibaba.fastjson.TypeReference;
import com.dianping.zebra.distsql.config.EnvironmentParams;
import com.google.common.collect.ImmutableList;
import com.sankuai.algoplatform.matchops.application.model.testtool.ReporterTemplatePo;
import com.sankuai.algoplatform.matchops.application.model.testtool.TestSubTaskPo;
import com.sankuai.algoplatform.matchops.application.model.testtool.resourcegroup.ResourceGroupPo;
import com.sankuai.algoplatform.matchops.application.service.testtool.TestSubTaskService;
import com.sankuai.algoplatform.matchops.domain.constant.TestToolConstants;
import com.sankuai.algoplatform.matchops.domain.enums.ReqPostTypeEnum;
import com.sankuai.algoplatform.matchops.domain.enums.TestSubTaskStatusEnum;
import com.sankuai.algoplatform.matchops.domain.enums.TestTaskStatusEnum;
import com.sankuai.algoplatform.matchops.domain.model.testtool.RunTime;
import com.sankuai.algoplatform.matchops.domain.service.testtool.EnvPrepareService;
import com.sankuai.algoplatform.matchops.domain.service.testtool.TaskExecuteService;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.ResourceGroupDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.TestSubTaskDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.TestTaskDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestSubTask;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestTask;
import com.sankuai.algoplatform.matchops.infrastructure.util.DateUtil;
import com.sankuai.algoplatform.matchops.infrastructure.util.ThreadPoolFactory;
import jdk.nashorn.internal.ir.annotations.Immutable;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.mortbay.util.ajax.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;

@Service
@Slf4j
public class TestSubTaskServiceImpl implements TestSubTaskService {

    @Autowired
    private TestTaskDao testTaskDao;

    @Autowired
    private TestSubTaskDao testSubTaskDao;

    @Autowired
    private TaskExecuteService taskExecuteService;

    @Autowired
    private EnvPrepareService envPrepareService;

    @Override
    public TestSubTaskPo getTestTaskDetailBySubTaskId(Long subTaskId) {
        TestSubTask testSubTask = testSubTaskDao.selectById(subTaskId);
        TestTask testTask = testTaskDao.selectById(testSubTask.getTaskId());
        return new TestSubTaskPo(testSubTask, testTask);
    }

    @Override
    public List<Map<String, String>> getTestSubTaskList(Long taskId) {
        List<Map<String, String>> result = new ArrayList<>();
        List<TestSubTask> testSubTasks = testSubTaskDao.selectByTaskId(taskId);
        if (CollectionUtils.isEmpty(testSubTasks)) {
            return result;
        }
        testSubTasks.forEach(task -> {
            TestSubTaskPo subTaskPo = new TestSubTaskPo(task, null);
            Map<String, String> data = new HashMap<>();
            data.put("testSubTaskId", String.valueOf(subTaskPo.getId()));
            Map<String, String> runParam = subTaskPo.getRunParam();
            data.put(TestToolConstants.MISSION_ID,runParam.get(TestToolConstants.MISSION_ID));
            runParam.remove(TestToolConstants.POST_TYPE);
            runParam.remove(TestToolConstants.ORIGIN_RUN_PARAM);
            runParam.remove(TestToolConstants.IP);
            runParam.remove(TestToolConstants.MISSION_ID);
            data.put("runParam", JSON.toString(runParam));
            RunTime runTime = subTaskPo.getRunTime();
            if (runTime != null) {
                data.put("runTime", runTime.getTaskStartTime());
                data.put("endTime", runTime.getTaskEndTime());
            }
            Map<String, String> extraInfo = subTaskPo.getExtraInfo();
            data.put("statusCode", String.valueOf(subTaskPo.getStatus().getCode()));
            data.put("statusName", subTaskPo.getStatus().getName());
            data.put("taskResult", subTaskPo.getTaskResult());
            data.put("owner", subTaskPo.getCreator());
            data.put("createTime", DateUtil.toDateTimeString(subTaskPo.getCreateTime()));
            data.put("updateTime", DateUtil.toDateTimeString(subTaskPo.getUpdateTime()));
            data.putAll(extraInfo);

            List<Map<String, String>> list = new ArrayList<>();
            if (Objects.nonNull(subTaskPo.getReporterInfo())) {
                for (ReporterTemplatePo reporterTemplatePo : subTaskPo.getReporterInfo()) {
                    if (StringUtils.isEmpty(reporterTemplatePo.getReporter())) {
                        continue;
                    }
                    Map<String, String> rdata = new HashMap<>();
                    rdata.put("reporterName", reporterTemplatePo.getName());
                    rdata.put("reporterAddr", reporterTemplatePo.getReporter());
                    list.add(rdata);
                }
            }
            data.put("reporters", JSON.toString(list));
            result.add(data);
        });
        return result;
    }

    @Override
    public Pair<Boolean, String> stopSubTask(Long subTaskId) {
        TestSubTask subTask = testSubTaskDao.selectById(subTaskId);
        if (subTask == null) {
            log.info("当前子任务不存在, subTaskId={}", subTaskId);
            return Pair.of(false, "当前子任务不存在");
        }

        if (!TestSubTaskStatusEnum.isCanBeStop(subTask.getStatus())) {
            return Pair.of(false, "任务当前不可被停止");
        }

        taskExecuteService.stopSubTask(subTask, TestSubTaskStatusEnum.STOP_MANUAL);
        return Pair.of(true, "");
    }

    @Override
    public Pair<Boolean, String> doneSubTask(Long subTaskId) {
        TestSubTask subTask = testSubTaskDao.selectById(subTaskId);
        if (subTask == null) {
            log.info("当前任务不存在, subTaskId={}", subTaskId);
            return Pair.of(false, "当前任务不存在");
        }
        taskExecuteService.doneSubTask(subTask);

        return Pair.of(true, "");
    }

    @Override
    public Pair<Boolean, String> runSubTask(Long subTaskId) {
        TestSubTask subTask = testSubTaskDao.selectById(subTaskId);
        if (subTask == null) {
            return Pair.of(false, "当前子任务不存在");
        }
        TestTask task = testTaskDao.selectById(subTask.getTaskId());
        if (!TestTaskStatusEnum.isCanBeRunByTask(task.getStatus())) {
            return Pair.of(false, "当前任务不可执行");
        }
        if (!TestSubTaskStatusEnum.isCanBeRun(subTask.getStatus())) {
            return Pair.of(false, "当前任务不可执行");
        }
        //如果有其他子任务在测试中，不可执行
        List<TestSubTask> subTasks = testSubTaskDao.selectByTaskIdAndStatus(subTask.getTaskId(), ImmutableList.of(TestSubTaskStatusEnum.TESTING.getCode()));
        if (CollectionUtils.isNotEmpty(subTasks)) {
            return Pair.of(false, "有其他任务正在执行中");
        }

        //校验环境是否OK
        TestSubTaskPo subTaskPo = new TestSubTaskPo(subTask, null);
        ResourceGroupPo resourceGroup = new ResourceGroupPo(subTaskPo);
        boolean ready = envPrepareService.queryEnvReady(resourceGroup.trans(), subTask.getCreator());
        if (!ready) {
            testTaskDao.updateTestTaskStatus(subTask.getTaskId(), TestTaskStatusEnum.ENV_FAIL.getCode());
            return Pair.of(false, "环境未就绪，请先准备环境");
        }

        ThreadPoolFactory.getTaskRunThreadPool().submit(() -> taskExecuteService.runSubTask(subTask));

        Map<String, String> subTaskParamMap = com.alibaba.fastjson.JSON.parseObject(subTask.getRunParam(), new TypeReference<Map<String, String>>() {
        });
        ReqPostTypeEnum postType = ReqPostTypeEnum.getByCode(Integer.parseInt(subTaskParamMap.get(TestToolConstants.POST_TYPE)));
        String link = postType == ReqPostTypeEnum.XT ? subTaskParamMap.get(TestToolConstants.REQ_POST_LINK) : "";
        return Pair.of(true, link);
    }

    @Override
    public Pair<Boolean, String> generateReporter(Long subTaskId) {
        TestSubTask subTask = testSubTaskDao.selectById(subTaskId);
        if (subTask == null) {
            return Pair.of(false, "当前任务不存在");
        }
        if (subTask.getStatus() != TestSubTaskStatusEnum.TEST_SUCCESS.getCode()) {
            return Pair.of(false, "当前状态不能生成报告");
        }

        ThreadPoolExecutor pool = ThreadPoolFactory.getGenerateReporterThreadPool();
        pool.submit(() -> {
            try {
                taskExecuteService.generateReporter(subTask);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        return Pair.of(true, "");
    }

}
