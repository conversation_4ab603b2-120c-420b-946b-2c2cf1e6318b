package com.sankuai.algoplatform.matchops.application.service.impl;

import com.dianping.lion.client.Lion;
import com.sankuai.algoplatform.matchops.application.service.OfflineTaskService;
import com.sankuai.algoplatform.matchops.domain.ability.offlinetask.OfflineTaskFullDataHandlerService;
import com.sankuai.algoplatform.matchops.domain.ability.offlinetask.OfflineTaskIncrDataHandlerService;
import com.sankuai.algoplatform.matchops.domain.model.OfflineTaskContext;
import com.sankuai.algoplatform.matchops.infrastructure.monitor.RaptorTrack;
import com.sankuai.algoplatform.matchops.infrastructure.util.ThreadPoolFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 离线任务服务，该类crane直接调用，勿删
 */
@Service
public class OfflineTaskServiceImpl implements OfflineTaskService {
    @Resource
    private OfflineTaskFullDataHandlerService fullDataHandlerService;
    @Resource
    private OfflineTaskIncrDataHandlerService incrDataHandlerService;
    @Override
    public void scanAndHandleFullData(String key) {
        String value = Lion.getConfigRepository().get(key);
        OfflineTaskContext context = OfflineTaskContext.from(key, value);
        fullDataHandlerService.scanAndHandle(context);
    }
    @Override
    public void statisticIncrData(){
        Map<String, String> configs = Lion.getConfigRepository().getConfigs();
        List<Map.Entry<String, String>> offlineTask = configs.entrySet().stream().filter(entry -> entry.getKey().startsWith("offline_task")).collect(Collectors.toList());
        for (Map.Entry<String, String> entry : offlineTask) {
            ThreadPoolFactory.getOfflineTaskIncrDataStatisticThreadPool().execute(() -> incrDataHandlerService.statisticIncrDataAndCache(OfflineTaskContext.from(entry.getKey(), entry.getValue())));
        }

    }
    @Override
    public void scanAndHandleIncrData(String key){
        String value = Lion.getConfigRepository().get(key);
        incrDataHandlerService.scanAndHandle(OfflineTaskContext.from(key, value));
    }

}
