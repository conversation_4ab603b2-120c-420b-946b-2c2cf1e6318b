package com.sankuai.algoplatform.matchops.application.model.testtool.matchstrategy;

import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.BizLlmpredictPromptTemplate;
import lombok.Data;

import java.util.Date;

@Data
public class LlmPromptTemplatePo {

    private Long id;

    private String note;

    private Boolean status;

    private String owner;

    private Date addTime;

    private Date updateTime;

    private String promptTemplate;

    public LlmPromptTemplatePo(BizLlmpredictPromptTemplate promptTemplate) {
        this.id = promptTemplate.getId();
        this.note = promptTemplate.getNote();
        this.status = promptTemplate.getStatus();
        this.owner = promptTemplate.getOwner();
        this.addTime = promptTemplate.getAddTime();
        this.updateTime = promptTemplate.getUpdateTime();
        this.promptTemplate = promptTemplate.getPromptTemplate();
    }
}
