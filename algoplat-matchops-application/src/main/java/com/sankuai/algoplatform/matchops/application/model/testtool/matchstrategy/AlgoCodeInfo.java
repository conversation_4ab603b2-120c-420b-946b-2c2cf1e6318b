package com.sankuai.algoplatform.matchops.application.model.testtool.matchstrategy;

import com.alibaba.fastjson.annotation.JSONField;
import com.sankuai.algoplatform.matchops.api.response.resourceGroup.TTestTaskDetailResponse;
import com.sankuai.algoplatform.matchops.domain.enums.EnvStatus;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class AlgoCodeInfo {

    private Long id;

    @JSONField(name = "algoBizCode")
    private String algoBizCode;

    @JSONField(name = "prodAlgoBizCode")
    private String prodAlgoBizCode;

    private String name;

    private AlgoStrategyAbtest algoStrategyAbtest;

    private EnvStatus env;

    private Long bizLineId;

    private Integer status;

    private String owner;

    private Date addTime;

    private Date updateTime;

    public TTestTaskDetailResponse.Data.AlgoCodeInfo trans2TData() {
        TTestTaskDetailResponse.Data.AlgoCodeInfo algoCodeInfo = new TTestTaskDetailResponse.Data.AlgoCodeInfo();
        algoCodeInfo.setAlgoBizCode(algoBizCode);
        List<AlgoStrategyPo> distributions = algoStrategyAbtest.getDistributions();
        List<TTestTaskDetailResponse.Data.AlgoCodeStrategy> strategies = distributions.stream().map(AlgoStrategyPo::trans2TData).collect(Collectors.toList());
        algoCodeInfo.setStrategies(strategies);
        return algoCodeInfo;
    }

}
