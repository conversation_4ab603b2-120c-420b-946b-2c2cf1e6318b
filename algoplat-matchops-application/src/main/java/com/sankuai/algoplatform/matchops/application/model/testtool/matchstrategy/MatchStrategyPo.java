package com.sankuai.algoplatform.matchops.application.model.testtool.matchstrategy;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.sankuai.algoplatform.matchops.application.model.testtool.TestScenePo;
import com.sankuai.algoplatform.matchops.domain.enums.EnvStatus;
import com.sankuai.algoplatform.matchops.domain.enums.MatchStrategyStatusEnum;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.*;
import com.sankuai.algoplatform.matchops.infrastructure.model.AlgoStrategyPackagePo;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.mortbay.util.ajax.JSON;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Data
public class MatchStrategyPo {
    private Long id;

    private String name;

    private Long bizLineId;

    private TestScenePo testScene;

    private List<AlgoCodeInfo> algoBizCodes;

    private PredictorCache predictorCache;

    private List<LlmInfo> llmBizCodes;

    private List<OctoServiceConfigInfo> octoServiceConfig;

    private MatchStrategyStatusEnum status;

    private String owner;

    private Date createTime;

    private Date updateTime;

    private Boolean isDel;

    public MatchStrategyPo(MatchStrategy matchStrategy) {
        this.id = matchStrategy.getId();
        this.name = matchStrategy.getName();
        this.bizLineId = matchStrategy.getBizLineId();


        this.predictorCache = new PredictorCache(matchStrategy.getPredictorCache());

        this.octoServiceConfig = JSONObject.parseObject(matchStrategy.getOctoServiceConfig(), new TypeReference<List<OctoServiceConfigInfo>>() {
        });

        this.status = MatchStrategyStatusEnum.getByCode(matchStrategy.getStatus());
        this.owner = matchStrategy.getOwner();
        this.createTime = matchStrategy.getCreateTime();
        this.updateTime = matchStrategy.getUpdateTime();
        this.isDel = matchStrategy.getIsDel();
    }

    public MatchStrategyPo(MatchStrategy matchStrategy,
                           TestScene testScene,
                           List<Pair<BizStrategy, List<AlgoStrategyPackagePo>>> bizStrategies,
                           List<LlmBizStrategy> llmBizStrategies,
                           List<BizLlmpredictConfig> bizLlmpredictConfigs,
                           List<BizLlmpredictPromptTemplate> bizLlmpredictPromptTemplates) {
        this.id = matchStrategy.getId();
        this.name = matchStrategy.getName();
        this.bizLineId = matchStrategy.getBizLineId();

        if (testScene != null) {
            this.testScene = new TestScenePo(testScene);
        }

        this.predictorCache = new PredictorCache(matchStrategy.getPredictorCache());

        //算法代码包信息
        if (CollectionUtils.isNotEmpty(bizStrategies)) {
            List<AlgoCodeInfo> algoCodeInfos = JSONObject.parseObject(matchStrategy.getAlgoBizCodes(), new TypeReference<List<AlgoCodeInfo>>() {
            });
            Map<String, Pair<BizStrategy, List<AlgoStrategyPackagePo>>> bizStrategyMap = bizStrategies.stream()
                    .collect(Collectors.toMap(biz -> biz.getLeft().getBizCode(), Function.identity(), (k1, k2) -> k2));

            algoCodeInfos.forEach(algoCodeInfo -> {
                Pair<BizStrategy, List<AlgoStrategyPackagePo>> bizStrategyPair = bizStrategyMap.get(algoCodeInfo.getAlgoBizCode());
                BizStrategy bizStrategy = bizStrategyPair.getLeft();
                Map<Long, AlgoStrategyPackagePo> algoStrategyPackagePoMap = bizStrategyPair.getRight().stream()
                        .collect(Collectors.toMap(algo -> algo.getAlgoStrategy().getId(), Function.identity(), (v1, v2) -> v1));

                algoCodeInfo.setId(bizStrategy.getId());
                algoCodeInfo.setName(bizStrategy.getNote());

                AlgoStrategyAbtest abtest = JSONObject.parseObject(bizStrategy.getAbtestConfig(), AlgoStrategyAbtest.class);
                abtest.getDistributions().forEach(algoStrategyPo -> {
                    AlgoStrategy algoStrategy = algoStrategyPackagePoMap.get(algoStrategyPo.getId()).getAlgoStrategy();
                    algoStrategyPo.setNote(algoStrategy.getNote());
                    algoStrategyPo.setBizLineId(algoStrategy.getBizLineId());
                    algoStrategyPo.setEntrancePath(algoStrategy.getEntrancePath());
                    algoStrategyPo.setEntranceMethod(algoStrategy.getEntranceMethod());
                    algoStrategyPo.setEnv(EnvStatus.getByCode(algoStrategy.getEnv()));
                    algoStrategyPo.setStatus(algoStrategy.getStatus());
                    algoStrategyPo.setConvertStrategyId(algoStrategy.getConvertStrategyId());
                    algoStrategyPo.setOwner(algoStrategy.getOwner());
                    algoStrategyPo.setAddTime(algoStrategy.getAddTime());
                    algoStrategyPo.setUpdateTime(algoStrategy.getUpdateTime());

                    AlgoPackage algoPackage = algoStrategyPackagePoMap.get(algoStrategyPo.getId()).getAlgoPackage();
                    algoStrategyPo.setAlgoPackage(new AlgoPackagePo(algoPackage));
                });
                algoCodeInfo.setAlgoStrategyAbtest(abtest);

                algoCodeInfo.setEnv(EnvStatus.getByCode(bizStrategy.getEnv()));
                algoCodeInfo.setBizLineId(bizStrategy.getBizLineId());
                algoCodeInfo.setStatus(bizStrategy.getStatus());
                algoCodeInfo.setOwner(bizStrategy.getOwner());
                algoCodeInfo.setAddTime(bizStrategy.getAddTime());
                algoCodeInfo.setUpdateTime(bizStrategy.getUpdateTime());
            });
            this.algoBizCodes = algoCodeInfos;
        }

        //llm配置信息
        if (CollectionUtils.isNotEmpty(llmBizStrategies)) {
            List<LlmInfo> llmInfos = JSONObject.parseObject(matchStrategy.getLlmBizCodes(), new TypeReference<List<LlmInfo>>() {
            });
            Map<String, LlmBizStrategy> llmBizStrategyMap = llmBizStrategies.stream().collect(Collectors.toMap(LlmBizStrategy::getBizCode, Function.identity(), (k1, k2) -> k2));
            Map<String, BizLlmpredictConfig> llmpredictConfigMap = bizLlmpredictConfigs.stream().collect(Collectors.toMap(BizLlmpredictConfig::getBizCode, Function.identity(), (k1, k2) -> k2));
            Map<Long, BizLlmpredictPromptTemplate> llmpredictPromptTemplateMap = bizLlmpredictPromptTemplates.stream().collect(Collectors.toMap(BizLlmpredictPromptTemplate::getId, Function.identity(), (k1, k2) -> k2));

            llmInfos.forEach(llmBizStrategyPo -> {
                LlmBizStrategy llmBizStrategy = llmBizStrategyMap.get(llmBizStrategyPo.getLlmBizCode());
                llmBizStrategyPo.setId(llmBizStrategy.getId());
                llmBizStrategyPo.setBizLineId(llmBizStrategy.getBizLineId());

                LlmStrategyAbtest abtest = JSONObject.parseObject(llmBizStrategy.getAbtestConfig(), LlmStrategyAbtest.class);

                abtest.getDistributions().forEach(llmPredictConfigPo -> {
                    BizLlmpredictConfig bizLlmpredictConfig = llmpredictConfigMap.get(llmPredictConfigPo.getBizCode());
                    llmPredictConfigPo.setId(bizLlmpredictConfig.getId());
                    llmPredictConfigPo.setNote(bizLlmpredictConfig.getNote());
                    if (StringUtils.isNotEmpty(bizLlmpredictConfig.getModelConfig())) {
                        llmPredictConfigPo.setModelConfig(JSONObject.parseObject(bizLlmpredictConfig.getModelConfig(), LlmModelConfig.class));
                    }
                    llmPredictConfigPo.setEnv(EnvStatus.getByCode(bizLlmpredictConfig.getEnv()));
                    llmPredictConfigPo.setBizLineId(bizLlmpredictConfig.getBizLineId());
                    llmPredictConfigPo.setStatus(bizLlmpredictConfig.getStatus());
                    llmPredictConfigPo.setOwner(bizLlmpredictConfig.getOwner());
                    llmPredictConfigPo.setAddTime(bizLlmpredictConfig.getAddTime());
                    llmPredictConfigPo.setUpdateTime(bizLlmpredictConfig.getUpdateTime());

                    BizLlmpredictPromptTemplate promptTemplate = llmpredictPromptTemplateMap.get(bizLlmpredictConfig.getPromptTemplateId());
                    llmPredictConfigPo.setLlmPromptTemplate(new LlmPromptTemplatePo(promptTemplate));
                });
                llmBizStrategyPo.setLlmStrategyAbtest(abtest);
                llmBizStrategyPo.setNote(llmBizStrategy.getNote());
                llmBizStrategyPo.setEnv(EnvStatus.getByCode(llmBizStrategy.getEnv()));
                llmBizStrategyPo.setStatus(llmBizStrategy.getStatus());
                llmBizStrategyPo.setOwner(llmBizStrategy.getOwner());
                llmBizStrategyPo.setAddTime(llmBizStrategy.getAddTime());
                llmBizStrategyPo.setUpdateTime(llmBizStrategy.getUpdateTime());
            });

            this.llmBizCodes = llmInfos;
        }

        //服务配置信息
        this.octoServiceConfig = JSONObject.parseObject(matchStrategy.getOctoServiceConfig(), new TypeReference<List<OctoServiceConfigInfo>>() {
        });

        if (StringUtils.isNotEmpty(matchStrategy.getPredictorCache())) {
            this.predictorCache = JSONObject.parseObject(matchStrategy.getPredictorCache(), PredictorCache.class);
        }


        this.status = MatchStrategyStatusEnum.getByCode(matchStrategy.getStatus());
        this.owner = matchStrategy.getOwner();
        this.createTime = matchStrategy.getCreateTime();
        this.updateTime = matchStrategy.getUpdateTime();
        this.isDel = matchStrategy.getIsDel();
    }

}
