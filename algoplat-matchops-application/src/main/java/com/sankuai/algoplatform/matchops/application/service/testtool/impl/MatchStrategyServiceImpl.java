package com.sankuai.algoplatform.matchops.application.service.testtool.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sankuai.algoplatform.matchops.application.model.testtool.matchstrategy.*;
import com.sankuai.algoplatform.matchops.application.service.testtool.AlgoPackageService;
import com.sankuai.algoplatform.matchops.application.service.testtool.PromptStrategyService;
import com.sankuai.algoplatform.matchops.domain.constant.TestToolConstants;
import com.sankuai.algoplatform.matchops.domain.enums.EnvStatus;
import com.sankuai.algoplatform.matchops.domain.enums.MatchStrategyOpStepEnum;
import com.sankuai.algoplatform.matchops.domain.enums.MatchStrategyStatusEnum;
import com.sankuai.algoplatform.matchops.infrastructure.model.*;
import com.sankuai.algoplatform.matchops.application.service.testtool.MatchStrategyService;
import com.sankuai.algoplatform.matchops.domain.model.testtool.PredictorCache1Config;
import com.sankuai.algoplatform.matchops.domain.model.testtool.PredictorCache234Config;
import com.sankuai.algoplatform.matchops.domain.service.testtool.LionHelper;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.*;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.*;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.LionConfigService;
import com.sankuai.algoplatform.matchops.infrastructure.util.ContextUtil;
import com.sankuai.algoplatform.matchops.infrastructure.util.DateUtil;
import com.sankuai.mcm.client.sdk.config.annotation.McmHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MatchStrategyServiceImpl implements MatchStrategyService {
    @Autowired
    MatchStrategyDao matchStrategyDao;

    @Autowired
    TestSceneDao testSceneDao;

    @Autowired
    BizStrategyDao bizStrategyDao;

    @Autowired
    LlmBizStrategyDao llmBizStrategyDao;

    @Autowired
    private AlgoPackageService algoPackageService;

    @Autowired
    private PromptStrategyService promptStrategyService;


    @Autowired
    private AlgoPackageDao algoPackageDao;
    @Autowired
    private AlgoStrategyDao algoStrategyDao;
    @Autowired
    private BizLlmPredictConfigDao bizLlmPredictConfigDao;
    @Autowired
    private BizLlmPredictPromptTemlateDao bizLlmPredictPromptTemlateDao;
    @Autowired
    private MatchStrategyServiceImpl self;

    @Override
    public Map<String, String> getMatchStrategyList(Integer current, Long bizLineId) {
        List<MatchStrategy> matchStrategys = matchStrategyDao.selectByPageAndBizLineId(current, 20, bizLineId);
        Long count = matchStrategyDao.selectCountByBizLineId(bizLineId);
        Map<String, String> result = new HashMap<>();
        List<Map<String, String>> strategyList = new ArrayList<>();
        matchStrategys.forEach(matchStrategy -> {
            TestScene testScene = testSceneDao.selectById(matchStrategy.getTestSceneId());
            Map<String, String> temp = new HashMap<>();
            temp.put("matchStrategyId", String.valueOf(matchStrategy.getId()));
            temp.put("matchStrategyName", matchStrategy.getName());
            temp.put("sceneName", testScene == null ? "" : testScene.getTestSceneName());
            if (StringUtils.equals(matchStrategy.getAlgoBizCodes(), "null")){
                temp.put("algoCodeInfo", null);
            }else{
                temp.put("algoCodeInfo", matchStrategy.getAlgoBizCodes());
            }

            if (StringUtils.equals(matchStrategy.getLlmBizCodes(), "null")){
                temp.put("llmInfo", null);
            }else{
                temp.put("llmInfo", matchStrategy.getLlmBizCodes());
            }
            MatchStrategyStatusEnum status = MatchStrategyStatusEnum.getByCode(matchStrategy.getStatus());
            temp.put("statusCode", String.valueOf(status.getCode()));
            temp.put("statusName", status.getName());
            temp.put("step", getStep(matchStrategy.getDeployInfo()));
            temp.put("owner", matchStrategy.getOwner());
            temp.put("create_time", DateUtil.toDateTimeString(matchStrategy.getCreateTime()));
            temp.put("update_time", DateUtil.toDateTimeString(matchStrategy.getUpdateTime()));
            strategyList.add(temp);
        });
        result.put("total", String.valueOf(count));
        result.put("list", JSON.toJSONString(strategyList));
        result.put("current", String.valueOf(current));
        return result;
    }

    private String getStep(String deployInfo) {
        if (StringUtils.isEmpty(deployInfo)) {
            return String.valueOf(MatchStrategyOpStepEnum.UNKNOWN.getCode());
        }
        DeployInfo deploy = JSON.parseObject(deployInfo, DeployInfo.class);
        if (deploy.getMcm() == 0) {
            return String.valueOf(MatchStrategyOpStepEnum.MCM.getCode());
        }
        if (deploy.getLion() == 1 &&
                deploy.getAlgoCode() == 1 &&
                deploy.getLlm() == 1 &&
                deploy.getService() == 1) {
            return String.valueOf(MatchStrategyOpStepEnum.REFRESH_CACHE.getCode());
        }
        return String.valueOf(MatchStrategyOpStepEnum.LION.getCode());
    }

    @Override
    public List<Map<String, String>> getDropdown(String type, Long bizLineId, String env) {
        List<Map<String, String>> dropdownList = new ArrayList<>();
        EnvStatus envStatus = EnvStatus.fromName(env);
        switch (type) {
            case "5":
                List<BizStrategy> bizStrategies = bizStrategyDao.selectAllByBizLineIdAndEnv(bizLineId, envStatus.getCode());
                bizStrategies.forEach(bizStrategy -> {
                    Map<String, String> temp = new HashMap<>();
                    temp.put("id", String.valueOf(bizStrategy.getId()));
                    temp.put("name", bizStrategy.getBizCode());
                    dropdownList.add(temp);
                });
                break;
            case "6":
                List<LlmBizStrategy> llmBizStrategies = llmBizStrategyDao.selectAllByBizLineIdAndEnv(bizLineId, envStatus.getCode());
                llmBizStrategies.forEach(llmBizStrategy -> {
                    Map<String, String> temp = new HashMap<>();
                    temp.put("id", String.valueOf(llmBizStrategy.getId()));
                    temp.put("name", llmBizStrategy.getBizCode());
                    dropdownList.add(temp);
                });
                break;
            case "8":
                List<MatchStrategy> matchStrategies = matchStrategyDao.selectByBizLine(bizLineId);
                matchStrategies.forEach(matchStrategy -> {
                    Map<String, String> temp = new HashMap<>();
                    temp.put("id", String.valueOf(matchStrategy.getId()));
                    temp.put("name", matchStrategy.getName());
                    dropdownList.add(temp);
                });
                break;
        }
        return dropdownList;
    }

    @Override
    public Long addMatchStrategy(MatchStrategy matchStrategy) {
        MatchStrategyPo matchStrategyPo = new MatchStrategyPo(matchStrategy);

        //配置缓存
        PredictorCache predictorCache = matchStrategyPo.getPredictorCache();
        setPredictorCache(predictorCache, matchStrategy.getAlgoBizCodes());

        //配置服务config
        List<OctoServiceConfigInfo> octoServiceConfig = matchStrategyPo.getOctoServiceConfig();
        setLionConfig(octoServiceConfig);

        return matchStrategyDao.insert(matchStrategy);
    }

    @Override
    public void editMatchStrategy(MatchStrategy matchStrategy) {
        //配置缓存
        PredictorCache cache = new PredictorCache(matchStrategy.getPredictorCache());
        setPredictorCache(cache, matchStrategy.getAlgoBizCodes());

        //配置服务config
        List<OctoServiceConfigInfo> octoServiceConfigInfos = JSONObject.parseObject(matchStrategy.getOctoServiceConfig(), new TypeReference<List<OctoServiceConfigInfo>>() {
        });
        setLionConfig(octoServiceConfigInfos);

        matchStrategyDao.update(matchStrategy);
    }

    @Override
    public Integer deleteMatchStrategy(Long matchStrategyId, String owner) {
        return matchStrategyDao.softDelete(matchStrategyId, owner);
    }

    @Override
    public void updateMatchStrategyStatus(Long matchStrategyId, Integer status) {
        MatchStrategy matchStrategy = matchStrategyDao.selectById(matchStrategyId);
        if (matchStrategy == null) {
            return;
        }
        matchStrategy.setStatus(status);
        matchStrategyDao.update(matchStrategy);
    }

    @Override
    public Map<String, String> queryMatchStrategy(Long matchStrategyId) {
        MatchStrategy matchStrategy = matchStrategyDao.selectById(matchStrategyId);
        if (matchStrategy == null) {
            return null;
        }
        TestScene testScene = testSceneDao.selectById(matchStrategy.getTestSceneId());
        Map<String, String> strategyMap = new HashMap<>();
        strategyMap.put("matchStrategyId", String.valueOf(matchStrategy.getId()));
        strategyMap.put("matchStrategyName", matchStrategy.getName());
        strategyMap.put("sceneName", testScene == null ? "" : testScene.getTestSceneName());
        strategyMap.put("sceneId", testScene == null ? "0" : String.valueOf(testScene.getId()));
        strategyMap.put("algoCodeInfo", JSON.toJSONString(JSON.parseArray(matchStrategy.getAlgoBizCodes(), AlgoBizCode.class)));
        strategyMap.put("llmInfo", JSON.toJSONString(JSON.parseArray(matchStrategy.getLlmBizCodes(), LlmBizCode.class)));
        strategyMap.put("octoInfo", JSON.toJSONString(JSON.parseArray(matchStrategy.getOctoServiceConfig(), OctoInfo.class)));
        MatchStrategyStatusEnum status = MatchStrategyStatusEnum.getByCode(matchStrategy.getStatus());
        strategyMap.put("statusCode", String.valueOf(status.getCode()));
        strategyMap.put("statusName", status.getName());
        strategyMap.put("owner", matchStrategy.getOwner());
        strategyMap.put("create_time", DateUtil.toDateTimeString(matchStrategy.getCreateTime()));
        strategyMap.put("update_time", DateUtil.toDateTimeString(matchStrategy.getUpdateTime()));
        Map<String, String> predictorCache = new HashMap<>();
        PredictorCache cache = new PredictorCache(matchStrategy.getPredictorCache());
        predictorCache.put("setName", cache.getSetName());
        predictorCache.put("cache1", cache.isCache1() ? "1" : "0");
        predictorCache.put("cache2", cache.isCache2() ? "1" : "0");
        predictorCache.put("cache3", cache.isCache3() ? "1" : "0");
        predictorCache.put("cache4", cache.isCache4() ? "1" : "0");
        strategyMap.put("predictorCache", JSON.toJSONString(predictorCache));
        return strategyMap;
    }

    @Override
    public Map<String, String> queryMatchStrategyByName(String matchStrategyName) {
        List<MatchStrategy> matchStrategies = matchStrategyDao.selectByName(matchStrategyName);
        if (CollectionUtils.isEmpty(matchStrategies)) {
            return null;
        }
        MatchStrategy matchStrategy = matchStrategies.get(0);
        return queryMatchStrategy(matchStrategy.getId());
    }

    @Override
    public Pair<Boolean, String> deployMatchStrategy(Long matchStrategyId, int type) {
        MatchStrategy matchStrategy = matchStrategyDao.selectById(matchStrategyId);
        if (!MatchStrategyStatusEnum.canDeploy(matchStrategy.getStatus())) {
            return Pair.of(false, "策略当前状态不允许上线");
        }

        DeployInfo deployInfo = JSON.parseObject(matchStrategy.getDeployInfo(), DeployInfo.class);
        switch (MatchStrategyOpStepEnum.getByCode(type)) {
            case MCM:
                if (deployInfo.getMcm() == 1) {
                    return Pair.of(true, "");
                }
                //todo 发送MCM
                deployInfo.setMcm(1);
                break;
            case LION:
                if (deployInfo.getLion() == 1) {
                    return Pair.of(true, "");
                }
                deployLion(matchStrategy);
                deployInfo.setMcm(1);
                break;
            case ALGO_CODE:
                if (deployInfo.getAlgoCode() == 1) {
                    return Pair.of(true, "");
                }
                deployAldoCode(matchStrategy);
                deployInfo.setAlgoCode(1);
                break;
            case LLM:
                if (deployInfo.getLlm() == 1) {
                    return Pair.of(true, "");
                }
                deployLlm(matchStrategy);
                deployInfo.setLlm(1);
                break;
            case SERVICE:
                if (deployInfo.getService() == 1) {
                    return Pair.of(true, "");
                }
                String url = deployService(matchStrategy);
                deployInfo.setService(1);
                return Pair.of(true, url);
            case REFRESH_CACHE:
                break;
            case DEPLOY_SUCCESS:
                matchStrategyDao.updateStatus(matchStrategyId, MatchStrategyStatusEnum.DEPLOY_SUCCESS.getCode());
                break;
            default:
                throw new RuntimeException("未知类型type");
        }
        MatchStrategy dbMatchStrategy = new MatchStrategy();
        dbMatchStrategy.setId(matchStrategyId);
        dbMatchStrategy.setDeployInfo(JSON.toJSONString(deployInfo));
        matchStrategyDao.update(dbMatchStrategy);
        return Pair.of(true, "");
    }


    public Pair<Boolean, String> deployMatchStrategyTest(Long matchStrategyId, int type) {

        // 1.根据策略判断具体哪些内容需要上线
        switch (MatchStrategyOpStepEnum.getByCode(type)) {
            case MCM:
                self.sendMCM(matchStrategyId, type);
                //todo 发送MCM
                break;
            case LION:
                self.testLion(matchStrategyId, type);
                break;
            case ALGO_CODE:
                //String commitId = getCommitId(matchStrategy.getAlgoBizCodes());
                //todo 模型部署信息

                break;
            case LLM:
                //String prompt = getPrompt(matchStrategy.getLlmBizCodes());
                //todo 大模型信息部署
                break;

            case DEPLOY_SUCCESS:
                //matchStrategyDao.updateStatus(matchStrategyId, MatchStrategyStatusEnum.DEPLOY_SUCCESS.getCode());
                break;
            default:
                throw new RuntimeException("未知类型type");
        }
        return Pair.of(true, "");
    }

    @Override
    public Pair<Boolean, String> rollMatchStrategy(Long matchStrategyId, int type) {

        return Pair.of(true, "");
    }

    public void setPredictorCache(PredictorCache predictorCache, String algoBizCodes) {
        if (predictorCache == null || StringUtils.isEmpty(predictorCache.getSetName())) {
            return;
        }

        //配置缓存1级缓存
        String cache1Switch = LionHelper.getPredictorCache1Switch(LionHelper.getLionConfigEnvName());
        if (Boolean.parseBoolean(cache1Switch) != predictorCache.isCache1()) {
            LionHelper.setPredictorCache1Switch(LionHelper.getLionConfigEnvName(), predictorCache.isCache1());
        }

        List<Map<String, String>> list = JSONObject.parseObject(algoBizCodes, new TypeReference<List<Map<String, String>>>() {
        });
        List<String> bizCodes = list.stream().map(m -> m.get(TestToolConstants.ALGO_BIZ_CODE)).collect(Collectors.toList());

        List<PredictorCache1Config> predictorCache1Configs = LionHelper.getPredictorCache1Config(LionHelper.getLionConfigEnvName());
        List<String> configuredBizCodes = predictorCache1Configs.stream().map(PredictorCache1Config::getBizCode).collect(Collectors.toList());

        boolean needUpdate = false;
        for (String bizCode : bizCodes) {
            if (!configuredBizCodes.contains(bizCode)) {
                //不存在新建
                needUpdate = true;
                PredictorCache1Config config = LionHelper.buildBizCodeCache1Config(bizCode);
                predictorCache1Configs.add(config);
            }
        }
        if (needUpdate) {
            LionHelper.setPredictorCache1Config(LionHelper.getLionConfigEnvName(), JSON.toJSONString(predictorCache1Configs));
        }

        //配置234级缓存
        PredictorCache234Config cache234Config = LionHelper.getPredictorCache234Config(LionHelper.getLionConfigEnvName(), predictorCache.getSetName());
        if (cache234Config.isGlobalCacheSwitch() != predictorCache.isCache2() || cache234Config.isCoupleCacheSwitch() != predictorCache.isCache2() || cache234Config.isModelPredictCacheSwitch() != predictorCache.isCache2()) {
            cache234Config.setGlobalCacheSwitch(predictorCache.isCache2());
            cache234Config.setCoupleCacheSwitch(predictorCache.isCache3());
            cache234Config.setModelPredictCacheSwitch(predictorCache.isCache4());
            LionHelper.setPredictorCache234Config(LionHelper.getLionConfigEnvName(), predictorCache.getSetName(), JSON.toJSONString(cache234Config));
        }
    }

    private void setLionConfig(List<OctoServiceConfigInfo> octoServiceConfig) {
        if (CollectionUtils.isEmpty(octoServiceConfig)) {
            return;
        }

        octoServiceConfig.forEach(servletConfig -> {
            LionConfigReq baseReq = new LionConfigReq();
            baseReq.setAppkey(servletConfig.getAppkey());
            baseReq.setEnv(LionHelper.getLionConfigEnvName());
            baseReq.setSet(servletConfig.getSet());
            if (StringUtils.isEmpty(servletConfig.getLionConfig())) {
                return;
            }
            Map<String, String> lionCfgs = JSONObject.parseObject(servletConfig.getLionConfig(), new TypeReference<Map<String, String>>() {
            });
            if (MapUtils.isNotEmpty(lionCfgs)) {
                lionCfgs.forEach((k, v) -> {
                    LionConfigReq req = new LionConfigReq();
                    BeanUtils.copyProperties(baseReq, req);
                    req.setKey(k);
                    req.setValue(v);
                    LionConfigService.setLionConfig(req);
                });
            }
        });
    }

    private void deployLion(MatchStrategy matchStrategy) {
        MatchStrategyPo matchStrategyPo = new MatchStrategyPo(matchStrategy);
        List<OctoServiceConfigInfo> list = matchStrategyPo.getOctoServiceConfig();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<OctoServiceConfigInfo> needDeployCfgs = list.stream().filter(octo -> StringUtils.isNotEmpty(octo.getLionConfig())).collect(Collectors.toList());
        for (OctoServiceConfigInfo configInfo : needDeployCfgs) {
            Map<String, String> lionCfgs = JSONObject.parseObject(configInfo.getLionConfig(), new TypeReference<Map<String, String>>() {
            });
            lionCfgs.forEach((k, v) -> {
                LionConfigReq req = new LionConfigReq();
                req.setAppkey(configInfo.getAppkey());
//                req.setEnv(EnvStatus.PROD.getName());
                req.setEnv(EnvStatus.ST.getName()); //todo 上线前改为PROD环境（下期再改）
                if (!StringUtils.equals(configInfo.getSet(), "default") && !StringUtils.equals(configInfo.getSet(), "default_cell") && !StringUtils.isNotEmpty(configInfo.getSet())) {
                    req.setSet(configInfo.getSet());
                }
                String onlineValue = LionConfigService.queryLionConfig(req);
                if (StringUtils.equals(onlineValue, v)) {
                    //完全一致不需要上线
                    return;
                }
                //todo mcm
                LionConfigService.setLionConfig(req);
            });
        }
    }

    private void deployAldoCode(MatchStrategy matchStrategy) {
        List<AlgoCodeInfo> algoCodeInfos = JSONObject.parseObject(matchStrategy.getAlgoBizCodes(), new TypeReference<List<AlgoCodeInfo>>() {
        });
        List<String> algoBizCodes = algoCodeInfos.stream().map(AlgoCodeInfo::getAlgoBizCode).collect(Collectors.toList());
        List<Pair<BizStrategy, List<AlgoStrategyPackagePo>>> algoCodeAllInfo = algoPackageService.getAlgoCodeAllInfo(algoBizCodes);
        MatchStrategyPo matchStrategyPo = new MatchStrategyPo(matchStrategy, null, algoCodeAllInfo, null, null, null);

        List<AlgoCodeInfo> infos = matchStrategyPo.getAlgoBizCodes();
        //todo mcc审核
        for (AlgoCodeInfo info : infos) {
            String prodAlgoBizCode = info.getProdAlgoBizCode();
            if (StringUtils.isEmpty(prodAlgoBizCode)) {
                continue;
            }

            BizStrategy prodBizStrategy = bizStrategyDao.selectByBizCode(prodAlgoBizCode);
            AlgoStrategyAbtest prodAlgoStrategyAbtest = JSON.parseObject(prodBizStrategy.getAbtestConfig(), AlgoStrategyAbtest.class);
            List<AlgoStrategyPo> prodDistributions = prodAlgoStrategyAbtest.getDistributions();

            AlgoStrategyAbtest algoStrategyAbtest = info.getAlgoStrategyAbtest();
            List<AlgoStrategyPo> distributions = algoStrategyAbtest.getDistributions();

            for (int i = 0; i < distributions.size() && i < prodDistributions.size(); i++) {
                AlgoStrategyPo algoStrategyPo = distributions.get(i);
                AlgoStrategyPo prodAlgoStrategyPo = prodDistributions.get(i);

                Long conventStrategyId = algoStrategyPo.getConvertStrategyId();
                Long prodConventStrategyId = prodAlgoStrategyPo.getConvertStrategyId();

                //更新线上文本转向量算法版本
                if (conventStrategyId != null && prodConventStrategyId != null) {
                    AlgoPackage algoPackage = algoPackageDao.selectById(algoStrategyDao.selectById(conventStrategyId).getPackageId());
                    AlgoPackage prodAlgoPackage = algoPackageDao.selectById(algoStrategyDao.selectById(prodConventStrategyId).getPackageId());
                    AlgoPackage dbProdAlgoPackage = new AlgoPackage();
                    dbProdAlgoPackage.setId(prodAlgoPackage.getId());
                    dbProdAlgoPackage.setOwnerMis(ContextUtil.getLoginUserMis());
                    dbProdAlgoPackage.setVersion(algoPackage.getVersion());//更新算法包
                    algoPackageDao.update(dbProdAlgoPackage);
                }

                //更新线上BizCode算法版本
                Long strategyId = algoStrategyPo.getId();
                Long prodStrategyId = prodAlgoStrategyPo.getId();
                if (strategyId != null && prodStrategyId != null) {
                    //更新线上文本转向量算法版本
                    AlgoPackage algoPackage = algoPackageDao.selectById(algoStrategyDao.selectById(strategyId).getPackageId());
                    AlgoPackage prodAlgoPackage = algoPackageDao.selectById(algoStrategyDao.selectById(prodStrategyId).getPackageId());
                    AlgoPackage dbProdAlgoPackage = new AlgoPackage();
                    dbProdAlgoPackage.setId(prodAlgoPackage.getId());
                    dbProdAlgoPackage.setOwnerMis(ContextUtil.getLoginUserMis());
                    dbProdAlgoPackage.setVersion(algoPackage.getVersion());//更新算法包
                    algoPackageDao.update(dbProdAlgoPackage);
                }

            }
        }
    }

    private void deployLlm(MatchStrategy matchStrategy) {
        List<LlmInfo> llmCodeInfos = JSONObject.parseObject(matchStrategy.getLlmBizCodes(), new TypeReference<List<LlmInfo>>() {
        });
        List<String> llmBizCodes = llmCodeInfos.stream().map(LlmInfo::getLlmBizCode).collect(Collectors.toList());
        Triple<List<LlmBizStrategy>, List<BizLlmpredictConfig>, List<BizLlmpredictPromptTemplate>> llmAllInfo = promptStrategyService.getLlmAllInfo(llmBizCodes);
        MatchStrategyPo matchStrategyPo = new MatchStrategyPo(matchStrategy, null, null, null, null, null);

        List<LlmInfo> infos = matchStrategyPo.getLlmBizCodes();
        //todo mcc审核
        for (LlmInfo info : infos) {
            String prodLlmBizCode = info.getProdLlmBizCode();
            if (StringUtils.isEmpty(prodLlmBizCode)) {
                continue;
            }

            LlmBizStrategy llmBizStrategy = llmBizStrategyDao.selectByBizCode(prodLlmBizCode);
            LlmStrategyAbtest prodLlmStrategyAbtest = JSON.parseObject(llmBizStrategy.getAbtestConfig(), LlmStrategyAbtest.class);
            List<LlmStrategyPo> prodDistributions = prodLlmStrategyAbtest.getDistributions();

            LlmStrategyAbtest llmStrategyAbtest = info.getLlmStrategyAbtest();
            List<LlmStrategyPo> distributions = llmStrategyAbtest.getDistributions();

            for (int i = 0; i < distributions.size() && i < prodDistributions.size(); i++) {
                LlmStrategyPo llmStrategyPo = distributions.get(i);
                LlmStrategyPo prodLlmStrategyPo = prodDistributions.get(i);

                //todo 一般更新什么？Prompt 模型配置？服务名称？
                AlgoPackage algoPackage = algoPackageDao.selectById(algoStrategyDao.selectById(0L).getPackageId());
                AlgoPackage prodAlgoPackage = algoPackageDao.selectById(algoStrategyDao.selectById(0L).getPackageId());
                AlgoPackage dbProdAlgoPackage = new AlgoPackage();
                dbProdAlgoPackage.setId(prodAlgoPackage.getId());
                dbProdAlgoPackage.setOwnerMis(ContextUtil.getLoginUserMis());
                dbProdAlgoPackage.setVersion(algoPackage.getVersion());//更新算法包
                algoPackageDao.update(dbProdAlgoPackage);
            }
        }
    }

    private String deployService(MatchStrategy matchStrategy) {
        MatchStrategyPo matchStrategyPo = new MatchStrategyPo(matchStrategy);
        List<OctoServiceConfigInfo> list = matchStrategyPo.getOctoServiceConfig();
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }
        Optional<String> appkey = list.stream().map(OctoServiceConfigInfo::getBranch).filter(StringUtils::isNotEmpty).collect(Collectors.toList()).stream().findAny();
        if (appkey.isPresent()) {
            return String.format("https://dev.sankuai.com/services/%s/to-deploy/job-list", appkey);
        }
        return "";
    }

    @McmHandler(eventName = "CreateNotice")
    public void sendMCM(Long matchStrategyId, int type) {

    }
    @McmHandler(eventName = "UpdateRule")
    public void testLion(Long matchStrategyId, int type) {
        log.info("testLion回调成功");
    }

    private String getCommitId(String bizCode) {
        BizStrategy bizStrategy = bizStrategyDao.selectByBizCode(bizCode);
        if (bizStrategy == null || bizStrategy.getAbtestConfig() == null) {
            return null;
        }

        try {
            // 解析 JSON 配置
            JSONObject configJson = JSON.parseObject(bizStrategy.getAbtestConfig());

            // 获取 distributions 数组
            JSONArray distributions = configJson.getJSONArray("distributions");
            if (distributions != null && distributions.size() > 0) {
                // 获取第一个分组的 strategyId
                JSONObject firstGroup = distributions.getJSONObject(0);
                Long strategyId = firstGroup.getLong("strategyId");

                if (strategyId != null) {
                    AlgoStrategy algoStrategy = algoStrategyDao.selectById(strategyId);
                    if (algoStrategy != null) {
                        AlgoPackage algoPackage = algoPackageDao.selectById(algoStrategy.getPackageId());
                        if (algoPackage != null) {
                            return algoPackage.getVersion();
                        }
                    }
                }
            }
        } catch (Exception e) {
            // 处理 JSON 解析异常
            log.error("解析 abtestConfig 失败, bizCode: {}", bizCode, e);
        }

        return null;
    }

    private String getPrompt(String bizCode) {
        BizLlmpredictConfig bizLlmpredictConfig = bizLlmPredictConfigDao.selectByBizCode(bizCode);
        BizLlmpredictPromptTemplate bizLlmpredictPromptTemplate = bizLlmPredictPromptTemlateDao.selectById(bizLlmpredictConfig.getPromptTemplateId());
        return bizLlmpredictPromptTemplate.getPromptTemplate();
    }

}
