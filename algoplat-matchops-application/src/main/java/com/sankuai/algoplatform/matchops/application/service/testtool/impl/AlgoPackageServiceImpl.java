package com.sankuai.algoplatform.matchops.application.service.testtool.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.ImmutableList;
import com.sankuai.algoplatform.matchops.application.model.testtool.matchstrategy.AlgoStrategyAbtest;
import com.sankuai.algoplatform.matchops.application.model.testtool.matchstrategy.AlgoStrategyPo;
import com.sankuai.algoplatform.matchops.application.service.testtool.AlgoPackageService;
import com.sankuai.algoplatform.matchops.application.service.testtool.TestTaskService;
import com.sankuai.algoplatform.matchops.domain.constant.TestToolConstants;
import com.sankuai.algoplatform.matchops.domain.enums.EnvStatus;
import com.sankuai.algoplatform.matchops.domain.enums.MatchStrategyStatusEnum;
import com.sankuai.algoplatform.matchops.infrastructure.enums.RunTimeStatus;
import com.sankuai.algoplatform.matchops.domain.enums.TestTaskStatusEnum;
import com.sankuai.algoplatform.matchops.domain.service.testtool.LionHelper;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.*;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.*;
import com.sankuai.algoplatform.matchops.infrastructure.model.AlgoStrategyPackagePo;
import com.sankuai.algoplatform.matchops.infrastructure.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AlgoPackageServiceImpl implements AlgoPackageService {
    @Autowired
    private BizStrategyDao bizStrategyDao;

    @Autowired
    private AlgoStrategyDao algoStrategyDao;

    @Autowired
    private AlgoPackageDao algoPackageDao;

    @Autowired
    private TestTaskDao testTaskDao;

    @Autowired
    private MatchStrategyDao matchStrategyDao;

    @Autowired
    private TestTaskService testTaskService;

    @Override
    public Map<String, String> getAlgoCodePackageList(Integer current, Long bizLineId) {
        Map<String, String> result = new HashMap<>();
        List<BizStrategy> bizStrategies = bizStrategyDao.selectByPageAndBizLineId(current, 20, bizLineId);
        Long total = bizStrategyDao.selectCountByBizLineId(bizLineId);
        List<Map<String, String>> list = new ArrayList<>();
        bizStrategies.forEach(bizStrategy -> {
            Map<String, String> map = new HashMap<>();
            map.put("algoBizCodeId", String.valueOf(bizStrategy.getId()));
            map.put(TestToolConstants.ALGO_BIZ_CODE, bizStrategy.getBizCode());
            JSONObject jsonObject = JSON.parseObject(bizStrategy.getAbtestConfig());
            // 获取strategyName
            String strategyName = jsonObject.getString("strategyName");
            map.put("abTestName", strategyName);
            map.put("owner", bizStrategy.getOwner());
            map.put("createTime", DateUtil.toDateTimeString(bizStrategy.getAddTime()));
            map.put("updateTime", DateUtil.toDateTimeString(bizStrategy.getUpdateTime()));
            // 循环获取quota
            JSONArray distributions = jsonObject.getJSONArray("distributions");
            for (int i = 0; i < distributions.size(); i++) {
                JSONObject distribution = distributions.getJSONObject(i);
                int quota = distribution.getIntValue("quota");
                Map<String, String> temp = new HashMap<>();
                temp.put("quota", String.valueOf(quota));
                Long strategyId = distribution.getLong("strategyId");
                AlgoStrategy algoStrategy = algoStrategyDao.selectById(strategyId);
                temp.put("algoCodeStrategyId", String.valueOf(strategyId));
                temp.put("algoCodeStrategyName", algoStrategy.getNote());
                temp.put("entrancePath", algoStrategy.getEntrancePath());
                temp.put("entranceMethod", algoStrategy.getEntranceMethod());
                Long convertStrategyId = algoStrategy.getConvertStrategyId();
                if (convertStrategyId != null && convertStrategyId > 0) {
                    AlgoStrategy convertAlgoStrategy = algoStrategyDao.selectById(convertStrategyId);
                    temp.put("convertEntrancePath", convertAlgoStrategy.getEntrancePath());
                    temp.put("convertEntranceMethod", convertAlgoStrategy.getEntranceMethod());
                }
                Long packageId = algoStrategy.getPackageId();
                AlgoPackage algoPackage = algoPackageDao.selectById(packageId);
                temp.put("modulePath", algoPackage.getModulePath());
                temp.put("algoEnvId", String.valueOf(RunTimeStatus.fromDesc(algoPackage.getRuntime()).getCode()));
                temp.put("algoEnv", algoPackage.getRuntime());
                temp.put("codeRepo", algoPackage.getCodeRepo());
                temp.put("algoVersion", algoPackage.getVersion());
                temp.put("note", algoPackage.getNote());
                temp.put("algoCodeNote", bizStrategy.getNote());

                temp.putAll(map);
                list.add(temp);
            }
        });
        result.put("list", JSON.toJSONString(list));
        result.put("total", String.valueOf(total));
        result.put("current", String.valueOf(current));
        return result;
    }

    @Override
    public List<Map<String, String>> getDropdown(String type, Long bizLineId, String env) {
        List<Map<String, String>> result = new ArrayList<>();
        switch (type) {
            case "2":
                for (RunTimeStatus status : RunTimeStatus.values()) {
                    Map<String, String> map = new HashMap<>();
                    map.put("id", String.valueOf(status.getCode()));
                    map.put("name", status.getName());
                    result.add(map);
                }
                break;
            case "3":
                EnvStatus envStatus = EnvStatus.fromName(env);
                List<AlgoStrategy> algoStrategies = algoStrategyDao.selectByBizLineIdAndEnv(bizLineId, envStatus.getCode());
                //过滤掉文本转向量的策略
                Set<Long> convertStrategyIds = algoStrategies.stream().map(AlgoStrategy::getConvertStrategyId).filter(convertStrategyId -> convertStrategyId > 0).collect(Collectors.toSet());

                algoStrategies.forEach(algoStrategy -> {
                    if (convertStrategyIds.contains(algoStrategy.getId())) {
                        return;
                    }
                    Map<String, String> map = new HashMap<>();
                    map.put("id", String.valueOf(algoStrategy.getId()));
                    map.put("name", algoStrategy.getNote());
                    result.add(map);
                });
                break;
        }

        return result;
    }

    @Override
    public Long addAlgoStrategy(List<AlgoStrategy> algoStrategy, List<AlgoPackage> algoPackage) {
        AlgoStrategy mainAlgoStrategy = algoStrategy.get(0);
        AlgoStrategy convertStrategy = algoStrategy.size() > 1 ? algoStrategy.get(1) : null;

        AlgoPackage mainAlgoPackage = algoPackage.get(0);
        AlgoPackage convertAlgoPackage = algoPackage.size() > 1 ? algoPackage.get(1) : null;

        List<AlgoPackage> algoPackages = algoPackageDao.selectByVersion(mainAlgoPackage.getVersion());
        long count = algoPackages.stream().filter(alg -> !StringUtils.equals(alg.getCodeRepo(), mainAlgoPackage.getCodeRepo())).count();
        if (count > 0) {
            throw new RuntimeException("仓库不同commitId相同");
        }

        if (convertStrategy != null && convertAlgoPackage != null) {
            algoPackageDao.insert(convertAlgoPackage);
            convertStrategy.setPackageId(convertAlgoPackage.getId());
            algoStrategyDao.insert(convertStrategy);
            mainAlgoStrategy.setConvertStrategyId(convertStrategy.getId());
        }
        algoPackageDao.insert(mainAlgoPackage);
        mainAlgoStrategy.setPackageId(mainAlgoPackage.getId());
        return algoStrategyDao.insert(mainAlgoStrategy);
    }

    @Override
    public void editAlgoStrategy(List<AlgoStrategy> algoStrategies, List<AlgoPackage> algoPackage) {
        AlgoStrategy mainAlgoStrategy = algoStrategies.get(0);
        AlgoStrategy convertStrategy = algoStrategies.size() > 1 ? algoStrategies.get(1) : null;

        AlgoPackage mainAlgoPackage = algoPackage.get(0);
        AlgoPackage convertAlgoPackage = algoPackage.size() > 1 ? algoPackage.get(1) : null;

        List<AlgoPackage> algoPackages = algoPackageDao.selectByVersion(mainAlgoPackage.getVersion());
        long count = algoPackages.stream().filter(alg -> !StringUtils.equals(alg.getCodeRepo(), mainAlgoPackage.getCodeRepo())).count();
        if (count > 0) {
            throw new RuntimeException("仓库不同commitId相同");
        }

        AlgoStrategy dbAlgoStrategy = algoStrategyDao.selectById(mainAlgoStrategy.getId());
//        if (EnvStatus.getByCode(dbAlgoStrategy.getEnv()) == EnvStatus.PROD) {
//            throw new RuntimeException("当前策略禁止修改");
//        }

        //如果当前代码有正在跑数中的任务，不让修改
        List<BizStrategy> bizStrategies = bizStrategyDao.selectByStrategyId(mainAlgoStrategy.getId());
        if (filterCurrentMatchStrategy(bizStrategies)) {
            throw new RuntimeException("当前不允许修改策略");
        }


        Long convertId = algoStrategyDao.selectById(mainAlgoStrategy.getId()).getConvertStrategyId();
        if (convertStrategy != null && convertAlgoPackage != null) {
            if (convertId != null && convertId > 0) {
                convertStrategy.setId(convertId);
                algoStrategyDao.update(convertStrategy);

                Long convertPackageId = algoStrategyDao.selectById(convertId).getPackageId();
                convertAlgoPackage.setId(convertPackageId);
                algoPackageDao.update(convertAlgoPackage);
            } else {
                algoPackageDao.insert(convertAlgoPackage);

                //不存在则新增
                convertStrategy.setNote(mainAlgoStrategy.getNote() + "文本转向量");
                convertStrategy.setEnv(LionHelper.getLionConfigEnv().getCode());
                convertStrategy.setBizLineId(dbAlgoStrategy.getBizLineId());
                convertStrategy.setPackageId(convertAlgoPackage.getId());
                Long newConvertId = algoStrategyDao.insert(convertStrategy);
                mainAlgoStrategy.setConvertStrategyId(newConvertId);
            }
        } else {
            //解除convert关系
            mainAlgoStrategy.setConvertStrategyId(0L);
            //删除convert策略
            if (convertId != null && convertId > 0) {
                Long packageId = algoStrategyDao.selectById(convertId).getPackageId();
                AlgoPackage delAlgoPackage = new AlgoPackage();
                delAlgoPackage.setId(packageId);
                delAlgoPackage.setStatus(-1);
                algoPackageDao.update(delAlgoPackage);

                AlgoStrategy delAlgoStrategy = new AlgoStrategy();
                delAlgoStrategy.setId(convertId);
                delAlgoStrategy.setStatus(-1);
                algoStrategyDao.update(delAlgoStrategy);
            }

        }

        algoStrategyDao.update(mainAlgoStrategy);
        mainAlgoPackage.setId(dbAlgoStrategy.getPackageId());
        algoPackageDao.update(mainAlgoPackage);

        //关联测试任务任务修改为待测试
        for (AlgoStrategy algoStrategy : algoStrategies) {
            List<BizStrategy> dbBizStrategy = bizStrategyDao.selectByStrategyId(algoStrategy.getId());
            for (BizStrategy bizStrategy : dbBizStrategy) {
                initTestTaskStatus(bizStrategy.getBizCode());
            }
        }
    }

    @Override
    public Long addAlgoBizCode(BizStrategy bizStrategy) {
        BizStrategy dbBizStrategy = bizStrategyDao.selectByBizCode(bizStrategy.getBizCode());
        if (dbBizStrategy != null) {
            throw new RuntimeException("bizCode不能重复");
        }
        return bizStrategyDao.insert(bizStrategy);
    }

    @Override
    public void editAlgoBizCode(BizStrategy bizStrategy) {
        BizStrategy dbBizStrategy = bizStrategyDao.selectById(bizStrategy.getId());
//        if (EnvStatus.getByCode(dbBizStrategy.getEnv()) == EnvStatus.PROD) {
//            throw new RuntimeException("当前算法代码bizCode不允许修改");
//        }
        if (filterCurrentMatchStrategy(ImmutableList.of(dbBizStrategy))) {
            throw new RuntimeException("当前不允许修改策略");
        }
        bizStrategyDao.update(bizStrategy);

        //关联测试任务任务修改为初始态
        initTestTaskStatus(bizStrategy.getBizCode());
    }

    @Override
    public Integer deleteAlgoBizCode(Long id, String mis) {
        BizStrategy bizStrategy = bizStrategyDao.selectById(id);
        JSONObject jsonObject = JSON.parseObject(bizStrategy.getAbtestConfig());

        // 循环获取quota
        JSONArray distributions = jsonObject.getJSONArray("distributions");
        for (int i = 0; i < distributions.size(); i++) {
            JSONObject distribution = distributions.getJSONObject(i);
            Long strategyId = distribution.getLong("strategyId");
            AlgoStrategy algoStrategy = algoStrategyDao.selectById(strategyId);
            Long packageId = algoStrategy.getPackageId();
            algoPackageDao.softDelete(packageId, mis);
            algoStrategyDao.softDelete(strategyId, mis);
        }
        return bizStrategyDao.softDelete(id, mis);
    }

    @Override
    public List<Pair<BizStrategy, List<AlgoStrategyPackagePo>>> getAlgoCodeAllInfo(List<String> algoBizCodes) {
        List<Pair<BizStrategy, List<AlgoStrategyPackagePo>>> algoCodeAllInfo = new ArrayList<>();

        List<BizStrategy> bizStrategies = bizStrategyDao.selectByBizCodes(algoBizCodes);

        for (BizStrategy bizStrategy : bizStrategies) {
            AlgoStrategyAbtest abtest = JSONObject.parseObject(bizStrategy.getAbtestConfig(), new TypeReference<AlgoStrategyAbtest>() {
            });
            List<Long> algoStrategyIds = abtest.getDistributions().stream().map(AlgoStrategyPo::getId).collect(Collectors.toList());
            List<AlgoStrategy> algoStrategies = algoStrategyDao.selectByIds(algoStrategyIds);
            List<AlgoStrategyPackagePo> algoStrategyPackagePoList = new ArrayList<>();
            for (AlgoStrategy algoStrategy : algoStrategies) {
                AlgoPackage algoPackage = algoPackageDao.selectById(algoStrategy.getPackageId());
                AlgoStrategyPackagePo algoStrategyPackagePo = new AlgoStrategyPackagePo(algoStrategy, algoPackage);
                if (Objects.nonNull(algoStrategy.getConvertStrategyId()) && algoStrategy.getConvertStrategyId() > 0) {
                    AlgoStrategy convertStrategy = algoStrategyDao.selectById(algoStrategy.getConvertStrategyId());
                    AlgoPackage convertPackage = algoPackageDao.selectById(convertStrategy.getPackageId());
                    algoStrategyPackagePo.setConvertStrategyPackage(convertStrategy, convertPackage);
                }
                algoStrategyPackagePoList.add(algoStrategyPackagePo);
            }
            algoCodeAllInfo.add(Pair.of(bizStrategy, algoStrategyPackagePoList));
        }
        return algoCodeAllInfo;
    }

    private boolean filterCurrentMatchStrategy(List<BizStrategy> bizStrategies) {
        //1. 判断当前策略是否可修改
        List<MatchStrategy> matchStrategies = new ArrayList<>();
        for (BizStrategy bizStrategy : bizStrategies) {
            List<MatchStrategy> dbs = matchStrategyDao.findByAlgoBizCode(bizStrategy.getBizCode());
            if (CollectionUtils.isNotEmpty(dbs)) {
                matchStrategies.addAll(dbs);
            }
        }
        if (CollectionUtils.isEmpty(matchStrategies)) {
            return false;
        }
        for (MatchStrategy matchStrategy : matchStrategies) {
            if (!MatchStrategyStatusEnum.matchStrategyCanUpdate(matchStrategy.getStatus())) {
                return true;
            }
        }
        //2. 判断当前策略是否存在正在测试中任务，存在则不准修改
        List<TestTask> testTasks = testTaskDao.selectByMatchStrategyIds(matchStrategies.stream().map(MatchStrategy::getId).collect(Collectors.toList()));
        for (TestTask testTask : testTasks) {
            TestTaskStatusEnum status = TestTaskStatusEnum.getByCode(testTask.getStatus());
            if (status == TestTaskStatusEnum.TESTING) {
                return true;
            }
        }
        return false;
    }


    private void initTestTaskStatus(String bizCode) {
        List<MatchStrategy> matchStrategies = matchStrategyDao.findByAlgoBizCode(bizCode);
        if (CollectionUtils.isEmpty(matchStrategies)) {
            return;
        }
        List<Long> matchStrategyIds = matchStrategies.stream().filter(f -> MatchStrategyStatusEnum.matchStrategyCanUpdate(f.getStatus())).map(MatchStrategy::getId).collect(Collectors.toList());
        List<TestTask> testTasks = testTaskDao.selectByMatchStrategyIds(matchStrategyIds);
        for (TestTask testTask : testTasks) {
            Integer oldStatus = testTask.getStatus();
            testTaskDao.updateTestTaskStatus(testTask.getId(), TestTaskStatusEnum.INIT.getCode());
            log.error("状态修改成功.testTaskId={}, oldStatus={}, newStatus={}", testTask.getId(), oldStatus, TestTaskStatusEnum.TOBE_TEST.getCode());
            //开始准备环境
//            testTaskService.prepareEnv(testTask.getId());
        }
    }

}
