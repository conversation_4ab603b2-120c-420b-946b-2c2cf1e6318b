package com.sankuai.algoplatform.matchops.application.model.testtool;

import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ReporterTemplate;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReporterTemplatePo {
    private Long id;

    private Long bizLineId;

    private String name;

    private String templateAddr;

    private String creator;

    private Date createTime;

    private Date updateTime;

    private Boolean isDel;

    private String reporter;

    public ReporterTemplatePo(ReporterTemplate reporterTemplate) {
        if (reporterTemplate == null) {
            return;
        }
        this.id = reporterTemplate.getId();
        this.bizLineId = reporterTemplate.getBizLineId();
        this.name = reporterTemplate.getName();
        this.templateAddr = reporterTemplate.getTemplateAddr();
        this.creator = reporterTemplate.getCreator();
        this.createTime = reporterTemplate.getCreateTime();
        this.updateTime = reporterTemplate.getUpdateTime();
        this.isDel = reporterTemplate.getIsDel();
    }
}
