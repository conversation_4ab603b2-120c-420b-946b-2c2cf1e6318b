package com.sankuai.algoplatform.matchops.application.model.testtool.resourcegroup;

import com.alibaba.fastjson.JSON;
import com.sankuai.algoplatform.matchops.application.model.testtool.TestSubTaskPo;
import com.sankuai.algoplatform.matchops.domain.model.testtool.DFridayResource;
import com.sankuai.algoplatform.matchops.domain.model.testtool.DMlpResource;
import com.sankuai.algoplatform.matchops.domain.model.testtool.DOctoResource;
import com.sankuai.algoplatform.matchops.domain.model.testtool.EnvPrepareContext;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ResourceGroup;
import lombok.Data;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class ResourceGroupPo {

    private Long id;

    private String name;

    private Long bizLineId;

    private List<OctoResource> octoResources;

    private List<MlpResource> mlpResources;

    private List<FridayResource> fridayResources;

    private String creator;

    private Date createTime;

    private Date updateTime;

    private Boolean isDel;

    public ResourceGroupPo() {
    }

    public ResourceGroupPo(TestSubTaskPo subTaskPo) {
        this.octoResources = subTaskPo.getOctoResources();
        this.mlpResources = subTaskPo.getMlpModelResources();
        this.fridayResources = subTaskPo.getFridayModelResources();
    }

    public ResourceGroupPo(ResourceGroup resourceGroup) {
        this.id = resourceGroup.getId();
        this.name = resourceGroup.getName();
        this.bizLineId = resourceGroup.getBizLineId();

        if (StringUtils.isNotEmpty(resourceGroup.getOctoResources())
                && !StringUtils.equals(resourceGroup.getOctoResources(), "[{}]")
                && !StringUtils.equals(resourceGroup.getOctoResources(), "[]")) {
            this.octoResources = JSON.parseArray(resourceGroup.getOctoResources(), OctoResource.class);
        }

        if (StringUtils.isNotEmpty(resourceGroup.getMlpResources())
                && !StringUtils.equals(resourceGroup.getMlpResources(), "[{}]")
                && !StringUtils.equals(resourceGroup.getMlpResources(), "[]")) {
            this.mlpResources = JSON.parseArray(resourceGroup.getMlpResources(), MlpResource.class);
        }

        if (StringUtils.isNotEmpty(resourceGroup.getFridayResources())
                && !StringUtils.equals(resourceGroup.getFridayResources(), "[{}]")
                && !StringUtils.equals(resourceGroup.getFridayResources(), "[]")) {
            this.fridayResources = JSON.parseArray(resourceGroup.getFridayResources(), FridayResource.class);
        }

        this.creator = resourceGroup.getCreator();
        this.createTime = resourceGroup.getCreateTime();
        this.updateTime = resourceGroup.getUpdateTime();
        this.isDel = resourceGroup.getIsDel();
    }

    @SneakyThrows
    public EnvPrepareContext trans() {
        EnvPrepareContext context = new EnvPrepareContext();
        if (CollectionUtils.isNotEmpty(octoResources)) {
            List<DOctoResource> resources = octoResources.stream()
                .map(resource -> {
                    try {
                        return resource.trans();
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                })
                .collect(Collectors.toList());
            context.setOctoResources(resources);
        }
        if (CollectionUtils.isNotEmpty(mlpResources)) {
            List<DMlpResource> resources = mlpResources.stream()
                .map(resource -> {
                    try {
                        return resource.trans();
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                })
                .collect(Collectors.toList());
            context.setMlpResources(resources);
        }
        if (CollectionUtils.isNotEmpty(fridayResources)) {
            List<DFridayResource> resources = fridayResources.stream()
                .map(resource -> {
                    try {
                        return resource.trans();
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                })
                .collect(Collectors.toList());
            context.setFridayResources(resources);
        }
        return context;
    }
}
