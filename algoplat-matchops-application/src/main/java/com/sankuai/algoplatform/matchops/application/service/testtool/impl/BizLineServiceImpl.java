package com.sankuai.algoplatform.matchops.application.service.testtool.impl;

import com.sankuai.algoplatform.matchops.application.service.testtool.BizLineService;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.BizLineDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.BizLine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

@Service
public class BizLineServiceImpl implements BizLineService {
    @Autowired
    BizLineDao bizLineDao;

    @Override
    public List<Map<String, String>> getBizLineList(Long bizLineId) {
        List<Map<String, String>> result = new ArrayList<>();
        List<BizLine> bizLineList = bizLineDao.selectAllNotDelete();
        AtomicBoolean select = new AtomicBoolean(false);
        bizLineList.forEach(bizLine -> {
            Map<String, String> temp = new HashMap<>();
            temp.put("id", String.valueOf(bizLine.getId()));
            temp.put("name", bizLine.getBizLineName());
            if (Objects.equals(bizLineId, bizLine.getId())) {
                select.set(true);
                temp.put("select", "1");
            }
            result.add(temp);
        });

        if (!select.get()) {
            result.get(0).put("select", "1");
        }
        return result;
    }
}
