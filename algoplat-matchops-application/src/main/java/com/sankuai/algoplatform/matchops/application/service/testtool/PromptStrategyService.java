package com.sankuai.algoplatform.matchops.application.service.testtool;

import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.*;
import org.apache.commons.lang3.tuple.Triple;

import java.util.List;
import java.util.Map;

public interface PromptStrategyService {

    Map<String, String> getPromptList(Integer current, Long bizLineId);

    List<Map<String, String>> getDropdown(String type, Long bizLineId, String env);

    Long addPromptStrategy(BizLlmpredictConfig bizLlmpredictConfig, BizLlmpredictPromptTemplate bizLlmpredictPromptTemplate);

    void editPromptStrategy(BizLlmpredictConfig bizLlmpredictConfig, BizLlmpredictPromptTemplate bizLlmpredictPromptTemplate);

    Long addPromptBizCode(LlmBizStrategy llmBizStrategy);

    void editPromptBizCode(LlmBizStrategy llmBizStrategy);

    Integer deletePromptBizCode(Long matchStrategyId,String owner);

    Triple<List<LlmBizStrategy>, List<BizLlmpredictConfig>, List<BizLlmpredictPromptTemplate>> getLlmAllInfo(List<String> llmBizCodes);

    BizLlmpredictConfig selectBizLlmpredictConfig(String llmBizCode);

    BizLlmpredictPromptTemplate selectBizLlmpredictPromptTemplate(Long promptTemplateId);

}
