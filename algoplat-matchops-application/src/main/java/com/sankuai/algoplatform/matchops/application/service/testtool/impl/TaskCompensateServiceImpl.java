package com.sankuai.algoplatform.matchops.application.service.testtool.impl;

import com.sankuai.algoplatform.matchops.application.service.testtool.TaskCompensateService;
import com.sankuai.algoplatform.matchops.application.service.testtool.TestTaskService;
import com.sankuai.algoplatform.matchops.domain.enums.TestTaskStatusEnum;
import com.sankuai.algoplatform.matchops.domain.service.testtool.TaskExecuteService;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.TestTaskDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
public class TaskCompensateServiceImpl implements TaskCompensateService {


    @Autowired
    private TestTaskService testTaskService;

    @Autowired
    private TaskExecuteService taskExecuteService;


    @Autowired
    private TestTaskDao testTaskDao;


    @Override
    public void checkTestingTaskException() {
        List<TestTask> testTasks = testTaskDao.selectByStatus(TestTaskStatusEnum.TESTING.getCode());
        //todo 并发执行
        for (TestTask task : testTasks) {
            taskExecuteService.checkTestingTaskException(task);
        }

    }


    @Override
    public void handleTestingTasks() {
        List<TestTask> testTasks = testTaskDao.selectByStatus(TestTaskStatusEnum.TESTING.getCode());
        //todo 并发执行
        for (TestTask task : testTasks) {
            taskExecuteService.handleTestingSubTask(task);
        }
    }
}
