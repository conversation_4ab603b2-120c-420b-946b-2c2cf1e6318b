package com.sankuai.algoplatform.matchops.application.model.testtool.resourcegroup;

import com.sankuai.algoplatform.matchops.api.response.resourceGroup.TTestTaskDetailResponse;
import com.sankuai.algoplatform.matchops.domain.model.testtool.DMlpResource;
import com.sankuai.algoplatform.matchops.infrastructure.model.InstanceInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.InvocationTargetException;

@Data
public class MlpResource {

    private String wxProject;

    private String appkey;

    private String modelName;

    private String modelType;

    private String modelVersion;

    private String instanceNum;

    private String retainTime;

    private InstanceInfo instanceInfo;

    public TTestTaskDetailResponse.Data.MlpResourceInfo trans2TData() {
        TTestTaskDetailResponse.Data.MlpResourceInfo resourceInfo = new TTestTaskDetailResponse.Data.MlpResourceInfo();
        // 原有字段
        resourceInfo.setAppkey(this.appkey);
        resourceInfo.setSet(this.instanceInfo.getSet());
        resourceInfo.setInstanceNum(this.instanceNum);

        // 需要添加的字段
        resourceInfo.setWxProject(this.wxProject);
        resourceInfo.setModelName(this.modelName);
        resourceInfo.setModelType(this.modelType);
        resourceInfo.setModelVersion(this.modelVersion);
        resourceInfo.setRetainTime(this.retainTime);
        resourceInfo.setQuqueName(this.instanceInfo.getQuqueName());
        resourceInfo.setGroupName(this.instanceInfo.getGroupName());
        resourceInfo.setServerImage(this.instanceInfo.getServerImage());
        resourceInfo.setRpcOutTime(this.instanceInfo.getRpcOutTime());
        resourceInfo.setBatching(this.instanceInfo.getBatching());
        resourceInfo.setMaxBatchSize(this.instanceInfo.getBatchingConfig().getMaxBatchSize());
        resourceInfo.setBatchTimeoutMicros(this.instanceInfo.getBatchingConfig().getBatchTimeoutMicros());
        resourceInfo.setBatchingExtra(this.instanceInfo.getBatchingConfig().getBatchingExtra());
        resourceInfo.setEnv(this.instanceInfo.getEnv());
        resourceInfo.setVcores(this.instanceInfo.getResourceConfig().getVcores());
        resourceInfo.setMemory(this.instanceInfo.getResourceConfig().getMemory());
        resourceInfo.setGcores(this.instanceInfo.getResourceConfig().getGcores());
        resourceInfo.setGcoresType(this.instanceInfo.getResourceConfig().getGcoresType());
        return resourceInfo;
    }

    public DMlpResource trans() throws InvocationTargetException, IllegalAccessException {
        DMlpResource resource = new DMlpResource();
        BeanUtils.copyProperties(resource, this);
        if (StringUtils.isEmpty(retainTime)) {
            retainTime = "1";
        }
        resource.setRetainTime(this.retainTime);
        return resource;
    }

}
