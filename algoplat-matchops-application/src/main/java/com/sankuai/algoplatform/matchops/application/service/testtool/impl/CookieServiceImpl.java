package com.sankuai.algoplatform.matchops.application.service.testtool.impl;

import com.meituan.mdp.boot.starter.config.client.MccConfigClient;
import com.sankuai.algoplatform.matchops.application.service.testtool.CookieService;
import com.sankuai.algoplatform.matchops.infrastructure.config.LionConfig;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.CookieConfigDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.CookieConfig;
import com.sankuai.algoplatform.matchops.infrastructure.util.ContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CookieServiceImpl implements CookieService {

    @Autowired
    private CookieConfigDao cookieConfigDao;

    @Override
    public int addCookieConfig() {
        CookieConfig config = new CookieConfig();
        config.setUserId(ContextUtil.getLoginUserId());
        config.setUserMis(ContextUtil.getLoginUserMis());
        config.setCookie(ContextUtil.getSsoId());
        return cookieConfigDao.insertOrUpdate(config);
    }

    @Override
    public String getCookie() {
        CookieConfig config = cookieConfigDao.selectByMis(LionConfig.AGENT_XUECHENG_USER);
        if (config != null) {
            return config.getCookie();
        }
        return "";
    }
}
