package com.sankuai.algoplatform.matchops.application.service.testtool.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.common.shade.org.apache.http.annotation.Immutable;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.sankuai.algoplatform.matchops.api.request.testTask.TRunTaskRequest;
import com.sankuai.algoplatform.matchops.application.model.testtool.ReporterTemplatePo;
import com.sankuai.algoplatform.matchops.application.model.testtool.TestScenePo;
import com.sankuai.algoplatform.matchops.application.model.testtool.TestSubTaskPo;
import com.sankuai.algoplatform.matchops.application.model.testtool.TestTaskPo;
import com.sankuai.algoplatform.matchops.application.model.testtool.matchstrategy.*;
import com.sankuai.algoplatform.matchops.application.model.testtool.resourcegroup.OctoResource;
import com.sankuai.algoplatform.matchops.application.model.testtool.resourcegroup.ResourceGroupPo;
import com.sankuai.algoplatform.matchops.application.service.testtool.AlgoPackageService;
import com.sankuai.algoplatform.matchops.application.service.testtool.ParamHelper;
import com.sankuai.algoplatform.matchops.application.service.testtool.PromptStrategyService;
import com.sankuai.algoplatform.matchops.application.service.testtool.TestTaskService;
import com.sankuai.algoplatform.matchops.domain.constant.TestToolConstants;
import com.sankuai.algoplatform.matchops.domain.enums.ReqPostTypeEnum;
import com.sankuai.algoplatform.matchops.domain.enums.TestSubTaskStatusEnum;
import com.sankuai.algoplatform.matchops.domain.enums.TestTaskStatusEnum;
import com.sankuai.algoplatform.matchops.domain.model.testtool.EnvPrepareContext;
import com.sankuai.algoplatform.matchops.domain.model.testtool.PredictorCache1Config;
import com.sankuai.algoplatform.matchops.domain.service.testtool.EnvPrepareService;
import com.sankuai.algoplatform.matchops.domain.service.testtool.LionHelper;
import com.sankuai.algoplatform.matchops.domain.service.testtool.TaskExecuteService;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.*;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.*;
import com.sankuai.algoplatform.matchops.infrastructure.model.AlgoStrategyPackagePo;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.DxService;
import com.sankuai.algoplatform.matchops.infrastructure.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.algoplatform.matchops.domain.constant.TestToolConstants.MISSION_ID;

@Service
@Slf4j
public class TestTaskServiceImpl implements TestTaskService {

    @Autowired
    private TestTaskDao testTaskDao;

    @Autowired
    private MatchStrategyDao matchStrategyDao;

    @Autowired
    private TestSceneDao testSceneDao;

    @Autowired
    private AlgoPackageService algoPackageService;

    @Autowired
    private PromptStrategyService promptStrategyService;

    @Autowired
    private ResourceGroupDao resourceGroupDao;

    @Autowired
    private ReporterTemplateDao reporterTemplateDao;

    @Autowired
    private EnvPrepareService envPrepareService;

    @Autowired
    private TestSubTaskDao testSubTaskDao;

    @Autowired
    private TaskExecuteService taskExecuteService;

    @Autowired
    private DxService dxService;

    @Override
    public Long getTestTaskTotalCountByBizLineId(Long bizLineId) {
        return testTaskDao.selectCountByBizLineId(bizLineId);
    }

    @Override
    public List<Map<String, String>> getTestTaskList(Integer current, Long bizLineId) {
        current = current <= 0 ? 1 : current;
        List<TestTask> testTask = testTaskDao.selectByPageAndBizLineId(current, 20, bizLineId);

        List<Map<String, String>> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(testTask)) {
            return result;
        }

        List<Long> matchStrategyIds = testTask.stream().map(TestTask::getMatchStrategyId).collect(Collectors.toList());
        List<Long> resourceGroupIds = testTask.stream().map(TestTask::getResourceGroupId).filter(Objects::nonNull).collect(Collectors.toList());
        List<String> templateIdss = testTask.stream().map(TestTask::getReporterTemplateId).filter(Objects::nonNull).collect(Collectors.toList());
        //打平所有的报告模板ID
        List<Long> templateIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(templateIdss)) {
            List<List<Long>> collect = templateIdss.stream().map(str -> JSONObject.parseObject(str, new TypeReference<List<Long>>() {
            })).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                templateIds = collect.stream().flatMap(List::stream).collect(Collectors.toList());
            }
        }

        List<MatchStrategy> matchStrategies = matchStrategyDao.selectByIds(matchStrategyIds);
        Map<Long, MatchStrategy> matchStrategyMap = matchStrategies.stream().collect(Collectors.toMap(MatchStrategy::getId, Function.identity(), (k1, k2) -> k1));

        Map<Long, ResourceGroup> resourceGroupMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(resourceGroupIds)) {
            List<ResourceGroup> resourceGroups = resourceGroupDao.selectByIds(resourceGroupIds);
            resourceGroupMap = resourceGroups.stream().collect(Collectors.toMap(ResourceGroup::getId, Function.identity(), (k1, k2) -> k1));
        }

        Map<Long, ReporterTemplate> reporterTemplateMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(templateIds)) {
            List<ReporterTemplate> reporterTemplateList = reporterTemplateDao.selectByIds(templateIds);
            reporterTemplateMap = reporterTemplateList.stream().collect(Collectors.toMap(ReporterTemplate::getId, Function.identity(), (k1, k2) -> k1));
        }


        Map<Long, ReporterTemplate> finalReporterTemplateMap = reporterTemplateMap;
        Map<Long, ResourceGroup> finalResourceGroupMap = resourceGroupMap;
        testTask.forEach(task -> {
            String reporterTemplateIds = task.getReporterTemplateId();
            List<ReporterTemplate> reporterTemplates = Lists.newArrayList();
            if (StringUtils.isNotEmpty(reporterTemplateIds)) {
                List<String> templateIdList = JSONObject.parseObject(reporterTemplateIds, new TypeReference<List<String>>() {
                });
                reporterTemplates = templateIdList.stream().map(id -> finalReporterTemplateMap.get(Long.parseLong(id))).collect(Collectors.toList());
            }

            TestTaskPo testTaskPo = new TestTaskPo(task, matchStrategyMap.get(task.getMatchStrategyId()), finalResourceGroupMap.get(task.getResourceGroupId()), reporterTemplates);
            Map<String, String> data = new HashMap<>();
            data.put("testTaskId", String.valueOf(task.getId()));
            data.put("testTaskName", String.valueOf(task.getName()));
            data.put("matchStrategyId", String.valueOf(testTaskPo.getMatchStrategy().getId()));
            data.put("matchStrategyName", testTaskPo.getMatchStrategy().getName());
            String resourceGroupId = testTaskPo.getResourceGroup() != null ? String.valueOf(testTaskPo.getResourceGroup().getId()) : "";
            String resourceGroupName = testTaskPo.getResourceGroup() != null ? String.valueOf(testTaskPo.getResourceGroup().getName()) : "";
            data.put("resourceGroupId", resourceGroupId);
            data.put("resourceGroupName", resourceGroupName);

            if (CollectionUtils.isNotEmpty(testTaskPo.getReporterTemplate())) {
                List<String> reporterTemplateNames = testTaskPo.getReporterTemplate().stream().map(ReporterTemplatePo::getName).collect(Collectors.toList());
                List<Long> repoTemplateIds = testTaskPo.getReporterTemplate().stream().map(ReporterTemplatePo::getId).collect(Collectors.toList());
                data.put("reporterTemplateIds", StringUtils.join(repoTemplateIds, ","));
                data.put("reporterTemplateName", StringUtils.join(reporterTemplateNames, "、"));
            } else {
                data.put("reporterTemplateIds", "");
                data.put("reporterTemplateName", "");
            }

            data.put("statusCode", String.valueOf(testTaskPo.getStatus().getCode()));
            data.put("statusName", testTaskPo.getStatus().getName());
            data.put("owner", testTaskPo.getOwner());
            data.put("createTime", DateUtil.toDateTimeString(testTaskPo.getCreateTime()));
            data.put("updateTime", DateUtil.toDateTimeString(testTaskPo.getUpdateTime()));
            result.add(data);
        });
        return result;
    }

    @Override
    public Pair<Boolean, String> addTestTask(TestTask testTask) {
        //XT任务参数不能为空
        //同一个匹配策略只能有一个任务
        testTask.setStatus(TestTaskStatusEnum.INIT.getCode());
        testTask.setOwner(ContextUtil.getLoginUserMis());
        Long taskId = testTaskDao.insert(testTask);

        //准备环境
        prepareEnv(taskId);

        return Pair.of(true, String.valueOf(taskId));
    }

    @Override
    public Long editTestTask(TestTask testTask) {
        testTask.setMatchStrategyId(null);//匹配策略不准改
        testTask.setBizLineId(null);//业务线不准改
        testTask.setOwner(ContextUtil.getLoginUserMis());
        testTaskDao.update(testTask);
        return -1L;
    }

    @Override
    public TestTaskPo getTestTaskDetailByTaskId(Long taskId) {
        return getTestTaskPoByTask(testTaskDao.selectById(taskId));
    }

    @Override
    public TestTaskPo getTestTaskDetailByTaskNameAndBizId(String taskName, Long bizLineId) {
        TestTask testTask = testTaskDao.selectByName(taskName, bizLineId);
        if (testTask == null) {
            return null;
        }
        return getTestTaskPoByTask(testTask);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Pair<Boolean, String> runTask(TRunTaskRequest request) {
        TestTask testTask = testTaskDao.selectById(request.getTestTaskId());//强读主

        if (testTask.getResourceGroupId() != null && testTask.getResourceGroupId() > 0) {
            ResourceGroup resourceGroup = resourceGroupDao.selectById(testTask.getResourceGroupId());
            ResourceGroupPo resourceGroupPo = new ResourceGroupPo(resourceGroup);
            boolean ready = envPrepareService.queryEnvReady(resourceGroupPo.trans(), testTask.getOwner());
            if (!ready) {
                testTaskDao.updateTestTaskStatus(request.getTestTaskId(), TestTaskStatusEnum.ENV_FAIL.getCode());
                dxService.sendMsg2Users("环境未就绪，请先准备环境", Lists.newArrayList(testTask.getOwner()));
                return Pair.of(false, "环境未就绪，请先准备环境");
            } else {
                testTask.setStatus(TestTaskStatusEnum.TOBE_TEST.getCode());
            }
        }


        MatchStrategy matchStrategy = matchStrategyDao.selectById(testTask.getMatchStrategyId());
        TestScene testScene = testSceneDao.selectById(matchStrategy.getTestSceneId());


        ReqPostTypeEnum reqPostType = ReqPostTypeEnum.getByCode(testScene.getReqPostType());
        switch (reqPostType) {
            case XT:
                //-version 18723611 -parseVersion 18720987 -cd 2025-02-18
                Map<String, String> validParam = ParamHelper.parseXTParam(request.getParam());
                if (StringUtils.isEmpty(validParam.get(TestToolConstants.VERSION)) || StringUtils.isEmpty(validParam.get(TestToolConstants.CD))) {
                    return Pair.of(false, "参数错误，XT类型必须包含参数version和cd，比如： -version 18723611 -parseVersion 18720987 -cd 2025-02-18 -hour 04 -dt 20250218");
                }
                List<String> replaceList = RegexUtil.extractDollar(testScene.getExtra());
                for (String replace : replaceList) {
                    String[] split = StringUtils.split(StringUtils.substring(replace, 2, replace.length() - 1), "#");
                    if (split.length >= 2 && StringUtils.equals(split[0], "param") && StringUtils.isEmpty(validParam.get(split[1]))) {
                        return Pair.of(false, "参数错误，必须包含场景中需要替换的参数");
                    }
                }

                break;
            case HTTP:
                Map<String, String> validParam2 = ParamHelper.parseHttpParam(request.getParam());
                if (StringUtils.isEmpty(validParam2.get(TestToolConstants.REQUEST_BODY)) && StringUtils.isEmpty(validParam2.get(TestToolConstants.SQL_PARAM)) && StringUtils.isEmpty(request.getFilePath())) {
                    return Pair.of(false, "参数错误，requestBody、sqlParam和上传文件不能全为空，比如：-requestBody {xxx} -sqlParam 'select...'");
                }
                if (StringUtils.contains(testScene.getReqPostLink(), "ip") && StringUtils.isEmpty(validParam2.get("ip"))) {
                    return Pair.of(false, "缺少ip参数");
                }
                break;
            case FUNCTION:
                Map<String, String> validParam3 = ParamHelper.parseHttpParam(request.getParam());
                if (StringUtils.isEmpty(validParam3.get(TestToolConstants.REQUEST_BODY)) && StringUtils.isEmpty(validParam3.get(TestToolConstants.SQL_PARAM)) && StringUtils.isEmpty(request.getFilePath())) {
                    return Pair.of(false, "参数错误，requestBody、sqlParam和上传文件不能全为空，比如：-requestBody {xxx} -sqlParam 'select...'");
                }
                break;
        }


        if (!TestTaskStatusEnum.isCanBeRunByTask(testTask.getStatus())) {
            return Pair.of(false, "当前任务不可被测试");
        }

        //如果包含algoCode并且开启了以及缓存，清除一级缓存
        clearPredictorCache1(request.getClearCache(), matchStrategy);


        Map<String, String> runParam = new HashMap<>();
        runParam.put(TestToolConstants.PARAM, request.getParam());
        runParam.put(TestToolConstants.FILE, request.getFilePath());
        runParam.put(TestToolConstants.CLEAR_CACHE, request.getClearCache());
        runParam.put(TestToolConstants.MISSION_ID, request.getMissionId());

        TestTask dbTask = new TestTask();
        dbTask.setId(request.getTestTaskId());
        dbTask.setStatus(TestTaskStatusEnum.TESTING.getCode());//todo 变更日志
        dbTask.setRunParam(JSON.toJSONString(runParam));

        testTask.setRunParam(JSON.toJSONString(runParam));
        List<TestSubTask> testSubTasks = buildTestSubTasks(testTask);
        testSubTaskDao.batchInsert(testSubTasks);

        testTaskDao.update(dbTask);

        //开始执行子任务
        ThreadPoolExecutor taskRunThreadPool = ThreadPoolFactory.getTaskRunThreadPool();
        TestSubTask subTask = testSubTaskDao.selectOneByTaskIdAndStatus(request.getTestTaskId(), TestSubTaskStatusEnum.INIT.getCode());

        taskRunThreadPool.submit(() -> {
            taskExecuteService.runSubTask(subTask);
        });
        Map<String, String> subTaskParamMap = JSON.parseObject(subTask.getRunParam(), new TypeReference<Map<String, String>>() {
        });
        String link = reqPostType == ReqPostTypeEnum.XT ? subTaskParamMap.get(TestToolConstants.REQ_POST_LINK) : "";

        return Pair.of(true, link);
    }

    @Override
    public void prepareEnv(Long taskId) {
        TestTask task = testTaskDao.selectById(taskId);
        if (task == null) {
            throw new RuntimeException("任务不存在");
        }
        if (!TestTaskStatusEnum.isCanBePrepareEnv(task.getStatus())) {
            return;
        }

        TestTaskPo testTaskPo = getTestTaskDetailByTaskId(taskId);

        ThreadPoolFactory.getTaskEnvThreadPool().submit(() -> {
            ResourceGroupPo resourceGroup = new ResourceGroupPo();
            if (Objects.nonNull(testTaskPo.getResourceGroup())) {
                resourceGroup = testTaskPo.getResourceGroup();
            }
            envPrepareService.prepareResourceGroup(resourceGroup.trans(), task);
        });

    }

    public TestTaskPo getTestTaskPoByTask(TestTask testTask) {
        MatchStrategy matchStrategy = matchStrategyDao.selectById(testTask.getMatchStrategyId());
        //查询测试场景
        TestScene testScene = testSceneDao.selectById(matchStrategy.getTestSceneId());

        //查询算法包
        List<Pair<BizStrategy, List<AlgoStrategyPackagePo>>> bizStrategies = new ArrayList<>();
        if (StringUtils.isNotEmpty(matchStrategy.getAlgoBizCodes())) {
            List<AlgoCodeInfo> algoCodeInfos = JSONObject.parseObject(matchStrategy.getAlgoBizCodes(), new TypeReference<List<AlgoCodeInfo>>() {
            });
            if (CollectionUtils.isNotEmpty(algoCodeInfos)) {
                List<String> algoBizCodes = algoCodeInfos.stream().map(AlgoCodeInfo::getAlgoBizCode).collect(Collectors.toList());
                bizStrategies = algoPackageService.getAlgoCodeAllInfo(algoBizCodes);
            }
        }


        //查询llm服务
        Triple<List<LlmBizStrategy>, List<BizLlmpredictConfig>, List<BizLlmpredictPromptTemplate>> llmAllInfo = Triple.of(null, null, null);
        if (StringUtils.isNotEmpty(matchStrategy.getLlmBizCodes())) {
            List<LlmInfo> llmCodeInfos = JSONObject.parseObject(matchStrategy.getLlmBizCodes(), new TypeReference<List<LlmInfo>>() {
            });
            if (CollectionUtils.isNotEmpty(llmCodeInfos)) {
                List<String> llmBizCodes = llmCodeInfos.stream().map(LlmInfo::getLlmBizCode).collect(Collectors.toList());
                llmAllInfo = promptStrategyService.getLlmAllInfo(llmBizCodes);
            }
        }


        //查询资源组
        ResourceGroup resourceGroup = null;
        if (testTask.getResourceGroupId() != null && testTask.getResourceGroupId() > 0) {
            resourceGroup = resourceGroupDao.selectById(testTask.getResourceGroupId());
        }

        //查询报告模板
        List<ReporterTemplate> reporterTemplates = new ArrayList<>();
        if (StringUtils.isNotEmpty(testTask.getReporterTemplateId())) {
            List<Long> templateIds = JSON.parseObject(testTask.getReporterTemplateId(), new TypeReference<List<Long>>() {
            });
            if (CollectionUtils.isNotEmpty(templateIds)) {
                reporterTemplates = reporterTemplateDao.selectByIds(templateIds);
            }
        }
        return new TestTaskPo(testTask, matchStrategy, testScene, bizStrategies, llmAllInfo.getLeft(), llmAllInfo.getMiddle(), llmAllInfo.getRight(), resourceGroup, reporterTemplates);
    }

    private List<TestSubTask> buildTestSubTasks(TestTask testTask) {
        //解析入参，分为多个子任务
        String runParam = testTask.getRunParam();
        Map<String, String> paramMap = JSON.parseObject(runParam, new TypeReference<Map<String, String>>() {
        });

        TestSubTask testSubTask = new TestSubTask();
        TestTaskPo testTaskPo = getTestTaskPoByTask(testTask);

        testSubTask.setTaskId(testTaskPo.getId());
        if (CollectionUtils.isNotEmpty(testTaskPo.getMatchStrategy().getAlgoBizCodes())) {
            testSubTask.setAlgoCodeInfo(JSON.toJSONString(testTaskPo.getMatchStrategy().getAlgoBizCodes()));
        }
        if (testTaskPo.getMatchStrategy().getPredictorCache() != null && StringUtils.isNotEmpty(testTaskPo.getMatchStrategy().getPredictorCache().getSetName())) {
            testSubTask.setPredictorCacheInfo(JSON.toJSONString(testTaskPo.getMatchStrategy().getPredictorCache()));
        }
        if (CollectionUtils.isNotEmpty(testTaskPo.getMatchStrategy().getLlmBizCodes())) {
            testSubTask.setLlmInfo(JSON.toJSONString(testTaskPo.getMatchStrategy().getLlmBizCodes()));
        }
        if (CollectionUtils.isNotEmpty(testTaskPo.getMatchStrategy().getOctoServiceConfig())) {
            testSubTask.setOctoInfo(JSON.toJSONString(testTaskPo.getMatchStrategy().getOctoServiceConfig()));
        }
        if (testTaskPo.getResourceGroup() != null) {
            if (CollectionUtils.isNotEmpty(testTaskPo.getResourceGroup().getOctoResources())) {
                testSubTask.setOctoResources(JSON.toJSONString(testTaskPo.getResourceGroup().getOctoResources()));
            }
            if (CollectionUtils.isNotEmpty(testTaskPo.getResourceGroup().getMlpResources())) {
                testSubTask.setMlpModelResources(JSON.toJSONString(testTaskPo.getResourceGroup().getMlpResources()));
            }
            if (CollectionUtils.isNotEmpty(testTaskPo.getResourceGroup().getFridayResources())) {
                testSubTask.setFridayModelResources(JSON.toJSONString(testTaskPo.getResourceGroup().getFridayResources()));
            }
        }
        if (CollectionUtils.isNotEmpty(testTaskPo.getReporterTemplate())) {
            testSubTask.setReporterInfo(JSON.toJSONString(testTaskPo.getReporterTemplate()));
        }

        Map<String, String> extraInfo = new HashMap<>();
        extraInfo.put(TestToolConstants.MATCH_STRATEGY_NAME, testTaskPo.getMatchStrategy().getName());
        TestScenePo testScene = testTaskPo.getMatchStrategy().getTestScene();
        extraInfo.put(TestToolConstants.SCENE_NAME, testScene.getTestSceneName());
        String groupName = testTaskPo.getResourceGroup() != null ? testTaskPo.getResourceGroup().getName() : "";
        extraInfo.put(TestToolConstants.RESOURCE_GROUP_NAME, groupName);
        extraInfo.put(TestToolConstants.TASK_CAL_TIME_INFO, testScene.getExtra());
        extraInfo.put(MISSION_ID, paramMap.get(MISSION_ID));
        testSubTask.setExtraInfo(JSON.toJSONString(extraInfo));

        testSubTask.setCreator(ContextUtil.getLoginUserMis());
        testSubTask.setStatus(TestSubTaskStatusEnum.INIT.getCode());//todo 变更日志

        List<TestSubTask> result;
        switch (testScene.getReqPostType()) {
            case XT:
                result = buildTestSubTaskByXT(testSubTask, paramMap, testScene);
                break;
            case HTTP:
            case FUNCTION:
                result = buildTestSubTaskByHttp(testSubTask, paramMap, testScene);
                break;
            default:
                throw new RuntimeException("未知跑数类型");
        }
        return result;
    }

    /**
     * runParam : -version 17098883 -cd 2024-10-01,2024-10-02 -hour -dt 20241001,20241002
     */
    private List<TestSubTask> buildTestSubTaskByXT(TestSubTask testSubTask, Map<String, String> paramMap, TestScenePo testScene) {
        List<TestSubTask> result = new ArrayList<>();

        String runParam = paramMap.get(TestToolConstants.PARAM);
        Map<String, String> param = ParamHelper.parseXTParam(runParam);

        //构造XT链接。 https://data.sankuai.com/wanxiang2/al-catering/job/dml/1803779?version=19342797
        String reqPostLink = testScene.getReqPostLink();
        param.put(TestToolConstants.REQ_POST_LINK, buildXtLink(reqPostLink, param.get(TestToolConstants.VERSION)));
        //-v --cd 2025-06-20
        param.put(TestToolConstants.XT_PARAM, "-v --cd " + param.get(TestToolConstants.CD));

        if (StringUtils.isNotEmpty(testScene.getExtra())) {
            String parseLink = JSON.parseObject(testScene.getExtra()).getString(TestToolConstants.PARSE_RESP_LINK);
            param.put(TestToolConstants.PARSE_RESP_LINK, buildXtLink(parseLink, param.get(TestToolConstants.PARSE_VERSION)));
            param.put(TestToolConstants.PARSE_XT_PARAM, "-v --cd " + param.get(TestToolConstants.CD));
        }

        param.put(TestToolConstants.POST_TYPE, String.valueOf(ReqPostTypeEnum.XT.getCode()));

        String[] dates = StringUtils.split(param.get(TestToolConstants.CD), ",");

        List<String> calParams = RegexUtil.extractDollar(testScene.getExtra());

        Map<String, String[]> splitMap = new HashMap<>();
        for (Map.Entry<String, String> entry : param.entrySet()) {
            String value = entry.getValue();
            if (StringUtils.startsWith(value, "{") && StringUtils.endsWith(value, "}")) {
                continue;
            }
            if (StringUtils.equals(entry.getKey(), TestToolConstants.CD)) {
                continue;
            }
            String[] split = StringUtils.split(value, ",");
            if (split.length > 1) {
                splitMap.put(entry.getKey(), split);
            }
        }


        for (int i = 0; i < dates.length; i++) {
            Map<String, String> newParam = JSONObject.parseObject(JSON.toJSONString(param), new TypeReference<Map<String, String>>() {
            });
            TestSubTask subTask = new TestSubTask();
            BeanUtils.copyProperties(testSubTask, subTask);
            newParam.put(TestToolConstants.CD, dates[i]);

            for (Map.Entry<String, String[]> entry : splitMap.entrySet()) {
                newParam.put(entry.getKey(), entry.getValue()[i]);
            }

            Map<String, String> replaceMap = new HashMap<>();
            for (String calParam : calParams) {
                String replace = StringUtils.substring(calParam, 2, calParam.length() - 1);
                String[] split = StringUtils.split(replace, "#");
                if (!StringUtils.equals(split[0], TestToolConstants.PARAM)) {
                    continue;
                }
                replaceMap.put(calParam, newParam.get(split[1]));
            }
            replaceMap.put("${now}", DateUtil.toDateTimeString(new Date()));
            String replaceExtraInfo = StringReplacerUtil.replace(subTask.getExtraInfo(), replaceMap);
            subTask.setExtraInfo(replaceExtraInfo);

            newParam.put(TestToolConstants.ORIGIN_RUN_PARAM, runParam);
            subTask.setRunParam(JSON.toJSONString(newParam));

            result.add(subTask);
        }
        return result;
    }

    private String buildXtLink(String link, String version) {
        if (StringUtils.contains(link, "version=")) {
            link = link.replaceAll("version=\\d+", "version=" + version);
        } else {
            link += "?version=" + version;
        }
        return link;
    }

    /**
     * runParam : -ip 127.0.0.1 -cookie xxxxxx -requestBody {"bizCode":"abc", "req":"xxxx"} -sqlParam 'select a,b from abc' -repeat
     */
    private List<TestSubTask> buildTestSubTaskByHttp(TestSubTask testSubTask, Map<String, String> paramMap, TestScenePo testScene) {
        List<TestSubTask> result = new ArrayList<>();
        String runParam = paramMap.get(TestToolConstants.PARAM);
        Map<String, String> runParamMap = ParamHelper.parseHttpParam(runParam);

        runParamMap.put(TestToolConstants.FILE, paramMap.get(TestToolConstants.FILE));
        runParamMap.put(TestToolConstants.POST_TYPE, String.valueOf(testScene.getReqPostType().getCode()));
        runParamMap.put(TestToolConstants.ORIGIN_RUN_PARAM, runParam);

        String addr = testScene.getReqPostLink();
        if (runParamMap.containsKey(TestToolConstants.IP)) {
            addr = StringUtils.replace(addr, TestToolConstants.IP, runParamMap.get(TestToolConstants.IP));
        }
        runParamMap.put(TestToolConstants.REQ_POST_LINK, addr);


        //处理成单条或者多条记录
        String sqlParam = runParamMap.get(TestToolConstants.SQL_PARAM);
        List<String> needReplacedList = RegexUtil.extractMatches(sqlParam, "#(.*?)#");

        if (StringUtils.isEmpty(sqlParam) || CollectionUtils.isEmpty(needReplacedList)) {
            TestSubTask updateSubTask = new TestSubTask();
            BeanUtils.copyProperties(testSubTask, updateSubTask);
            updateSubTask.setRunParam(JSON.toJSONString(runParamMap));
            result.add(updateSubTask);
            return result;
        }


        Map<String, String> finalParamMap = new HashMap<>(runParamMap);
        Map<String, String[]> tmp = new HashMap<>();
        int length = 100;
        for (String key : needReplacedList) {
            key = StringUtils.replace(key, "#", "");
            String[] split = StringUtils.split(runParamMap.get(key), ",");
            length = Math.min(length, split.length);
            tmp.put(key, split);
        }

        for (int i = 0; i < length; i++) {
            String finalSql = sqlParam;
            for (Map.Entry<String, String[]> entry : tmp.entrySet()) {
                finalSql = StringUtils.replace(finalSql, "#" + entry.getKey() + "#", entry.getValue()[i]);
                finalParamMap.put(entry.getKey(), entry.getValue()[i]);
            }
            finalParamMap.put(TestToolConstants.SQL_PARAM, finalSql);
            TestSubTask updateSubTask = new TestSubTask();
            BeanUtils.copyProperties(testSubTask, updateSubTask);
            updateSubTask.setRunParam(JSON.toJSONString(finalParamMap));
            result.add(updateSubTask);
        }

        return result;
    }

    private void clearPredictorCache1(String clearCahce, MatchStrategy matchStrategy) {
        if (!StringUtils.equals(clearCahce, "1") && !StringUtils.equalsIgnoreCase(clearCahce, "true")) {
            return;
        }

        if (StringUtils.isEmpty(matchStrategy.getAlgoBizCodes())) {
            return;
        }

        PredictorCache cache = new PredictorCache(matchStrategy.getPredictorCache());
        if (StringUtils.isEmpty(cache.getSetName()) || !cache.isCache1()) {
            return;
        }

        List<PredictorCache1Config> predictorCache1Config = LionHelper.getPredictorCache1Config(LionHelper.getLionConfigEnvName());

        List<AlgoCodeInfo> algoCodeInfos = JSONObject.parseObject(matchStrategy.getAlgoBizCodes(), new TypeReference<List<AlgoCodeInfo>>() {
        });
        List<String> bizCodes = algoCodeInfos.stream().map(AlgoCodeInfo::getAlgoBizCode).collect(Collectors.toList());
        boolean isChange = false;
        for (PredictorCache1Config cache1Config : predictorCache1Config) {
            if (bizCodes.contains(cache1Config.getBizCode())) {
                isChange = true;
                cache1Config.setVersion(String.valueOf(Integer.parseInt(cache1Config.getVersion()) + 1));
            }
        }
        if (isChange) {
            LionHelper.setPredictorCache1Config(LionHelper.getLionConfigEnvName(), JSON.toJSONString(predictorCache1Config));
        }
    }
}
