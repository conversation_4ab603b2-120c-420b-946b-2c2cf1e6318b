package com.sankuai.algoplatform.matchops.application.model.testtool.matchstrategy;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.algoplatform.matchops.api.response.resourceGroup.TTestTaskDetailResponse;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class PredictorCache {

    private String setName;

    private boolean cache1;

    private boolean cache2;

    private boolean cache3;

    private boolean cache4;

    public PredictorCache() {}

    public PredictorCache(String predictorCache) {
        if (StringUtils.isEmpty(predictorCache)) {
            return;
        }
        PredictorCache cache = JSONObject.parseObject(predictorCache, PredictorCache.class);
        this.setName = cache.getSetName();
        this.cache1 = cache.isCache1();
        this.cache2 = cache.isCache2();
        this.cache3 = cache.isCache3();
        this.cache4 = cache.isCache4();

    }


    public TTestTaskDetailResponse.Data.PredictorCache trans2TData() {
        TTestTaskDetailResponse.Data.PredictorCache predictorCache = new TTestTaskDetailResponse.Data.PredictorCache();
        if (StringUtils.isNotEmpty(setName)) {
            predictorCache.setSetName(setName);
            predictorCache.setCache1(String.valueOf(cache1));
            predictorCache.setCache2(String.valueOf(cache2));
            predictorCache.setCache3(String.valueOf(cache3));
            predictorCache.setCache4(String.valueOf(cache4));
        }
        return predictorCache;
    }
}
