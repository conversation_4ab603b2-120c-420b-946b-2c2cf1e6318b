package com.sankuai.algoplatform.matchops.application.service.testtool.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.util.StringUtils;
import com.dianping.zebra.tool.util.Env;
import com.google.common.collect.ImmutableList;
import com.sankuai.algoplatform.matchops.application.model.testtool.matchstrategy.LlmStrategyAbtest;
import com.sankuai.algoplatform.matchops.application.model.testtool.matchstrategy.LlmStrategyPo;
import com.sankuai.algoplatform.matchops.application.service.testtool.PromptStrategyService;
import com.sankuai.algoplatform.matchops.application.service.testtool.TestTaskService;
import com.sankuai.algoplatform.matchops.domain.enums.EnvStatus;
import com.sankuai.algoplatform.matchops.domain.enums.MatchStrategyStatusEnum;
import com.sankuai.algoplatform.matchops.domain.enums.TestTaskStatusEnum;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.*;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.*;
import com.sankuai.algoplatform.matchops.infrastructure.util.DateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class PromptStrategyServiceImpl implements PromptStrategyService {

    @Autowired
    private LlmBizStrategyDao llmBizStrategyDao;

    @Autowired
    private BizLlmPredictConfigDao bizLlmPredictConfigDao;

    @Autowired
    private BizLlmPredictPromptTemlateDao bizLlmPredictPromptTemlateDao;

    @Autowired
    private MatchStrategyDao matchStrategyDao;

    @Autowired
    private TestTaskDao testTaskDao;

    @Override
    public Map<String, String> getPromptList(Integer current, Long bizLineId) {
        Map<String, String> result = new HashMap<>();
        List<LlmBizStrategy> llmBizStrategies = llmBizStrategyDao.selectByPageAndBizLineId(current, 20, bizLineId);
        Long total = llmBizStrategyDao.selectCountByBizLineId(bizLineId);
        List<Map<String, String>> list = new ArrayList<>();
        llmBizStrategies.forEach(llmBizStrategy -> {
            Map<String, String> map = new HashMap<>();
            map.put("llmBizCodeId", String.valueOf(llmBizStrategy.getId()));
            map.put("llmBizCode", llmBizStrategy.getBizCode());
            JSONObject jsonObject = JSON.parseObject(llmBizStrategy.getAbtestConfig());
            // 获取strategyName
            String strategyName = jsonObject.getString("strategyName");
            map.put("abTestName", strategyName);
            map.put("owner", llmBizStrategy.getOwner());
            map.put("createTime", DateUtil.toDateTimeString(llmBizStrategy.getAddTime()));
            map.put("updateTime", DateUtil.toDateTimeString(llmBizStrategy.getUpdateTime()));

            // 循环获取quota
            JSONArray distributions = jsonObject.getJSONArray("distributions");
            for (int i = 0; i < distributions.size(); i++) {
                JSONObject distribution = distributions.getJSONObject(i);
                int quota = distribution.getIntValue("quota");
                Map<String, String> temp = new HashMap<>();
                temp.put("quota", String.valueOf(quota));
                String bizCode = distribution.getString("modelBizCode");
                BizLlmpredictConfig bizLlmpredictConfig = bizLlmPredictConfigDao.selectByBizCode(bizCode);
                if (bizLlmpredictConfig != null){
                    temp.put("llmStrategyId", String.valueOf(bizLlmpredictConfig.getId()));
                    temp.put("llmStrategyName", bizLlmpredictConfig.getNote());
                    temp.put("llmStrategyBizCode", bizLlmpredictConfig.getBizCode());
                    Map<String, Object> modelConfig = JSON.parseObject(bizLlmpredictConfig.getModelConfig());
                    temp.put("llmModelName", String.valueOf(modelConfig.get("modelName")));
                    temp.put("llmServiceName", String.valueOf(modelConfig.get("modelServiceName")));
                    temp.put("batchSize", String.valueOf(modelConfig.get("batchSize")));
                    temp.put("isTemplate", String.valueOf(modelConfig.get("useTemplate")));
                    temp.put("appId", String.valueOf(modelConfig.get("appId")));
                    temp.put("debug", Boolean.parseBoolean(String.valueOf(modelConfig.get("debug"))) ? "1" : "0");
                    Map<String, Object> modelParams = (Map<String, Object>) modelConfig.get("modelParms");
                    if (MapUtils.isNotEmpty(modelParams)) {
                        temp.put("maxTokens", String.valueOf(modelParams.getOrDefault("maxTokens", "")));
                        temp.put("top_p", String.valueOf(modelParams.getOrDefault("top_p", "")));
                        temp.put("top_k", String.valueOf(modelParams.getOrDefault("top_k", "")));
                        temp.put("temperature", String.valueOf(modelParams.getOrDefault("temperature", "")));
                    }

                    BizLlmpredictPromptTemplate bizLlmpredictPromptTemplate = bizLlmPredictPromptTemlateDao.selectById(bizLlmpredictConfig.getPromptTemplateId());
                    if (bizLlmpredictPromptTemplate != null){
                        temp.put("prompt", bizLlmpredictPromptTemplate.getPromptTemplate());
                        temp.put("note", bizLlmpredictPromptTemplate.getNote());
                    }
                }
                temp.put("llmNote", llmBizStrategy.getNote());
                temp.putAll(map);
                list.add(temp);
            }
        });
        result.put("list", JSON.toJSONString(list));
        result.put("total", String.valueOf(total));
        result.put("current", String.valueOf(current));
        return result;
    }

    @Override
    public List<Map<String, String>> getDropdown(String type, Long bizLineId, String env) {
        List<Map<String, String>> result = new ArrayList<>();
        EnvStatus envStatus = EnvStatus.fromName(env);
        List<BizLlmpredictConfig> bizLlmpredictConfigs = bizLlmPredictConfigDao.selectAllByBizLineIdAndEnv(bizLineId,
                envStatus.getCode());
        bizLlmpredictConfigs.forEach(bizLlmpredictConfig -> {
            Map<String, String> map = new HashMap<>();
            map.put("id", String.valueOf(bizLlmpredictConfig.getId()));
            map.put("name", bizLlmpredictConfig.getNote() + "（bizCode：" + bizLlmpredictConfig.getBizCode() + "）");
            result.add(map);
        });
        return result;
    }

    @Override
    public Long addPromptStrategy(BizLlmpredictConfig bizLlmpredictConfig,
                                  BizLlmpredictPromptTemplate bizLlmpredictPromptTemplate) {
        BizLlmpredictConfig dbLlmBizStrategy = bizLlmPredictConfigDao.selectByBizCode(bizLlmpredictConfig.getBizCode());
        if (dbLlmBizStrategy != null) {
            throw new RuntimeException("bizCode不能重复");
        }
        bizLlmPredictPromptTemlateDao.insert(bizLlmpredictPromptTemplate);
        bizLlmpredictConfig.setPromptTemplateId(bizLlmpredictPromptTemplate.getId());
        return bizLlmPredictConfigDao.insert(bizLlmpredictConfig);
    }

    @Override
    public void editPromptStrategy(BizLlmpredictConfig bizLlmpredictConfig,
                                   BizLlmpredictPromptTemplate bizLlmpredictPromptTemplate) {

        BizLlmpredictConfig dbBizLlmpredictConfig = bizLlmPredictConfigDao.selectById(bizLlmpredictConfig.getId());
//        if (EnvStatus.getByCode(dbBizLlmpredictConfig.getEnv()) == EnvStatus.PROD) {
//            throw new RuntimeException("当前策略不允许修改");
//        }
        List<LlmBizStrategy> llmBizStrategies = llmBizStrategyDao.selectByStrategyId(dbBizLlmpredictConfig.getBizCode());
        if (filterCurrentMatchStrategy(llmBizStrategies)) {
            throw new RuntimeException("当前不允许修改策略");
        }

        bizLlmPredictConfigDao.update(bizLlmpredictConfig);
        BizLlmpredictConfig updateBizLlmpredictConfig = bizLlmPredictConfigDao.selectById(bizLlmpredictConfig.getId());
        bizLlmpredictPromptTemplate.setId(updateBizLlmpredictConfig.getPromptTemplateId());
        bizLlmPredictPromptTemlateDao.update(bizLlmpredictPromptTemplate);
    }

    @Override
    public BizLlmpredictConfig selectBizLlmpredictConfig(String llmBizCode) {
        if (StringUtils.isBlank(llmBizCode)) {
            return null;
        }
        BizLlmpredictConfig bizLlmpredictConfig = bizLlmPredictConfigDao.selectByBizCode(llmBizCode);
        return bizLlmpredictConfig;
    }

    @Override
    public BizLlmpredictPromptTemplate selectBizLlmpredictPromptTemplate(Long promptTemplateId) {
        if (promptTemplateId == null) {
            return null;
        }
        return bizLlmPredictPromptTemlateDao.selectById(promptTemplateId);
    }

    @Override
    public Long addPromptBizCode(LlmBizStrategy llmBizStrategy) {
        LlmBizStrategy dbLlmBizStrategy = llmBizStrategyDao.selectByBizCode(llmBizStrategy.getBizCode());
        if (dbLlmBizStrategy != null) {
            throw new RuntimeException("bizCode不能重复");
        }
        return llmBizStrategyDao.insert(llmBizStrategy);
    }

    @Override
    public void editPromptBizCode(LlmBizStrategy llmBizStrategy) {
        LlmBizStrategy dbLlmBizStrategy = llmBizStrategyDao.selectById(llmBizStrategy.getId());
//        if (EnvStatus.getByCode(dbLlmBizStrategy.getEnv()) == EnvStatus.PROD) {
//            throw new RuntimeException("当前bizCode不允许修改");
//        }
        if (filterCurrentMatchStrategy(ImmutableList.of(dbLlmBizStrategy))) {
            throw new RuntimeException("当前不允许修改策略");
        }

        llmBizStrategyDao.update(llmBizStrategy);
    }

    @Override
    public Integer deletePromptBizCode(Long llmbizStrategyId, String owner) {
        LlmBizStrategy llmBizStrategy = llmBizStrategyDao.selectById(llmbizStrategyId);
        JSONObject jsonObject = JSON.parseObject(llmBizStrategy.getAbtestConfig());

        // 循环获取quota
        JSONArray distributions = jsonObject.getJSONArray("distributions");
        for (int i = 0; i < distributions.size(); i++) {
            JSONObject distribution = distributions.getJSONObject(i);
            Long strategyId = distribution.getLong("strategyId");
            BizLlmpredictConfig bizLlmpredictConfig = bizLlmPredictConfigDao.selectById(strategyId);
            Long promptTemplateId = bizLlmpredictConfig.getPromptTemplateId();
            bizLlmPredictPromptTemlateDao.softDelete(promptTemplateId, owner);
            bizLlmPredictConfigDao.softDelete(strategyId, owner);
        }
        return llmBizStrategyDao.softDelete(llmbizStrategyId, owner);
    }

    @Override
    public Triple<List<LlmBizStrategy>, List<BizLlmpredictConfig>, List<BizLlmpredictPromptTemplate>> getLlmAllInfo(List<String> llmBizCodes) {
        List<LlmBizStrategy> llmBizStrategies = llmBizStrategyDao.selectByBizCodes(llmBizCodes);

        List<String> llmStrategyBizCodes = new ArrayList<>();
        for (LlmBizStrategy bizStrategy : llmBizStrategies) {
            LlmStrategyAbtest abtest = JSONObject.parseObject(bizStrategy.getAbtestConfig(), new TypeReference<LlmStrategyAbtest>() {
            });
            List<String> llmStrategyIdList = abtest.getDistributions().stream().map(LlmStrategyPo::getBizCode).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(llmStrategyIdList)) {
                llmStrategyBizCodes.addAll(llmStrategyIdList);
            }
        }
        List<BizLlmpredictConfig> bizLlmpredictConfigs = bizLlmPredictConfigDao.selectByBizCodes(llmStrategyBizCodes);
        List<Long> promptTemplateIds = bizLlmpredictConfigs.stream().map(BizLlmpredictConfig::getPromptTemplateId).collect(Collectors.toList());
        List<BizLlmpredictPromptTemplate> bizLlmpredictPromptTemplates = bizLlmPredictPromptTemlateDao.selectByIds(promptTemplateIds);
        return Triple.of(llmBizStrategies, bizLlmpredictConfigs, bizLlmpredictPromptTemplates);
    }


    private boolean filterCurrentMatchStrategy(List<LlmBizStrategy> llmBizStrategies) {
        //1. 判断当前策略是否可修改
        List<MatchStrategy> matchStrategies = new ArrayList<>();
        for (LlmBizStrategy bizStrategy : llmBizStrategies) {
            List<MatchStrategy> dbs = matchStrategyDao.findByLlmBizCode(bizStrategy.getBizCode());
            if (CollectionUtils.isNotEmpty(dbs)) {
                matchStrategies.addAll(dbs);
            }
        }
        if (CollectionUtils.isEmpty(matchStrategies)) {
            return false;
        }
        for (MatchStrategy matchStrategy : matchStrategies) {
            if (!MatchStrategyStatusEnum.matchStrategyCanUpdate(matchStrategy.getStatus())) {
                return true;
            }
        }
        //2. 判断当前策略是否存在正在测试中任务，存在则不准修改
        List<TestTask> testTasks = testTaskDao.selectByMatchStrategyIds(matchStrategies.stream().map(MatchStrategy::getId).collect(Collectors.toList()));
        for (TestTask testTask : testTasks) {
            TestTaskStatusEnum status = TestTaskStatusEnum.getByCode(testTask.getStatus());
            if (status == TestTaskStatusEnum.TESTING) {
                return true;
            }
        }
        return false;
    }

}
