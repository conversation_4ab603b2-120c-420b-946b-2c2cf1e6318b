package com.sankuai.algoplatform.matchops.application.service.testtool;

import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.AlgoPackage;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.AlgoStrategy;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.BizStrategy;
import com.sankuai.algoplatform.matchops.infrastructure.model.AlgoStrategyPackagePo;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;

import java.util.List;
import java.util.Map;

public interface AlgoPackageService {

    Map<String, String> getAlgoCodePackageList(Integer current, Long bizLineId);

    List<Map<String, String>> getDropdown(String type, Long bizLineId, String env);

    Long addAlgoStrategy(List<AlgoStrategy> algoStrategy, List<AlgoPackage> algoPackage);

    void editAlgoStrategy(List<AlgoStrategy> algoStrategy, List<AlgoPackage> algoPackage);

    Long addAlgoBizCode(BizStrategy bizStrategy);

    void editAlgoBizCode(BizStrategy bizStrategy);

    Integer deleteAlgoBizCode(Long startegyId, String mis);

    List<Pair<BizStrategy, List<AlgoStrategyPackagePo>>> getAlgoCodeAllInfo(List<String> algoBizCodes);
}
