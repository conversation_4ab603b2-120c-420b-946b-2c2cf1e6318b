package com.sankuai.algoplatform.matchops.application.model.testtool.resourcegroup;

import com.sankuai.algoplatform.matchops.api.response.resourceGroup.TTestTaskDetailResponse;
import com.sankuai.algoplatform.matchops.domain.model.testtool.DOctoResource;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@Data
public class OctoResource {

    private String appkey;

    private String set;

    private String instanceNum;

    private String retainTime; //只有当实例不够时，才生效


    public TTestTaskDetailResponse.Data.ResourceInfo trans2TData() {
        TTestTaskDetailResponse.Data.ResourceInfo resourceInfo = new TTestTaskDetailResponse.Data.ResourceInfo();
        resourceInfo.setAppkey(this.appkey);
        resourceInfo.setSet(this.set);
        resourceInfo.setInstanceNum(this.instanceNum);
        return resourceInfo;
    }

    public DOctoResource trans() {
        DOctoResource resource = new DOctoResource();
        resource.setAppkey(appkey);
        resource.setSet(set);
        resource.setInstanceNum(Integer.parseInt(instanceNum));
        if (StringUtils.isEmpty(retainTime)) {
            retainTime = "1";
        }
        resource.setRetainTime(Integer.parseInt(retainTime));
        return resource;
    }
}
