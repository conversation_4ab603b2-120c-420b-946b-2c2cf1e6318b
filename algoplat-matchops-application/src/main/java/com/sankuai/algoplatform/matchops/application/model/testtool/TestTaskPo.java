package com.sankuai.algoplatform.matchops.application.model.testtool;

import com.alibaba.fastjson.JSON;
import com.sankuai.algoplatform.matchops.api.request.testTask.TAddOrEditTestTaskRequest;
import com.sankuai.algoplatform.matchops.api.response.resourceGroup.TTestTaskDetailResponse;
import com.sankuai.algoplatform.matchops.application.model.testtool.matchstrategy.*;
import com.sankuai.algoplatform.matchops.application.model.testtool.resourcegroup.FridayResource;
import com.sankuai.algoplatform.matchops.application.model.testtool.resourcegroup.MlpResource;
import com.sankuai.algoplatform.matchops.application.model.testtool.resourcegroup.OctoResource;
import com.sankuai.algoplatform.matchops.application.model.testtool.resourcegroup.ResourceGroupPo;
import com.sankuai.algoplatform.matchops.domain.enums.TestTaskStatusEnum;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.*;
import com.sankuai.algoplatform.matchops.infrastructure.model.AlgoStrategyPackagePo;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Data
public class TestTaskPo {

    private Long id;

    private String name;

    private Long bizLineId;

    private MatchStrategyPo matchStrategy;

    private ResourceGroupPo resourceGroup;

    private List<ReporterTemplatePo> reporterTemplate;

    private String runParam;

    private TestTaskStatusEnum status;

    private String owner;

    private Date createTime;

    private Date updateTime;

    public TestTaskPo(TestTask testTask,
                      MatchStrategy matchStrategy,
                      ResourceGroup resourceGroup,
                      List<ReporterTemplate> reporterTemplate) {
        this.id = testTask.getId();
        this.name = testTask.getName();
        this.bizLineId = testTask.getBizLineId();
        this.matchStrategy = new MatchStrategyPo(matchStrategy);
        if (resourceGroup != null) {
            this.resourceGroup = new ResourceGroupPo(resourceGroup);
        }
        if (CollectionUtils.isNotEmpty(reporterTemplate)) {
            this.reporterTemplate = reporterTemplate.stream().map(ReporterTemplatePo::new).collect(Collectors.toList());
        }
        this.runParam = testTask.getRunParam();
        this.status = TestTaskStatusEnum.getByCode(testTask.getStatus());
        this.owner = testTask.getOwner();
        this.createTime = testTask.getCreateTime();
        this.updateTime = testTask.getUpdateTime();
    }


    public TestTaskPo(TestTask testTask,
                      MatchStrategy matchStrategy,
                      TestScene testScene,
                      List<Pair<BizStrategy, List<AlgoStrategyPackagePo>>> bizStrategies,
                      List<LlmBizStrategy> llmBizStrategies,
                      List<BizLlmpredictConfig> bizLlmpredictConfigs,
                      List<BizLlmpredictPromptTemplate> bizLlmpredictPromptTemplates,
                      ResourceGroup resourceGroup,
                      List<ReporterTemplate> reporterTemplate) {
        this.id = testTask.getId();
        this.name = testTask.getName();
        this.bizLineId = testTask.getBizLineId();
        this.matchStrategy = new MatchStrategyPo(matchStrategy, testScene, bizStrategies,
                llmBizStrategies, bizLlmpredictConfigs, bizLlmpredictPromptTemplates);
        if (resourceGroup != null) {
            this.resourceGroup = new ResourceGroupPo(resourceGroup);
        }
        if (CollectionUtils.isNotEmpty(reporterTemplate)) {
            this.reporterTemplate = reporterTemplate.stream().map(ReporterTemplatePo::new).collect(Collectors.toList());
        }
        this.runParam = testTask.getRunParam();
        this.status = TestTaskStatusEnum.getByCode(testTask.getStatus());
        this.owner = testTask.getOwner();
        this.createTime = testTask.getCreateTime();
        this.updateTime = testTask.getUpdateTime();
    }

    public static TestTask trans(TAddOrEditTestTaskRequest request) {
        TestTask testTask = new TestTask();
        testTask.setId(request.getTestTaskId());
        testTask.setName(request.getTestTaskName());
        testTask.setBizLineId(request.getBizLineId());
        testTask.setMatchStrategyId(request.getMatchStrategyId());
        if (request.getResourceGroupId() == null) {
            testTask.setResourceGroupId(0L);
        } else {
            testTask.setResourceGroupId(request.getResourceGroupId());
        }
        if (Objects.nonNull(request.getTestTemplates())) {
            testTask.setReporterTemplateId(JSON.toJSONString(request.getTestTemplates()));
        }
//        if(Objects.nonNull(request.getExtra())){
//            testTask.setExtraInfo(JSON.toJSONString(request.getExtra()));
//        }
        return testTask;
    }

    public TTestTaskDetailResponse.Data trans2TData() {
        TTestTaskDetailResponse.Data data = new TTestTaskDetailResponse.Data();
        data.setTaskName(name);
        data.setMatchStrategyName(matchStrategy.getName());
        data.setSceneName(matchStrategy.getTestScene().getTestSceneName());

        List<AlgoCodeInfo> algoBizCodes = matchStrategy.getAlgoBizCodes();
        if (CollectionUtils.isNotEmpty(algoBizCodes)) {
            List<TTestTaskDetailResponse.Data.AlgoCodeInfo> algoCodeInfos = algoBizCodes.stream().map(AlgoCodeInfo::trans2TData)
                    .collect(Collectors.toList());
            data.setAlgoCodeInfo(algoCodeInfos);
        }

        PredictorCache predictorCache = matchStrategy.getPredictorCache();
        if (Objects.nonNull(predictorCache)) {
            data.setPredictorCache(predictorCache.trans2TData());
        }

        List<LlmInfo> llmBizCodes = matchStrategy.getLlmBizCodes();
        if (CollectionUtils.isNotEmpty(llmBizCodes)) {
            List<TTestTaskDetailResponse.Data.LlmInfo> llmCodeInfos = llmBizCodes.stream().map(LlmInfo::trans2TData)
                    .collect(Collectors.toList());
            data.setLlmInfo(llmCodeInfos);
        }


        List<OctoServiceConfigInfo> octoServiceConfig = matchStrategy.getOctoServiceConfig();
        if (CollectionUtils.isNotEmpty(octoServiceConfig)) {
            List<TTestTaskDetailResponse.Data.OctoInfo> octoInfos = octoServiceConfig.stream().map(OctoServiceConfigInfo::trans2TData).collect(Collectors.toList());
            data.setOctoInfo(octoInfos);
        }

        if (resourceGroup != null) {
            data.setResourceGroupName(resourceGroup.getName());
            if (CollectionUtils.isNotEmpty(resourceGroup.getOctoResources())) {
                List<TTestTaskDetailResponse.Data.ResourceInfo> resources = resourceGroup.getOctoResources().stream().map(OctoResource::trans2TData).collect(Collectors.toList());
                data.setCpuResourceInfo(resources);
            }
            if (CollectionUtils.isNotEmpty(resourceGroup.getMlpResources())) {
                List<TTestTaskDetailResponse.Data.MlpResourceInfo> resources = resourceGroup.getMlpResources().stream().map(MlpResource::trans2TData).collect(Collectors.toList());
                data.setMlpResourceInfo(resources);
            }
            if (CollectionUtils.isNotEmpty(resourceGroup.getFridayResources())) {
                List<TTestTaskDetailResponse.Data.LlmResourceInfo> resources = resourceGroup.getFridayResources().stream().map(FridayResource::trans2TData).collect(Collectors.toList());
                data.setLlmResourceInfo(resources);
            }
        }
        return data;
    }

}
