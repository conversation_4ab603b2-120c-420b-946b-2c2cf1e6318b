package com.sankuai.algoplatform.matchops.application.model.testtool.matchstrategy;

import com.sankuai.algoplatform.matchops.api.response.resourceGroup.TTestTaskDetailResponse;
import lombok.Data;

@Data
public class OctoServiceConfigInfo {

    private String appkey;

    private String set;

    private String branch;

    private String lionConfig;


    public TTestTaskDetailResponse.Data.OctoInfo trans2TData() {
        TTestTaskDetailResponse.Data.OctoInfo octoInfo = new TTestTaskDetailResponse.Data.OctoInfo();
        octoInfo.setAppkey(appkey);
        octoInfo.setSetName(set);
        octoInfo.setLionConfig(lionConfig);
        octoInfo.setBranch(branch);
        return octoInfo;
    }
}
