package com.sankuai.algoplatform.matchops.application.service.testtool.impl;

import com.alibaba.fastjson.JSON;
import com.sankuai.algoplatform.matchops.application.service.testtool.TestSceneService;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.TestSceneDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestScene;
import com.sankuai.algoplatform.matchops.infrastructure.util.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class TestSceneServiceImpl implements TestSceneService {
    @Autowired
    TestSceneDao testSceneDao;

    @Override
    public Map<String, String> getTestSceneList(Integer current, Long bizLineId) {
        List<TestScene> testScenes = testSceneDao.selectByPageAndBizLineId(current, 20, bizLineId);
        Long count = testSceneDao.selectCountByBizLineId(bizLineId);
        Map<String, String> result = new HashMap<>();
        List<Map<String, String>> testSceneList = new ArrayList<>();
        testScenes.forEach(testScene -> {
            Map<String, String> temp = new HashMap<>();
            temp.put("sceneId", String.valueOf(testScene.getId()));
            temp.put("sceneName", testScene.getTestSceneName());
            temp.put("runType", String.valueOf(testScene.getReqPostType()));
            temp.put("runAddress", testScene.getReqPostLink());
            temp.put("extra", testScene.getExtra());
            temp.put("owner", testScene.getOwner());
            temp.put("createTime", DateUtil.toDateTimeString(testScene.getCreateTime()));
            temp.put("updateTime", DateUtil.toDateTimeString(testScene.getUpdateTime()));
            testSceneList.add(temp);
        });
        result.put("total", String.valueOf(count));
        result.put("list", JSON.toJSONString(testSceneList));
        result.put("current", String.valueOf(current));
        return result;
    }

    @Override
    public Long addTestScene(TestScene testScene) {
        return testSceneDao.insert(testScene);
    }

    @Override
    public void editTestScene(TestScene testScene) {
        testSceneDao.update(testScene);
    }

    @Override
    public Integer delTestScene(Long sceneId,String mis) {
        return testSceneDao.softDelete(sceneId,mis);
    }

    @Override
    public List<Map<String, String>> getDropdown(String type, Long bizLineId) {
        List<Map<String, String>> dropdownList = new ArrayList<>();
        List<TestScene> testScenes = testSceneDao.selectAllByBizLineId(bizLineId);
        testScenes.forEach(testScene -> {
            Map<String, String> temp = new HashMap<>();
            temp.put("id", String.valueOf(testScene.getId()));
            temp.put("name", testScene.getTestSceneName());
            dropdownList.add(temp);
        });
        return dropdownList;
    }

    @Override
    public TestScene queryTestSceneByIdAndBizLineId(Long sceneId){
        return testSceneDao.selectById(sceneId);
    }
}
