package com.sankuai.algoplatform.matchops.application.service.impl;

import com.sankuai.algoplatform.matchops.application.service.GpuScheduleFlowHandlerService;
import com.sankuai.algoplatform.matchops.domain.ability.gpuschedule.MainFlowHandlerService;
import com.sankuai.algoplatform.matchops.domain.ability.gpuschedule.ResourceMonitorHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class GpuScheduleFlowHandlerServiceImpl implements GpuScheduleFlowHandlerService {
    @Resource
    private MainFlowHandlerService mainFlowHandlerService;
    @Resource
    private ResourceMonitorHandlerService resourceMonitorHandlerService;

    @Override
    public void generateTaskInstance() {
        mainFlowHandlerService.generateTaskInstance();
    }

    @Override
    public void invokeTaskInstance() {
        mainFlowHandlerService.invokeTaskInstance();
    }

    @Override
    public void updateTaskInstance() {
        mainFlowHandlerService.updateTaskInstance();
    }

    @Override
    public void generateResourceMonitorTaskInstance() {
        resourceMonitorHandlerService.generateTaskInstance();
    }

    @Override
    public void resourceMonitor() {
        try {
            resourceMonitorHandlerService.monitorResourcePool();
        } catch (Exception e) {
            log.error("monitorResourcePool error", e);
        }
        try {
            resourceMonitorHandlerService.monitorServiceNode();
        } catch (Exception e) {
            log.error("monitorServiceNode error", e);
        }
    }

}
