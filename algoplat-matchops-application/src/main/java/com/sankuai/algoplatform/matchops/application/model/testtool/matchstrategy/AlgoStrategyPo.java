package com.sankuai.algoplatform.matchops.application.model.testtool.matchstrategy;

import com.alibaba.fastjson.annotation.JSONField;
import com.sankuai.algoplatform.matchops.api.response.resourceGroup.TTestTaskDetailResponse;
import com.sankuai.algoplatform.matchops.domain.enums.EnvStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.checkerframework.checker.units.qual.A;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlgoStrategyPo {

    @JSONField(name = "strategyId")
    private Long id;

    @JSONField(name = "note")
    private String note;

    private Long bizLineId;

    private AlgoPackagePo algoPackage;

    private String entrancePath;

    private String entranceMethod;

    private Integer status;

    private Date addTime;

    private Date updateTime;

    private EnvStatus env;

    private Long convertStrategyId;

    private String owner;

    @JSONField(name = "quota")
    private int quota;

    public TTestTaskDetailResponse.Data.AlgoCodeStrategy trans2TData() {
        TTestTaskDetailResponse.Data.AlgoCodeStrategy algoCodeStrategy = new TTestTaskDetailResponse.Data.AlgoCodeStrategy();
        algoCodeStrategy.setStrategyName(note);
        algoCodeStrategy.setQuota(String.valueOf(quota));
        algoCodeStrategy.setVersion(algoPackage.getVersion());
        return algoCodeStrategy;
    }
}
