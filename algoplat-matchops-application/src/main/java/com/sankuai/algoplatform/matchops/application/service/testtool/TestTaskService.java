package com.sankuai.algoplatform.matchops.application.service.testtool;

import com.sankuai.algoplatform.matchops.api.request.testTask.TRunTaskRequest;
import com.sankuai.algoplatform.matchops.application.model.testtool.TestTaskPo;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestTask;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;
import java.util.Map;

public interface TestTaskService {

    Long getTestTaskTotalCountByBizLineId(Long bizLineId);

    List<Map<String, String>> getTestTaskList(Integer current, Long bizLineId);

    Pair<Boolean, String> addTestTask(TestTask testTask);

    Long editTestTask(TestTask testTask);

    TestTaskPo getTestTaskDetailByTaskId(Long taskId);

    TestTaskPo getTestTaskDetailByTaskNameAndBizId(String taskName, Long bizLineId);

    Pair<Boolean, String> runTask(TRunTaskRequest request);

    void prepareEnv(Long taskId);
}
