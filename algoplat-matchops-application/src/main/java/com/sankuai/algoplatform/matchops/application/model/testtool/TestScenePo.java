package com.sankuai.algoplatform.matchops.application.model.testtool;

import com.sankuai.algoplatform.matchops.domain.enums.ReqPostTypeEnum;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestScene;
import lombok.Data;

import java.util.Date;

@Data
public class TestScenePo {

    private Long id;

    private Long bizLineId;

    private String testSceneName;

    private ReqPostTypeEnum reqPostType;

    private String reqPostLink;

    private String extra;

    private String owner;

    private Boolean isDel;

    private Date createTime;

    private Date updateTime;

    public TestScenePo(TestScene testScene) {
        this.id = testScene.getId();
        this.bizLineId = testScene.getBizLineId();
        this.testSceneName = testScene.getTestSceneName();
        this.reqPostType = ReqPostTypeEnum.getByCode(testScene.getReqPostType());
        this.reqPostLink = testScene.getReqPostLink();
        this.extra = testScene.getExtra();
        this.owner = testScene.getOwner();
        this.isDel = testScene.getIsDel();
        this.createTime = testScene.getCreateTime();
        this.updateTime = testScene.getUpdateTime();
    }
}

