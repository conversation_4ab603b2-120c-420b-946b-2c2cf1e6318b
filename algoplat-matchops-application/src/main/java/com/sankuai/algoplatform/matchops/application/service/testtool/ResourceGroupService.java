package com.sankuai.algoplatform.matchops.application.service.testtool;


import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ResourceGroup;

import java.util.List;
import java.util.Map;

public interface ResourceGroupService {

    Map<String, String> getResourceGroupList(Integer current, Long bizLineId);

    ResourceGroup getResourceGroup(Long bizLineId, Long resourceGroupId);

    Long addResourceGroupList(ResourceGroup resourceGroup);

    void editResourceGroupList(ResourceGroup resourceGroup);

    Integer delResourceGroupList(Long resourceGroupId, String mis);

    List<Map<String, String>> getDropdown(String type, Long bizLineId);

}
