// 基础环境BML平台cookie配置
const BML_ONLINE_URL = "https://bml.sankuai.com"
const BML_ONLINE_COOKIE_KEY = "55be5040bb_ssoid"
const BML_OFFLINE_URL = "https://bml.nibdata.test.sankuai.com"
const BML_OFFLINE_COOKIE_KEY = "37eb0143da_ssoid"
const REFRESH_COOKIE_URL = "/gw/TToolService/saveCache"

// 需要保存cookie的配置（platform不能重复，否则在cookieMap中会被覆盖）
const PLATFORM_URL_COOKIE_KEY_LIST = [{
    platform: "friday",
    urls: ["https://friday.sankuai.com", "https://aigc.sankuai.com"],
    cookieKey: "12d702aa62_ssoid",
    cacheKeyPrefix: "bml_match_agent_friday_cookie",
    env: ["online", "offline"]
},{
    platform: "xuecheng-test",
    urls: ["https://km.it.test.sankuai.com"],
    cookieKey: "com.sankuai.it.ead.citadel_ssoid",
    cacheKeyPrefix: "bml_agent_xuecheng_test_cookie",
    env: ["offline"]
},{
    platform: "xuecheng-prod",
    urls: ["https://km.sankuai.com"],
    cookieKey: "com.sankuai.it.ead.citadel_ssoid",
    cacheKeyPrefix: "bml_agent_xuecheng_test_cookie",
    env: ["online"]
},{
    platform: "xt",
    urls: ["https://data.sankuai.com"],
    cookieKey: "com.sankuai.data.wanxiang.wanxiang_ssoid",
    cacheKeyPrefix: "bml_agent_xt_test_cookie",
    env: ["online", "offline"]
}]

// 这段代码会被注入到符合 matches 模式的页面中
console.log("扩展已注入到页面!");

// 调用函数获取 Cookie
getAllCookies();

function getAllCookies() {
    const urlCookieKeys = [
        BML_ONLINE_URL + '@' + BML_ONLINE_COOKIE_KEY,
        BML_OFFLINE_URL + '@' + BML_OFFLINE_COOKIE_KEY,
    ];

    PLATFORM_URL_COOKIE_KEY_LIST.forEach(pt => {
        pt.urls.forEach(url => {
            urlCookieKeys.push(url + '@' + pt.cookieKey);
        });
    });
    // console.log("urlCookieKeys", urlCookieKeys)

    // 向 background script 发送请求
    chrome.runtime.sendMessage(
        {
            action: "getCookies",
            cookieKeys: urlCookieKeys
        },
        (response) => {
            // console.log('接收到response', response)
            if (response && response.cookies) {
                // console.log("所有 Cookie:", response.cookies);
                const onlineCookie = response.cookies[0]
                const offlineCookie = response.cookies[1]
                if (Object.keys(onlineCookie).length === 0) {
                    console.log("BML平台线上环境cookie不存在")
                }
                if (Object.keys(offlineCookie).length === 0) {
                    console.log("BML平台线下环境cookie不存在")
                }

                const cookieMap = {}
                let idx = 2;

                PLATFORM_URL_COOKIE_KEY_LIST.forEach(pt => {
                    const end = idx + pt.urls.length;
                    for (; idx < end; idx++) {
                        if (Object.keys(response.cookies[idx]).length !== 0) {
                            cookieMap[pt.platform] = response.cookies[idx]
                            break
                        }
                    }
                    idx = end;
                });

                PLATFORM_URL_COOKIE_KEY_LIST.forEach(pt => {
                    let cookie = cookieMap[pt.platform]
                    if (cookie) {
                        const cookieValue = cookie[pt.cookieKey]
                        for (let i = 0; i < pt.env.length; i++) {
                            if (pt.env[i] === 'online') {
                                if (Object.keys(onlineCookie).length !== 0) {
                                    console.log("发起线上环境更新" + pt.platform + " cookie请求")
                                    sendHttpRequest(BML_ONLINE_URL+REFRESH_COOKIE_URL, onlineCookie, cookieValue, pt.cacheKeyPrefix, pt.platform);
                                }
                            }
                            if (pt.env[i] === 'offline') {
                                if (Object.keys(offlineCookie).length !== 0) {
                                    console.log("发起线下环境更新" + pt.platform + " cookie请求")
                                    sendHttpRequest(BML_OFFLINE_URL+REFRESH_COOKIE_URL, offlineCookie, cookieValue, pt.cacheKeyPrefix, pt.platform);
                                }
                            }
                        }
                    } else {
                        console.log(pt.platform + " cookie不存在")
                    }
                })
            }
        }
    );
}

function sendHttpRequest(url, cookieObj, value, cacheKeyPrefix, platform) {
    const init = {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Cookie': objectToKeyValueString(cookieObj)
        },
        body: JSON.stringify({
            cacheKeyPrefix: cacheKeyPrefix,
            value: value
        }),
        credentials: 'include'
    };
    const envPrefix = (url.startsWith(BML_ONLINE_URL) ? '线上' : '线下') + '环境' + platform + ' cookie更新'
    // 发送HTTP请求
    fetch(url, init)
        .then(response => response.json())
        .then(data => {
            const status = data.code === 0 ? '成功' : '失败'
            console.log(envPrefix+status, data)
        })
        .catch(error => console.error(envPrefix + '请求异常', url, init, error));
}

/**
 * 将js对象转换为key=value格式的字符串
 * @param obj - 要转换的js对象
 * @returns {string} - 格式为"key1=value1;key2=value2"的字符串
 */
function objectToKeyValueString(obj) {
    // 获取对象的所有键
    const keys = Object.keys(obj);

    // 将每个键值对转换为 "key=value" 格式
    const pairs = keys.map(key => {
        return `${key}=${obj[key]}`;
    });
    // 用分号连接所有键值对
    return pairs.join('; ');
}
