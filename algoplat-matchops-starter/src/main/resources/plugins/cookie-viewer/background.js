chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === "getCookies") {
        const cookieKeys = request.cookieKeys;
        collectCookies(cookieKeys)
            .then(cookies => sendResponse({cookies: cookies}))
        return true; // 保持消息通道开放，以便异步响应
    }
});

async function collectCookies(cookieKeys) {
    // console.log('接收到getCookies请求', cookieKeys)

    const promises = cookieKeys.map(cookieKey => {
        const [url, name] = cookieKey.split("@")
        return getCookie(url, name)
    })

    // 等待所有请求完成
    const results = await Promise.all(promises);

    // 所有异步操作完成后的处理
    // console.log("所有数据获取完成", results);
    return results;
}

function getCookie(url, name) {
    return new Promise((resolve) => {
        const cookieMap = {};
        chrome.cookies.getAll({ url: url }, (cookies) => {
            cookies.forEach(cookie => {
                if (name === cookie.name) {
                    // console.log('findCookie', url, name, cookie);
                    cookieMap[name] = cookie.value;
                }
            });
            resolve(cookieMap);
        });
    });
}
