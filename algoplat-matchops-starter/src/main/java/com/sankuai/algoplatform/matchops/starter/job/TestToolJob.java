package com.sankuai.algoplatform.matchops.starter.job;

import com.cip.crane.client.spring.annotation.Crane;
import com.sankuai.algoplatform.matchops.application.service.testtool.TaskCompensateService;
import com.sankuai.algoplatform.matchops.infrastructure.monitor.OctoNodeMonitorService;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.TalosSqlQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class TestToolJob {

    @Resource
    private TaskCompensateService taskCompensateService;

    @Resource
    private OctoNodeMonitorService octoNodeMonitorService;

    @Resource
    private TalosSqlQueryService talosSqlQueryService;


    @Crane("TestToolSchedule_inspection")
    public void inspection() {
        //线上服务实例数巡检
        octoNodeMonitorService.serverNodeMonitor();
    }


    @Crane("TestToolSchedule_checkException")
    public void checkException() {
        //检查跑数中任务是否存在报错
        taskCompensateService.checkTestingTaskException();
    }


    @Crane("TestToolSchedule_handleTestingTasks")
    public void handleTestingTasks() {
        taskCompensateService.handleTestingTasks();
    }


    @Crane("TestToolSchedule_refreshTalosToken")
    public void refreshTalosSession() {
        talosSqlQueryService.refreshTalosSession();
    }



}
