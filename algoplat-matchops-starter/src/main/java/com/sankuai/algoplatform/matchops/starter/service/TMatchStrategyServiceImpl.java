package com.sankuai.algoplatform.matchops.starter.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.google.common.collect.ImmutableMap;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServerExtConfig;
import com.sankuai.algoplatform.matchops.api.enums.ResultEnum;
import com.sankuai.algoplatform.matchops.api.request.matchStrategy.*;
import com.sankuai.algoplatform.matchops.api.response.TBaseResponse;
import com.sankuai.algoplatform.matchops.api.response.matchStrategy.*;
import com.sankuai.algoplatform.matchops.api.service.TMatchStrategyService;
import com.sankuai.algoplatform.matchops.domain.enums.MatchStrategyStatusEnum;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.MatchStrategy;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.DxService;
import com.sankuai.algoplatform.matchops.infrastructure.util.ContextUtil;
import com.sankuai.algoplatform.matchops.starter.aop.anno.AddLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.sankuai.algoplatform.matchops.domain.constant.TestToolConstants.SYSTEM_OPS_MIS_ID_LIST;

@MdpThriftServer(port = 9001)
@MdpThriftServerExtConfig(enableAuthHandler = true)
@Slf4j
public class TMatchStrategyServiceImpl implements TMatchStrategyService {
    @Autowired
    private com.sankuai.algoplatform.matchops.application.service.testtool.MatchStrategyService matchStrategyService;
    @Autowired
    private DxService dxService;

    @AddLog
    @Override
    public TMatchStrategyListResponse getMatchStrategyList(TMatchStrategyListRequest request) {
        TMatchStrategyListResponse response = new TMatchStrategyListResponse();
        response.setCode(-1);
        response.setMessage("系统异常");
        try {
            if (request.getCurrent() == null || request.getBizLineId() == null) {
                response.setMessage("参数不完整");
                return response;
            }
            Map<String, String> data = matchStrategyService.getMatchStrategyList(request.getCurrent(),
                    request.getBizLineId());
            response.setCode(0);
            response.setMessage("success");
            response.setData(data);
        } catch (Exception e) {
            log.error("getMatchStrategyList error request is {}", request, e);
        }
        return response;
    }

    @AddLog
    public void updateMatchStrategyStatus(Long matchStrategyId,Integer status){
        Map<String, String> map = matchStrategyService.queryMatchStrategy(matchStrategyId);


    }

    @AddLog
    @Override
    public TAddMatchStrategyResponse addMatchStrategy(TAddEditMatchStrategyRequest request) {
        TAddMatchStrategyResponse response = new TAddMatchStrategyResponse();
        try {

            if (request.getSceneId() == null || request.getSceneId() <= 0) {
                response.setCode(ResultEnum.PARAMS_FAIL.getCode());
                response.setMessage("测试场景不能为空");
                return response;
            }

            if (CollectionUtils.isEmpty(request.getAlgoCodeInfo()) &&
                    CollectionUtils.isEmpty(request.getLlmInfo()) &&
                    CollectionUtils.isEmpty(request.getOctoInfo())) {
                response.setCode(ResultEnum.PARAMS_FAIL.getCode());
                response.setMessage("算法代码包、LLM配置、服务信息不能都为空");
                return response;
            }

            Pair<Boolean, String> pair = paramCheck(request);
            if (!pair.getLeft()) {
                response.setCode(ResultEnum.PARAMS_FAIL.getCode());
                response.setMessage(pair.getRight());
                return response;
            }

            Long id = matchStrategyService.addMatchStrategy(convert(request));
            Map<String, String> data = new HashMap<>();
            data.put("id", id.toString());
            response.setCode(0);
            response.setMessage("success");
            response.setData(data);
        } catch (Exception e) {
            response.setCode(-1);
            response.setMessage(e.getMessage());
            log.error("addMatchStrategy error request is {}", request, e);
        }
        return response;
    }

    @AddLog
    @Override
    public TEditMatchStrategyResponse editMatchStrategy(TAddEditMatchStrategyRequest request) {
        TEditMatchStrategyResponse response = new TEditMatchStrategyResponse();
        response.setCode(-1);
        response.setMessage("系统异常");
        try {
            if (request.getMatchStrategyId() == null || request.getMatchStrategyId() <= 0) {
                response.setMessage("参数不完整");
                return response;
            }

            Pair<Boolean, String> pair = paramCheck(request);
            if (!pair.getLeft()) {
                response.setCode(ResultEnum.PARAMS_FAIL.getCode());
                response.setMessage(pair.getRight());
                return response;
            }

            matchStrategyService.editMatchStrategy(convert(request));
            response.setCode(0);
            response.setMessage("success");
        } catch (Exception e) {
            if (StringUtils.contains(e.getMessage(), "No privilege for user bml_match_test_tool")) {
                response.setMessage("请求错误，请在LION中给账号bml_match_test_tool添加权限后再试");
            } else {
                response.setMessage(e.getMessage());
            }
            log.error("editMatchStrategy error request is {}", request, e);
        }
        return response;
    }

    @AddLog
    @Override
    public TQueryMatchStrategyResponse queryMatchStrategy(TQueryMatchStrategyRequest request) {
        TQueryMatchStrategyResponse response = new TQueryMatchStrategyResponse();
        response.setCode(-1);
        response.setMessage("系统异常");
        try {
            if (request.getMatchStrategyId() == null) {
                response.setMessage("参数不完整");
                return response;
            }
            Map<String, String> result = matchStrategyService.queryMatchStrategy(request.getMatchStrategyId());
            response.setCode(0);
            response.setMessage("success");
            response.setData(result);
        } catch (Exception e) {
            if (StringUtils.contains(e.getMessage(), "No privilege for user bml_match_test_tool")) {
                response.setMessage("请求错误，请在LION中给账号bml_match_test_tool添加权限后再试");
            } else {
                response.setMessage(e.getMessage());
            }
            log.error("queryMatchStrategy error request is {}", request, e);
        }
        return response;
    }

    @AddLog
    @Override
    public TDeleteMatchStrategyResponse deleteMatchStrategy(TDeleteMatchStrategyRequest request) {
        TDeleteMatchStrategyResponse response = new TDeleteMatchStrategyResponse();
        response.setCode(-1);
        response.setMessage("系统异常");
        try {
            if (request.getMatchStrategyId() == null) {
                response.setMessage("参数不完整");
                return response;
            }
            Integer result = matchStrategyService.deleteMatchStrategy(request.getMatchStrategyId(), ContextUtil.getLoginUserMis());
            if (result == 1) {
                response.setCode(0);
                response.setMessage("success");
            } else {
                response.setMessage("删除失败");
            }

        } catch (Exception e) {
            log.error("deleteMatchStrategy error request is {}", request, e);
        }
        return response;
    }

    @AddLog
    @Override
    public TQueryMatchStrategyResponse queryDeployInfo(TQueryMatchStrategyRequest request) {
        return queryMatchStrategy(request);

    }

    @AddLog
    @Override
    public TQueryMatchStrategyResponse queryRollbackInfo(TQueryMatchStrategyRequest request) {
        return queryMatchStrategy(request);
    }

    @AddLog
    @Override
    public TBaseResponse deployMatchStrategy(TDeployRollbackMatchStrategyRequest request) {
        TBaseResponse response = new TBaseResponse();
        try {
            if (Objects.isNull(request) || request.getMatchStrategyId() <= 0) {
                response.setCode(ResultEnum.PARAMS_FAIL.getCode());
                response.setMessage("参数不完整");
                return response;
            }
            Pair<Boolean, String> pair = matchStrategyService.deployMatchStrategy(request.getMatchStrategyId(), request.getType());
            if (pair.getKey()) {
                response.setCode(ResultEnum.SUCCESS.getCode());
                response.setMessage(ResultEnum.SUCCESS.getMsg());
            } else {
                response.setCode(ResultEnum.SYS_ERROR.getCode());
                response.setMessage(pair.getValue());
            }
        } catch (Exception e) {
            log.error("deployMatchStrategy error request is {}", request, e);
            response.setCode(ResultEnum.PARAMS_FAIL.getCode());
            response.setMessage(ResultEnum.PARAMS_FAIL.getMsg());
        }
        return response;
    }

    @AddLog
    @Override
    public TBaseResponse rollbackMatchStrategy(TDeployRollbackMatchStrategyRequest request) {
        TBaseResponse response = new TBaseResponse();
        try {
            if (request.getMatchStrategyId() == null) {
                response.setMessage("参数不完整");
                return response;
            }
            Pair<Boolean, String> pair = matchStrategyService.rollMatchStrategy(request.getMatchStrategyId(), request.getType());
            if (pair.getKey()) {
                response.setCode(ResultEnum.SUCCESS.getCode());
                response.setMessage(ResultEnum.SUCCESS.getMsg());
            } else {
                response.setCode(ResultEnum.SYS_ERROR.getCode());
                response.setMessage(pair.getValue());
            }
        } catch (Exception e) {
            log.error("rollbackMatchStrategy error request is {}", request, e);
            response.setCode(ResultEnum.PARAMS_FAIL.getCode());
            response.setMessage(ResultEnum.PARAMS_FAIL.getMsg());
        }
        return response;
    }

    @Override
    public TQueryMatchStrategyResponse queryMatchStrategyDetail(TQueryMatchStrategyRequest request) {
        TQueryMatchStrategyResponse response = new TQueryMatchStrategyResponse();
        response.setCode(-1);
        response.setMessage("系统异常");
        try {
            if (StringUtils.isBlank(request.getMatchStrategyName())) {
                response.setMessage("参数不完整");
                return response;
            }
            Map<String, String> result = matchStrategyService.queryMatchStrategyByName(request.getMatchStrategyName());
            response.setCode(0);
            response.setMessage("success");
            response.setData(result);
        } catch (Exception e) {
            if (StringUtils.contains(e.getMessage(), "No privilege for user bml_match_test_tool")) {
                String msg = "请求错误，请在LION中给账号bml_match_test_tool添加权限后再试request:" + JSONObject.toJSONString(request);
                dxService.sendMsg2Users(msg, SYSTEM_OPS_MIS_ID_LIST);
                response.setMessage(msg);
            } else {
                response.setMessage(e.getMessage());
            }
            log.error("queryMatchStrategy error request is {}", request, e);
        }
        return response;

    }

    private MatchStrategy convert(TAddEditMatchStrategyRequest request) {
        MatchStrategy matchStrategy = new MatchStrategy();
        matchStrategy.setId(request.getMatchStrategyId());
        matchStrategy.setName(request.getMatchStrategyName());
        matchStrategy.setTestSceneId(request.getSceneId());
        matchStrategy.setBizLineId(request.getBizLineId());
        matchStrategy.setAlgoBizCodes(JSON.toJSONString(request.getAlgoCodeInfo()));
        if (MapUtils.isNotEmpty(request.getPredictorCache()) && StringUtils.isNotEmpty(request.getPredictorCache().get("setName"))) {
            ImmutableMap<String, ? extends Serializable> predictorCache = ImmutableMap.of("setName", request.getPredictorCache().get("setName"),
                    "cache1", request.getPredictorCache().get("cache1"),
                    "cache2", request.getPredictorCache().get("cache2"),
                    "cache3", request.getPredictorCache().get("cache3"),
                    "cache4", request.getPredictorCache().get("cache4"));
            matchStrategy.setPredictorCache(JSON.toJSONString(predictorCache));
        }
        matchStrategy.setLlmBizCodes(JSON.toJSONString(request.getLlmInfo()));
        matchStrategy.setOctoServiceConfig(JSON.toJSONString(request.getOctoInfo()));
        matchStrategy.setOwner(ContextUtil.getLoginUserMis());
        matchStrategy.setStatus(MatchStrategyStatusEnum.INIT.getCode());
        return matchStrategy;

    }


    private Pair<Boolean, String> paramCheck(TAddEditMatchStrategyRequest request) {
        if (CollectionUtils.isNotEmpty(request.getAlgoCodeInfo())) {
            for (Map<String, String> map : request.getAlgoCodeInfo()) {
                if (StringUtils.isEmpty(map.get("algoBizCode"))) {
                    return Pair.of(false, "algoCodeInfo错误");
                }
            }
        }


        if (CollectionUtils.isNotEmpty(request.getLlmInfo())) {
            for (Map<String, String> map : request.getLlmInfo()) {
                if (StringUtils.isEmpty(map.get("llmBizCode"))) {
                    return Pair.of(false, "llmInfo错误");
                }
            }
        }

        return Pair.of(true, "");
    }
}
