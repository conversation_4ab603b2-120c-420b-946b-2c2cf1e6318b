package com.sankuai.algoplatform.matchops.starter.service;

import com.dianping.lion.client.util.StringUtils;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServerExtConfig;
import com.sankuai.algoplatform.matchops.api.enums.ResultEnum;
import com.sankuai.algoplatform.matchops.api.request.testScene.TAddEditTestSceneRequest;
import com.sankuai.algoplatform.matchops.api.request.testScene.TDeleteTestSceneRequest;
import com.sankuai.algoplatform.matchops.api.request.testScene.TTestSceneListRequest;
import com.sankuai.algoplatform.matchops.api.response.testScene.TAddTestSceneResponse;
import com.sankuai.algoplatform.matchops.api.response.testScene.TDeleteTestSceneResponse;
import com.sankuai.algoplatform.matchops.api.response.testScene.TEditTestSceneResponse;
import com.sankuai.algoplatform.matchops.api.response.testScene.TTestSceneListResponse;
import com.sankuai.algoplatform.matchops.api.service.TTestSceneService;
import com.sankuai.algoplatform.matchops.application.service.testtool.TestSceneService;
import com.sankuai.algoplatform.matchops.domain.enums.ReqPostTypeEnum;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestScene;
import com.sankuai.algoplatform.matchops.infrastructure.util.ContextUtil;
import com.sankuai.algoplatform.matchops.infrastructure.util.ParamUtil;
import com.sankuai.algoplatform.matchops.infrastructure.util.RegexUtil;
import com.sankuai.algoplatform.matchops.starter.aop.anno.AddLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.mortbay.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

@MdpThriftServer(port = 9001)
@MdpThriftServerExtConfig(enableAuthHandler = true)
@Slf4j
public class TTestSceneServiceImpl implements TTestSceneService {

    private static final String URL_REGEX = "^(https?://)?([a-zA-Z0-9.-]+)(:[0-9]{1,5})?(/.*)?$";

    @Autowired
    private TestSceneService testSceneService;

    @AddLog
    @Override
    public TTestSceneListResponse getTestSceneList(TTestSceneListRequest request) {
        TTestSceneListResponse response = new TTestSceneListResponse();
        response.setCode(-1);
        response.setMessage("系统异常");
        try {
            if (request.getCurrent() == null || request.getBizLineId() == null) {
                response.setMessage("参数不完整");
                return response;
            }
            Map<String, String> data = testSceneService.getTestSceneList(request.getCurrent(), request.getBizLineId());
            response.setCode(0);
            response.setMessage("success");
            response.setData(data);
        } catch (Exception e) {
            log.error("getTestSceneList error request is {}", request, e);
        }
        return response;
    }

    @AddLog
    @Override
    public TAddTestSceneResponse addTestSceneList(TAddEditTestSceneRequest request) {
        TAddTestSceneResponse response = new TAddTestSceneResponse();

        try {
            Map<String, Object> validParam = new HashMap<>();
            validParam.put("bizLineId", request.getBizLineId());
            validParam.put("sceneName", request.getSceneName());
            validParam.put("runAddress", request.getRunAddress());
            Pair<Boolean, String> validRes = ParamUtil.checkNotNull(validParam);
            if (!validRes.getKey()) {
                response.setCode(ResultEnum.PARAMS_FAIL.getCode());
                response.setMessage(validRes.getRight());
                return response;
            }


            ReqPostTypeEnum postTypeEnum = ReqPostTypeEnum.getByCode(request.getRunType());
            switch (postTypeEnum) {
                case XT:
                    if (!request.getRunAddress().contains("data.sankuai.com/wanxiang2")) {
                        response.setCode(ResultEnum.PARAMS_FAIL.getCode());
                        response.setMessage("runAddress错误");
                        return response;
                    }
                    break;
                case HTTP:
                    if (!RegexUtil.matches(request.getRunAddress(), URL_REGEX)) {
                        response.setCode(ResultEnum.PARAMS_FAIL.getCode());
                        response.setMessage("runAddress错误");
                        return response;
                    }
                    break;
                case FUNCTION:
                    //todo 判断bean和method是否存在
                    break;
                case UNKNOWN:
                    response.setCode(ResultEnum.PARAMS_FAIL.getCode());
                    response.setMessage("runType错误");
                    return response;
            }

            response.setCode(-1);
            response.setMessage("系统异常");

            Long id = testSceneService.addTestScene(convert(request));
            Map<String, String> data = new HashMap<>();
            data.put("id", id.toString());
            response.setCode(0);
            response.setMessage("success");
            response.setData(data);
        } catch (Exception e) {
            log.error("addTestSceneList error request is {}", request, e);
        }
        return response;
    }

    @AddLog
    @Override
    public TEditTestSceneResponse editTestSceneList(TAddEditTestSceneRequest request) {
        TEditTestSceneResponse response = new TEditTestSceneResponse();
        response.setCode(-1);
        response.setMessage("系统异常");
        try {
            if (request.getSceneId() == null || request.getSceneId() <= 0) {
                response.setMessage("sceneId不能为空");
                return response;
            }

            ReqPostTypeEnum postTypeEnum = ReqPostTypeEnum.getByCode(request.getRunType());
            String address = request.getRunAddress();
            if ((postTypeEnum == ReqPostTypeEnum.UNKNOWN && StringUtils.isNotEmpty(address)) ||
                    (postTypeEnum != ReqPostTypeEnum.UNKNOWN && StringUtils.isEmpty(address))) {
                response.setCode(ResultEnum.PARAMS_FAIL.getCode());
                response.setMessage("参数错误");
                return response;
            }

            if (postTypeEnum != ReqPostTypeEnum.UNKNOWN && StringUtils.isNotEmpty(address)) {
                switch (postTypeEnum) {
                    case XT:
                        if (!request.getRunAddress().contains("data.sankuai.com/wanxiang2")) {
                            response.setCode(ResultEnum.PARAMS_FAIL.getCode());
                            response.setMessage("runAddress错误");
                            return response;
                        }
                        break;
                    case HTTP:
                        if (!RegexUtil.matches(request.getRunAddress(), URL_REGEX) || request.getRunAddress().contains("xt.sankuai.com/workbench/task")) {
                            response.setCode(ResultEnum.PARAMS_FAIL.getCode());
                            response.setMessage("runAddress错误");
                            return response;
                        }
                        break;
                    case FUNCTION:
                        //todo 判断bean和method是否存在
                        break;
                }
            }

            testSceneService.editTestScene(convert(request));
            response.setCode(0);
            response.setMessage("success");
        } catch (Exception e) {
            log.error("editTestSceneList error request is {}", request, e);
        }
        return response;
    }

    @AddLog
    @Override
    public TDeleteTestSceneResponse delTestSceneList(TDeleteTestSceneRequest request) {
        TDeleteTestSceneResponse response = new TDeleteTestSceneResponse();
        response.setCode(-1);
        response.setMessage("系统异常");
        try {
            if (request.getSceneId() == null) {
                response.setMessage("参数不完整");
                return response;
            }
            Integer result = testSceneService.delTestScene(request.getSceneId(), ContextUtil.getLoginUserMis());
            if (result == 1) {
                response.setCode(0);
                response.setMessage("success");
            } else {
                response.setMessage("删除失败");
            }

        } catch (Exception e) {
            log.error("delTestSceneList error request is {}", request, e);
        }
        return response;
    }

    private TestScene convert(TAddEditTestSceneRequest request) {
        TestScene testScene = new TestScene();
        testScene.setId(request.getSceneId());
        testScene.setTestSceneName(request.getSceneName());
        testScene.setReqPostType(request.getRunType());
        testScene.setBizLineId(request.getBizLineId());
        testScene.setReqPostLink(request.getRunAddress());
        testScene.setOwner(ContextUtil.getLoginUserMis());
        testScene.setExtra(request.getExtra());
        return testScene;

    }
}
