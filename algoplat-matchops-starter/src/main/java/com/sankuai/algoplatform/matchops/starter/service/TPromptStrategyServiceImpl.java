package com.sankuai.algoplatform.matchops.starter.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.util.Pair;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServerExtConfig;
import com.sankuai.algoplatform.matchops.api.enums.ResultEnum;
import com.sankuai.algoplatform.matchops.api.request.prompt.TAddEditPromptBizCodeRequest;
import com.sankuai.algoplatform.matchops.api.request.prompt.TAddEditPromptStrategyRequest;
import com.sankuai.algoplatform.matchops.api.request.prompt.TDeletePromptBizCodeRequest;
import com.sankuai.algoplatform.matchops.api.request.prompt.TPromptListRequest;
import com.sankuai.algoplatform.matchops.api.response.prompt.*;
import com.sankuai.algoplatform.matchops.api.service.TPromptStrategyService;
import com.sankuai.algoplatform.matchops.application.service.testtool.PromptStrategyService;
import com.sankuai.algoplatform.matchops.domain.enums.EnvStatus;
import com.sankuai.algoplatform.matchops.domain.service.testtool.LionHelper;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.BizLlmPredictConfigDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.*;
import com.sankuai.algoplatform.matchops.infrastructure.util.ContextUtil;
import com.sankuai.algoplatform.matchops.infrastructure.util.ParamUtil;
import com.sankuai.algoplatform.matchops.starter.aop.anno.AddLog;
import com.sankuai.mcm.client.sdk.config.annotation.McmHandler;
import com.sankuai.mcm.client.sdk.config.annotation.McmPreHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@MdpThriftServer(port = 9001)
@MdpThriftServerExtConfig(enableAuthHandler = true)
@Slf4j
public class TPromptStrategyServiceImpl implements TPromptStrategyService {

    @Autowired
    private PromptStrategyService promptStrategyService;

    @Autowired
    BizLlmPredictConfigDao bizLlmPredictConfigDao;

    @AddLog
    @Override
    public TPromptListResponse getPromptList(TPromptListRequest request) {
        TPromptListResponse response = new TPromptListResponse();
        try {
            if (request.getCurrent() == null || request.getBizLineId() == null) {
                response.setMessage("参数不完整");
                return response;
            }
            Map<String, String> data = promptStrategyService.getPromptList(request.getCurrent(),
                    request.getBizLineId());
            response.setCode(0);
            response.setMessage("success");
            response.setData(data);
        } catch (Exception e) {
            response.setCode(-1);
            response.setMessage(e.getMessage());
            log.error("getPromptList error request is {}", request, e);
        }
        return response;
    }

    @AddLog
    @Override
    public TAddPromptStrategyResponse addPromptStrategy(TAddEditPromptStrategyRequest request) {
        TAddPromptStrategyResponse response = new TAddPromptStrategyResponse();
        Map<String, Object> validParam = new HashMap<>();
        validParam.put("llmStrategyBizCode", request.getLlmStrategyBizCode());
        validParam.put("bizLineId", request.getBizLineId());
        validParam.put("llmModelName", request.getLlmModelName());
        validParam.put("batchSize", request.getBatchSize());
        validParam.put("appId", request.getAppId());
        validParam.put("maxToken", request.getMaxTokens());
        validParam.put("topP", request.getTopP());
        validParam.put("topK", request.getTopK());
        validParam.put("temperature", request.getTemperature());
        org.apache.commons.lang3.tuple.Pair<Boolean, String> validRes = ParamUtil.checkNotNull(validParam);
        if (!validRes.getKey()) {
            response.setCode(ResultEnum.PARAMS_FAIL.getCode());
            response.setMessage(validRes.getRight());
            return response;
        }

        try {
            Pair<BizLlmpredictConfig, BizLlmpredictPromptTemplate> temp = convert(request);
            Long id = promptStrategyService.addPromptStrategy(temp.getKey(), temp.getValue());
            Map<String, String> data = new HashMap<>();
            data.put("id", id.toString());
            response.setCode(0);
            response.setMessage("success");
            response.setData(data);
        } catch (Exception e) {
            response.setCode(-1);
            response.setMessage(e.getMessage());
            log.error("addAlgoStrategy error request is {}", request, e);
        }
        return response;
    }

    @AddLog
    @Override
    @McmHandler(eventName = "UpdatePromptStrategy")
    @McmPreHandler(eventName = "UpdatePromptStrategy")
    public TEditPromptStrategyResponse editPromptStrategy(TAddEditPromptStrategyRequest request) {
        TEditPromptStrategyResponse response = new TEditPromptStrategyResponse();
        try {
            if (request.getLlmStrategyId() == null) {
                response.setMessage("参数不完整");
                return response;
            }
            request.setLlmStrategyBizCode(null);//bizCode不能修改
            Pair<BizLlmpredictConfig, BizLlmpredictPromptTemplate> temp = convert(request);
            promptStrategyService.editPromptStrategy(temp.getKey(), temp.getValue());
            response.setCode(0);
            response.setMessage("success");
        } catch (Exception e) {
            response.setCode(-1);
            response.setMessage(e.getMessage());
            log.error("editPromptStrategy error request is {}", request, e);
        }
        return response;
    }

    @AddLog
    @Override
    public TAddPromptBizCodeResponse addPromptBizCode(TAddEditPromptBizCodeRequest request) {
        TAddPromptBizCodeResponse response = new TAddPromptBizCodeResponse();
        Map<String, Object> validParam = new HashMap<>();
        validParam.put("llmBizCode", request.getLlmBizCode());
        validParam.put("bizLineId", request.getBizLineId());
        validParam.put("strategy", request.getStrategy());
        org.apache.commons.lang3.tuple.Pair<Boolean, String> validRes = ParamUtil.checkNotNull(validParam);
        if (!validRes.getKey()) {
            response.setCode(ResultEnum.PARAMS_FAIL.getCode());
            response.setMessage(validRes.getRight());
            return response;
        }

        try {
            Long id = promptStrategyService.addPromptBizCode(convertBizCode(request));
            Map<String, String> data = new HashMap<>();
            data.put("id", id.toString());
            response.setCode(0);
            response.setMessage("success");
            response.setData(data);
        } catch (Exception e) {
            response.setCode(-1);
            response.setMessage(e.getMessage());
            log.error("addAlgoBizCode error request is {}", request, e);
        }
        return response;
    }

    @AddLog
    @Override
    @McmHandler(eventName = "UpdatePromptBizCode")
    @McmPreHandler(eventName = "UpdatePromptBizCode")
    public TEditPromptBizCodeResponse editPromptBizCode(TAddEditPromptBizCodeRequest request) {
        TEditPromptBizCodeResponse response = new TEditPromptBizCodeResponse();
        try {
            if (request.getLlmBizCodeId() == null) {
                response.setMessage("参数不完整");
                return response;
            }
            request.setLlmBizCode(null);//bizCode不能修改
            promptStrategyService.editPromptBizCode(convertBizCode(request));
            response.setCode(0);
            response.setMessage("success");
        } catch (Exception e) {
            response.setCode(-1);
            response.setMessage(e.getMessage());
            log.error("editPromptBizCode error request is {}", request, e);
        }
        return response;
    }

    @AddLog
    @Override
    public TDeletePromptBizCodeResponse deletePromptBizCode(TDeletePromptBizCodeRequest request) {
        TDeletePromptBizCodeResponse response = new TDeletePromptBizCodeResponse();
        try {
            if (request.getLlmStrategyId() == null) {
                response.setMessage("参数不完整");
                return response;
            }
            Integer result = promptStrategyService.deletePromptBizCode(request.getLlmStrategyId(), request.getOwner());
            if (result == 1) {
                response.setCode(0);
                response.setMessage("success");
            } else {
                response.setMessage("删除失败");
            }

        } catch (Exception e) {
            response.setCode(-1);
            response.setMessage(e.getMessage());
            log.error("deletePromptBizCode error request is {}", request, e);
        }
        return response;
    }

    @AddLog
    private Pair<BizLlmpredictConfig, BizLlmpredictPromptTemplate> convert(TAddEditPromptStrategyRequest request) {
        BizLlmpredictConfig bizLlmpredictConfig = new BizLlmpredictConfig();
        bizLlmpredictConfig.setId(request.getLlmStrategyId());
        bizLlmpredictConfig.setNote(request.getLlmStrategyName());
        bizLlmpredictConfig.setBizCode(request.getLlmStrategyBizCode());
        Map<String, Object> modelConfig = new HashMap<>();
        modelConfig.put("modelName", request.getLlmModelName());
        modelConfig.put("modelServiceName", request.getLlmServiceName());
        modelConfig.put("batchSize", request.getBatchSize());
        modelConfig.put("useTemplate", request.getIsTemplate());
        modelConfig.put("appId", request.getAppId());
        modelConfig.put("debug", request.getIsDebug() == 1);
        Map<String, Object> modelParams = new HashMap<>();
        modelParams.put("maxTokens", request.getMaxTokens());
        modelParams.put("top_p", request.getTopP());
        modelParams.put("top_k", request.getTopK());
        modelParams.put("temperature", request.getTemperature());
        modelConfig.put("modelParms", modelParams);
        bizLlmpredictConfig.setModelConfig(JSON.toJSONString(modelConfig));
        if (request.getBizLineId() != null) {
            bizLlmpredictConfig.setBizLineId(Long.parseLong(request.getBizLineId()));
        }
        bizLlmpredictConfig.setStatus(true);
        bizLlmpredictConfig.setEnv(EnvStatus.ST.getCode());
        bizLlmpredictConfig.setOwner(ContextUtil.getLoginUserMis());
        BizLlmpredictPromptTemplate bizLlmpredictPromptTemplate = new BizLlmpredictPromptTemplate();
        bizLlmpredictPromptTemplate.setPromptTemplate(request.getPrompt());
        bizLlmpredictPromptTemplate.setStatus(true);
        bizLlmpredictPromptTemplate.setNote(request.getNote());
        bizLlmpredictPromptTemplate.setOwner(ContextUtil.getLoginUserMis());
        return Pair.from(bizLlmpredictConfig, bizLlmpredictPromptTemplate);
    }

    @AddLog
    private LlmBizStrategy convertBizCode(TAddEditPromptBizCodeRequest request) {
        LlmBizStrategy llmBizStrategy = new LlmBizStrategy();
        llmBizStrategy.setId(request.getLlmBizCodeId());
        llmBizStrategy.setBizCode(request.getLlmBizCode());
        llmBizStrategy.setBizLineId(request.getBizLineId());
        llmBizStrategy.setNote(request.getNote());
        List<Map<String, String>> list = JSON.parseObject(request.getStrategy().get("distributions"), new TypeReference<List<Map<String, String>>>() {
        });

        for (int i = 0; i < list.size(); i++) {
            Map<String, String> dist = list.get(i);
            BizLlmpredictConfig bizLlmpredictConfig = bizLlmPredictConfigDao.selectById(Long.parseLong(dist.get("strategyId")));
            dist.put("modelBizCode", bizLlmpredictConfig.getBizCode());
            if (StringUtils.isEmpty(dist.get("name"))) {
                dist.put("name", "group_" + (i + 1));
            }
        }

        HashMap<Object, Object> abtest = new HashMap<>();
        abtest.put("strategyName", request.getStrategy().get("strategyName"));
        abtest.put("distributions", list);
        llmBizStrategy.setAbtestConfig(JSON.toJSONString(abtest));
        llmBizStrategy.setOwner(ContextUtil.getLoginUserMis());
        llmBizStrategy.setStatus(true);
        return llmBizStrategy;
    }
}
