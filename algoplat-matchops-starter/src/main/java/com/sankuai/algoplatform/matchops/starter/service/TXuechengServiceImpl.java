package com.sankuai.algoplatform.matchops.starter.service;

import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServerExtConfig;
import com.sankuai.algoplatform.matchops.api.enums.ResultEnum;
import com.sankuai.algoplatform.matchops.api.request.testreprotertemplate.TGetXuechengContentRequest;
import com.sankuai.algoplatform.matchops.api.response.testreportertemplate.TGetXuechengContentResponse;
import com.sankuai.algoplatform.matchops.api.service.TXuechengService;
import com.sankuai.algoplatform.matchops.application.service.testtool.CookieService;
import com.sankuai.algoplatform.matchops.infrastructure.cache.TairClient;
import com.sankuai.algoplatform.matchops.infrastructure.dal.po.XmContentResult;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.XuechengService;
import com.sankuai.algoplatform.matchops.infrastructure.util.ContextUtil;
import com.sankuai.algoplatform.matchops.starter.aop.anno.AddLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

@MdpThriftServer(port = 9001)
@MdpThriftServerExtConfig(enableAuthHandler = true)
@Slf4j
public class TXuechengServiceImpl implements TXuechengService {

    @Autowired
    private XuechengService xuechengService;

    @Autowired
    private CookieService cookieService;

    @Autowired
    private TairClient tairClient;

    @Value("${xuecheng.key}")
    private String xuechengCacheKey;

    @Override
    @AddLog
    public TGetXuechengContentResponse getXuechengContent(TGetXuechengContentRequest request) {
        TGetXuechengContentResponse response = new TGetXuechengContentResponse();
        response.setCode(ResultEnum.PARAMS_FAIL.getCode());


        if (StringUtils.isEmpty(request.getLink())) {
            response.setMessage("link is null");
            return response;
        }

        String ssoId = request.getSsoId();
        String misId = ContextUtil.getLoginUserMis();
        if (StringUtils.isEmpty(request.getSsoId())) {
            ssoId = tairClient.get(xuechengCacheKey + misId);
//            ssoId = cookieService.getCookie();
        }
        String contentId = StringUtils.substringAfter(request.getLink(), "collabpage/");
        XmContentResult content = xuechengService.getCollaborationContent(contentId, misId);
        if (content.getCode() == 0) {
            response.setCode(ResultEnum.SUCCESS.getCode());
            response.setMessage(ResultEnum.SUCCESS.getMsg());
            response.setData(content.getContent());
            return response;
        }
        response.setMessage(content.getMessage());
        return response;
    }
}
