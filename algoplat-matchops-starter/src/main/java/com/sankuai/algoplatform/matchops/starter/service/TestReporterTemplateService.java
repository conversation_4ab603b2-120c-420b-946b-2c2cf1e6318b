package com.sankuai.algoplatform.matchops.starter.service;

import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServerExtConfig;
import com.sankuai.algoplatform.matchops.api.enums.ResultEnum;
import com.sankuai.algoplatform.matchops.api.request.testreprotertemplate.TAddEditTestReporterTemplateRequest;
import com.sankuai.algoplatform.matchops.api.request.testreprotertemplate.TDeleteTestReporterTemplateRequest;
import com.sankuai.algoplatform.matchops.api.request.testreprotertemplate.TTestReporterTemplateListRequest;
import com.sankuai.algoplatform.matchops.api.response.testreportertemplate.TAddTestReporterTemplateResponse;
import com.sankuai.algoplatform.matchops.api.response.testreportertemplate.TDeleteTestReporterTemplateResponse;
import com.sankuai.algoplatform.matchops.api.response.testreportertemplate.TEditTestReporterTemplateResponse;
import com.sankuai.algoplatform.matchops.api.response.testreportertemplate.TTestReporterTemplateListResponse;
import com.sankuai.algoplatform.matchops.api.service.TTestReporterTemplateService;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ReporterTemplate;
import com.sankuai.algoplatform.matchops.infrastructure.util.ContextUtil;
import com.sankuai.algoplatform.matchops.infrastructure.util.ParamUtil;
import com.sankuai.algoplatform.matchops.starter.aop.anno.AddLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

@MdpThriftServer(port = 9001)
@MdpThriftServerExtConfig(enableAuthHandler = true)
@Slf4j
public class TestReporterTemplateService implements TTestReporterTemplateService {
    @Autowired
    private com.sankuai.algoplatform.matchops.application.service.testtool.TestReporterTemplateService testReporterTemplateService;


    @AddLog
    @Override
    public TTestReporterTemplateListResponse getTestReporterTemplateList(TTestReporterTemplateListRequest request) {
        TTestReporterTemplateListResponse response = new TTestReporterTemplateListResponse();
        response.setCode(-1);
        response.setMessage("系统异常");
        try {
            if (request.getCurrent() == null || request.getBizLineId() == null) {
                response.setMessage("参数不完整");
                return response;
            }
            Map<String, String> data = testReporterTemplateService.getTestReporterTemplateList(request.getCurrent(),
                    request.getBizLineId());
            response.setCode(0);
            response.setMessage("success");
            response.setData(data);
        } catch (Exception e) {
            log.error("getTestReporterTemplateList error request is {}", request, e);
        }
        return response;
    }

    @AddLog
    @Override
    public TAddTestReporterTemplateResponse addTestReporterTemplate(TAddEditTestReporterTemplateRequest request) {
        TAddTestReporterTemplateResponse response = new TAddTestReporterTemplateResponse();
        response.setCode(-1);
        response.setMessage("系统异常");
        try {
            Map<String, Object> validParam = new HashMap<>();
            validParam.put("bizLineId", request.getBizLineId());
            validParam.put("templateName", request.getTemplateName());
            validParam.put("templateAddr", request.getTemplateAddr());
            Pair<Boolean, String> validRes = ParamUtil.checkNotNull(validParam);
            if (!validRes.getKey()) {
                response.setCode(ResultEnum.PARAMS_FAIL.getCode());
                response.setMessage(validRes.getRight());
                return response;
            }

            if (!request.getTemplateAddr().contains("https://km.it.test.sankuai.com") &&
                    !request.getTemplateAddr().contains("https://km.sankuai.com")) {
                response.setCode(ResultEnum.PARAMS_FAIL.getCode());
                response.setMessage("模板地址错误");
                return response;
            }

            Long id = testReporterTemplateService.addTestReporterTemplate(convert(request));
            Map<String, String> data = new HashMap<>();
            data.put("id", id.toString());
            response.setCode(0);
            response.setMessage("success");
            response.setData(data);
        } catch (Exception e) {
            log.error("addTestReporterTemplate error request is {}", request, e);
        }
        return response;
    }

    @AddLog
    @Override
    public TEditTestReporterTemplateResponse editTestReporterTemplate(TAddEditTestReporterTemplateRequest request) {
        TEditTestReporterTemplateResponse response = new TEditTestReporterTemplateResponse();
        response.setCode(-1);
        response.setMessage("系统异常");
        try {
            if (request.getTemplateId() == null) {
                response.setMessage("参数不完整");
                return response;
            }

            if (StringUtils.isNotEmpty(request.getTemplateAddr())) {
                if (!request.getTemplateAddr().contains("https://km.it.test.sankuai.com") &&
                        !request.getTemplateAddr().contains("https://km.sankuai.com")) {
                    response.setCode(ResultEnum.PARAMS_FAIL.getCode());
                    response.setMessage("模板地址错误");
                    return response;
                }
            }
            testReporterTemplateService.editTestReporterTemplate(convert(request));
            response.setCode(0);
            response.setMessage("success");
        } catch (Exception e) {
            log.error("editTestReporterTemplate error request is {}", request, e);
        }
        return response;
    }

    @AddLog
    @Override
    public TDeleteTestReporterTemplateResponse delTestReporterTemplate(TDeleteTestReporterTemplateRequest request) {
        TDeleteTestReporterTemplateResponse response = new TDeleteTestReporterTemplateResponse();
        response.setCode(-1);
        response.setMessage("系统异常");
        try {
            if (request.getTemplateId() == null) {
                response.setMessage("参数不完整");
                return response;
            }
            Integer result = testReporterTemplateService.delTestReporterTemplate(request.getTemplateId(),
                    ContextUtil.getLoginUserMis());
            if (result == 1) {
                response.setCode(0);
                response.setMessage("success");
            } else {
                response.setMessage("删除失败");
            }

        } catch (Exception e) {
            log.error("delTestReporterTemplate error request is {}", request, e);
        }
        return response;
    }

    private ReporterTemplate convert(TAddEditTestReporterTemplateRequest request) {
        ReporterTemplate reporterTemplate = new ReporterTemplate();
        reporterTemplate.setId(request.getTemplateId());
        reporterTemplate.setBizLineId(request.getBizLineId());
        reporterTemplate.setName(request.getTemplateName());
        reporterTemplate.setTemplateAddr(request.getTemplateAddr());
        reporterTemplate.setCreator(ContextUtil.getLoginUserMis());
        return reporterTemplate;
    }
}
