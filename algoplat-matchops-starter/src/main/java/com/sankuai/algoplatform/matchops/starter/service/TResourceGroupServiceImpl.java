package com.sankuai.algoplatform.matchops.starter.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.util.StringUtils;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServerExtConfig;
import com.sankuai.algoplatform.matchops.api.request.resourceGroup.TAddEditResourceGroupRequest;
import com.sankuai.algoplatform.matchops.api.request.resourceGroup.TDeleteResourceGroupRequest;
import com.sankuai.algoplatform.matchops.api.request.resourceGroup.TResourceGroupListRequest;
import com.sankuai.algoplatform.matchops.api.response.resourceGroup.TAddResourceGroupResponse;
import com.sankuai.algoplatform.matchops.api.response.resourceGroup.TDeleteResourceGroupResponse;
import com.sankuai.algoplatform.matchops.api.response.resourceGroup.TEditResourceGroupResponse;
import com.sankuai.algoplatform.matchops.api.response.resourceGroup.TRSourceGroupListResponse;
import com.sankuai.algoplatform.matchops.api.service.TResourceGroupService;
import com.sankuai.algoplatform.matchops.application.service.testtool.ResourceGroupService;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ResourceGroup;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.DxService;
import com.sankuai.algoplatform.matchops.infrastructure.util.ContextUtil;
import com.sankuai.algoplatform.matchops.starter.aop.anno.AddLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

import static com.sankuai.algoplatform.matchops.domain.constant.TestToolConstants.SYSTEM_OPS_MIS_ID_LIST;

@MdpThriftServer(port = 9001)
@MdpThriftServerExtConfig(enableAuthHandler = true)
@Slf4j
public class TResourceGroupServiceImpl implements TResourceGroupService {
    @Autowired
    private ResourceGroupService resourceGroupService;
    @Autowired
    private DxService dxService;

    @AddLog
    @Override
    public TRSourceGroupListResponse getResourceGroupList(TResourceGroupListRequest request) {
        TRSourceGroupListResponse response = new TRSourceGroupListResponse();
        response.setCode(-1);
        response.setMessage("系统异常");
        try {
            if (request.getCurrent() == null || request.getBizLineId() == null) {
                response.setMessage("参数不完整");
                return response;
            }
            Map<String, String> data = resourceGroupService.getResourceGroupList(request.getCurrent(),
                    request.getBizLineId());
            response.setCode(0);
            response.setMessage("success");
            response.setData(data);
        } catch (Exception e) {
            log.error("getResourceGroupList error request is {}", request, e);
        }
        return response;
    }


//    @Override
//    public TRSourceGroupListResponse getResourceGroup(TAddEditResourceGroupRequest request) {
//        TRSourceGroupListResponse response = new TRSourceGroupListResponse();
//        response.setCode(-1);
//        response.setMessage("系统异常");
//        try {
//            if (request.getResourceGroupId() == null || request.getBizLineId() == null) {
//                response.setMessage("参数不完整,请检查资源组参数");
//                return response;
//            }
//            ResourceGroup resourceGroup = resourceGroupService.getResourceGroup(request.getBizLineId(), request.resourceGroupId);
//            if (resourceGroup == null) {
//                String msg = String.format("bizLineId:%s,resourceGroupName:%s,资源组不存在！", request.getBizLineId(), request.getResourceGroupName());
//                dxService.sendMsg2Users(msg, SYSTEM_OPS_MIS_ID_LIST);
//                response.setMessage(msg);
//                return response;
//            }
//            response.setCode(0);
//            response.setMessage("success");
//            Map<String, String> map = JSONObject.parseObject(JSONObject.toJSONString(resourceGroup), Map.class);
//            response.setData(map);
//        } catch (Exception e) {
//            log.error("getResourceGroup error request is {}", request, e);
//        }
//        return response;
//    }

    @AddLog
    @Override
    public TAddResourceGroupResponse addResourceGroupList(TAddEditResourceGroupRequest request) {
        TAddResourceGroupResponse response = new TAddResourceGroupResponse();
        response.setCode(-1);
        response.setMessage("系统异常");
        try {
            Long id = resourceGroupService.addResourceGroupList(convert(request));
            Map<String, String> data = new HashMap<>();
            data.put("id", id.toString());
            response.setCode(0);
            response.setMessage("success");
            response.setData(data);
        } catch (Exception e) {
            log.error("addResourceGroupList error request is {}", request, e);
        }
        return response;
    }

    @AddLog
    @Override
    public TEditResourceGroupResponse editResourceGroupList(TAddEditResourceGroupRequest request) {
        TEditResourceGroupResponse response = new TEditResourceGroupResponse();
        response.setCode(-1);
        response.setMessage("系统异常");
        try {
            if (request.getResourceGroupId() == null) {
                response.setMessage("参数不完整");
                return response;
            }
            resourceGroupService.editResourceGroupList(convert(request));
            response.setCode(0);
            response.setMessage("success");
        } catch (Exception e) {
            log.error("editResourceGroupList error request is {}", request, e);
        }
        return response;
    }

    @AddLog
    @Override
    public TDeleteResourceGroupResponse delResourceGroupList(TDeleteResourceGroupRequest request) {
        TDeleteResourceGroupResponse response = new TDeleteResourceGroupResponse();
        response.setCode(-1);
        response.setMessage("系统异常");
        try {
            if (request.getResourceGroupId() == null) {
                response.setMessage("参数不完整");
                return response;
            }
            Integer result = resourceGroupService.delResourceGroupList(request.getResourceGroupId(),
                    ContextUtil.getLoginUserMis());
            if (result == 1) {
                response.setCode(0);
                response.setMessage("success");
            } else {
                response.setMessage("删除失败");
            }

        } catch (Exception e) {
            log.error("delResourceGroupList error request is {}", request, e);
        }
        return response;
    }

    private ResourceGroup convert(TAddEditResourceGroupRequest request) {
        ResourceGroup resourceGroup = new ResourceGroup();
        resourceGroup.setId(request.getResourceGroupId());
        resourceGroup.setName(request.getResourceGroupName());
        resourceGroup.setOctoResources(JSON.toJSONString(request.getCpuResourceInfo()));
        resourceGroup.setMlpResources(JSON.toJSONString(request.getMlpResourceInfo()));
        resourceGroup.setFridayResources(JSON.toJSONString(request.getLlmResourceInfo()));
        resourceGroup.setBizLineId(request.getBizLineId());
        resourceGroup.setCreator(ContextUtil.getLoginUserMis());
        return resourceGroup;
    }
}
