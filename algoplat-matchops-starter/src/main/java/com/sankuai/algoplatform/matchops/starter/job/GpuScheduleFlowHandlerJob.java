package com.sankuai.algoplatform.matchops.starter.job;

import com.cip.crane.client.spring.annotation.Crane;
import com.sankuai.algoplatform.matchops.application.service.GpuScheduleFlowHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class GpuScheduleFlowHandlerJob {
    @Resource
    private GpuScheduleFlowHandlerService gpuScheduleFlowHandlerService;

    @Crane("GpuSchedule_generateTaskInstance")
    public void generateTaskInstance() {
        gpuScheduleFlowHandlerService.generateTaskInstance();
    }

    @Crane("GpuSchedule_invokeTaskInstance")
    public void invokeTaskInstance() {
        gpuScheduleFlowHandlerService.invokeTaskInstance();
    }

    @Crane("GpuSchedule_updateTaskInstance")
    public void updateTaskInstance() {
        gpuScheduleFlowHandlerService.updateTaskInstance();
    }

    @Crane("GpuSchedule_generateResourceMonitorTaskInstance")
    public void generateResourceMonitorTaskInstance() {
        gpuScheduleFlowHandlerService.generateResourceMonitorTaskInstance();
    }

    @Crane("GpuSchedule_resourceMonitor")
    public void resourceMonitor() {
        gpuScheduleFlowHandlerService.resourceMonitor();
    }
}
