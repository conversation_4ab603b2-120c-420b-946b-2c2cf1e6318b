package com.sankuai.algoplatform.matchops.starter.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServerExtConfig;
import com.sankuai.algoplatform.matchops.api.enums.ResultEnum;
import com.sankuai.algoplatform.matchops.api.request.testTask.*;
import com.sankuai.algoplatform.matchops.api.response.TBaseResponse;
import com.sankuai.algoplatform.matchops.api.response.TPage;
import com.sankuai.algoplatform.matchops.api.response.testTask.*;
import com.sankuai.algoplatform.matchops.api.service.TTestTaskService;
import com.sankuai.algoplatform.matchops.application.model.testtool.TestSubTaskPo;
import com.sankuai.algoplatform.matchops.application.model.testtool.TestTaskPo;
import com.sankuai.algoplatform.matchops.application.service.testtool.TestSubTaskService;
import com.sankuai.algoplatform.matchops.application.service.testtool.TestTaskService;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestTask;
import com.sankuai.algoplatform.matchops.api.response.resourceGroup.TTestTaskDetailResponse;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.S3Service;
import com.sankuai.algoplatform.matchops.infrastructure.util.ParamUtil;
import com.sankuai.algoplatform.matchops.starter.aop.anno.AddLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.core.config.plugins.validation.constraints.Required;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.*;

@MdpThriftServer(port = 9001)
@MdpThriftServerExtConfig(enableAuthHandler = true)
@Slf4j
public class TTestTaskServiceImpl implements TTestTaskService {

    @Resource
    private TestTaskService testTaskService;

    @Resource
    private TestSubTaskService testSubTaskService;

    @Autowired
    private S3Service s3Service;

    @AddLog
    @Override
    public TTestTaskListResponse getTestTaskList(TTestTaskListRequest request) {
        TTestTaskListResponse response = new TTestTaskListResponse();
        if (ParamUtil.isNullOrEmpty(request.getBizLineId())) {
            response.setCode(ResultEnum.PARAMS_FAIL.getCode());
            response.setMessage("bizLineId不能为空");
            return response;
        }
        try {
            Long total = testTaskService.getTestTaskTotalCountByBizLineId(request.getBizLineId());
            List<Map<String, String>> testTaskList = testTaskService.getTestTaskList(request.getCurrent(), request.getBizLineId());
            TPage<Map<String, String>> page = new TPage<>();
            page.setList(testTaskList);
            page.setTotal(total);
            page.setCurrent(request.getCurrent());
            response.setData(page);
            response.setCode(ResultEnum.SUCCESS.getCode());
            response.setMessage(ResultEnum.SUCCESS.getMsg());
        } catch (Exception e) {
            log.error("TTestTaskServiceImpl.getTestTaskList error.request={}", JSON.toJSONString(request), e);
            response.setCode(ResultEnum.SYS_ERROR.getCode());
            response.setMessage(ResultEnum.SYS_ERROR.getMsg());
        }
        return response;
    }

    @AddLog
    @Override
    public TAddOrEditTestTaskResponse addTestTask(TAddOrEditTestTaskRequest request) {
        TAddOrEditTestTaskResponse response = new TAddOrEditTestTaskResponse();

        //校验、打点
        Map<String, Object> validParam = new HashMap<>();
        validParam.put("bizLineId", request.getBizLineId());
        validParam.put("testTaskName", request.getTestTaskName());
        validParam.put("matchStrategyId", request.getMatchStrategyId());
        Pair<Boolean, String> validRes = ParamUtil.checkNotNull(validParam);
        if (!validRes.getKey()) {
            response.setCode(ResultEnum.PARAMS_FAIL.getCode());
            response.setMessage(validRes.getRight());
            return response;
        }


        try {
            TestTask testTask = TestTaskPo.trans(request);
            Pair<Boolean, String> pair = testTaskService.addTestTask(testTask);
            if (!pair.getLeft()) {
                Map<String, String> data = new HashMap<>();
                response.setCode(ResultEnum.PARAMS_FAIL.getCode());
                response.setMessage(pair.getRight());
                response.setData(data);
            } else {
                Map<String, String> data = new HashMap<>();
                data.put("id", pair.getRight());
                response.setCode(ResultEnum.SUCCESS.getCode());
                response.setMessage(ResultEnum.SUCCESS.getMsg());
                response.setData(data);
            }
        } catch (Exception e) {
            log.error("TTestTaskServiceImpl.addTestTask error.request={}", JSON.toJSONString(request), e);
            response.setCode(ResultEnum.SYS_ERROR.getCode());
            response.setMessage(ResultEnum.SYS_ERROR.getMsg());
        }
        return response;
    }

    @AddLog
    @Override
    public TAddOrEditTestTaskResponse editTestTask(TAddOrEditTestTaskRequest request) {
        //校验、打点
        TAddOrEditTestTaskResponse response = new TAddOrEditTestTaskResponse();

        if (ParamUtil.isNullOrEmpty(request.getTestTaskId())) {
            response.setCode(ResultEnum.PARAMS_FAIL.getCode());
            response.setMessage("testTaskId不能为空");
            return response;
        }

        try {
            TestTask testTask = TestTaskPo.trans(request);
            testTaskService.editTestTask(testTask);
            response.setCode(ResultEnum.SUCCESS.getCode());
            response.setMessage(ResultEnum.SUCCESS.getMsg());

            //准备环境
            prepareEnv(String.valueOf(testTask.getId()));
        } catch (Exception e) {
            log.error("TTestTaskServiceImpl.addTestTask error.request={}", JSON.toJSONString(request), e);
            response.setCode(ResultEnum.SYS_ERROR.getCode());
            response.setMessage(ResultEnum.SYS_ERROR.getMsg());
        }
        return response;
    }

    @AddLog
    @Override
    public TRunTaskResponse runTask(TRunTaskRequest request) {
        TRunTaskResponse response = new TRunTaskResponse();

        Map<String, Object> validParam = new HashMap<>();
        validParam.put("testTaskId", request.getTestTaskId());
        validParam.put("param", request.getParam());
        Pair<Boolean, String> validRes = ParamUtil.checkNotNull(validParam);
        if (!validRes.getKey()) {
            response.setCode(ResultEnum.PARAMS_FAIL.getCode());
            response.setMessage(validRes.getRight());
            return response;
        }

        try {
            Pair<Boolean, String> pair = testTaskService.runTask(request);
            if (!pair.getLeft()) {
                response.setCode(ResultEnum.SYS_ERROR.getCode());
                response.setMessage(pair.getRight());
            } else {
                response.setCode(ResultEnum.SUCCESS.getCode());
                response.setMessage(ResultEnum.SUCCESS.getMsg());
                response.setData(ImmutableMap.of("link", pair.getRight()));
            }
        } catch (Exception e) {
            log.error("TTestTaskServiceImpl.runTask error, req:{}", JSON.toJSONString(request), e);
            response.setCode(ResultEnum.SYS_ERROR.getCode());
            response.setMessage(ResultEnum.SYS_ERROR.getMsg());
        }
        return response;
    }

    @AddLog
    @Override
    public TBaseResponse prepareEnv(String taskId) {
        TBaseResponse response = new TBaseResponse();
        try {
            testTaskService.prepareEnv(Long.valueOf(taskId));
            response.setCode(ResultEnum.SUCCESS.getCode());
            response.setMessage(ResultEnum.SUCCESS.getMsg());
        } catch (Exception e) {
            log.error("TTestTaskServiceImpl.prepareEnv error. taskId:{}", taskId, e);
            response.setCode(ResultEnum.SYS_ERROR.getCode());
            response.setMessage(e.getMessage());
        }

        return response;
    }

    @AddLog
    @Override
    public TTestTaskDetailResponse getTestTaskDetail(String taskId, String type) {
        TTestTaskDetailResponse response = new TTestTaskDetailResponse();
        if (StringUtils.isEmpty(taskId)) {
            response.setCode(ResultEnum.PARAMS_FAIL.getCode());
            response.setMessage("taskId不能为空");
            return response;
        }


        TTestTaskDetailResponse.Data data;
        try {
            if (StringUtils.equals(type, "task")) {
                TestTaskPo detail = testTaskService.getTestTaskDetailByTaskId(Long.valueOf(taskId));
                data = detail.trans2TData();
            } else if (StringUtils.equals(type, "subtask")) {
                TestSubTaskPo detail = testSubTaskService.getTestTaskDetailBySubTaskId(Long.valueOf(taskId));
                data = detail.trans2TData();
            } else {
                throw new Exception("type参数错误");
            }
            response.setCode(ResultEnum.SUCCESS.getCode());
            response.setMessage(ResultEnum.SUCCESS.getMsg());
            response.setData(data);
        } catch (Exception e) {
            log.error("TTestTaskServiceImpl.getTestTaskDetail error. taskId={} type={}", taskId, type, e);
            response.setCode(ResultEnum.SYS_ERROR.getCode());
            response.setMessage(ResultEnum.SYS_ERROR.getMsg());
        }
        return response;
    }

    @AddLog
    @Override
    public TTestTaskDetailResponse getTestTaskDetailByTaskName(@Required String taskName, @Required Long bizLineId) {
        TTestTaskDetailResponse response = new TTestTaskDetailResponse();
        if (StringUtils.isEmpty(taskName)) {
            response.setCode(ResultEnum.PARAMS_FAIL.getCode());
            response.setMessage("taskName不能为空");
            return response;
        }

        TTestTaskDetailResponse.Data data;
        try {
            TestTaskPo detail = testTaskService.getTestTaskDetailByTaskNameAndBizId(taskName,bizLineId);
            if (Objects.isNull(detail)) {
                response.setMessage("taskName不存在");
                return response;
            }
            data = detail.trans2TData();
            response.setCode(ResultEnum.SUCCESS.getCode());
            response.setMessage(ResultEnum.SUCCESS.getMsg());
            response.setData(data);
        } catch (Exception e) {
            log.error("TTestTaskServiceImpl.getTestTaskDetail error. taskName={}", taskName, e);
            response.setCode(ResultEnum.SYS_ERROR.getCode());
            response.setMessage(ResultEnum.SYS_ERROR.getMsg());
        }
        return response;
    }

    @AddLog
    @Override
    public TTestSubTaskListResponse getTestSubTaskList(TTestSubTaskListRequest request) {
        TTestSubTaskListResponse response = new TTestSubTaskListResponse();
        if (Objects.isNull(request) || request.getTestTaskId() <= 0) {
            response.setCode(ResultEnum.PARAMS_FAIL.getCode());
            response.setMessage("testTaskId不能为空");
            return response;
        }

        try {
            List<Map<String, String>> testSubTaskList = testSubTaskService.getTestSubTaskList(request.getTestTaskId());
            TPage<Map<String, String>> page = new TPage<>();
            page.setList(new ArrayList<>(testSubTaskList));
            response.setData(page);
            response.setCode(ResultEnum.SUCCESS.getCode());
            response.setMessage(ResultEnum.SUCCESS.getMsg());
        } catch (Exception e) {
            log.error("TTestTaskServiceImpl.getTestSubTaskList error.request={}", JSON.toJSONString(request), e);
            response.setCode(ResultEnum.SYS_ERROR.getCode());
            response.setMessage(ResultEnum.SYS_ERROR.getMsg());
        }
        return response;
    }

    @AddLog
    @Override
    public TRunTaskResponse runSubTask(String testSubTaskId) {
        TRunTaskResponse response = new TRunTaskResponse();
        if (StringUtils.isEmpty(testSubTaskId)) {
            response.setCode(ResultEnum.PARAMS_FAIL.getCode());
            response.setMessage("testSubTaskId不能为空");
            return response;
        }

        try {
            Pair<Boolean, String> pair = testSubTaskService.runSubTask(Long.valueOf(testSubTaskId));
            if (pair.getLeft()) {
                response.setCode(ResultEnum.SUCCESS.getCode());
                response.setMessage(ResultEnum.SUCCESS.getMsg());
                response.setData(ImmutableMap.of("link", pair.getRight()));
            } else {
                response.setCode(ResultEnum.SYS_ERROR.getCode());
                response.setMessage(pair.getRight());
            }
        } catch (Exception e) {
            log.error("TTestTaskServiceImpl.runSubTask error.request={}", testSubTaskId, e);
            response.setCode(ResultEnum.SYS_ERROR.getCode());
            response.setMessage(ResultEnum.SYS_ERROR.getMsg());
        }
        return response;
    }

    @AddLog
    @Override
    public TBaseResponse stopSubTask(String testSubTaskId) {
        TBaseResponse response = new TBaseResponse();
        if (StringUtils.isEmpty(testSubTaskId)) {
            response.setCode(ResultEnum.PARAMS_FAIL.getCode());
            response.setMessage("testSubTaskId不能为空");
            return response;
        }

        try {
            Pair<Boolean, String> pair = testSubTaskService.stopSubTask(Long.valueOf(testSubTaskId));
            if (pair.getLeft()) {
                response.setCode(ResultEnum.SUCCESS.getCode());
                response.setMessage(ResultEnum.SUCCESS.getMsg());
            } else {
                response.setCode(ResultEnum.SYS_ERROR.getCode());
                response.setMessage(pair.getRight());
            }
        } catch (Exception e) {
            log.error("TTestTaskServiceImpl.stopTask error.request={}", testSubTaskId, e);
            response.setCode(ResultEnum.SYS_ERROR.getCode());
            response.setMessage(ResultEnum.SYS_ERROR.getMsg());
        }
        return response;
    }

    @AddLog
    @Override
    public TBaseResponse doneSubTask(String testSubTaskId) {
        TBaseResponse response = new TBaseResponse();
        if (StringUtils.isEmpty(testSubTaskId)) {
            response.setCode(ResultEnum.PARAMS_FAIL.getCode());
            response.setMessage("testSubTaskId不能为空");
            return response;
        }

        try {
            Pair<Boolean, String> pair = testSubTaskService.doneSubTask(Long.valueOf(testSubTaskId));
            if (pair.getLeft()) {
                response.setCode(ResultEnum.SUCCESS.getCode());
                response.setMessage(ResultEnum.SUCCESS.getMsg());
            } else {
                response.setCode(ResultEnum.SYS_ERROR.getCode());
                response.setMessage(pair.getRight());
            }
        } catch (Exception e) {
            log.error("TTestTaskServiceImpl.stopTask error.request={}", testSubTaskId, e);
            response.setCode(ResultEnum.SYS_ERROR.getCode());
            response.setMessage(ResultEnum.SYS_ERROR.getMsg());
        }
        return response;
    }

    @AddLog
    @Override
    public TBaseResponse generateReporter(String testSubTaskId) {
        TBaseResponse response = new TBaseResponse();
        if (StringUtils.isEmpty(testSubTaskId)) {
            response.setCode(ResultEnum.PARAMS_FAIL.getCode());
            response.setMessage("testSubTaskId不能为空");
            return response;
        }
        try {
            Pair<Boolean, String> pair = testSubTaskService.generateReporter(Long.valueOf(testSubTaskId));
            if (pair.getLeft()) {
                response.setCode(ResultEnum.SUCCESS.getCode());
                response.setMessage("报告生成中");
            } else {
                response.setCode(ResultEnum.SYS_ERROR.getCode());
                response.setMessage(pair.getRight());
            }
        } catch (Exception e) {
            log.error("TTestTaskServiceImpl.generateReporter error.testSubTaskId={}", testSubTaskId, e);
            response.setCode(ResultEnum.SYS_ERROR.getCode());
            response.setMessage(e.getMessage());
        }
        return response;
    }

}
