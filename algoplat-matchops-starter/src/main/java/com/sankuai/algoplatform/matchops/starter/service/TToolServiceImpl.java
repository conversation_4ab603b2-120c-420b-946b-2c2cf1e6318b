package com.sankuai.algoplatform.matchops.starter.service;

import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServerExtConfig;
import com.sankuai.algoplatform.matchops.api.request.tool.TSaveCacheRequest;
import com.sankuai.algoplatform.matchops.api.response.TBaseResponse;
import com.sankuai.algoplatform.matchops.api.service.TToolService;
import com.sankuai.algoplatform.matchops.infrastructure.cache.TairClient;
import com.sankuai.algoplatform.matchops.infrastructure.config.LionConfig;
import com.sankuai.algoplatform.matchops.infrastructure.util.ContextUtil;
import com.sankuai.algoplatform.matchops.starter.aop.anno.AddLog;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.TimeUnit;

/**
 * 工具服务实现类
 *
 * <AUTHOR>
 * @date 2025/4/30
 */
@MdpThriftServer(port = 9001)
@MdpThriftServerExtConfig(enableAuthHandler = true)
public class TToolServiceImpl implements TToolService {

    @Autowired
    private TairClient tairClient;

    @AddLog
    @Override
    public TBaseResponse saveCache(TSaveCacheRequest request) {
        TBaseResponse response = new TBaseResponse();
        if (request == null || StringUtils.isBlank(request.getCacheKeyPrefix()) || StringUtils.isBlank(request.getValue())) {
            response.setCode(-1);
            response.setMessage("请求参数为空");
            return response;
        }

        // 获取当前登录用户的MIS
        String userMis = ContextUtil.getLoginUserMis();
        if (StringUtils.isBlank(userMis)) {
            response.setCode(-1);
            response.setMessage("用户未登录");
            return response;
        }

        // 构建缓存key，更新缓存
        String cacheKey = request.getCacheKeyPrefix() + "_" + userMis;
        boolean res = tairClient.put(cacheKey, request.getValue(), LionConfig.SAVE_CACHE_TIMEOUT, TimeUnit.MINUTES);
        response.setCode(res ? 0 : -1);
        response.setMessage(res ? "success" : "fail");
        return response;
    }
}