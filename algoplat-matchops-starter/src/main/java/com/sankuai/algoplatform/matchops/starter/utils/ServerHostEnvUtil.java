package com.sankuai.algoplatform.matchops.starter.utils;

import com.sankuai.algoplatform.matchops.domain.enums.EnvStatus;
import com.sankuai.inf.octo.mns.model.HostEnv;
import com.sankuai.inf.octo.mns.util.ProcessInfoUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

public class ServerHostEnvUtil {

    private static final HostEnv CURRENT_HOST_ENV = ProcessInfoUtil.getHostEnv();

    public static String currentEnvSLAndIp() {
        if (StringUtils.isNotBlank(swimLine())) {
            return currentEnv() + "-" + swimLine() + "@" + getLocalIpV4();
        }

        return currentEnv() + "@" + getLocalIpV4();
    }

    public static String currentEnvAndSL() {
        if (StringUtils.isNotBlank(swimLine())) {
            return currentEnv() + "-" + swimLine();
        }

        return currentEnv();
    }

    public static String currentEnv() {
        return CURRENT_HOST_ENV.name();
    }

    /**
     * @return 是否是线上环境
     */
    public static boolean isOnline() {
        return isProd() || isSt();
    }

    /**
     * @return 是否是线下环境
     */
    public static boolean isOffline() {
        return isTest() || isDev();
    }

    /**
     * @return 是否是prod环境
     */
    public static boolean isProd() {
        return CURRENT_HOST_ENV == HostEnv.PROD;
    }

    public static EnvStatus envJudge() {
        if (CURRENT_HOST_ENV == HostEnv.PROD) {
            return EnvStatus.PROD;
        } else if (CURRENT_HOST_ENV == HostEnv.STAGING) {
            return EnvStatus.ST;
        } else {
            return EnvStatus.TEST;
        }
    }


    /**
     * @return 是否是st环境
     */
    public static boolean isSt() {
        return CURRENT_HOST_ENV == HostEnv.STAGING;
    }

    /**
     * @return 是否是test环境
     */
    public static boolean isTest() {
        return CURRENT_HOST_ENV == HostEnv.TEST;
    }

    /**
     * @return 是否是dev环境
     */
    public static boolean isDev() {
        return CURRENT_HOST_ENV == HostEnv.DEV;
    }

    /**
     * @return 泳道
     */
    public static String swimLine() {
        return Optional.ofNullable(ProcessInfoUtil.getSwimlane()).orElse("");
    }

    /**
     * @return ipv4
     */
    public static String getLocalIpV4() {
        return ProcessInfoUtil.getLocalIpV4();
    }

}
