package com.sankuai.algoplatform.matchops.starter.controller;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.sankuai.aifree.thrift.generation.query.BatchingConfigQuery;
import com.sankuai.algoplatform.matchops.api.request.mcptool.TMcpTestMissionRequest;
import com.sankuai.algoplatform.matchops.api.request.testTask.LoadTestRequest;
import com.sankuai.algoplatform.matchops.api.response.mcptool.TMcpTestMissionResponse;
import com.sankuai.algoplatform.matchops.api.service.TMcpTestMissionService;
import com.sankuai.algoplatform.matchops.application.service.testtool.impl.MatchStrategyServiceImpl;
import com.sankuai.algoplatform.matchops.domain.model.OfflineTaskContext;
import com.sankuai.algoplatform.matchops.domain.model.OfflineTaskIncrData;
import com.sankuai.algoplatform.matchops.domain.service.testtool.BadcaseTestService;
import com.sankuai.algoplatform.matchops.domain.service.testtool.LoadTestService;
import com.sankuai.algoplatform.matchops.domain.service.testtool.impl.ReporterServiceImpl;
import com.sankuai.algoplatform.matchops.domain.service.testtool.reporter.CatDataFetch;
import com.sankuai.algoplatform.matchops.infrastructure.dal.blade.dao.OfflineTaskDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.blade.entity.OfflineTaskDetail;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.TestSubTaskDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.FridayModelDeployRequest;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ModelDeployRequest;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestSubTask;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.FridayDeployService;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.MLPDeployService;
import com.sankuai.algoplatform.matchops.infrastructure.util.CompressUtil;
import com.sankuai.algoplatform.matchops.starter.service.TXuechengServiceImpl;
import com.sankuai.ead.citadel.document.parser.DocumentParsingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.poi.ss.usermodel.*;
import org.mortbay.util.ajax.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("testtool/api")
public class TestController {

    @Autowired
    private CatDataFetch catDataFetch;

    @Autowired
    private TestSubTaskDao subTaskDao;

    @Autowired
    private TXuechengServiceImpl xuechengService;

    @Autowired
    private BadcaseTestService badcaseTestService;

    @Autowired
    private TMcpTestMissionService missionService;

    @Autowired
    private FridayDeployService fridayDeployService;

    @Autowired
    private MLPDeployService mlpDeployService;

    @Autowired
    private LoadTestService loadTestService;

    @Autowired
    private ReporterServiceImpl reporterService;

    @Autowired
    private MatchStrategyServiceImpl matchStrategyService;

    @PostMapping("/testMission")
    public TMcpTestMissionResponse testMisson(@RequestBody TMcpTestMissionRequest req) {
        if (StringUtils.equals(req.getTestType(), "5")) {
            return missionService.submitMcpLoadTestTask(req);
        } else {
            return missionService.submitMcpTestTask(req);
        }
    }

    @PostMapping("/getCatData")
    public String getCatData(String rule, Long subTaskId, int isMiddleRes) {
        TestSubTask testSubTask = subTaskDao.selectById(subTaskId);
        String[] txt = StringUtils.split(rule, "∫");
        return catDataFetch.query(txt, testSubTask, isMiddleRes != 0);
    }

    @PostMapping("/getResultData")
    public String getResultData(String rule, Long subTaskId, int isMiddleRes) throws DocumentParsingException {
        TestSubTask testSubTask = subTaskDao.selectById(subTaskId);
        String[] rules = StringUtils.split(rule, "∫");
        return reporterService.queryDataByType(rules, testSubTask, isMiddleRes != 0);
    }

//    @GetMapping("/getXuechengContent")
//    public String getGetXuechengContent(String link) {
//        TGetXuechengContentRequest request = new TGetXuechengContentRequest();
//        request.setLink(link);
//        TGetXuechengContentResponse content = xuechengService.getXuechengContent(request);
//        return content.getData();
//    }

    @PostMapping("/evaluateSetTest")
    public Map<String, String> test(@RequestBody JSONObject req) {
        try {
            return badcaseTestService.test(JSON.toString(req));
        } catch (Exception e) {
            Cat.logError(e);
            Map<String, String> map = new HashMap<>();
            map.put("code", "-1");
            map.put("message", String.valueOf(e.getMessage()));
            return map;
        }
    }


    /**
     * 部署模型
     *
     * @param requestBody 请求参数
     * @return 部署结果
     */
    @PostMapping("/friday/model")
    public ResponseEntity<Map<String, Object>> deployModel(@RequestBody Map<String, Object> requestBody) {
        log.info("接收到模型部署请求: {}", requestBody);
        Map<String, Object> response = new HashMap<>();

        try {
            // 从请求体中提取参数
            FridayModelDeployRequest deployRequest = FridayModelDeployRequest.builder()
                    .modelDeployName((String) requestBody.get("modelDeployName"))
                    .requiredInstanceCount((Integer) requestBody.get("requiredInstanceCount"))
                    .modelBaseName((String) requestBody.get("modelBaseName"))
                    .gpuType((String) requestBody.get("gpuType"))
                    //.requiredGpuCount((Integer) requestBody.get("requiredGpuCount"))
                    .gpuPerInstance((Integer) requestBody.get("gpuPerInstance"))
                    .trainMethod((String) requestBody.get("trainMethod"))
                    .trainingTaskId((String) requestBody.get("trainingTaskId"))
                    .description((String) requestBody.get("description"))
                    .groupName((String) requestBody.get("groupName"))
                    .misId((String) requestBody.get("misId"))
                    .modelParam((Map<String, String>) requestBody.get("modelParam"))
                    .build();


            // 调用服务进行模型部署
            boolean result = fridayDeployService.deployModel(deployRequest);

            // 构建响应
            response.put("success", result);
            response.put("message", result ? "模型部署成功" : "模型部署失败");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("模型部署过程中发生异常", e);
            response.put("success", false);
            response.put("message", "模型部署异常: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * MLP模型部署
     *
     * @param requestBody 请求参数
     * @return 部署结果
     */
    @PostMapping("/mlp/model")
    public ResponseEntity<Map<String, Object>> deployMlpModel(@RequestBody Map<String, Object> requestBody) {
        log.info("接收到MLP模型部署请求: {}", requestBody);
        Map<String, Object> response = new HashMap<>();

        try {
            // 处理 batchingConfigQuery
            BatchingConfigQuery batchingConfigQuery = null;
            Map<String, Object> batchingMap = (Map<String, Object>) requestBody.get("batchingConfigQuery");
            if (batchingMap != null) {
                batchingConfigQuery = new BatchingConfigQuery();
                batchingConfigQuery.setMaxBatchSize((Integer) batchingMap.get("maxBatchSize"));
                batchingConfigQuery.setBatchTimeoutMicros((Integer) batchingMap.get("batchTimeoutMicros"));
                batchingConfigQuery.setBatchingExtra((String) batchingMap.get("batchingExtra"));
            }

            // 处理 versionNums
            List<Long> versionNums = ((List<Integer>) requestBody.get("versionNums"))
                    .stream()
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
            // 从请求体中提取参数
            ModelDeployRequest deployRequest = ModelDeployRequest.builder()
                    .appkey((String) requestBody.get("appkey"))
                    .wxProject((String) requestBody.get("wxProject"))
                    .group((String) requestBody.get("group"))
                    //.queueName((String) requestBody.get("queueName"))
                    //.modelName((String) requestBody.get("modelName"))
                    .set((String) requestBody.get("set"))
                    .requireInstanceNum((Integer) requestBody.get("requireInstanceNum"))
                    .vcores((Integer) requestBody.get("vcores"))
                    .servingType((String) requestBody.get("servingType"))
                    //.versionNums((String) requestBody.get("versionNums"))
                    .memory((Integer) requestBody.get("memory"))
                    .gcores((Integer) requestBody.get("gcores"))
                    .gcoresType((String) requestBody.get("gcoresType"))
                    .optMis((String) requestBody.get("optMis"))
                    .serverImage((String) requestBody.get("serverImage"))
                    .env((String) requestBody.get("env"))
                    .rpcTimeout((Integer) requestBody.get("rpcTimeout"))
                    .batching((Boolean) requestBody.get("batching"))
                    .batchingConfigQuery(batchingConfigQuery)
                    .versionNums(versionNums)
                    //.models((List<Map<String, Object>>) requestBody.get("models"))
                    .build();


            // 调用服务进行模型部署
            Pair<Boolean, String> result = mlpDeployService.deployModel(deployRequest);

            // 构建响应
            response.put("success", result.getLeft());
            response.put("message", result.getRight());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("MLP模型部署过程中发生异常", e);
            response.put("success", false);
            response.put("message", "模型部署异常: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @PostMapping("/loadtest")
    public ResponseEntity<Map<String, Object>> runLoadTest(@RequestBody LoadTestRequest loadTestRequest) {
        log.info("接收到压测请求: {}", loadTestRequest);
        Map<String, Object> response = new HashMap<>();

        try {
            // 获取测试类型
            String testType = loadTestRequest.getModelType();
            if (testType == null) {
                throw new IllegalArgumentException("testType 不能为空，必须为 'tf' 或 'bge' 或 'llm'");
            }

            Map<String, Object> result;

            // 根据测试类型选择不同的测试方法
            switch (testType) {
                case "llm":
                    // Friday压测逻辑
                    String testDataUrl = loadTestRequest.getTestDataUrl();
                    Integer workers = loadTestRequest.getInitialWorkers();
                    String modelName = loadTestRequest.getModelName();

                    // 参数校验
                    if (StringUtils.isEmpty(testDataUrl)) {
                        throw new IllegalArgumentException("testDataUrl 不能为空");
                    }
                    if (workers == null || workers <= 0) {
                        workers = 1;
                    }

                    // 执行Friday压测
                    result = loadTestService.fridayRunLoadTest(testDataUrl, workers, modelName);
                    break;

                case "tf":
                case "bge":
                    // 执行万象模型压测
                    result = loadTestService.wanxiangRunTest(loadTestRequest);
                    break;

                default:
                    throw new IllegalArgumentException("不支持的测试类型: " + testType + "，只支持 'friday' 或 'wanxiang'");
            }

            // 构建响应
            response.put("code", 0);
            response.put("data", result);
            return ResponseEntity.ok(response);

        } catch (IllegalArgumentException e) {
            log.error("压测参数错误: {}", e.getMessage());
            response.put("success", false);
            response.put("message", "参数错误: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            log.error("压测执行异常", e);
            response.put("success", false);
            response.put("message", "压测执行失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @PostMapping("/getGpuMetrics")
    public String getGpuMetrics(@RequestBody LoadTestRequest request) {
        return loadTestService.getGpuMetrics(request, "");
    }


    @PostMapping("/mcm")
    public String testMcm(@RequestBody JSONObject req) {
        Long matchStrategyId = req.getLong("matchStrategyId");
        Integer type = req.getInteger("type");
        matchStrategyService.testLion(matchStrategyId, type);
        return "test";
    }
    
    @Resource
    private OfflineTaskDao offlineTaskDao;
    @ResponseBody
    @RequestMapping(value = "/runOfflineTask", method = RequestMethod.POST)
    public Object runOfflineTask(@RequestParam("file") MultipartFile file,
                                 @RequestParam("key") String key,
                                 @RequestParam("partitionDate") String partitionDate,
                                 @RequestParam("saveDb") Boolean saveDb,
                                 @RequestParam("saveCache") Boolean saveCache) {
        String value = Lion.getConfigRepository().get(key);
        if (file.isEmpty()) {
            return "文件为空";
        }

        List<String> dataList = new ArrayList<>();
        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = WorkbookFactory.create(inputStream)) {
            Sheet sheet = workbook.getSheetAt(0);
            for (Row row : sheet) {
                StringBuilder rowData = new StringBuilder();
                for (Cell cell : row) {
                    switch (cell.getCellType()) {
                        case STRING:
                            rowData.append(cell.getStringCellValue());
                            break;
                        case NUMERIC:
                            rowData.append(cell.getNumericCellValue());
                            break;
                        case BOOLEAN:
                            rowData.append(cell.getBooleanCellValue());
                            break;
                        default:
                            rowData.append("");
                    }
                    rowData.append("\t");
                }
                dataList.add(rowData.toString().trim());
            }
        } catch (Exception e) {
            log.error("Excel文件解析失败", e);
            return "Excel文件解析失败: " + e.getMessage();
        }
        OfflineTaskContext context = OfflineTaskContext.from(key, value);
        context.getOfflineTaskConfig().getIncrDataHandleConfig().getStoreResultConfig().setDoStore(saveCache);
        context.getOfflineTaskConfig().getIncrDataHandleConfig().getStoreResultConfig().setExpireTime(-1L);
        context.setPartitionDate(partitionDate);
        List<Pair<String, String>> result = new ArrayList<>();
        for(String uniqueKey : dataList){
            OfflineTaskDetail detail = new OfflineTaskDetail();
            detail.setUniqueKey(uniqueKey);
            detail.setPartitionDate(partitionDate);
            List<OfflineTaskDetail> offlineTaskDetails = offlineTaskDao.selectByCondition(detail, null, 1, context.getIndustryType(), context.getOfflineTaskName());
            if(CollectionUtils.isEmpty(offlineTaskDetails)){
                continue;
            }
            OfflineTaskDetail offlineTaskDetail = offlineTaskDetails.get(0);
            String compressedResult = OfflineTaskIncrData.from(context, offlineTaskDetail).handle();
            offlineTaskDetail.markAsIncrCompleted();
            offlineTaskDetail.setResult(compressedResult);
            if(saveDb){
                offlineTaskDao.updateBatchByUniqueKey(Lists.newArrayList(offlineTaskDetail), context.getPartitionDate(), context.getIndustryType(), context.getOfflineTaskName());
            }
            result.add(Pair.of(uniqueKey, StringUtils.isEmpty(compressedResult)? "": CompressUtil.decompress(compressedResult, "gzip")));
        }
        return result;

    }
}
