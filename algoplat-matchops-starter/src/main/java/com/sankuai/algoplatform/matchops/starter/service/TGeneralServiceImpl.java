package com.sankuai.algoplatform.matchops.starter.service;

import com.alibaba.fastjson.JSON;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServerExtConfig;
import com.sankuai.algoplatform.matchops.api.enums.ResultEnum;
import com.sankuai.algoplatform.matchops.api.request.general.TDropdownRequest;
import com.sankuai.algoplatform.matchops.api.response.general.TAppResponse;
import com.sankuai.algoplatform.matchops.api.response.general.TDropdownResponse;
import com.sankuai.algoplatform.matchops.api.response.general.TUploadSignResponse;
import com.sankuai.algoplatform.matchops.api.service.TGeneralService;
import com.sankuai.algoplatform.matchops.application.service.testtool.*;
import com.sankuai.algoplatform.matchops.application.service.testtool.TestReporterTemplateService;
import com.sankuai.algoplatform.matchops.infrastructure.model.OctoNode;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.OctoNodeService;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.S3Service;
import com.sankuai.algoplatform.matchops.starter.aop.anno.AddLog;
import com.sankuai.inf.octo.mns.model.HostEnv;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@MdpThriftServer(port = 9001)
@MdpThriftServerExtConfig(enableAuthHandler = true)
@Slf4j
public class TGeneralServiceImpl implements TGeneralService {
    @Autowired
    private MatchStrategyService matchStrategyService;

    @Autowired
    private BizLineService bizLineService;

    @Autowired
    private AlgoPackageService algoPackageService;

    @Autowired
    private ResourceGroupService resourceGroupService;

    @Autowired
    private PromptStrategyService promptService;

    @Autowired
    private TestReporterTemplateService testReporterTemplateService;

    @Autowired
    private TestSceneService testSceneService;

    @Autowired
    private OctoNodeService octoNodeService;

    @Autowired
    private CookieService cookieService;

    @Autowired
    private S3Service s3Service;

    @AddLog
    @Override
    public TDropdownResponse getDropdown(TDropdownRequest request) {
        TDropdownResponse response = new TDropdownResponse();
        try {
            if (request.getType() == null) {
                response.setCode(-1);
                response.setMessage("参数错误");
                return response;
            }
            Integer type = Integer.valueOf(request.getType());
            List<Map<String, String>> data = new ArrayList<>();
            switch (type) {
                case 1:
                    data = bizLineService.getBizLineList(request.getBizLineId());
                    break;
                case 2:
                case 3:
                    data = algoPackageService.getDropdown(request.getType(), request.getBizLineId(), request.getEnv());
                    break;
                case 4:
                    data = promptService.getDropdown(request.getType(), request.getBizLineId(), request.getEnv());
                    break;
                case 5:
                case 6:
                case 8:
                    data = matchStrategyService.getDropdown(request.getType(), request.getBizLineId(), request.getEnv());
                    break;
                case 9:
                    data = resourceGroupService.getDropdown(request.getType(), request.getBizLineId());
                    break;
                case 10:
                    data = testReporterTemplateService.getDropdown(request.getType(), request.getBizLineId());
                    break;
                case 7:
                    data = testSceneService.getDropdown(request.getType(), request.getBizLineId());
                    break;
            }
            try {
                cookieService.addCookieConfig();
            } catch (Exception e) {
                log.error("更新cookie失败", e);
                response.setCode(-1);
                response.setMessage("系统异常");
            }
            response.setCode(0);
            response.setMessage("success");
            response.setData(data);
        } catch (Exception e) {
            log.error("TGeneralServiceImpl.getDropdown error request is {}", JSON.toJSONString(request), e);
            response.setCode(ResultEnum.SYS_ERROR.getCode());
            response.setMessage(ResultEnum.SYS_ERROR.getMsg());
        }
        return response;
    }

    @AddLog
    @Override
    public TAppResponse getApps(String keyword) {
        TAppResponse response = new TAppResponse();
        try {
            List<String> appKeys = octoNodeService.getAppKeys(keyword);
            if (CollectionUtils.isNotEmpty(appKeys)) {
                List<String> data = appKeys.stream()
                        .limit(20)
                        .collect(Collectors.toList());
                response.setData(data);
            }
            response.setCode(ResultEnum.SUCCESS.getCode());
            response.setMessage(ResultEnum.SUCCESS.getMsg());
        } catch (Exception e) {
            log.error("TGeneralServiceImpl.getApps error. keyword={}", keyword, e);
            response.setCode(ResultEnum.SYS_ERROR.getCode());
            response.setMessage(ResultEnum.SYS_ERROR.getMsg());
        }
        return response;
    }

    @AddLog
    @Override
    public TAppResponse getCells(String appkey) {
        TAppResponse response = new TAppResponse();
        if (StringUtils.isEmpty(appkey)) {
            response.setCode(ResultEnum.PARAMS_FAIL.getCode());
            response.setMessage("appkey不能为空");
            return response;
        }
        try {
            List<OctoNode> octoNodeStatus = octoNodeService.getOctoNodeStatus(appkey, null, HostEnv.STAGING, null);

            if (CollectionUtils.isNotEmpty(octoNodeStatus)) {
                List<String> data = octoNodeStatus.stream()
                        .map(l -> StringUtils.isEmpty(l.getCell()) ? "default" : l.getCell())
                        .distinct().collect(Collectors.toList());
                response.setData(data);
            }
            response.setCode(ResultEnum.SUCCESS.getCode());
            response.setMessage(ResultEnum.SUCCESS.getMsg());
        } catch (Exception e) {
            log.error("TGeneralServiceImpl.getCells error. appkey={}", appkey, e);
            response.setCode(ResultEnum.SYS_ERROR.getCode());
            response.setMessage(ResultEnum.SYS_ERROR.getMsg());
        }
        return response;
    }

    @AddLog
    @Override
    public TUploadSignResponse getUploadSign(String fileName) {
        TUploadSignResponse response = new TUploadSignResponse();
        try {
            Map<String, String> data = s3Service.getNormalUploadSign(fileName);
            response.setCode(ResultEnum.SUCCESS.getCode());
            response.setMessage(ResultEnum.SUCCESS.getMsg());
            response.setData(data);
        } catch (Exception e) {
            log.error("TGeneralServiceImpl.getUploadSign error. fileName={}", fileName, e);
            response.setCode(ResultEnum.SYS_ERROR.getCode());
            response.setMessage(ResultEnum.SYS_ERROR.getMsg());
        }
        return response;
    }


}
