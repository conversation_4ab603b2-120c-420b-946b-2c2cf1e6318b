package com.sankuai.algoplatform.matchops.starter.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.google.common.reflect.TypeToken;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServerExtConfig;
import com.meituan.mtrace.Tracer;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.algoplatform.matchops.api.request.matchStrategy.TAddEditMatchStrategyRequest;
import com.sankuai.algoplatform.matchops.api.request.matchStrategy.TQueryMatchStrategyRequest;
import com.sankuai.algoplatform.matchops.api.request.mcptool.*;
import com.sankuai.algoplatform.matchops.api.request.resourceGroup.TAddEditResourceGroupRequest;
import com.sankuai.algoplatform.matchops.api.request.testTask.LoadTestRequest;
import com.sankuai.algoplatform.matchops.api.request.testTask.TAddOrEditTestTaskRequest;
import com.sankuai.algoplatform.matchops.api.request.testTask.TRunTaskRequest;
import com.sankuai.algoplatform.matchops.api.response.matchStrategy.TAddMatchStrategyResponse;
import com.sankuai.algoplatform.matchops.api.response.matchStrategy.TQueryMatchStrategyResponse;
import com.sankuai.algoplatform.matchops.api.response.mcptool.TMcpTestMissionResponse;
import com.sankuai.algoplatform.matchops.api.response.resourceGroup.TEditResourceGroupResponse;
import com.sankuai.algoplatform.matchops.api.response.resourceGroup.TTestTaskDetailResponse;
import com.sankuai.algoplatform.matchops.api.response.testTask.TAddOrEditTestTaskResponse;
import com.sankuai.algoplatform.matchops.api.service.TMatchStrategyService;
import com.sankuai.algoplatform.matchops.api.service.TMcpTestMissionService;
import com.sankuai.algoplatform.matchops.api.service.TPromptStrategyService;
import com.sankuai.algoplatform.matchops.api.service.TTestTaskService;
import com.sankuai.algoplatform.matchops.application.model.testtool.TestTaskPo;
import com.sankuai.algoplatform.matchops.application.model.testtool.matchstrategy.LlmModelConfig;
import com.sankuai.algoplatform.matchops.application.model.testtool.matchstrategy.PredictorCache;
import com.sankuai.algoplatform.matchops.application.model.testtool.resourcegroup.FridayResource;
import com.sankuai.algoplatform.matchops.application.model.testtool.resourcegroup.MlpResource;
import com.sankuai.algoplatform.matchops.application.model.testtool.resourcegroup.OctoResource;
import com.sankuai.algoplatform.matchops.application.model.testtool.resourcegroup.ResourceGroupPo;
import com.sankuai.algoplatform.matchops.application.service.testtool.*;
import com.sankuai.algoplatform.matchops.domain.constant.TestToolConstants;
import com.sankuai.algoplatform.matchops.domain.enums.IndustryTypeEnum;
import com.sankuai.algoplatform.matchops.domain.enums.MatchStrategyStatusEnum;
import com.sankuai.algoplatform.matchops.domain.enums.ReqPostTypeEnum;
import com.sankuai.algoplatform.matchops.domain.enums.TestTaskStatusEnum;
import com.sankuai.algoplatform.matchops.domain.model.testtool.DFridayModelParam;
import com.sankuai.algoplatform.matchops.domain.repository.ResourcePoolRepository;
import com.sankuai.algoplatform.matchops.domain.service.testtool.EnvPrepareService;
import com.sankuai.algoplatform.matchops.infrastructure.config.LionConfig;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.ResourceGroupDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.TestTaskDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.*;
import com.sankuai.algoplatform.matchops.infrastructure.exceptions.TMcpTestMissionException;
import com.sankuai.algoplatform.matchops.infrastructure.model.*;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.DxService;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.MLPDeployService;
import com.sankuai.algoplatform.matchops.infrastructure.util.ParserUtil;
import com.sankuai.algoplatform.matchops.starter.aop.anno.AddLog;
import com.sankuai.algoplatform.matchops.starter.utils.ServerHostEnvUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import static com.sankuai.algoplatform.matchops.domain.constant.TestToolConstants.SYSTEM_OPS_MIS_ID_LIST;

/**
 * @program: algoplat-matchops
 * <AUTHOR>
 * @Date 2025/5/23
 */
@MdpThriftServer(port = 9001)
@MdpThriftServerExtConfig(enableAuthHandler = true)
@Slf4j
@Validated
public class TMcpTestMissionServiceImpl implements TMcpTestMissionService {

    @Autowired
    private DxService dxService;

    @Value("${lion.link}")
    private String LION_LINK;

    private static final long MAX_POLL_TIME = 3 * 60 * 60 * 1000L; // 3小时
    private static final long POLL_INTERVAL = 5 * 60 * 1000L; // 5分钟

    private static final Long TEST_WITHOUT_PROMPT_TEMPLATE = 9999L;

    private static final Long PROD_WITHOUT_PROMPT_TEMPLATE = 18L;

    @Autowired
    private TPromptStrategyService tPromptStrategyService;

    private static final String PREDICTOR_APP_KEY = "com.sankuai.algoplatform.predictor";

    private static final String LION_GROUP = "default";

    private static final String DEFAULT_SET = "default";

    private static final String FAST_FOOD_SET = "gray-release-fast-food";

    @Autowired
    private AlgoPackageService algoPackageService;

    @Autowired
    private PromptStrategyService promptStrategyService;

    @Autowired
    private TMatchStrategyService tMatchStrategyService;

    @Autowired
    private ResourceGroupService resourceGroupService;

    @Autowired
    private TResourceGroupServiceImpl tResourceGroupService;


    @Autowired
    private TTestTaskService tTestTaskService;

    @Autowired
    private TestSceneService testSceneService;

    @Autowired
    private TestSubTaskService testSubTaskService;

    @Autowired
    private TestTaskService testTaskService;

    @Autowired
    private TestTaskDao testTaskDao;

    @Resource
    private ResourcePoolRepository resourcePoolRepository;


    @Autowired
    private ResourceGroupDao resourceGroupDao;

    @Resource
    private EnvPrepareService envPrepareService;

    @Resource
    private MLPDeployService mLPDeployService;

    private static final ExecutorService threadPool = TraceExecutors.getTraceExecutorService(Rhino.newThreadPool("testToolThreadPool", DefaultThreadPoolProperties.Setter().withCoreSize(10).withMaxSize(10).withMaxQueueSize(100)).getExecutor());


    @AddLog
    @Override
    public TMcpTestMissionResponse submitMcpTestTask(TMcpTestMissionRequest request) {
        TMcpTestMissionResponse response = new TMcpTestMissionResponse();
        response.setCode(-1);
        response.setMessage("系统异常");
        try {
            log.info("submitMcpTestTask,req:{}", JSONObject.toJSONString(request));
            Tracer.putContext("userMis", request.getMisId());

            handleTestType(request);

            checkRequestParamsVaild(request);

            // 查询测试场景BizCode
            BusinessMetaData businessMetaData = questTestSceneBizCode(request);

            // 更新算法包或Prompt信息，当前逻辑是只更新不新增，若算法包不存在则报错，并根据报错类型通过大象推送给用户或运维人员。
            updateAlgoPackageInfo(request, businessMetaData);

            // 处理策略组
            Long matchStrategyId = processStrategyGroupInfo(request, businessMetaData);

            // 更新资源组
            Long resourceGroupId = updateResourceGroup(request, businessMetaData);

            // 检查任务是否存在
            Long testTaskId = editOrAddTestTask(request, businessMetaData, matchStrategyId, resourceGroupId);

            // 防止出现主从延迟，这里等待1秒
            Thread.sleep(1000l);
            executeTestTask(request, businessMetaData, testTaskId);

            response.setCode(0);
            response.setMessage("成功");
            Map<String, String> extraInfo = request.getExtraInfo();
            extraInfo.put("sessionId", request.getSessionId());
            response.setData(ImmutableMap.of("taskId", testTaskId.toString()));
            response.setExtra(extraInfo);
            log.info("submitMcpTestTask success!sessionId:{} result:{} ", request.getSessionId(), testTaskId);
            return response;
        } catch (Exception e) {
            response.setCode(-1);
            response.setMessage("任务执行失败!" + "sessionId为" + request.getSessionId() + "报错信息" + e.getMessage());
            Cat.logError(e);
            log.error("submitMcpTestTask failed!sessionId:{}", request.getSessionId(), e);
            return response;
        }
    }

    @AddLog
    public TMcpTestMissionResponse queryMcpTestTaskByMissionId(Long taskId) {
        TMcpTestMissionResponse response = new TMcpTestMissionResponse();
        response.setCode(-1);
        response.setMessage("系统异常");
        try {
            log.info("queryMcpTestTaskByMissionId,missionId:{}", taskId);
            List<Map<String, String>> testSubTaskList = testSubTaskService.getTestSubTaskList(taskId);
            if (CollectionUtils.isEmpty(testSubTaskList)) {
                response.setMessage("未查询到测试任务信息");
                return response;
            }
            List<TestSubTaskContext> testSubTaskContexts = new ArrayList<>();
            for (Map<String, String> subTaskInfo : testSubTaskList) {
                TestSubTaskContext testSubTaskContext = new TestSubTaskContext();
                Integer statusCode = Integer.valueOf(subTaskInfo.get("statusCode"));
                String statusName = subTaskInfo.get("statusName");
                testSubTaskContext.setStatus(statusCode);
                testSubTaskContext.setStatusName(statusName);
                Type type = new TypeToken<List<TestSubTaskContext.Reporter>>() {
                }.getType();
                List<TestSubTaskContext.Reporter> reporters = JSON.parseObject(subTaskInfo.get("reporters"), type);
                String taskResultMessage = subTaskInfo.get("taskResult");
                testSubTaskContext.setReporters(reporters);
                testSubTaskContext.setTaskResultMessage(taskResultMessage);
                testSubTaskContext.setTaskId(taskId.toString());
                testSubTaskContext.setMissionId(subTaskInfo.get(TestToolConstants.MISSION_ID));
                testSubTaskContext.setExtra(subTaskInfo);
                testSubTaskContexts.add(testSubTaskContext);
            }
            response.setCode(0);
            response.setMessage("成功");
            response.setData(ImmutableMap.of("taskId", taskId.toString(), "testSubTaskContext", JSON.toJSONString(testSubTaskContexts)));
            return response;
        } catch (Exception e) {
            response.setCode(-1);
            response.setMessage(e.getMessage());
            Cat.logError(e);
            log.error("queryMcpTestTaskByMissionId failed!missionId:{}", taskId, e);
            return response;
        }
    }

    @Override
    public TMcpTestMissionResponse submitMcpLoadTestTask(TMcpTestMissionRequest request) {
        TMcpTestMissionResponse response = new TMcpTestMissionResponse();
        response.setCode(-1);
        response.setMessage("系统异常");
        try {
            log.info("submitMcpTestTask,req:{}", JSONObject.toJSONString(request));
            Tracer.putContext("userMis", request.getMisId());

            checkLoadTestParamsVaild(request);

            // 查询元数据
            BusinessMetaData businessMetaData = questTestSceneBizCode(request);

            // 处理策略组
            Long matchStrategyId = processStrategyGroupInfo(request, businessMetaData);

            // 更新压测资源组
            Long resourceGroupId = updateLoadTestResourceGroup(request, businessMetaData);

            // 检查任务是否存在
            Long testTaskId = editOrAddTestTask(request, businessMetaData, matchStrategyId, resourceGroupId);

            executeTestTask(request, businessMetaData, testTaskId);

            response.setCode(0);
            response.setMessage("成功");
            Map<String, String> extraInfo = request.getExtraInfo();
            extraInfo.put("sessionId", request.getSessionId());
            response.setData(ImmutableMap.of("taskId", testTaskId.toString()));
            response.setExtra(extraInfo);
            log.info("submitMcpTestTask success!sessionId:{} result:{} ", request.getSessionId(), testTaskId);
            return response;
        } catch (Exception e) {
            response.setCode(-1);
            response.setMessage("任务执行失败!" + "sessionId为" + request.getSessionId() + "报错信息" + e.getMessage());
            Cat.logError(e);
            log.error("submitMcpTestTask failed!sessionId:{}", request.getSessionId(), e);
            return response;
        }
    }

    @AddLog
    public void executeTestTask(TMcpTestMissionRequest request, BusinessMetaData businessMetaData, Long testTaskId) throws TMcpTestMissionException {
        Tracer.putContext("userMis", request.getMisId());
        TestScene testScene = getSceneIdByBuAndTestScene(request, businessMetaData);
        ReqPostTypeEnum typeEnum = ReqPostTypeEnum.getByCode(testScene.getReqPostType());
        IndustryTypeEnum industryTypeEnum = IndustryTypeEnum.getByCode(request.getIndustryType());

        TRunTaskRequest tRunTaskRequest = new TRunTaskRequest();
        tRunTaskRequest.setTestTaskId(testTaskId);
        String cleanCache = request.getPredictorClearCache() ? "1" : "0";
        tRunTaskRequest.setClearCache(cleanCache);
        tRunTaskRequest.setMissionId(request.getExtraInfo().get("missionId"));
        if (typeEnum == ReqPostTypeEnum.XT) {
            Map<String, String> executeParams = new HashMap<>();
            executeParams.put(TestToolConstants.CD, request.getTestDataDate());
            executeParams.put(TestToolConstants.VERSION, request.getPostXtVersion());
            if (request.getParseXtVersion() != null) {
                executeParams.put(TestToolConstants.PARSE_VERSION, request.getParseXtVersion());
            }
            String dt = convertCD(request.getTestDataDate());
            if (dt != null) {
                executeParams.put(TestToolConstants.DT, dt);
            }
            if (request.getHour() != null) {
                executeParams.put(TestToolConstants.HOUR, request.getHour());
            }
            tRunTaskRequest.setParam(ParserUtil.mapToCmdArgs(executeParams));
        } else {
            //模型压测
            if (request.getTestType().equals("5")) {
                LoadTestRequest testRequest = buildLoadTestRequest(request);
                String param = String.format("-requestBody %s -timeout 3600", JSON.toJSONString(testRequest));
                tRunTaskRequest.setParam(param);//requestBody or sqlParam
            }
            //badcase率测试
            else if (request.getTestType().equals("1")) {
                Map<String, String> requestBody = initHttpRequestBody(request);
                tRunTaskRequest.setFilePath(request.getTestDataSet());
                switch (industryTypeEnum) {
                    case DAOCAN:
                        String bizCode = businessMetaData.getAlgoBizCodeList().get(0).getAlgoBizCode();
                        requestBody.put("bizCode", bizCode);
                        requestBody.put("partitionDate", request.getTestDataDate());
                        break;
                    case ZHUSU:
                        requestBody.put("strategyName", businessMetaData.getZsStrategyName());
                }
                String param = String.format("-requestBody %s -timeout 3600", JSON.toJSONString(requestBody));
                tRunTaskRequest.setParam(param);//requestBody or sqlParam
            }
        }

        executeTestTask(testTaskId, tRunTaskRequest);
    }

    private Map<String, String> initHttpRequestBody(TMcpTestMissionRequest request) {
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("bu", request.getBu());
        requestBody.put("industryType", request.getIndustryType());
        requestBody.put("matchStandard", request.getMatchStandard());
        return requestBody;
    }

    private void executeTestTask(Long testTaskId, TRunTaskRequest tRunTaskRequest) throws TMcpTestMissionException {

        TestTask task = testTaskDao.selectById(testTaskId);
        if (task != null && task.getStatus() != null) {
            TestTaskStatusEnum statusEnum = TestTaskStatusEnum.getByCode(task.getStatus());
            if (statusEnum == TestTaskStatusEnum.TESTING) {
                throw new TMcpTestMissionException("当前会话存在正在测试中的任务，请稍后再发起测试，testTaskId:" + testTaskId);
            }
        }
        threadPool.execute(() -> {
            try {
                Boolean status = pollTestTaskStatus(testTaskId);
                if (status) {
                    tTestTaskService.runTask(tRunTaskRequest);
                } else {
                    log.error("轮询任务状态失败, testTaskId: {}", testTaskId);
                }
            } catch (Exception e) {
                log.error("执行测试任务失败, testTaskId: {}", testTaskId, e);
            }
        });
    }

    private Boolean pollTestTaskStatus(Long testTaskId) {
        long startTime = System.currentTimeMillis();

        while (System.currentTimeMillis() - startTime < MAX_POLL_TIME) {
            TestTaskStatusEnum status = checkTestResourceStatus(testTaskId);
            // 如果状态是待测试或失败,则结束轮询
            if (status == TestTaskStatusEnum.TOBE_TEST || status == TestTaskStatusEnum.ENV_FAIL) {
                return true;
            }
            try {
                Thread.sleep(POLL_INTERVAL);
            } catch (InterruptedException e) {
                log.error("轮询任务被中断, testTaskId: {}", testTaskId, e);
                Thread.currentThread().interrupt();
                return false;
            }
        }
        // 超时处理
        log.error("轮询任务超时, testTaskId: {}", testTaskId);
        try {
            testTaskDao.updateTestTaskStatus(testTaskId, TestTaskStatusEnum.ENV_FAIL.getCode());
            dxService.sendMsg2Users("环境准备超时!", Lists.newArrayList(testTaskDao.selectById(testTaskId).getOwner()));

        } catch (Exception e) {
            log.error("更新超时任务状态失败, testTaskId: {}", testTaskId, e);
        }
        return false;
    }

    private TestTaskStatusEnum checkTestResourceStatus(Long testTaskId) {
        try {
            TestTask testTask = testTaskDao.selectById(testTaskId);//强读主
            if (testTask.getResourceGroupId() != null && testTask.getResourceGroupId() > 0) {
                ResourceGroup resourceGroup = resourceGroupDao.selectById(testTask.getResourceGroupId());
                ResourceGroupPo resourceGroupPo = new ResourceGroupPo(resourceGroup);
                boolean ready = envPrepareService.queryEnvReady(resourceGroupPo.trans(), testTask.getOwner());
                if (!ready) {
                    testTaskDao.updateTestTaskStatus(testTaskId, TestTaskStatusEnum.ENV_FAIL.getCode());
                    //todo 大象通知
                    dxService.sendMsg2Users("环境未就绪!任务ID=" + testTaskId, Lists.newArrayList(testTask.getOwner()));
                    log.info("环境未就绪，任务ID: {}", testTaskId);
                    return TestTaskStatusEnum.PREPARE_ENV_ING;
                } else {
                    // 更新任务状态为待测试
                    testTaskDao.updateTestTaskStatus(testTaskId, TestTaskStatusEnum.TOBE_TEST.getCode());
                    log.info("环境已就绪，任务状态更新为待测试，任务ID: {}", testTaskId);
                    // 这里可以添加执行测试的逻辑
                    return TestTaskStatusEnum.TOBE_TEST;
                }
            } else {
                // 如果没有资源组ID，直接设置为待测试状态
                testTaskDao.updateTestTaskStatus(testTaskId, TestTaskStatusEnum.TOBE_TEST.getCode());
                log.info("任务无需环境准备，直接设置为待测试状态，任务ID: {}", testTaskId);
                // 这里可以添加执行测试的逻辑
                return TestTaskStatusEnum.TOBE_TEST;
            }
        } catch (Exception e) {
            log.error("executeTestTask error, testTaskId: {}", testTaskId, e);
            // 更新任务状态为失败
            try {
                testTaskDao.updateTestTaskStatus(testTaskId, TestTaskStatusEnum.ENV_FAIL.getCode());
                return TestTaskStatusEnum.ENV_FAIL;
            } catch (Exception ex) {
                log.error("更新任务状态失败, testTaskId: {}", testTaskId, ex);
            }
            return TestTaskStatusEnum.ENV_FAIL;
        }

    }

    private String convertCD(String testDataDate) {
        if (StringUtils.isBlank(testDataDate)) {
            return null;
        }
        return testDataDate.replaceAll("-", "");
    }

    @AddLog
    public Long editOrAddTestTask(TMcpTestMissionRequest request, BusinessMetaData businessMetaData, Long matchStrategyId, Long resourceGroupId) throws TMcpTestMissionException {
        String taskName = buildStrategyOrTaskName(request);
        TestTaskPo detail = testTaskService.getTestTaskDetailByTaskNameAndBizId(taskName, Long.valueOf(request.getIndustryType()));
        Long taskId = null;
        TAddOrEditTestTaskRequest tAddOrEditTestTaskRequest = new TAddOrEditTestTaskRequest();
        if (detail != null) {
            //执行更新操作
            taskId = detail.getId();
            tAddOrEditTestTaskRequest.setTestTaskId(taskId);
            tAddOrEditTestTaskRequest.setResourceGroupId(resourceGroupId);
            tAddOrEditTestTaskRequest.setStatus(TestTaskStatusEnum.INIT.getCode());
            BusinessMetaData.TestSceneInfo testSceneInfo = getTestSceneInfo(request, businessMetaData);
            tAddOrEditTestTaskRequest.setTestTemplates(testSceneInfo.getReporterTemplateIds());
            tAddOrEditTestTaskRequest.setMatchStrategyId(matchStrategyId);
            TAddOrEditTestTaskResponse tAddOrEditTestTaskResponse = tTestTaskService.editTestTask(tAddOrEditTestTaskRequest);
            if (tAddOrEditTestTaskResponse.getCode() != 0) {
                TMcpTestMissionException tMcpTestMissionException = new TMcpTestMissionException(tAddOrEditTestTaskResponse.getMessage());
                Cat.logError(tMcpTestMissionException);
                log.error("update test task failed,sessionId:{}", request.getSessionId(), tMcpTestMissionException);
                throw tMcpTestMissionException;
            }
            return taskId;
        } else {
            // 新增测试任务
            BusinessMetaData.TestSceneInfo testSceneInfo = getTestSceneInfo(request, businessMetaData);
            tAddOrEditTestTaskRequest.setExtra(ImmutableMap.of("request", JSON.toJSONString(request)));
            tAddOrEditTestTaskRequest.setTestTemplates(testSceneInfo.getReporterTemplateIds());
            tAddOrEditTestTaskRequest.setTestTaskName(taskName);
            tAddOrEditTestTaskRequest.setTestTaskId(null);
            tAddOrEditTestTaskRequest.setResourceGroupId(resourceGroupId);
            tAddOrEditTestTaskRequest.setMatchStrategyId(matchStrategyId);
            tAddOrEditTestTaskRequest.setBizLineId(Long.valueOf(request.getIndustryType()));
            TAddOrEditTestTaskResponse tAddOrEditTestTaskResponse = tTestTaskService.addTestTask(tAddOrEditTestTaskRequest);
            if (tAddOrEditTestTaskResponse.getCode() != 0) {
                TMcpTestMissionException tMcpTestMissionException = new TMcpTestMissionException(tAddOrEditTestTaskResponse.getMessage());
                Cat.logError(tMcpTestMissionException);
                log.error("add test task failed,sessionId:{}", request.getSessionId(), tMcpTestMissionException);
                throw tMcpTestMissionException;
            }
            taskId = Long.valueOf(tAddOrEditTestTaskResponse.getData().get("id"));
            return taskId;
        }
    }

    private BusinessMetaData.TestSceneInfo getTestSceneInfo(TMcpTestMissionRequest request, BusinessMetaData businessMetaData) {
        return Optional.ofNullable(businessMetaData).map(BusinessMetaData::getTestSceneInfo).filter(
                list -> !list.isEmpty()).flatMap(testSceneInfos ->
                testSceneInfos.stream().filter(testScene ->
                        (testScene.getChannel() == null || Objects.equals(testScene.getChannel(), request.getTestScene())) &&
                                Objects.equals(testScene.getTestType(), request.getTestType())
                ).findFirst()).orElse(null);
    }

    @AddLog
    private Long updateResourceGroup(TMcpTestMissionRequest request, BusinessMetaData businessMetaData) throws TMcpTestMissionException {
//        TAddEditResourceGroupRequest tAddEditResourceGroupRequest = new TAddEditResourceGroupRequest();
//        tAddEditResourceGroupRequest.setResourceGroupId(Long.valueOf(testSceneInfo.getResourceGroupId()));
//        tAddEditResourceGroupRequest.setBizLineId(Long.valueOf(request.getIndustryType()));

        BusinessMetaData.TestSceneInfo testSceneInfo = getTestSceneInfo(request, businessMetaData);
        Long resourceGroupId = Long.valueOf(testSceneInfo.getResourceGroupId());
        Long bizLineId = Long.valueOf(request.getIndustryType());
        ResourceGroup resourceGroup = resourceGroupService.getResourceGroup(bizLineId, resourceGroupId);

        if (Objects.isNull(resourceGroup)) {
            dxService.sendMsg2Users("资源组为空", SYSTEM_OPS_MIS_ID_LIST);
            throw new TMcpTestMissionException("元数据配置的资源组为空");
        }

        List<AlgoPackageInfo> algoPackageInfos = request.getAlgoPackageInfos();

        // 更新mlp模型资源组
        List<Map<String, String>> mlpResourceListToUpdate = new ArrayList<>();
        ResourceConfig resourceConfigInfo = businessMetaData.getResourceConfigInfo();
        List<ResourceConfig.ModelConfig> mlp = resourceConfigInfo.getMlp();
        if (!CollectionUtils.isEmpty(mlp) && !CollectionUtils.isEmpty(algoPackageInfos)) {
            for (AlgoPackageInfo algoPackageInfo : algoPackageInfos) {
                List<ModelDetail> models = algoPackageInfo.getModels();
                for (ModelDetail model : models) {
                    buildMlpResourceInstanceInfo(mlpResourceListToUpdate, model, resourceConfigInfo, request.getMisId());
                }
            }
        }

        // 更新大模型资源组
        List<LlmDetail> llmDetails = request.getLlmInfos();
        List<Map<String, String>> fridayResourceListToUpdate = new ArrayList<>();
        for (LlmDetail llmDetail : llmDetails) {
            String modelName = llmDetail.getModelName();
            FridayResource fridayResource = new FridayResource();
            fridayResource.setModelName(modelName);
            fridayResource.setInstanceNum(Integer.valueOf(llmDetail.getInstanceNum()));
            fridayResource.setGpuPerInstance("1");
            fridayResource.setBatchSize(llmDetail.getBatchSize());
            fridayResource.setUseTemplate(BooleanUtils.isTrue(llmDetail.getUseTemplate()));
            fridayResource.setPromptTemplate(llmDetail.getPromptTemplate());
            fridayResource.setModelBaseName(llmDetail.getModelBaseName());
            fridayResource.setTrainMethod(llmDetail.getTrainMethod());
//            fridayResource.setTrainingTaskId();
            modelParam modelParam = llmDetail.getModelParam();
            if (modelParam != null) {
                fridayResource.setModelParam(new DFridayModelParam(modelParam.getTopP(), modelParam.getMaxTokens(), modelParam.getPromptPrefix(),
                        modelParam.getTopK(), modelParam.getPromptSuffix(), modelParam.getTemperature(),
                        modelParam.getMaxSeqLength()));
            }
            fridayResourceListToUpdate.add(JSONObject.parseObject(JSONObject.toJSONString(fridayResource), Map.class));
        }

        //更新OCTO资源组（predict服务）
        List<CpuInfo> cpuInfos = request.getCpuInfos();
        List<Map<String, String>> octoResourceListToUpdate = new ArrayList<>();
        for (CpuInfo cpuInfo : cpuInfos) {
            String set = null;
            Integer instanceNum = cpuInfo.getInstanceNum();
            String secondCateName = cpuInfo.getSecondCateName();
            List<ResourceConfig.ServiceConfig> predictor = resourceConfigInfo.getPredictor();
            for (ResourceConfig.ServiceConfig serviceConfig : predictor) {
                if (serviceConfig.getCate().equals(secondCateName)) {
                    set = serviceConfig.getSet();
                    break;
                }
            }
            if (set == null) {
                throw new TMcpTestMissionException("未找到对应set");
            }
            OctoResource octoResource = new OctoResource();
            octoResource.setAppkey(PREDICTOR_APP_KEY);
            octoResource.setSet(set);
            octoResource.setRetainTime("1");
            octoResource.setInstanceNum(String.valueOf(instanceNum));
            octoResourceListToUpdate.add(JSONObject.parseObject(JSONObject.toJSONString(octoResource), Map.class));
        }
        resourceGroup.setOctoResources(JSONObject.toJSONString(octoResourceListToUpdate));
        resourceGroup.setCreator(request.getMisId());
        resourceGroup.setUpdateTime(new Date());

        TAddEditResourceGroupRequest tAddEditResourceGroupRequest = new TAddEditResourceGroupRequest();
        tAddEditResourceGroupRequest.setResourceGroupId(resourceGroup.getId());
        tAddEditResourceGroupRequest.setBizLineId(Long.valueOf(request.getIndustryType()));
        tAddEditResourceGroupRequest.setMlpResourceInfo(mlpResourceListToUpdate);
        tAddEditResourceGroupRequest.setLlmResourceInfo(fridayResourceListToUpdate);
        tAddEditResourceGroupRequest.setCpuResourceInfo(octoResourceListToUpdate);

        TEditResourceGroupResponse tEditResourceGroupResponse = tResourceGroupService.editResourceGroupList(tAddEditResourceGroupRequest);
        if (tEditResourceGroupResponse.getCode() != 0) {
            String msg = "更新资源组时发生错误！sessionId:" + request.getSessionId() + "报错信息:" + tEditResourceGroupResponse.getMessage();
            log.error(msg);
            TMcpTestMissionException tMcpTestMissionException = new TMcpTestMissionException(msg);
            Cat.logError(tMcpTestMissionException);
            throw tMcpTestMissionException;
        }

        return resourceGroupId;
    }

    private List<Map<String, String>> buildMlpResourceInstanceInfo(List<Map<String, String>> mlpResourceListToUpdate, ModelDetail modelDetail, ResourceConfig resourceConfigInfo, String misId) throws TMcpTestMissionException {
        List<ResourceConfig.ModelConfig> mlp = resourceConfigInfo.getMlp();
        String modelType = modelDetail.getModelType();
        String modelName = modelDetail.getName();
        String instanceNum = modelDetail.getInstanceNum();
        String modelVersion = modelDetail.getModelVersion();
        for (ResourceConfig.ModelConfig modelConfig : mlp) {
            if (modelConfig.getModelType().equals(modelType)) {
                List<ResourceConfig.GroupConfig> group = modelConfig.getGroup();
                for (ResourceConfig.GroupConfig groupConfig : group) {

                    MlpResource mlpResource = new MlpResource();
                    mlpResource.setModelName(modelName);
                    mlpResource.setInstanceNum(instanceNum);
                    mlpResource.setModelVersion(modelVersion);
                    mlpResource.setModelType(modelType);
                    mlpResource.setAppkey(modelConfig.getAppkey());
                    mlpResource.setRetainTime("1");
                    mlpResource.setWxProject(modelConfig.getWxProject());
                    InstanceInfo instanceInfo = new InstanceInfo();
                    instanceInfo.setServerImage(groupConfig.getServerImage());
                    instanceInfo.setBatching(groupConfig.isBatching());
                    instanceInfo.setBatchingConfig(groupConfig.getBatchingConfig());
                    instanceInfo.setEnv(groupConfig.getEnv());
                    instanceInfo.setSet(groupConfig.getSet());
                    instanceInfo.setRpcOutTime(groupConfig.getRpcOutTime());
                    instanceInfo.setResourceConfig(groupConfig.getResourceConfig());
                    instanceInfo.setGroupName(groupConfig.getGroupName());

                    List<ResourcePoolConfig> resourcePoolConfigs1 = resourcePoolRepository.queryResourcePoolConfigByResourceType(instanceInfo.getResourceConfig().getGcoresType());
                    if (CollectionUtils.isEmpty(resourcePoolConfigs1)) {
                        String msg = String.format("未找到%s资源池", modelConfig.getModelType());
                        dxService.sendMsg2Users(msg, Lists.newArrayList(misId));
                        throw new TMcpTestMissionException("未找到对应资源池");
                    }
                    String queue = null;
                    List<String> queueNames = new ArrayList<>();
                    for (ResourcePoolConfig resourcePoolConfig : resourcePoolConfigs1) {
                        queue = resourcePoolConfig.getQueue();
                        if (StringUtils.isNotEmpty(queue)) {
                            queueNames.add(queue);
                        }
                    }
                    if (StringUtils.isEmpty(queue)) {
                        throw new TMcpTestMissionException("未找到部署队列");
                    }
                    instanceInfo.setQuqueName(queueNames);
                    mlpResource.setInstanceInfo(instanceInfo);
                    Map<String, String> map = JSONObject.parseObject(JSONObject.toJSONString(mlpResource), Map.class);
                    mlpResourceListToUpdate.add(map);
                }
            }
        }
        return mlpResourceListToUpdate;
    }


    @AddLog
    private Long processStrategyGroupInfo(TMcpTestMissionRequest request, BusinessMetaData businessMetaData) throws TMcpTestMissionException {
        Long matchStrategyId = null;
        try {
            String matchingStrategyName = buildStrategyOrTaskName(request);
            TQueryMatchStrategyRequest tQueryMatchStrategyRequest = new TQueryMatchStrategyRequest();
            tQueryMatchStrategyRequest.setMatchStrategyName(matchingStrategyName);
            TQueryMatchStrategyResponse tQueryMatchStrategyResponse = tMatchStrategyService.queryMatchStrategyDetail(tQueryMatchStrategyRequest);

            //检查匹配策略是否已经存在
            if (tQueryMatchStrategyResponse.getCode() == 0 && MapUtils.isNotEmpty(tQueryMatchStrategyResponse.getData())) {
                matchStrategyId = Long.valueOf(tQueryMatchStrategyResponse.getData().get("matchStrategyId"));
                Map<String, String> strategyMap = tQueryMatchStrategyResponse.getData();
                Integer statusCode = Integer.valueOf(strategyMap.get("statusCode"));
                //若已存在则更新匹配策略组,且当前的状态支持更新
                if (MatchStrategyStatusEnum.matchStrategyCanUpdate(statusCode)) {
                    //性能压测无需更新策略信息
                    if (StringUtils.equals(request.getTestType(), "5")) {
                        return matchStrategyId;
                    }
                    TAddEditMatchStrategyRequest tAddEditMatchStrategyRequest = buildTAddEditMatchStrategyUpdateRequest(strategyMap, request, matchingStrategyName, businessMetaData);
                    tMatchStrategyService.editMatchStrategy(tAddEditMatchStrategyRequest);
                }
            } else {
                //不存在则创建策略组
                TAddEditMatchStrategyRequest tAddEditMatchStrategyRequest = buildTAddMatchStrategyRequest(request, matchingStrategyName, businessMetaData);
                TAddMatchStrategyResponse tAddMatchStrategyResponse = tMatchStrategyService.addMatchStrategy(tAddEditMatchStrategyRequest);
                if (tAddMatchStrategyResponse.getCode() != 0) {
                    String msg = "创建匹配策略组时发生错误！sessionId:" + request.getSessionId() + "报错信息:" + tAddMatchStrategyResponse.getMessage();
                    log.error(msg);
                    TMcpTestMissionException tMcpTestMissionException = new TMcpTestMissionException(msg);
                    Cat.logError(tMcpTestMissionException);
                    throw tMcpTestMissionException;
                }
                Map<String, String> data = tAddMatchStrategyResponse.getData();
                matchStrategyId = Long.valueOf(data.get("id"));
            }
            return matchStrategyId;
        } catch (Exception e) {
            if (matchStrategyId != null) {
                log.error("匹配策略更新或创建失败！sessionID:{}", request.getSessionId());
                tMatchStrategyService.updateMatchStrategyStatus(matchStrategyId, 2);
            }
            throw e;
        }
    }


    private TAddEditMatchStrategyRequest buildTAddMatchStrategyRequest(TMcpTestMissionRequest request, String matchingStrategyName, BusinessMetaData businessMetaData) throws TMcpTestMissionException {
        TAddEditMatchStrategyRequest tAddEditMatchStrategyRequest = new TAddEditMatchStrategyRequest();
        tAddEditMatchStrategyRequest.setMatchStrategyName(matchingStrategyName);
        tAddEditMatchStrategyRequest.setMatchStrategyId(null);
        tAddEditMatchStrategyRequest.setBizLineId(Long.valueOf(request.getIndustryType()));
        TestScene testScene = getSceneIdByBuAndTestScene(request, businessMetaData);
        tAddEditMatchStrategyRequest.setSceneId(testScene.getId());
        String testType = request.getTestType();

        List<Map<String, String>> octoInfoValue = Lists.newArrayList(Maps.newHashMap());
        tAddEditMatchStrategyRequest.setOctoInfo(octoInfoValue);

        //模型压测无匹配策略信息，切缓存全关
        if (StringUtils.equals(testType, "5")) {
            tAddEditMatchStrategyRequest.setAlgoCodeInfo(new ArrayList<>());
            tAddEditMatchStrategyRequest.setPredictorCache(new HashMap<>());
            tAddEditMatchStrategyRequest.setLlmInfo(new ArrayList<>());
            return tAddEditMatchStrategyRequest;
        }

        List<Map<String, String>> algoCodeInfos = new ArrayList<>();
        List<AlgoBizCode> algoBizCodeList = businessMetaData.getAlgoBizCodeList();
        if (CollectionUtils.isNotEmpty(algoBizCodeList)) {
            algoBizCodeList.forEach(algoBizCode -> {
                Map<String, String> algoCodeInfo = JSON.parseObject(JSON.toJSONString(algoBizCode), Map.class);
                algoCodeInfos.add(algoCodeInfo);
            });
        }
        tAddEditMatchStrategyRequest.setAlgoCodeInfo(algoCodeInfos);
        List<Map<String, String>> llmInfoList = new ArrayList<>();
        List<LlmBizCode> llmBizCodeList = businessMetaData.getLlmBizCodeList();
        if (CollectionUtils.isNotEmpty(llmBizCodeList)) {
            llmBizCodeList.forEach(llmBizCode -> {
                Map<String, String> llmInfo = JSON.parseObject(JSON.toJSONString(llmBizCode), Map.class);
                llmInfoList.add(llmInfo);
            });
        }
        tAddEditMatchStrategyRequest.setLlmInfo(llmInfoList);
        PredictorCache predictorCache = new PredictorCache();
        predictorCache.setSetName(DEFAULT_SET);
        predictorCache.setCache1(true);
        predictorCache.setCache2(true);
        predictorCache.setCache3(true);
        predictorCache.setCache4(true);
        TTestTaskDetailResponse.Data.PredictorCache predictorCacheRes = predictorCache.trans2TData();
        Map<String, String> predictorCacheMap = JSON.parseObject(JSON.toJSONString(predictorCacheRes), Map.class);
        tAddEditMatchStrategyRequest.setPredictorCache(predictorCacheMap);

        return tAddEditMatchStrategyRequest;

    }

    public TestScene getSceneIdByBuAndTestScene(TMcpTestMissionRequest request, BusinessMetaData businessMetaData) throws TMcpTestMissionException {
        if (!CollectionUtils.isEmpty(businessMetaData.getTestSceneInfo())) {
            List<BusinessMetaData.TestSceneInfo> testSceneInfo = businessMetaData.getTestSceneInfo();
            BusinessMetaData.TestSceneInfo info = testSceneInfo.stream().filter(testScene -> {
                if (StringUtils.isNotEmpty(testScene.getChannel())) {
                    return StringUtils.equals(testScene.getChannel(), request.getTestScene()) && StringUtils.equals(testScene.getTestType(), request.getTestType());
                }
                return StringUtils.equals(testScene.getTestType(), request.getTestType());
            }).findFirst().get();
            return testSceneService.queryTestSceneByIdAndBizLineId(Long.valueOf(info.getSceneId()));
        }
        String msg = String.format("测试场景不存在,行业类型：%s, bu:%s，匹配标准%s，测试场景：%s ", request.getIndustryType(), request.getBu(), request.getMatchStandard(), request.getTestScene());
        dxService.sendMsg2Users(msg, SYSTEM_OPS_MIS_ID_LIST);
        log.error(msg);
        throw new TMcpTestMissionException(msg);
    }

    private String getTestSceneCnName(String testSceneCode) {
        String testSceneCnName = null;
        if (testSceneCode.equals("1")) {
            testSceneCnName = "货架*货架";
        } else if (testSceneCode.equals("2")) {
            testSceneCnName = "货架*直播";
        } else if (testSceneCode.equals("3")) {
            testSceneCnName = "直播*直播";
        } else if (testSceneCode.equals("4")) {
            testSceneCnName = "直播*货架";
        }
        return testSceneCnName;
    }

    private String getTestTypeCnName(String type) {
        String testTypeCnName = null;
        if (type.equals("1")) {
            testTypeCnName = "业务回测（badcase率测试）";
        } else if (type.equals("2")) {
            testTypeCnName = "一致性测试";
        } else if (type.equals("3")) {
            testTypeCnName = "GSB测试";
        } else if (type.equals("4")) {
            testTypeCnName = "整体性能压测";
        } else if (type.equals("5")) {
            testTypeCnName = "模型压测";
        }
        return testTypeCnName;
    }

    private TAddEditMatchStrategyRequest buildTAddEditMatchStrategyUpdateRequest(Map<String, String> oriStrategyMap, TMcpTestMissionRequest request, String matchingStrategyName, BusinessMetaData businessMetaData) throws TMcpTestMissionException {
        TAddEditMatchStrategyRequest tAddEditMatchStrategyRequest = new TAddEditMatchStrategyRequest();
        tAddEditMatchStrategyRequest.setMatchStrategyId(Long.valueOf(oriStrategyMap.get("matchStrategyId")));
        tAddEditMatchStrategyRequest.setMatchStrategyName(matchingStrategyName);
        if (!request.getIndustryType().equals(businessMetaData.getIndustryType())) {
            throw new TMcpTestMissionException("业务线不匹配!");
        }
        TestScene testScene = getSceneIdByBuAndTestScene(request, businessMetaData);
        tAddEditMatchStrategyRequest.setSceneId(testScene.getId());
        List<Map<String, String>> algoCodeInfos = new ArrayList<>();
        List<AlgoBizCode> algoBizCodeList = businessMetaData.getAlgoBizCodeList();
        if (CollectionUtils.isNotEmpty(algoBizCodeList)) {
            algoBizCodeList.forEach(algoBizCode -> {
                Map<String, String> algoCodeInfo = JSON.parseObject(JSON.toJSONString(algoBizCode), Map.class);
                algoCodeInfos.add(algoCodeInfo);
            });
        }
        tAddEditMatchStrategyRequest.setAlgoCodeInfo(algoCodeInfos);
        String octoInfoJsonArray = oriStrategyMap.get("octoInfo");
        List<Map<String, String>> octoInfoList = new ArrayList<>();
        Type type = new TypeToken<List<Map<String, String>>>() {
        }.getType();
        if (!StringUtils.isBlank(octoInfoJsonArray)) {
            octoInfoList = JSON.parseObject(octoInfoJsonArray, type);
        }
        tAddEditMatchStrategyRequest.setOctoInfo(octoInfoList);

        List<Map<String, String>> llmInfoList = new ArrayList<>();
        String llmInfoJsonArray = oriStrategyMap.get("llmInfo");
        if (!StringUtils.isBlank(llmInfoJsonArray)) {
            llmInfoList = JSON.parseObject(llmInfoJsonArray, type);
        }
        tAddEditMatchStrategyRequest.setLlmInfo(llmInfoList);
        String predictorCacheJson = oriStrategyMap.get("predictorCache");
        Map<String, String> predictorCacheMap = JSONObject.parseObject(predictorCacheJson, Map.class);
        predictorCacheMap.put("setName", DEFAULT_SET);
        tAddEditMatchStrategyRequest.setPredictorCache(predictorCacheMap);
        return tAddEditMatchStrategyRequest;

    }


    private String buildStrategyOrTaskName(TMcpTestMissionRequest request) {
        // 匹配策略名称构建格式：testType_sessionId
        if (StringUtils.isBlank(request.getSessionId())) {
            return null;
        }
        String testTypeCnName = getTestTypeCnName(request.getTestType());
        StringBuffer sb = new StringBuffer();
        sb.append(Optional.ofNullable(testTypeCnName).orElse("")).append("_").append(Optional.ofNullable(request.getSessionId()).orElse(""));
        return sb.toString();
    }

    @AddLog
    public void updateAlgoPackageInfo(TMcpTestMissionRequest request, BusinessMetaData businessMetaData) throws TMcpTestMissionException {
        //大模型的配置和小模型的配置不能同时为空

        if (CollectionUtils.isEmpty(request.getAlgoPackageInfos()) && CollectionUtils.isEmpty(request.getLlmInfos()) && !request.getTestType().equals("1")) {
            log.info("算法包信息为空, 无需更新");
            throw new TMcpTestMissionException("算法包信息和大模型信息同时为空");
        }
        try {
            // 更新算法包信息
            if (CollectionUtils.isNotEmpty(request.getAlgoPackageInfos())) {
                String commitId = request.getAlgoPackageInfos().get(0).getCommitId();
                if (StringUtils.isNotBlank(commitId)) {
                    List<AlgoBizCode> algoBizCodeCfgList = businessMetaData.getAlgoBizCodeList();
                    List<String> bizCodes = algoBizCodeCfgList.stream().map(AlgoBizCode::getAlgoBizCode).collect(Collectors.toList());
                    List<Pair<BizStrategy, List<AlgoStrategyPackagePo>>> algoCodeAllInfo = algoPackageService.getAlgoCodeAllInfo(bizCodes);
                    validateAlgoCodeInfo(algoCodeAllInfo, bizCodes);
                    for (Pair<BizStrategy, List<AlgoStrategyPackagePo>> bizStrategyPair : algoCodeAllInfo) {
                        for (AlgoStrategyPackagePo algoStrategyPackagePo : bizStrategyPair.getRight()) {
                            List<AlgoStrategy> algoStrategyList = algoStrategyPackagePo.getStrategyList();
                            List<AlgoPackage> algoPackageList = algoStrategyPackagePo.getPackageList();
                            algoStrategyList.forEach(algoStrategy -> {
                                algoStrategy.setUpdateTime(new Date());
                                algoStrategy.setOwner(request.getMisId());
                            });
                            algoPackageList.forEach(algoPackage -> {
                                algoPackage.setVersion(commitId);
                                algoPackage.setUpdateTime(new Date());
                                algoPackage.setOwnerMis(request.getMisId());
                            });
                            algoPackageService.editAlgoStrategy(algoStrategyList, algoPackageList);
                        }
                    }
                }
            }

            // 更新大模型相关内容,如模版信息和模型参数
            List<LlmDetail> llmDetails = request.getLlmInfos();
            if (!CollectionUtils.isEmpty(llmDetails)) {
                List<LlmBizCode> llmBizCodeCfgList = businessMetaData.getLlmBizCodeList();
                if (llmBizCodeCfgList.isEmpty()) {
                    throw new TMcpTestMissionException("在更新大模型配置时发生失败，请检查LlmBizCode配置");
                }
                for (LlmBizCode bizCodeInfo : llmBizCodeCfgList) {
                    String llmBizCode = bizCodeInfo.getLlmBizCode();
                    BizLlmpredictConfig bizLlmpredictConfig = promptStrategyService.selectBizLlmpredictConfig(llmBizCode);
                    if (bizLlmpredictConfig == null) {
                        String msg = "在更新大模型配置时发生失败,未找到大模型配置,请检查大模型配置! llmBizCode:" + llmBizCode;
                        dxService.sendMsg2Users(msg, SYSTEM_OPS_MIS_ID_LIST);
                        throw new TMcpTestMissionException(msg);
                    }
                    String modelConfig = bizLlmpredictConfig.getModelConfig();
                    LlmModelConfig llmModelConfig = JSONObject.parseObject(modelConfig, LlmModelConfig.class);
                    String modelName = llmModelConfig.getModelName();
                    for (LlmDetail llmDetail : llmDetails) {
                        if (modelName.equals(llmDetail.getModelName())) {
                            updateLargeModelConfigInfo(llmDetail, request.getMisId(), bizLlmpredictConfig, llmModelConfig);
                        }
                    }
                }
            }
        } catch (Exception e) {
            String msg = "在更新大模型配置时发生失败" + e.getMessage();
            Cat.logError(msg, e);
            log.error(e.getMessage());
            throw new TMcpTestMissionException("更新算法包配置时发生异常:" + e.getMessage());
        }

    }

    private void updateLargeModelConfigInfo(LlmDetail llmDetail, String misId, BizLlmpredictConfig bizLlmpredictConfig, LlmModelConfig llmModelConfig) throws TMcpTestMissionException {
        Long promptTemplateId = bizLlmpredictConfig.getPromptTemplateId();
        BizLlmpredictPromptTemplate bizLlmpredictPromptTemplate = promptStrategyService.selectBizLlmpredictPromptTemplate(promptTemplateId);
        if (bizLlmpredictPromptTemplate == null) {
            String msg = "在更新大模型配置时发生失败，未找到promptTemplateId:" + promptTemplateId + "对应的prompt信息，请检查PromptTemplate配置!";
            dxService.sendMsg2Users(msg, SYSTEM_OPS_MIS_ID_LIST);
            throw new TMcpTestMissionException(msg);
        }
        if (llmDetail.getUseTemplate()) {
            bizLlmpredictPromptTemplate.setPromptTemplate(llmDetail.getPromptTemplate());
        } else if (ServerHostEnvUtil.isTest()) {
            promptTemplateId = TEST_WITHOUT_PROMPT_TEMPLATE;
        } else {
            promptTemplateId = PROD_WITHOUT_PROMPT_TEMPLATE;
        }
        bizLlmpredictPromptTemplate.setStatus(Boolean.TRUE);
        bizLlmpredictPromptTemplate.setOwner(misId);
        bizLlmpredictPromptTemplate.setUpdateTime(new Date());
        //更新prompt信息
        modelParam modelParam = llmDetail.getModelParam();
        //设置模型配置
        LlmModelConfig.ModelParms oriModelParams = new LlmModelConfig.ModelParms();
        oriModelParams.setMaxTokens(Integer.valueOf(modelParam.getMaxTokens()));
        oriModelParams.setTemperature(Double.valueOf(modelParam.getTemperature()));
        oriModelParams.setTopP(Double.valueOf(modelParam.getTopP()));
        oriModelParams.setTopK(Integer.valueOf(modelParam.getTopK()));
        llmModelConfig.setModelParms(oriModelParams);
        llmModelConfig.setUseTemplate(llmDetail.getUseTemplate() ? 1 : 0);
        llmModelConfig.setBatchSize(llmDetail.getBatchSize());
        bizLlmpredictConfig.setModelConfig(JSON.toJSONString(llmModelConfig));
        bizLlmpredictConfig.setOwner(misId);
        bizLlmpredictConfig.setBizLineId(Long.valueOf(bizLlmpredictConfig.getBizLineId()));
        bizLlmpredictConfig.setPromptTemplateId(promptTemplateId);
        bizLlmpredictConfig.setUpdateTime(new Date());
        promptStrategyService.editPromptStrategy(bizLlmpredictConfig, bizLlmpredictPromptTemplate);
    }


    private void validateAlgoCodeInfo(List<Pair<BizStrategy, List<AlgoStrategyPackagePo>>> algoCodeInfo, List<String> bizCodes) throws TMcpTestMissionException {
        // 验证算法包信息完整性
        List<String> algoBizCodes = algoCodeInfo.stream().map(pair -> pair.getLeft().getBizCode()).collect(Collectors.toList());
        if (!algoBizCodes.containsAll(bizCodes)) {
            throw new TMcpTestMissionException("算法包信息为空");
        }
        boolean valid = true;
        // 验证业务策略完整性
        for (Pair<BizStrategy, List<AlgoStrategyPackagePo>> pair : algoCodeInfo) {
            if (CollectionUtils.isEmpty(pair.getRight())) {
                valid = false;
                break;
            }
            if (!pair.getRight().stream().allMatch(AlgoStrategyPackagePo::isValid)) {
                valid = false;
                break;
            }
        }
        if (!valid) {
            dxService.sendMsg2Users("BizCode:" + bizCodes + "算法包信息不全,请检查策略相关的配置:" + LION_LINK, SYSTEM_OPS_MIS_ID_LIST);
            throw new TMcpTestMissionException("算法包信息不全");
        }
    }

    public BusinessMetaData questTestSceneBizCode(TMcpTestMissionRequest request) throws TMcpTestMissionException {
        List<BusinessMetaData> businessMetaDatas = StringUtils.isBlank(LionConfig.BUSINESS_METADATA) ? Lists.newArrayList() : JSONObject.parseObject(LionConfig.BUSINESS_METADATA, new TypeToken<List<BusinessMetaData>>() {
        }.getType());
        if (CollectionUtils.isEmpty(businessMetaDatas)) {
            dxService.sendMsg2Users("业务线元数据为空,请检查Lion配置:" + LION_LINK, SYSTEM_OPS_MIS_ID_LIST);
            throw new TMcpTestMissionException("业务线元数据为空");
        }
        for (BusinessMetaData businessMetadatum : businessMetaDatas) {
            if (businessMetadatum == null || businessMetadatum.getBu() == null || businessMetadatum.getIndustryType() == null || businessMetadatum.getMatchStandard() == null) {
                continue;
            }
            if (businessMetadatum.getBu().equals(request.getBu()) && businessMetadatum.getIndustryType().equals(request.getIndustryType()) && businessMetadatum.getMatchStandard().equals(request.getMatchStandard())) {
//                if (CollectionUtils.isEmpty(businessMetadatum.getAlgoBizCodeList()) && CollectionUtils.isEmpty(businessMetadatum.getLlmBizCodeList())) {
//                    dxService.sendMsg2Users("业务线元数据异常,请检查Lion配置:" + LION_LINK, SYSTEM_OPS_MIS_ID_LIST);
//                    throw new TMcpTestMissionException("业务线元数据异常");
//                }
                return businessMetadatum;
            }
        }
        dxService.sendMsg2Users("测试场景不存在,请检查Lion配置:" + LION_LINK, Lists.newArrayList(request.getMisId()));
        throw new TMcpTestMissionException("测试场景不存在");
    }

    private BusinessMetaData questBusinessMetaData(TMcpTestMissionRequest request) throws TMcpTestMissionException {
        List<BusinessMetaData> businessMetaDatas = StringUtils.isBlank(LionConfig.BUSINESS_METADATA) ? Lists.newArrayList() : JSONObject.parseObject(LionConfig.BUSINESS_METADATA, new TypeToken<List<BusinessMetaData>>() {
        }.getType());
        if (CollectionUtils.isEmpty(businessMetaDatas)) {
            dxService.sendMsg2Users("业务线元数据为空,请检查Lion配置:" + LION_LINK, SYSTEM_OPS_MIS_ID_LIST);
            throw new TMcpTestMissionException("业务线元数据为空");
        }
        for (BusinessMetaData businessMetadatum : businessMetaDatas) {
            if (businessMetadatum == null || businessMetadatum.getBu() == null || businessMetadatum.getIndustryType() == null || businessMetadatum.getMatchStandard() == null) {
                continue;
            }
            if (businessMetadatum.getBu().equals(request.getBu()) && businessMetadatum.getIndustryType().equals(request.getIndustryType()) && businessMetadatum.getMatchStandard().equals(request.getMatchStandard())) {
//                if (CollectionUtils.isEmpty(businessMetadatum.getAlgoBizCodeList()) && CollectionUtils.isEmpty(businessMetadatum.getLlmBizCodeList())) {
//                    dxService.sendMsg2Users("业务线元数据异常,请检查Lion配置:" + LION_LINK, SYSTEM_OPS_MIS_ID_LIST);
//                    throw new TMcpTestMissionException("业务线元数据异常");
//                }
                return businessMetadatum;
            }
        }
        dxService.sendMsg2Users("测试场景不存在,请检查Lion配置:" + LION_LINK, Lists.newArrayList(request.getMisId()));
        throw new TMcpTestMissionException("测试场景不存在");
    }


    @AddLog
    private Long updateLoadTestResourceGroup(TMcpTestMissionRequest request, BusinessMetaData businessMetaData) throws TMcpTestMissionException {
        BusinessMetaData.TestSceneInfo testSceneInfo = getTestSceneInfo(request, businessMetaData);
        Long resourceGroupId = Long.valueOf(testSceneInfo.getResourceGroupId());
        Long bizLineId = Long.valueOf(request.getIndustryType());
        ResourceGroup resourceGroup = resourceGroupService.getResourceGroup(bizLineId, resourceGroupId);

        if (Objects.isNull(resourceGroup)) {
            dxService.sendMsg2Users("资源组为空", SYSTEM_OPS_MIS_ID_LIST);
            throw new TMcpTestMissionException("元数据配置的资源组为空");
        }

        String modelType = request.getModelType();
        ResourceConfig resourceConfigInfo = businessMetaData.getResourceConfigInfo();
        List<ResourceConfig.ModelConfig> mlp = resourceConfigInfo.getMlp();

        if (StringUtils.equalsIgnoreCase(modelType, "tf") || StringUtils.equalsIgnoreCase(modelType, "bge")) {
            if (CollectionUtils.isEmpty(mlp)) {
                dxService.sendMsg2Users("资源组为空", SYSTEM_OPS_MIS_ID_LIST);
                throw new TMcpTestMissionException("元数据配置的mlp信息为空");
            }
            //更新MLP资源组信息
            ModelDetail modelDetail = new ModelDetail();
            modelDetail.setModelType(modelType);
            modelDetail.setName(request.getModelName());
            modelDetail.setInstanceNum(request.getModelName());
            modelDetail.setModelVersion(request.getModelVersion());
            modelDetail.setInstanceNum(request.getInstanceNum());

            List<Map<String, String>> mlpResourceListToUpdate = new ArrayList<>();
            buildMlpResourceInstanceInfo(mlpResourceListToUpdate, modelDetail, resourceConfigInfo, request.getMisId());
            resourceGroup.setMlpResources(JSON.toJSONString(mlpResourceListToUpdate));
        }


        if (StringUtils.equalsIgnoreCase(modelType, "llm")) {
            // 更新大模型资源组
            FridayResource fridayResource = new FridayResource();
            fridayResource.setModelName(request.getModelName());
            fridayResource.setInstanceNum(Integer.parseInt(request.getInstanceNum()));

            Map map = JSONObject.parseObject(JSON.toJSONString(fridayResource), Map.class);
            List<Map<String, Object>> fridayResourceList = new ArrayList<>();
            fridayResourceList.add(map);
            resourceGroup.setFridayResources(JSON.toJSONString(fridayResourceList));

        }

        resourceGroupService.editResourceGroupList(resourceGroup);
        return resourceGroupId;
    }

    private LoadTestRequest buildLoadTestRequest(TMcpTestMissionRequest request) {
        LoadTestRequest testRequest = new LoadTestRequest();
        Map<String, String> extraInfo = request.getExtraInfo();
        testRequest.setModelType(request.getModelType());
        testRequest.setModelName(request.getModelName());
        testRequest.setModelPath(request.getModelPath());
        testRequest.setMaxSeqLen(extraInfo.get("maxSeqLen"));
        testRequest.setModelIndex(extraInfo.get("modelIndex"));
        testRequest.setPreHandleStructType(extraInfo.getOrDefault("modelOutputType", "0"));
        testRequest.setFuncType(extraInfo.get("funcType"));
        String similarityComparisonStr = extraInfo.get("similarityComparison");
        if (similarityComparisonStr == null) {
            similarityComparisonStr = "false";
        }
        testRequest.setSimilarityComparison(Boolean.parseBoolean(similarityComparisonStr));
        testRequest.setTestDataUrl(request.getTestDataSet());
        testRequest.setBatchSizes("1,5");//todo 改成自动寻找最佳batch
        testRequest.setInitialWorkers(1);//初始默认1
        testRequest.setMisId(request.getMisId());
        return testRequest;
    }

    private void handleTestType(TMcpTestMissionRequest request) throws Exception {
        //餐
        if ("1".equals(request.getIndustryType()) && !"1".equals(request.getTestType())) {
            request.setTestType("2");
        }

        //综
        if (StringUtils.equals("2", request.getIndustryType())) {
            if (StringUtils.equals("3", request.getTestType())) {
                request.setTestType("1");
            }
        }
    }

    private void checkRequestParamsVaild(TMcpTestMissionRequest request) throws Exception {
        if (request == null) {
            throw new TMcpTestMissionException("请求参数不能为空");
        }
        if (StringUtils.isBlank(request.getMisId())) {
            throw new TMcpTestMissionException("发起人MisId不能为空,请检查misId是否填写正确");
        }
        if (StringUtils.isBlank(request.getSessionId())) {
            throw new TMcpTestMissionException("sessionId不能为空,请检查sessionId是否填写正确");
        }
        if (StringUtils.isBlank(request.getIndustryType())) {
            throw new TMcpTestMissionException("产业类型字段不能为空，请检查industryType是否填写正确");
        }
        if (StringUtils.isBlank(request.getBu())) {
            throw new TMcpTestMissionException("业务类型字段不能为空，请检查bu是否填写正确");
        }
        if (StringUtils.isBlank(request.getTestType())) {
            throw new TMcpTestMissionException("测试类型不能为空，请检查testType是否填写正确");
        }
        if (StringUtils.isBlank(request.getMatchStandard())) {
            throw new TMcpTestMissionException("匹配标准不能为空，请检查matchStandard是否填写正确");
        }

        if (request.getPredictorClearCache() == null) {
            request.setPredictorClearCache(false);
        }
//        if (!StringUtils.equals("1", request.getTestType()) && CollectionUtils.isEmpty(request.getCpuInfos())) {
//            throw new TMcpTestMissionException("cpu信息不能为空,请检查cpuInfos是否填写正确");
//        }

        //badcase测试，评测集不能为空
        if (StringUtils.equals("1", request.getTestType()) && StringUtils.isBlank(request.getTestDataSet())) {
            throw new TMcpTestMissionException("评测集不能为空，请检查testDataSet是否填写正确");
        }

        //分行业类型进行判断
        //餐
        if (StringUtils.equals("1", request.getIndustryType())) {
            // 餐 所有测试都需要有测试日期
            if (StringUtils.isBlank(request.getTestDataDate())) {
                throw new TMcpTestMissionException("测试数据日期不能为空,请检查testDataDate是否填写正确");
            }

            if (!StringUtils.equals("1", request.getTestType())) {
                if (StringUtils.isBlank(request.getTestScene())) {
                    request.setTestScene("1");//默认测试货架*货架场景
                }
                if (StringUtils.isBlank(request.getPostXtVersion()) || StringUtils.isBlank(request.getParseXtVersion())) {
                    throw new TMcpTestMissionException("postXtVersion和postXtVersion必填");
                }
            }
        }

        //综
        if (StringUtils.equals("2", request.getIndustryType())) {
            if (StringUtils.equals("2", request.getTestType())) {
                throw new TMcpTestMissionException("服务零售当前不支持GSB测试，请检查参数testType是否正确");
            }
        }

        //住宿
        if (StringUtils.equals("3", request.getIndustryType())) {
            if (StringUtils.equals("2", request.getTestType())) {
                throw new TMcpTestMissionException("住宿当前不支持GSB测试，请检查参数testType是否正确");
            }
            if (StringUtils.equals("3", request.getTestType())) {
                throw new TMcpTestMissionException("住宿当前不支持一致性测试，请检查参数testType是否正确");
            }
        }
    }

    private void checkLoadTestParamsVaild(TMcpTestMissionRequest request) throws Exception {
        if (request == null) {
            throw new TMcpTestMissionException("请求参数不能为空");
        }
        if (StringUtils.isBlank(request.getMisId())) {
            throw new TMcpTestMissionException("发起人MisId不能为空,请检查misId是否填写正确");
        }
        if (StringUtils.isBlank(request.getSessionId())) {
            throw new TMcpTestMissionException("sessionId不能为空,请检查sessionId是否填写正确");
        }
        if (StringUtils.isBlank(request.getIndustryType())) {
            throw new TMcpTestMissionException("产业类型字段不能为空，请检查industryType是否填写正确");
        }
        if (StringUtils.isBlank(request.getBu())) {
            throw new TMcpTestMissionException("业务类型字段不能为空，请检查bu是否填写正确");
        }
        if (StringUtils.isBlank(request.getTestType())) {
            throw new TMcpTestMissionException("测试类型不能为空，请检查testType是否填写正确");
        }
        if (StringUtils.isBlank(request.getMatchStandard())) {
            throw new TMcpTestMissionException("匹配标准不能为空，请检查matchStandard是否填写正确");
        }
        if (StringUtils.isBlank(request.getModelType())) {
            throw new TMcpTestMissionException("模型类型不能为空，请检查modelType是否填写正确");
        }
        if (StringUtils.isBlank(request.getModelName())) {
            throw new TMcpTestMissionException("模型名称不能为空，请检查modelName是否填写正确");
        }
        if (StringUtils.isBlank(request.getInstanceNum())) {
            throw new TMcpTestMissionException("压测机器实例数不能为空，请检查instanceNum是否填写正确");
        }
    }
}
