package com.sankuai.algoplatform.matchops.starter.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.util.Pair;
import com.dianping.pigeon.remoting.provider.config.annotation.Service;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServerExtConfig;
import com.sankuai.algoplatform.matchops.api.enums.ResultEnum;
import com.sankuai.algoplatform.matchops.api.request.algoPackage.TAddEditAlgoBizCodeRequest;
import com.sankuai.algoplatform.matchops.api.request.algoPackage.TAddEditAlgoStrategyRequest;
import com.sankuai.algoplatform.matchops.api.request.algoPackage.TAlgoCodePackageListRequest;
import com.sankuai.algoplatform.matchops.api.request.algoPackage.TDeleteAlgoBizCodeRequest;
import com.sankuai.algoplatform.matchops.api.response.algoPackage.*;
import com.sankuai.algoplatform.matchops.api.service.TAlgoPackageService;
import com.sankuai.algoplatform.matchops.application.service.testtool.AlgoPackageService;
import com.sankuai.algoplatform.matchops.domain.enums.EnvStatus;
import com.sankuai.algoplatform.matchops.infrastructure.enums.RunTimeStatus;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.AlgoPackage;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.AlgoStrategy;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.BizStrategy;
import com.sankuai.algoplatform.matchops.infrastructure.util.ContextUtil;
import com.sankuai.algoplatform.matchops.infrastructure.util.ParamUtil;
import com.sankuai.algoplatform.matchops.infrastructure.util.RegexUtil;
import com.sankuai.algoplatform.matchops.starter.aop.anno.AddLog;
import com.sankuai.mcm.client.sdk.config.annotation.McmHandler;
import com.sankuai.mcm.client.sdk.config.annotation.McmPreHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@MdpThriftServer(port = 9001)
@MdpThriftServerExtConfig(enableAuthHandler = true)
@Slf4j
public class TAlgoPackageServiceImpl implements TAlgoPackageService {

    private static final String COMMIT_ID_REGEX = "^[0-9a-f]{7,40}$";

    private static final String SSH_GIT_REPO_URL_REGEX = "^ssh://[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+(:[0-9]+)?/[a-zA-Z0-9._%+-]+(/[a-zA-Z0-9._%+-]+)*(\\.git)?$";


    @Autowired
    private AlgoPackageService algoPackageService;


    @AddLog
    @Override
    public TAlgoPackageListResponse getAlgoCodePackageList(TAlgoCodePackageListRequest request) {
        TAlgoPackageListResponse response = new TAlgoPackageListResponse();
        try {
            if (request.getCurrent() == null || request.getBizLineId() == null) {
                response.setMessage("参数不完整");
                return response;
            }
            Map<String, String> data = algoPackageService.getAlgoCodePackageList(request.getCurrent(),
                    request.getBizLineId());
            response.setCode(0);
            response.setMessage("success");
            response.setData(data);
        } catch (Exception e) {
            response.setCode(-1);
            response.setMessage(e.getMessage());
            log.error("getAlgoCodePackageList error request is {}", request, e);
        }
        return response;
    }

    @AddLog
    @Override
    public TAddAlgoStrategyResponse addAlgoStrategy(TAddEditAlgoStrategyRequest request) {
        TAddAlgoStrategyResponse response = new TAddAlgoStrategyResponse();
        try {

            Map<String, Object> validParam = new HashMap<>();
            validParam.put("bizLineId", request.getBizLineId());
            validParam.put("algoCodeStrategyName", request.getAlgoCodeStrategyName());
            validParam.put("modulePath", request.getModulePath());
            validParam.put("entrancePath", request.getEntrancePath());
            validParam.put("entranceMethod", request.getEntranceMethod());
            validParam.put("algoRuntime", request.getAlgoRuntime());
            validParam.put("version", request.getVersion());
            validParam.put("codeRepo", request.getCodeRepo());
            org.apache.commons.lang3.tuple.Pair<Boolean, String> validRes = ParamUtil.checkNotNull(validParam);
            if (!validRes.getKey()) {
                response.setCode(ResultEnum.PARAMS_FAIL.getCode());
                response.setMessage(validRes.getRight());
                return response;
            }

            if (!RegexUtil.matches(request.getVersion(), COMMIT_ID_REGEX)) {
                response.setCode(ResultEnum.PARAMS_FAIL.getCode());
                response.setMessage("算法代码版本错误");
                return response;
            }

            if (!RegexUtil.matches(request.getCodeRepo(), SSH_GIT_REPO_URL_REGEX)) {
                response.setCode(ResultEnum.PARAMS_FAIL.getCode());
                response.setMessage("代码仓库错误");
                return response;
            }


            Pair<List<AlgoStrategy>, List<AlgoPackage>> temp = convert(request);
            Long id = algoPackageService.addAlgoStrategy(temp.getKey(), temp.getValue());
            Map<String, String> data = new HashMap<>();
            data.put("id", id.toString());
            response.setCode(0);
            response.setMessage("success");
            response.setData(data);
        } catch (Exception e) {
            response.setCode(-1);
            response.setMessage(e.getMessage());
            log.error("addAlgoStrategy error request is {}", request, e);
        }
        return response;
    }

    @AddLog
    @Override
    @McmHandler(eventName = "UpdateAlgoStrategy")
    @McmPreHandler(eventName = "UpdateAlgoStrategy")
    public TEditAlgoStrategyResponse editAlgoStrategy(TAddEditAlgoStrategyRequest request) {
        TEditAlgoStrategyResponse response = new TEditAlgoStrategyResponse();
        try {
            if (request.getAlgoCodeStrategyId() == null) {
                response.setMessage("参数不完整");
                return response;
            }

            if (StringUtils.isNotEmpty(request.getVersion()) && !RegexUtil.matches(request.getVersion(), COMMIT_ID_REGEX)) {
                response.setCode(ResultEnum.PARAMS_FAIL.getCode());
                response.setMessage("算法代码版本错误");
                return response;
            }

            if (!RegexUtil.matches(request.getCodeRepo(), SSH_GIT_REPO_URL_REGEX)) {
                response.setCode(ResultEnum.PARAMS_FAIL.getCode());
                response.setMessage("代码仓库错误");
                return response;
            }

            Pair<List<AlgoStrategy>, List<AlgoPackage>> temp = convert(request);
            algoPackageService.editAlgoStrategy(temp.getKey(), temp.getValue());
            response.setCode(0);
            response.setMessage("success");
        } catch (Exception e) {
            response.setCode(-1);
            response.setMessage(e.getMessage());
            log.error("editMatchStrategy error request is {}", request, e);
        }
        return response;
    }

    @AddLog
    @Override
    public TAddAlgoBizCodeResponse addAlgoBizCode(TAddEditAlgoBizCodeRequest request) {
        TAddAlgoBizCodeResponse response = new TAddAlgoBizCodeResponse();

        Map<String, Object> validParam = new HashMap<>();
        validParam.put("algoBizCode", request.getAlgoBizCode());
        validParam.put("bizLineId", request.getBizLineId());
        validParam.put("algoCodeStrategyName", request.getStrategy());
        org.apache.commons.lang3.tuple.Pair<Boolean, String> validRes = ParamUtil.checkNotNull(validParam);
        if (!validRes.getKey()) {
            response.setCode(ResultEnum.PARAMS_FAIL.getCode());
            response.setMessage(validRes.getRight());
            return response;
        }


        try {
            Long id = algoPackageService.addAlgoBizCode(convertBizCode(request));
            Map<String, String> data = new HashMap<>();
            data.put("id", id.toString());
            response.setCode(0);
            response.setMessage("success");
            response.setData(data);
        } catch (Exception e) {
            response.setCode(-1);
            response.setMessage(e.getMessage());
            log.error("addAlgoBizCode error request is {}", request, e);
        }
        return response;
    }

    @AddLog
    @Override
    @McmHandler(eventName = "UpdateAlgoBizCode")
    @McmPreHandler(eventName = "UpdateAlgoBizCode")
    public TEditAlgoBizCodeResponse editAlgoBizCode(TAddEditAlgoBizCodeRequest request) {
        TEditAlgoBizCodeResponse response = new TEditAlgoBizCodeResponse();
        try {
            if (request.getAlgoBizCodeId() == null) {
                response.setMessage("参数不完整");
                return response;
            }
            request.setAlgoBizCode(null);//bizCode不能更新
            algoPackageService.editAlgoBizCode(convertBizCode(request));
            response.setCode(0);
            response.setMessage("success");
        } catch (Exception e) {
            response.setCode(-1);
            response.setMessage(e.getMessage());
            log.error("editAlgoBizCode error request is {}", request, e);
        }
        return response;
    }

    @AddLog
    @Override
    public TDeleteAlgoBizCodeResponse deleteAlgoBizCode(TDeleteAlgoBizCodeRequest request) {
        TDeleteAlgoBizCodeResponse response = new TDeleteAlgoBizCodeResponse();
        try {
            if (request.getAlgoBizCodeId() == null) {
                response.setMessage("参数不完整");
                return response;
            }
            Integer result = algoPackageService.deleteAlgoBizCode(request.getAlgoBizCodeId(), request.getOwner());
            if (result == 1) {
                response.setCode(0);
                response.setMessage("success");
            } else {
                response.setMessage("删除失败");
            }

        } catch (Exception e) {
            response.setCode(-1);
            response.setMessage(e.getMessage());
            log.error("deleteAlgoBizCode error request is {}", request, e);
        }
        return response;
    }

    private Pair<List<AlgoStrategy>, List<AlgoPackage>> convert(TAddEditAlgoStrategyRequest request) {
        List<AlgoStrategy> algoStrategies = new ArrayList<>();
        List<AlgoPackage> algoPackages = new ArrayList<>();
        AlgoStrategy mainAlgoStrategy = new AlgoStrategy();
        mainAlgoStrategy.setId(request.getAlgoCodeStrategyId());
        mainAlgoStrategy.setNote(request.getAlgoCodeStrategyName());
        mainAlgoStrategy.setBizLineId(request.getBizLineId());
        mainAlgoStrategy.setEntranceMethod(request.getEntranceMethod());
        mainAlgoStrategy.setEntrancePath(request.getEntrancePath());
        mainAlgoStrategy.setOwner(ContextUtil.getLoginUserMis());
        mainAlgoStrategy.setStatus(0);
        mainAlgoStrategy.setEnv(EnvStatus.ST.getCode());
        algoStrategies.add(mainAlgoStrategy);

        AlgoPackage mainAlgoPackage = new AlgoPackage();
        mainAlgoPackage.setNote(StringUtils.isEmpty(request.getNote()) ? request.getAlgoCodeStrategyName() : request.getNote());
        mainAlgoPackage.setModulePath(request.getModulePath());
        mainAlgoPackage.setRuntime(RunTimeStatus.fromValue(Integer.parseInt(request.getAlgoRuntime())).getDesc());
        mainAlgoPackage.setVersion(request.getVersion());
        mainAlgoPackage.setOwnerMis(ContextUtil.getLoginUserMis());
        mainAlgoPackage.setCodeRepo(request.getCodeRepo());
        mainAlgoPackage.setStatus(0);
        algoPackages.add(mainAlgoPackage);

        if (StringUtils.isNotBlank(request.getConvertEntranceMethod()) && StringUtils.isNotBlank(request.getConvertEntrancePath())) {
            AlgoStrategy convertAlgoStrategy = new AlgoStrategy();
            convertAlgoStrategy.setId(request.getAlgoCodeStrategyId());
            convertAlgoStrategy.setNote(request.getAlgoCodeStrategyName());
            convertAlgoStrategy.setBizLineId(request.getBizLineId());
            convertAlgoStrategy.setEntranceMethod(request.getConvertEntranceMethod());
            convertAlgoStrategy.setEntrancePath(request.getConvertEntrancePath());
            convertAlgoStrategy.setOwner(ContextUtil.getLoginUserMis());
            convertAlgoStrategy.setStatus(0);
            convertAlgoStrategy.setEnv(EnvStatus.ST.getCode());
            algoStrategies.add(convertAlgoStrategy);


            AlgoPackage convertAlgoPackage = new AlgoPackage();
            convertAlgoPackage.setNote(StringUtils.isEmpty(request.getNote()) ? request.getAlgoCodeStrategyName() : request.getNote());
            convertAlgoPackage.setModulePath(request.getModulePath());
            convertAlgoPackage.setRuntime(RunTimeStatus.fromValue(Integer.parseInt(request.getAlgoRuntime())).getDesc());
            convertAlgoPackage.setVersion(request.getVersion());
            convertAlgoPackage.setOwnerMis(ContextUtil.getLoginUserMis());
            convertAlgoPackage.setCodeRepo(request.getCodeRepo());
            convertAlgoPackage.setStatus(0);
            algoPackages.add(convertAlgoPackage);
        }
        return Pair.from(algoStrategies, algoPackages);
    }

    private BizStrategy convertBizCode(TAddEditAlgoBizCodeRequest request) {
        BizStrategy bizStrategy = new BizStrategy();
        bizStrategy.setId(request.getAlgoBizCodeId());
        bizStrategy.setBizCode(request.getAlgoBizCode());
        bizStrategy.setBizLineId(request.getBizLineId());
        bizStrategy.setNote(request.getNote());
        bizStrategy.setEnv(EnvStatus.ST.getCode());

        List<Map<String, String>> list = JSON.parseObject(request.getStrategy().get("distributions"), new TypeReference<List<Map<String, String>>>() {
        });

        HashMap<Object, Object> abtest = new HashMap<>();
        abtest.put("strategyName", request.getStrategy().get("strategyName"));
        for (int i = 0; i < list.size(); i++) {
            Map<String, String> dist = list.get(i);
            if (StringUtils.isEmpty(dist.get("name"))) {
                dist.put("name", "group_" + (i + 1));
            }
        }
        abtest.put("distributions", list);

        bizStrategy.setAbtestConfig(JSON.toJSONString(abtest));
        bizStrategy.setOwner(ContextUtil.getLoginUserMis());
        return bizStrategy;
    }

}
