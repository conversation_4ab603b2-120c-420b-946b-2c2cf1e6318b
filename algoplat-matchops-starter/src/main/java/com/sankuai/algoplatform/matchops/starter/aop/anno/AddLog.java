package com.sankuai.algoplatform.matchops.starter.aop.anno;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface AddLog {
    /**
     * 是否需要加cat
     * @return
     */
    boolean addCat() default false;

    String catType() default "";

    String catName() default "";

    /**
     * 是否是门户层接口，如果是的话，需要拿接口的code来判断是否成功
     * @return
     */
    boolean facade() default false;


}
