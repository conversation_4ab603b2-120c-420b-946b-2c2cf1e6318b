package com.sankuai.algoplatform.matchops.starter.aop;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.meituan.mtrace.Tracer;
import com.sankuai.algoplatform.matchops.starter.aop.anno.AddLog;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @date 2023/7/17 19:45
 **/
@Component
@Aspect
public class AddLogAspect {

    private static final String LOG_FORMAT_IN = "%s#%s,入参=%s,tracerInfo=%s";
    private static final String LOG_FORMAT_OUT = "%s#%s,出参=%s,耗时%s毫秒";
    private static final String LOG_FORMAT_ERROR = "%s#%s,error,message=%s";
    private static final Logger log = org.slf4j.LoggerFactory.getLogger(AddLogAspect.class);

    @Pointcut("@annotation(com.sankuai.algoplatform.matchops.starter.aop.anno.AddLog)")
    private void pointcut() {
    }


    @Around("pointcut()")
    public Object advice(ProceedingJoinPoint point) throws Throwable {

        Object[] args = point.getArgs();
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        AddLog annotation = method.getAnnotation(AddLog.class);
        boolean addCat = annotation.addCat();
        String catType = annotation.catType();
        String catName = annotation.catName();
        boolean facade = annotation.facade();

        try {
            long start = System.currentTimeMillis();
            log.info(String.format(LOG_FORMAT_IN, signature.getDeclaringTypeName(), method.getName(), JSON.toJSONString(args),
                    JSON.toJSONString(Tracer.getAllContext())));
            Object proceed = point.proceed();
            long end = System.currentTimeMillis();
            log.info(String.format(LOG_FORMAT_OUT, signature.getDeclaringTypeName(), method.getName(), JSON.toJSONString(proceed), end - start));
            if (addCat) {
                if (facade) {
                    //facade层接口异常已经被trycatch住了，需要判断code来判断是否成功
                    if (checkSuccessResponse(proceed)) {
                        Cat.logEvent(StringUtils.isBlank(catType) ? signature.getDeclaringTypeName() : catType,
                                StringUtils.isBlank(catName) ? method.getName() : catName);
                    } else {
                        Cat.logEvent(StringUtils.isBlank(catType) ? signature.getDeclaringTypeName() : catType,
                                StringUtils.isBlank(catName) ? "error_" + method.getName() : "error_" + catName);
                    }
                } else {
                    Cat.logEvent(StringUtils.isBlank(catType) ? signature.getDeclaringTypeName() : catType,
                            StringUtils.isBlank(catName) ? method.getName() : catName);
                }
            }
            return proceed;
        } catch (Throwable e) {
            log.error(String.format(LOG_FORMAT_ERROR, signature.getDeclaringType().toString(), method.getName(), e.getMessage()), e);
            if (addCat) {
                Cat.logEvent(StringUtils.isBlank(catType) ? signature.getDeclaringTypeName() : catType,
                        StringUtils.isBlank(catName) ? "error_" + method.getName() : "error_" + catName);
            }
            throw e;
        }

    }

    private boolean checkSuccessResponse(Object proceed) {

        if (proceed == null) {
            return false;
        }
        String jsonString = JSON.toJSONString(proceed);

        JSONObject jsonObject = JSON.parseObject(jsonString);
        if (jsonObject == null) {
            return false;
        }
        Integer code = jsonObject.getInteger("code");
        if (new Integer(0).equals(code)) {
            return true;
        } else {
            return false;
        }
    }
}
