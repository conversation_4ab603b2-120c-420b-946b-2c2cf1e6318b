# MDP DOCS=https://km.sankuai.com/custom/onecloud/page/1302115131
mdp.zebra[0].jdbcRef=bmlmatch_bmlmatch_test
mdp.zebra[0].datasourceLazyInit=false
mdp.zebra[0].basePackage=com.sankuai.algoplatform.matchops.infrastructure.dal.mapper
mdp.zebra[0].mapperLocations=classpath:mappers/*.xml
mdp.zebra[0].configLocation=classpath:mybatis-config.xml
mdp.zebra[0].extraJdbcUrlParams=useSSL=false


mdp.zebra[1].bladeJdbcRef=inf_blade_blade2function_dzu_bmlmatch_test
mdp.zebra[1].datasourceLazyInit=false
mdp.zebra[1].basePackage=com.sankuai.algoplatform.matchops.infrastructure.dal.blade.mapper
mdp.zebra[1].mapperLocations=classpath:mappers/blade/*.xml
mdp.zebra[1].configLocation=classpath:mybatis-config.xml