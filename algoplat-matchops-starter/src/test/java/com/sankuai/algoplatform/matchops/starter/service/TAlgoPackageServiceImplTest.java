package com.sankuai.algoplatform.matchops.starter.service;

import com.alibaba.fastjson.JSON;
import com.sankuai.algoplatform.matchops.BaseTest;
import com.sankuai.algoplatform.matchops.api.request.algoPackage.TAddEditAlgoBizCodeRequest;
import com.sankuai.algoplatform.matchops.api.request.algoPackage.TAddEditAlgoStrategyRequest;
import com.sankuai.algoplatform.matchops.api.request.algoPackage.TAlgoCodePackageListRequest;
import com.sankuai.algoplatform.matchops.api.request.algoPackage.TDeleteAlgoBizCodeRequest;
import com.sankuai.algoplatform.matchops.api.response.algoPackage.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;

@Slf4j
public class TAlgoPackageServiceImplTest extends BaseTest {
    @Resource
    private TAlgoPackageServiceImpl algoPackageService;

    @Test
    public void testGetAlgoCodePackageList() {
        TAlgoCodePackageListRequest request = new TAlgoCodePackageListRequest();
        request.setBizLineId(1L);
        request.setCurrent(1);
        TAlgoPackageListResponse tAlgoPackageListResponse = algoPackageService.getAlgoCodePackageList(request);
        log.info("{}", JSON.toJSONString(tAlgoPackageListResponse));
    }

    @Test
    public void testAddAlgoStrategy() {
        String str = "{\n" + "    \"algoCodeStrategyName\": \"精准匹配\",        \n"
                + "    \"modulePath\": \"barbecue_realtime_code_muti_kinds\",        \n"
                + "    \"entrancePath\": \"deal_match_main_lanconic_multi_version_online\",\n"
                + "    \"entranceMethod\": \"calculate\",           \n" + "    \"algoRuntime\": \"0\",           \n"
                + "    \"version\": \"042193e1f95\",     \n"
                + "    \"codeRepo\": \"ssh://*******************/dcgstgy/algoplat-package.git\",    \n"
                + "    \"note\": \"xxxxxx\",            \n" + "    \"bizLineId\": 1,                \n"
                + "    \"owner\":\"xxx\"\n" + "}";
        TAddEditAlgoStrategyRequest request = JSON.parseObject(str, TAddEditAlgoStrategyRequest.class);
        log.info("{}", JSON.toJSONString(request));
        TAddAlgoStrategyResponse result = algoPackageService.addAlgoStrategy(request);
        log.info("{}", JSON.toJSONString(result));

    }

    @Test
    public void testEditAlgoStrategy() {
        String str = "{\n" + "    \"algoCodeStrategyId\": 76,\n" + "  \"algoCodeStrategyName\": \"精准匹配1\",\n"
                + "  \"modulePath\": \"barbecue_realtime_code_muti_kinds1\",\n"
                + "  \"entrancePath\": \"deal_match_main_lanconic_multi_version_online1\", \n"
                + "  \"entranceMethod\": \"calculate1\",\n" + "  \"algoRuntime\": \"01\",\n"
                + "  \"version\": \"042193e1f951\", \n"
                + "  \"codeRepo\": \"ssh://*******************/dcgstgy/algoplat-package.git1\", \n"
                + "  \"note\": \"xxxxxx1\",\n" + "  \"owner\":\"xxx1\"\n" + "}";
        TAddEditAlgoStrategyRequest request = JSON.parseObject(str, TAddEditAlgoStrategyRequest.class);
        log.info("{}", JSON.toJSONString(request));
        TEditAlgoStrategyResponse tBaseResponse = algoPackageService.editAlgoStrategy(request);
        log.info("{}", JSON.toJSONString(tBaseResponse));
    }


    @Test
    public void testAddAlgoBizCode() {
        String str = "{\n" + "    \"algoBizCode\": \"test\",\n" + "    \"note\": \"test\",\n"
                + "    \"owner\": \"wb_xinzhishuang\",\n" + "    \"strategy\": {\n"
                + "        \"strategyName\": \"test\",\n"
                + "        \"distributions\": \"[{\\\"strategyId\\\":\\\"79\\\",\\\"quota\\\":50},{\\\"strategyId\\\":\\\"95\\\",\\\"quota\\\":50}]\"\n"
                + "    },\n" + "    \"bizLineId\": \"1\"\n" + "}";
        TAddEditAlgoBizCodeRequest request = JSON.parseObject(str, TAddEditAlgoBizCodeRequest.class);
        log.info("{}", JSON.toJSONString(request));
        TAddAlgoBizCodeResponse tBaseResponse = algoPackageService.addAlgoBizCode(request);
        log.info("{}", JSON.toJSONString(tBaseResponse));
    }

    @Test
    public void testEditAlgoBizCode() {
        String str = "{\n" + "    \"algoBizCodeId\": \"64\", \n" + "    \"strategy\": {\n"
                + "        \"strategyName\": \"schema\",\n" + "        \"distributions\": \"[\n" + "            {\n"
                + "                \\\"strategyId\\\": \\\"76\\\", \n" + "                \\\"quota\\\": 30 \n"
                + "            },\n" + "            {\n" + "                \\\"strategyId\\\": \\\"77\\\",\n"
                + "                \\\"quota\\\": 30\n" + "            },\n" + "            {\n"
                + "                \\\"strategyId\\\": \\\"78\\\",\n" + "                \\\"quota\\\": 40\n"
                + "            }\n" + "        ]\"\n" + "    },\n" + "    \"note\": \"xxxxxx11\",\n"
                + "    \"bizLineId\": 1,\n" + "    \"owner\":\"xxx11\"\n" + "}";
        TAddEditAlgoBizCodeRequest request = JSON.parseObject(str, TAddEditAlgoBizCodeRequest.class);
        log.info("{}", JSON.toJSONString(request));
        TEditAlgoBizCodeResponse tBaseResponse = algoPackageService.editAlgoBizCode(request);
        log.info("{}", JSON.toJSONString(tBaseResponse));
    }

    @Test
    public void testDeleteAlgoBizCode() {
        TDeleteAlgoBizCodeRequest request = new TDeleteAlgoBizCodeRequest();
        request.setAlgoBizCodeId(64L);
        request.setOwner("siweichen");
        TDeleteAlgoBizCodeResponse tBaseResponse = algoPackageService.deleteAlgoBizCode(request);
        log.info("{}", JSON.toJSONString(tBaseResponse));
    }
}