package com.sankuai.algoplatform.matchops.infrastructure.proxy;

import com.alibaba.fastjson.JSON;
import com.sankuai.aifree.thrift.generation.vo.ServingDetailDataVo;
import com.sankuai.algoplatform.matchops.BaseTest;
import com.sankuai.algoplatform.matchops.infrastructure.model.GPUQueueResourceDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
public class MlpTrainingServiceTest extends BaseTest {
    @Resource
    private MlpTrainingService mlpTrainingService;

    @Test
    public void test() throws Exception {
//        List<GPUQueueResourceDetail> resources = mlpTrainingService.getQueueResource("root.hh_serving_cluster.hadoop-daodian.serving");
//        log.info("{}", resources);
        List<GPUQueueResourceDetail>  resources = mlpTrainingService.getQueueResource("root.yg_serving_cluster.hadoop-daodian.serving");
        log.info("{}", resources);
        resources = mlpTrainingService.getQueueResource("root.zw05_training_cluster.hadoop-daodian.bml-match");
        log.info("{}", resources);
    }

    @Test
    public void test2() throws Exception {
        ServingDetailDataVo detail = mlpTrainingService.getServingDetail("com.sankuai.algoplatform.modelserver", "liyue31");
        log.info("{}", JSON.toJSONString(detail));
    }

    @Test
    public void test1() throws Exception {
//        Pair<Boolean, String> pair = mlpTrainingService.addInstance(
//                "com.sankuai.algoplatform.modelserver",
//                "test-group-a",
//                "",
//                "root.hh_testing_cluster.hadoop-hdpmlp.mlpserving",
//                100,
//                4,
//                8,
//                1,
//                GPUResourceType.A30_MIG_12G,
//                "liyue31"
//
//        );
//        log.info("{}", JSON.toJSONString(pair));
    }

//    @Test
    public void test3() throws Exception {
        Pair<Boolean, String> pair = mlpTrainingService.deleteInstance(
                "com.sankuai.algoplatform.modelserver",
                "test-group-a",
                100,
                "liyue31"

        );
        log.info("{}", JSON.toJSONString(pair));
    }

    @Test
    public void test4() throws Exception {
        Pair<Boolean, String> pair = mlpTrainingService.deleteServingGroupQuery("com.sankuai.algoplatform.modelserver",
                "test-group-b", "zhoumi10");
        log.info("{}", pair);
    }

}
