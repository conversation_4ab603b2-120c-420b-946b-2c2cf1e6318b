package com.sankuai.algoplatform.matchops.infrastructure.proxy;

import com.google.common.collect.Lists;
import com.sankuai.algoplatform.matchops.BaseTest;
import com.sankuai.algoplatform.matchops.infrastructure.model.RaptorData;
import com.sankuai.algoplatform.matchops.infrastructure.util.DateUtil;
import com.sankuai.inf.octo.mns.model.HostEnv;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class RaptorDataServiceTest extends BaseTest {
    @Resource
    private RaptorDataService raptorDataService;

//    @Test
    public void queryTransactions() {
        RaptorData.Transaction.Root manage = raptorDataService.queryTransactionHourly(
                HostEnv.STAGING,
                "com.sankuai.algoplatform.predictor",
                DateUtil.parseDateByShortStr("20241204150000"),
                "PythonMonitor_Heartbeat",
                "default_cell",
                null
        );
        log.info("{}", manage);
    }
    @Test
    public void queryTransactionHourlyGraphs() {
        Map<String, String> params = new HashMap<>();
        params.put("ip", "*************");
        RaptorData.Transaction.Root root = null;
        int retryCount = 0;
        int maxRetries = 10;

        while (retryCount <= maxRetries) {
            try {
                root = raptorDataService.queryTransactionHourlyGraphs(
                        HostEnv.STAGING,
                        "com.sankuai.algoplatform.predictor",
                        DateUtil.parseDateByShortStr("20250620140000"),//20250613150000
                        "URL",
                        "",
                        "/test/predictWithCache",
                        params
                );
                log.info("{}", root);
                retryCount++;
                //break;
            } catch (Exception e) {
                // 只有在特定错误消息时才重试
                if (e.getMessage() != null && e.getMessage().contains("query raptor host data failed, resp is blank")) {
                    retryCount++;
                    if (retryCount > maxRetries) {
                        log.error("获取Raptor数据失败，已重试{}次，错误信息：{}", maxRetries, e.getMessage(), e);
                        break;
                    }
                    log.warn("获取Raptor数据失败: {}，正在进行第{}次重试", e.getMessage(), retryCount);
                    try {
                        Thread.sleep(10000); // 重试前等待1秒
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.error("重试等待被中断", ie);
                        break;
                    }
                } else {
                    // 其他错误直接抛出
                    log.error("获取Raptor数据失败，错误信息：{}", e.getMessage(), e);
                    throw e;
                }
            }
        }
    }

//    @Test
    public void queryEvent() {
        RaptorData.Event.Root manage = raptorDataService.queryEventHourly(
                HostEnv.STAGING,
                "com.sankuai.algoplatform.predictor",
                DateUtil.parseDateByShortStr("20241212100000"),
                "Git_Checkout",
                "default_cell",
                null
        );
        log.info("{}", manage);
    }

//    @Test
    public void queryBusiness() {
        RaptorData.Business.Root manage = raptorDataService.queryBusiness(
                HostEnv.STAGING,
                "com.sankuai.algoplatform.predictor",
                DateUtil.parseDateByShortStr("20241204130000"),
                DateUtil.parseDateByShortStr("20241204135900"),
                "BridgeRunningTaskCnt",
                null
        );
        log.info("{}", manage);
    }

//    @Test
    public void queryHost() {
        RaptorData.Host.Root host = raptorDataService.queryHost(
                HostEnv.STAGING,
                DateUtil.parseDateByShortStr("20241204130000"),
                DateUtil.parseDateByShortStr("20241204135959"),
                Lists.newArrayList("mem.memused.percent",
                        "cpu.busy"),
                Lists.newArrayList("set-hh-algoplatform-predictor-staging07"),
                null
        );
        log.info("{}", host);
    }
}