package com.sankuai.algoplatform.matchops.domain.service;

import com.alibaba.fastjson.JSON;
import com.sankuai.algoplatform.matchops.BaseTest;
import com.sankuai.algoplatform.matchops.domain.service.testtool.impl.ReporterServiceImpl;
import com.sankuai.algoplatform.matchops.domain.service.testtool.reporter.*;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.TestSubTaskDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestSubTask;
import com.sankuai.ead.citadel.document.parser.DocumentParsingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class ReporterServiceTest extends BaseTest {

    @Autowired
    ReporterServiceImpl reporterService;

    @Autowired
    SqlDataFetch sqlDataFetch;

    @Autowired
    private CatDataFetch catDataFetch;

    @Autowired
    TestSubTaskDao subTaskDao;

    //    @Test
    public void testBuildReporterByContentId() {
//        TestSubTask testSubTask = subTaskDao.selectById(4L);
//        Pair<Boolean, String> pair = reporterService.buildReporterByContentId("4298186008", "测试", testSubTask);

//        Pair<Boolean, String> pair = reporterService.buildReporterByContentId("4298241489", 32L);
//        log.info("{}", JSON.toJSONString(pair));

    }

    //    @Test
    public void testMatchStrategyDataFetch() {
        TestSubTask testSubTask = subTaskDao.selectById(1L);
        String info = "strategy#ttype=algoCode";
        String[] rule = StringUtils.split(info, "#");
        String query = MatchStrategyDataFetch.query(rule, testSubTask);
        log.info("{}", JSON.toJSONString(query));

        info = "strategy#ttype=llm";
        String[] rule2 = StringUtils.split(info, "#");
        query = MatchStrategyDataFetch.query(rule2, testSubTask);
        log.info("{}", JSON.toJSONString(query));

        info = "strategy#ttype=algoCode#$[0].algoBizCode";
        rule = StringUtils.split(info, "#");
        query = MatchStrategyDataFetch.query(rule, testSubTask);
        log.info("{}", JSON.toJSONString(query));
        assert query != null;
    }

    //    @Test
    public void testRunParamDataFetch() {
        TestSubTask testSubTask = subTaskDao.selectById(1L);
        String info = "param#cd";
        String[] rule = StringUtils.split(info, "#");
        String query = RunParamDataFetch.query(rule, testSubTask);
        log.info("{}", JSON.toJSONString(query));
        assert query != null;
    }

    //    @Test
    public void testResourceDataFetch() throws Exception {
//        TestSubTask testSubTask = subTaskDao.selectById(1L);
//        String info = "resource#ttype=octo&appkey=com.sankuai.algoplatform.predictor&set=default";
//        String[] rule = StringUtils.split(info, "#");
//        String query = ResourceDataFetch.query(rule, testSubTask);
//        log.info("{}", JSON.toJSONString(query));
//
//        info = "resource#ttype=mlp&appkey=com.sankuai.algoplatform.modelserver&set=default";
//        rule = StringUtils.split(info, "#");
//        query = ResourceDataFetch.query(rule, testSubTask);
//        log.info("{}", JSON.toJSONString(query));
//
//        info = "resource#ttype=friday&serviceName=亲子游乐";
//        rule = StringUtils.split(info, "#");
//        query = ResourceDataFetch.query(rule, testSubTask);
//        log.info("{}", JSON.toJSONString(query));

        TestSubTask testSubTask = subTaskDao.selectById(32L);
        String info = "resource#ttype=mlp&appkey=com.sankuai.algoplatform.modelserver&set=default";
        String[] rule = StringUtils.split(info, "#");
        String query = ResourceDataFetch.query(rule, testSubTask);
        log.info("{}", JSON.toJSONString(query));

        assert query != null;
    }

    //    @Test
    public void testSQLDataFetch() throws Exception {
        TestSubTask testSubTask = subTaskDao.selectById(1L);
        String info = "hive#sql=select count(*) as total,sum(if(get_json_object(extra,'$.useCache')='true',1,0)) AS cache_cnt FROM al_catering.app_algoplatform_predictor_result WHERE dt='20241202'AND HOUR='04' AND get_json_object(extra,'$.source')='udf'";
        String[] rule = StringUtils.split(info, "#");
        String query = sqlDataFetch.query(rule, testSubTask);
        log.info("{}", JSON.toJSONString(query));
        assert query != null;
    }

    //    @Test
    public void testProblemData() {
        TestSubTask testSubTask = subTaskDao.selectById(1L);
        String info = "raptor#ttype=problem&appkey=com.sankuai.algoplatform.predictor&group=default_cell#$.report[0].name";
        String[] rule = StringUtils.split(info, "#");
//        log.info("problem：{}", catDataFetch.query(rule, testSubTask, false));

        info = "raptor#ttype=problem&appkey=com.sankuai.algoplatform.predictor&group=default_cell";
        rule = StringUtils.split(info, "#");
        log.info("problem：{}", catDataFetch.query(rule, testSubTask, false));
        assert testSubTask != null;
    }

    //    @Test
    public void testBusinessData() {
//        TestSubTask testSubTask = subTaskDao.selectById(32L);
//        String info = "raptor∫ttype=business&appkey=com.sankuai.algoplatform.predictor&item=Predict_SlowRequest";
        TestSubTask testSubTask = subTaskDao.selectById(52L);
        String info = "raptor∫ttype=business&appkey=com.sankuai.wenchang.infer.service&item=preProcess&tagValues=type:*";
        String[] rule = StringUtils.split(info, "∫");
        String query = catDataFetch.query(rule, testSubTask, false);
        log.info("business：{}", query);

        assert testSubTask != null;
    }

    //    @Test
    public void testHostData() {
//        TestSubTask testSubTask = subTaskDao.selectById(1L);
//        String info = "raptor#ttype=host&appkey=com.sankuai.algoplatform.predictor&cell=default_cell&metrics=cpu.busy,mem.memused.percent,python.mem.size/name=python3.7_common_0,proc.cpu=python3.7_common/bin/python main.py python3.7_common 25333 python3.7_common_0";
//        String[] rule = StringUtils.split(info, "#");
//        String query = catDataFetch.query(rule, testSubTask, false);
//        log.info("problem：{}", query);


        TestSubTask testSubTask = subTaskDao.selectById(7L);
        String info = "raptor#ttype=host&appkey=com.sankuai.algoplatform.modelserver&cell=gray-release-approximate-match&metrics=serving.gpu.util";
        String[] rule = StringUtils.split(info, "#");
        log.info("host：{}", catDataFetch.query(rule, testSubTask, false));
        assert testSubTask != null;
    }

    //    @Test
    public void testTransationData() {
//        TestSubTask testSubTask = subTaskDao.selectById(1L);
//        String info = "raptor#ttype=transaction&appkey=com.sankuai.algoplatform.predictor&group=default_cell&type=OctoCall&name=com.sankuai.algoplatform.modelserver:OnlineService.GetResponse";
//        String info = "raptor#ttype=transaction&appkey=com.sankuai.algoplatform.predictor&group=default_cell&type=MafkaRecvMessage&name=algoplatform.predictor.predict.listener_staging#com.sankuai.algoplatform.predictor";
//        String[] rule = StringUtils.split(info, "#");
//        String query = catDataFetch.query(rule, testSubTask, false);
//        log.info("problem：{}", query);

        TestSubTask testSubTask = subTaskDao.selectById(32L);
        String info = "raptor∫ttype=transaction&appkey=com.sankuai.algoplatform.predictor&type=MafkaRecvMessage&group=default_cell&name=algoplatform.predictor.predict.listener_staging#com.sankuai.algoplatform.predictor∫$.avg";
        String[] rule = StringUtils.split(info, "∫");
        String query = catDataFetch.query(rule, testSubTask, false);
        log.info("problem：{}", query);

        assert query != null;
    }

    //    @Test
    public void testEventData() {
        TestSubTask testSubTask = subTaskDao.selectById(1L);
        String info = "raptor#ttype=event&appkey=com.sankuai.algoplatform.predictor&group=default_cell&type=SimplePredictServiceImpl&name=predictWithCacheError";
        String[] rule = StringUtils.split(info, "#");
        String query = catDataFetch.query(rule, testSubTask, false);
        log.info("problem：{}", query);
    }


    @Test
    public void testResult() throws DocumentParsingException {
        TestSubTask testSubTask = subTaskDao.selectById(99L);
//        String info = "result∫ttype=result";
        String info = "result∫ttype=result∫table";
        String[] rule = StringUtils.split(info, "∫");
        String res = ResultDataFetch.query(rule, testSubTask);
        log.info("result：{}", res);


//        TestSubTask testSubTask2 = subTaskDao.selectById(107L);
//        String info2 = "result∫ttype=result";
//        String[] rule2 = StringUtils.split(info2, "∫");
//        String res2 = ResultDataFetch.query(rule2, testSubTask2);
//        log.info("result：{}", res2);

//        String info1 = "raptor∫ttype=transaction&appkey=com.sankuai.algoplatform.predictor&group=default_cell,gray-release-fast-food&type=Cellar.com.sankuai.tair.web.storage&name=Area15:GetRequest∫$.failPercent";
//        String[] rule1 = StringUtils.split(info1, "∫");
//        String res1 = catDataFetch.query(rule1, testSubTask, false);
//        log.info("raptor：{}", res1);
//
//        String info2 = "raptor∫ttype=business&appkey=com.sankuai.algoplatform.predictor&item=deal_match_poi_cache_ratio∫$.deal_match_poi_cache_ratio.AVG";
//        String[] rule2 = StringUtils.split(info2, "∫");
//        String res2 = catDataFetch.query(rule2, testSubTask, false);
//        log.info("raptor：{}", res2);

        assert testSubTask != null;
    }
}
