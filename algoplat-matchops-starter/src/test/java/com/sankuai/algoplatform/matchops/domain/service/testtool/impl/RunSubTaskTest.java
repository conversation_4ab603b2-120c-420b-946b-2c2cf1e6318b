package com.sankuai.algoplatform.matchops.domain.service.testtool.impl;

import com.alibaba.fastjson.JSON;
import com.sankuai.algoplatform.matchops.ApplicationLoader;
import com.sankuai.algoplatform.matchops.domain.constant.TestToolConstants;
import com.sankuai.algoplatform.matchops.domain.enums.ReqPostTypeEnum;

import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestSubTask;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationLoader.class)
public class RunSubTaskTest {
    @Autowired
    private TaskExecuteServiceImpl taskExecuteService;


    @Test
    public void testRunXTSubTask() {
        // 1. 构造测试数据
        TestSubTask subTask = new TestSubTask();
        subTask.setId(133L);
        subTask.setTaskId(19L);
        subTask.setCreator("zhoumi10");

        // 2. 构造运行参数 - XT类型任务
        Map<String, String> runParam = new HashMap<>();
        runParam.put("dt","20250601");
        runParam.put(TestToolConstants.CD,"2025-06-01");
        runParam.put("hour","10");
        runParam.put(TestToolConstants.REQ_POST_LINK, "https://data.sankuai.com/wanxiang2/al-catering/job/dml/1803779?version=19342797");
        runParam.put(TestToolConstants.VERSION, "19342797");
        runParam.put(TestToolConstants.XT_PARAM, "-v --cd 2025-07-01");
        runParam.put(TestToolConstants.POST_TYPE, String.valueOf(ReqPostTypeEnum.XT.getCode()));
        runParam.put(TestToolConstants.PARSE_RESP_LINK, "https://data.sankuai.com/wanxiang2/al-catering/job/dml/846968?version=19569713");
        runParam.put(TestToolConstants.PARSE_VERSION, "19569713");
        runParam.put(TestToolConstants.PARSE_XT_PARAM, "-v --cd 2025-07-01");

        subTask.setRunParam(JSON.toJSONString(runParam));
        String extraInfo = "{\"sceneName\": \"精准匹配-货架*货架\", " +
                "\"taskCalTimeInfo\": \"{\\\"reqTotalCalSql\\\": \\\"select count(*) from al_catering_test.bml_dj_mt_poi_deal_realtime_match where partition_date = '2025-03-23'\\\", " +
                "\\\"resTotalCalSql\\\": \\\"SELECT count(*) FROM al_catering.app_algoplatform_predictor_result WHERE dt = '20250323' AND get_json_object(extra, '$.source') = 'udf' AND HOUR = '04' AND get_json_object(extra, '$.version') IN ( SELECT max(get_json_object(extra, '$.version')) FROM al_catering.app_algoplatform_predictor_result WHERE dt = '20250323' AND get_json_object(extra, '$.source') = 'udf' AND HOUR = '04' ) AND match_time_str > '2025-03-26 14:00:00'\\\", " +
                "\\\"taskTimeCalByReq\\\": [{\\\"set\\\": \\\"default_cell\\\", \\\"name\\\": \\\"algoplatform.predictor.predict.listener_staging#com.sankuai.algoplatform.predictor\\\", \\\"type\\\": \\\"MafkaRecvMessage\\\", \\\"appkey\\\": \\\"com.sankuai.algoplatform.predictor\\\"}, " +
                "{\\\"set\\\": \\\"gray-release-fast-food\\\", \\\"name\\\": \\\"algoplatform.predictor.predict.listener_staging_AKFAMSWIMLANE_AKFAMSETgray-release-fast-food#com.sankuai.algoplatform.predictor\\\", \\\"type\\\": \\\"MafkaRecvMessage\\\", \\\"appkey\\\": \\\"com.sankuai.algoplatform.predictor\\\"}]}\", " +
                "\"matchStrategyName\": \"11月精准匹配正餐迭代\", " +
                "\"resourceGroupName\": \"精准匹配跑数\"}";
        subTask.setExtraInfo(extraInfo);

        // 3. 执行测试
        taskExecuteService.runSubTask(subTask);

        // 4. 打印结果
        System.out.println("XT Task execution completed. Task status: " + subTask.getStatus());
        System.out.println("XT Task extraInfo: " + subTask.getExtraInfo());
    }
}
