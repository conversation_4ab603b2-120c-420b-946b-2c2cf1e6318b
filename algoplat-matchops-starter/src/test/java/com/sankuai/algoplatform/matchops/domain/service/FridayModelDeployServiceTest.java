package com.sankuai.algoplatform.matchops.domain.service;

import com.alibaba.fastjson.JSON;
import com.sankuai.algoplatform.matchops.BaseTest;
import com.sankuai.algoplatform.matchops.infrastructure.model.FridayServiceInstance;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.FridayService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
public class FridayModelDeployServiceTest extends BaseTest {
    @Resource
    private FridayService fridayService;

//    @Test
    public void testQueryInstanceStatus() {
//        List<FridayServiceInstance> instances = fridayService.queryInstanceStatus("ceshiyuanxiande");
        List<FridayServiceInstance> instances = fridayService.queryInstanceStatus("testststst");
//        List<FridayServiceInstance> instances = fridayModelDeployService.queryInstanceStatus("ceshimoxing");
        log.info("testQueryInstanceStatus:{}", JSON.toJSONString(instances));
        // [{"createTime":"2024-10-25 16:46:43","instanceDetail":"23核/245G/L40-48G*1","instanceName":"set-hh-llm-fridayllm-qwen33333s1662a08.mt","status":"运行中"},{"createTime":"2024-10-31 14:14:39","instanceDetail":"23核/245G/L40-48G*1","status":"实例准备中"}]
    }

//    @Test
    public void testAddInstance() {
        Pair<Boolean, String> res = fridayService.addInstance("ceshiyuanxiande", 1);
        //资源不足value：mtthrift remote(**************:9001) invoke(addInstance), traceId:8666227543023729400 Exception:java.lang.IllegalArgumentException:CPU/Memory资源不足, 请联系管理员, Queue：root.hh_serving_cluster.hadoop-aipnlp.serving, CPU:1000, Memory:10584(AppUserExecService.java,arrangeInstances() line 1593) traceId:8666227543023729400
        log.info("testAddInstance:{}", JSON.toJSONString(res));
    }

//    @Test
    public void testDeleteInstance() {
        Pair<Boolean, String> res = fridayService.deleteInstance("ceshiyuanxiande", 100);
        log.info("testDeleteInstance:{}", JSON.toJSONString(res));
    }
}
