package com.sankuai.algoplatform.matchops.infrastructure.proxy;

import com.sankuai.algoplatform.matchops.BaseTest;
import com.sankuai.algoplatform.matchops.domain.enums.EnvStatus;
import com.sankuai.algoplatform.matchops.domain.service.testtool.LionHelper;
import com.sankuai.algoplatform.matchops.infrastructure.model.LionConfigReq;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import static com.sankuai.algoplatform.matchops.domain.service.testtool.LionHelper.*;

@Slf4j
public class LionConfigTest extends BaseTest {

    //    @Test
    public void queryTest() {
        LionConfigReq req = new LionConfigReq();
        req.setEnv("test");
        req.setAppkey("com.sankuai.algoplatform.predictor");
        req.setKey("predict_cache_config");
        String s = LionConfigService.queryLionConfig(req);
        log.info("{}", s);
    }

    //    @Test
    public void updateTest() {
        LionConfigReq req = new LionConfigReq();
        req.setEnv("test");
        req.setAppkey("com.sankuai.algoplatform.predictor");
        req.setKey("predict_cache_switch");
        req.setValue("true");
        req.setSet("gray-release-big-request");
        LionConfigService.setLionConfig(req);
        assert req != null;
    }

    //    @Test
    public void setPredictorCache1ConfigTest() {
        String value = "[\n" +
                "    {\n" +
                "        \"bizCode\": \"zb_dealMatching\",\n" +
                "        \"version\": \"1\",\n" +
                "        \"expireMinutes\": 2880\n" +
                "    },\n" +
                "    {\n" +
                "        \"bizCode\": \"dish_struct_recognize\",\n" +
                "        \"version\": \"1\",\n" +
                "        \"expireMinutes\": 2880\n" +
                "    },\n" +
                "    {\n" +
                "        \"bizCode\": \"test-pypy\",\n" +
                "        \"version\": \"3\",\n" +
                "        \"expireMinutes\": 2880\n" +
                "    },\n" +
                "    {\n" +
                "        \"bizCode\": \"test\",\n" +
                "        \"version\": \"2\",\n" +
                "        \"expireMinutes\": 2880\n" +
                "    },\n" +
                "    {\n" +
                "        \"bizCode\": \"test-mjf\",\n" +
                "        \"version\": \"16\",\n" +
                "        \"expireMinutes\": 2880\n" +
                "    },\n" +
                "    {\n" +
                "        \"bizCode\": \"zb_dealApproximateMatching\",\n" +
                "        \"version\": \"1\",\n" +
                "        \"expireMinutes\": 2880\n" +
                "    },\n" +
                "    {\n" +
                "        \"bizCode\": \"test-approxmatch\",\n" +
                "        \"version\": \"2\",\n" +
                "        \"expireMinutes\": 2880\n" +
                "    },\n" +
                "    {\n" +
                "        \"bizCode\": \"zb_bmlDealTag\",\n" +
                "        \"version\": \"3\",\n" +
                "        \"expireMinutes\": 2880\n" +
                "    },\n" +
                "    {\n" +
                "        \"bizCode\": \"zb_sameGoodsMatch\",\n" +
                "        \"version\": \"4\",\n" +
                "        \"expireMinutes\": 2880\n" +
                "    },\n" +
                "    {\n" +
                "        \"bizCode\": \"zb_dealDjOcrMatching\",\n" +
                "        \"version\": \"1\",\n" +
                "        \"expireMinutes\": 2880\n" +
                "    },\n" +
                "    {\n" +
                "        \"bizCode\": \"zb_dealDjOcrMatching_st\",\n" +
                "        \"version\": \"1\",\n" +
                "        \"expireMinutes\": 2880\n" +
                "    }\n" +
                "]";
        setPredictorCache1Config(EnvStatus.ST.getName(), value);
    }


}
