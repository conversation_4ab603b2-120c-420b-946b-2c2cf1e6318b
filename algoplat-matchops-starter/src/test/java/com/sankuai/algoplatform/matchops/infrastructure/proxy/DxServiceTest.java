package com.sankuai.algoplatform.matchops.infrastructure.proxy;

import com.google.common.collect.Lists;
import com.sankuai.algoplatform.matchops.BaseTest;
import org.junit.Test;

import javax.annotation.Resource;

public class DxServiceTest extends BaseTest {
    @Resource
    private DxService dxService;

    @Test
    public void sendMsg2Users() {
        dxService.sendMsg2Users("test", Lists.newArrayList("liyue31"));
    }

    @Test
    public void sendMsg2Room() {
        dxService.sendMsg2Group("test", 64012900753L);
    }
}