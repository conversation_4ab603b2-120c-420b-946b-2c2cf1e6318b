package com.sankuai.algoplatform.matchops.starter.service;

import com.alibaba.fastjson.JSON;
import com.sankuai.algoplatform.matchops.BaseTest;
import com.sankuai.algoplatform.matchops.api.request.testScene.TAddEditTestSceneRequest;
import com.sankuai.algoplatform.matchops.api.request.testScene.TDeleteTestSceneRequest;
import com.sankuai.algoplatform.matchops.api.request.testScene.TTestSceneListRequest;
import com.sankuai.algoplatform.matchops.api.response.testScene.TAddTestSceneResponse;
import com.sankuai.algoplatform.matchops.api.response.testScene.TDeleteTestSceneResponse;
import com.sankuai.algoplatform.matchops.api.response.testScene.TEditTestSceneResponse;
import com.sankuai.algoplatform.matchops.api.response.testScene.TTestSceneListResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class TestSceneServiceTTestImpl extends BaseTest {

    @Autowired
    private TTestSceneServiceImpl testSceneService;

    @Test
    public void testGetTestSceneList() {
        TTestSceneListRequest tTestSceneListRequest = new TTestSceneListRequest();
        tTestSceneListRequest.setCurrent(1);
        tTestSceneListRequest.setBizLineId(1L);
        log.info("{}", JSON.toJSONString(tTestSceneListRequest));
        TTestSceneListResponse testSceneListResponse = testSceneService.getTestSceneList(tTestSceneListRequest);
        log.info("{}", JSON.toJSONString(testSceneListResponse));
    }

    @Test
    public void testAddTestSceneList() {
        String str = "{\n" + "    \"sceneName\": \"精准匹配\",\n" + "    \"runType\": \"0\",\n"
                + "    \"runAddress\": \"https://xt.sankuai.com/workbench/task/al_catering.app_bml_dj_mt_deal_appro_match/version\",\n"
                + "    \"bizLineId\": 1,\n" + "    \"owner\": \"xxx\"\n" + "}";
        TAddEditTestSceneRequest request = JSON.parseObject(str, TAddEditTestSceneRequest.class);
        log.info("{}", JSON.toJSONString(request));
        TAddTestSceneResponse tBaseResponse = testSceneService.addTestSceneList(request);
        log.info("{}", JSON.toJSONString(tBaseResponse));
    }

    @Test
    public void testEditTestSceneList() {
        String str = "{\n" + "    \"sceneId\":1,\n" + "    \"sceneName\": \"精准匹配1\",\n" + "    \"runType\": \"1\",\n"
                + "    \"runAddress\": \"https://xt.sankuai.com/workbench/task/al_catering.app_bml_dj_mt_deal_appro_match/version1\",\n"
                + "    \"owner\": \"xxx1\"\n" + "}";
        TAddEditTestSceneRequest request = JSON.parseObject(str, TAddEditTestSceneRequest.class);
        log.info("{}", JSON.toJSONString(request));
        TEditTestSceneResponse tBaseResponse = testSceneService.editTestSceneList(request);
        log.info("{}", JSON.toJSONString(tBaseResponse));
    }

    @Test
    public void testDelTestSceneList() {
        TDeleteTestSceneRequest request = new TDeleteTestSceneRequest();
        request.setSceneId(1L);
//        request.setOwner("siweichen");
        TDeleteTestSceneResponse tBaseResponse = testSceneService.delTestSceneList(request);
        log.info("{}", JSON.toJSONString(tBaseResponse));
    }
}