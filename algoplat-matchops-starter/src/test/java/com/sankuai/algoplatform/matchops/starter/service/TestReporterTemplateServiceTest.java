package com.sankuai.algoplatform.matchops.starter.service;

import com.alibaba.fastjson.JSON;
import com.sankuai.algoplatform.matchops.BaseTest;
import com.sankuai.algoplatform.matchops.api.request.testreprotertemplate.TAddEditTestReporterTemplateRequest;
import com.sankuai.algoplatform.matchops.api.request.testreprotertemplate.TDeleteTestReporterTemplateRequest;
import com.sankuai.algoplatform.matchops.api.request.testreprotertemplate.TTestReporterTemplateListRequest;
import com.sankuai.algoplatform.matchops.api.response.testreportertemplate.TAddTestReporterTemplateResponse;
import com.sankuai.algoplatform.matchops.api.response.testreportertemplate.TDeleteTestReporterTemplateResponse;
import com.sankuai.algoplatform.matchops.api.response.testreportertemplate.TEditTestReporterTemplateResponse;
import com.sankuai.algoplatform.matchops.api.response.testreportertemplate.TTestReporterTemplateListResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class TestReporterTemplateServiceTest extends BaseTest {

    @Autowired
    private TestReporterTemplateService testReporterTemplateService;

    @Test
    public void testGetTestReporterTemplateList() {
        TTestReporterTemplateListRequest request = new TTestReporterTemplateListRequest();
        request.setBizLineId(1L);
        request.setCurrent(1);
        TTestReporterTemplateListResponse response = testReporterTemplateService.getTestReporterTemplateList(request);
        log.info("{}", JSON.toJSONString(response));
    }

    @Test
    public void testAddTestReporterTemplate() {
        String str = "{\n" + "    \"templateName\": \"精准匹配性能测试\",\n"
                + "    \"templateAddr\": \"https://km.sankuai.com/collabpage/24569044263\", \n"
                + "\t\t \"owner\":\"xxx\",\n" + "       \"bizLineId\":1\n" + "}";
        TAddEditTestReporterTemplateRequest request = JSON.parseObject(str, TAddEditTestReporterTemplateRequest.class);
        log.info("{}", JSON.toJSONString(request));
        TAddTestReporterTemplateResponse result = testReporterTemplateService.addTestReporterTemplate(request);
        log.info("{}", JSON.toJSONString(result));
    }

    @Test
    public void testEditTestReporterTemplate() {
        String str = "{\n" + "    \"templateId\": 1, \n" + "    \"templateName\": \"精准匹配性能测试1\", \n"
                + "    \"templateAddr\": \"https://km.sankuai.com/collabpage/245690442631\",\n"
                + "\t\t \"owner\":\"xxx1\"\n" + "}";
        TAddEditTestReporterTemplateRequest request = JSON.parseObject(str, TAddEditTestReporterTemplateRequest.class);
        log.info("{}", JSON.toJSONString(request));
        TEditTestReporterTemplateResponse result = testReporterTemplateService.editTestReporterTemplate(request);
        log.info("{}", JSON.toJSONString(result));
    }

    @Test
    public void testDelTestReporterTemplate() {
        TDeleteTestReporterTemplateRequest request = new TDeleteTestReporterTemplateRequest();
        request.setTemplateId(1L);
//        request.setOwner("siweichen");
        TDeleteTestReporterTemplateResponse tBaseResponse = testReporterTemplateService.delTestReporterTemplate(request);
        log.info("{}", JSON.toJSONString(tBaseResponse));
    }
}