package com.sankuai.algoplatform.matchops.domain.ability.gpuschedule.rule;

import com.sankuai.algoplatform.matchops.domain.ability.gpuschedule.ScheduleTask;
import com.sankuai.algoplatform.matchops.domain.model.ScheduleTaskCheckResult;
import com.sankuai.algoplatform.matchops.infrastructure.util.DateUtil;
import org.junit.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;


public class TimedScheduleRuleExecutorTest {

    /**
     * 测试 generateCheckResult 方法，TimedScheduleType 为 ONCE，时间点在范围内
     */
    @Test
    public void testGenerateCheckResultTypeOnceInRange() throws Throwable {
        {
            Map<String, Object> params = new HashMap<>();
            params.put("beforeTime", DateUtil.parseDate("2024-11-01 12:00:00"));
            params.put("afterTime", DateUtil.parseDate("2024-11-02 12:00:00"));
            TimedScheduleRuleExecutor executor = new TimedScheduleRuleExecutor(new ScheduleTask(),
                    "{\"scheduleDateTime\":\"2024-11-01 14:00:00\", \"scheduleType\":2}");
            List<ScheduleTaskCheckResult> results = executor.generateCheckResult(params);
            assertEquals(1, results.size());

        }
        {
            Map<String, Object> params = new HashMap<>();
            params.put("beforeTime", DateUtil.parseDate("2024-11-01 12:00:00"));
            params.put("afterTime", DateUtil.parseDate("2024-11-02 12:00:00"));
            TimedScheduleRuleExecutor executor = new TimedScheduleRuleExecutor(new ScheduleTask(),
                    "{\"scheduleDateTime\":\"2024-11-01 12:00:00\", \"scheduleType\":2}");
            List<ScheduleTaskCheckResult> results = executor.generateCheckResult(params);
            assertEquals(1, results.size());
        }
        {
            Map<String, Object> params = new HashMap<>();
            params.put("beforeTime", DateUtil.parseDate("2024-11-01 12:00:00"));
            params.put("afterTime", DateUtil.parseDate("2024-11-02 12:00:00"));
            TimedScheduleRuleExecutor executor = new TimedScheduleRuleExecutor(new ScheduleTask(),
                    "{\"scheduleDateTime\":\"2024-11-02 12:00:00\", \"scheduleType\":2}");
            List<ScheduleTaskCheckResult> results = executor.generateCheckResult(params);
            assertEquals(1, results.size());
        }
        {
            Map<String, Object> params = new HashMap<>();
            params.put("beforeTime", DateUtil.parseDate("2024-11-01 12:00:00"));
            params.put("afterTime", DateUtil.parseDate("2024-11-02 12:00:00"));
            TimedScheduleRuleExecutor executor = new TimedScheduleRuleExecutor(new ScheduleTask(),
                    "{\"scheduleDateTime\":\"2024-11-02 14:00:00\", \"scheduleType\":2}");
            List<ScheduleTaskCheckResult> results = executor.generateCheckResult(params);
            assertEquals(0, results.size());
        }
        {
            Map<String, Object> params = new HashMap<>();
            params.put("beforeTime", DateUtil.parseDate("2024-11-01 12:00:00"));
            params.put("afterTime", DateUtil.parseDate("2024-11-02 12:00:00"));
            TimedScheduleRuleExecutor executor = new TimedScheduleRuleExecutor(new ScheduleTask(),
                    "{\"scheduleDateTime\":\"2024-11-01 10:00:00\", \"scheduleType\":2}");
            List<ScheduleTaskCheckResult> results = executor.generateCheckResult(params);
            assertEquals(0, results.size());
        }
    }


    /**
     * 测试 generateCheckResult 方法，TimedScheduleType 为 DAILY，时间点在范围内的每一天
     */
    @Test
    public void testGenerateCheckResultTypeDailyInRange() throws Throwable {
        {
            Map<String, Object> params = new HashMap<>();
            params.put("beforeTime", DateUtil.parseDate("2024-11-01 12:00:00"));
            params.put("afterTime", DateUtil.parseDate("2024-11-02 12:00:00"));
            TimedScheduleRuleExecutor executor = new TimedScheduleRuleExecutor(new ScheduleTask(),
                    "{\"scheduleDateTime\":\"14:00:00\", \"scheduleType\":1}");
            List<ScheduleTaskCheckResult> results = executor.generateCheckResult(params);
            assertEquals(1, results.size());
        }
        {
            Map<String, Object> params = new HashMap<>();
            params.put("beforeTime", DateUtil.parseDate("2024-11-01 12:00:00"));
            params.put("afterTime", DateUtil.parseDate("2024-11-02 12:00:00"));
            TimedScheduleRuleExecutor executor = new TimedScheduleRuleExecutor(new ScheduleTask(),
                    "{\"scheduleDateTime\":\"12:00:00\", \"scheduleType\":1}");
            List<ScheduleTaskCheckResult> results = executor.generateCheckResult(params);
            assertEquals(2, results.size());
        }
        {
            Map<String, Object> params = new HashMap<>();
            params.put("beforeTime", DateUtil.parseDate("2024-11-01 12:00:00"));
            params.put("afterTime", DateUtil.parseDate("2024-11-02 12:00:00"));
            TimedScheduleRuleExecutor executor = new TimedScheduleRuleExecutor(new ScheduleTask(),
                    "{\"scheduleDateTime\":\"10:00:00\", \"scheduleType\":1}");
            List<ScheduleTaskCheckResult> results = executor.generateCheckResult(params);
            assertEquals(1, results.size());
        }
        {
            Map<String, Object> params = new HashMap<>();
            params.put("beforeTime", DateUtil.parseDate("2024-11-01 12:00:00"));
            params.put("afterTime", DateUtil.parseDate("2024-11-02 12:00:00"));
            TimedScheduleRuleExecutor executor = new TimedScheduleRuleExecutor(new ScheduleTask(),
                    "{\"scheduleDateTime\":\"00:00:00\", \"scheduleType\":1}");
            List<ScheduleTaskCheckResult> results = executor.generateCheckResult(params);
            assertEquals(1, results.size());
        }
        {
            Map<String, Object> params = new HashMap<>();
            params.put("beforeTime", DateUtil.parseDate("2024-11-01 20:00:00"));
            params.put("afterTime", DateUtil.parseDate("2024-11-02 12:00:00"));
            TimedScheduleRuleExecutor executor = new TimedScheduleRuleExecutor(new ScheduleTask(),
                    "{\"scheduleDateTime\":\"16:00:00\", \"scheduleType\":1}");
            List<ScheduleTaskCheckResult> results = executor.generateCheckResult(params);
            assertEquals(0, results.size());
        }
    }
}
