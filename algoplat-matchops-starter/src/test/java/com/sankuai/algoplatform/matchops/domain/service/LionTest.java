package com.sankuai.algoplatform.matchops.domain.service;

import com.alibaba.fastjson.JSON;
import com.sankuai.algoplatform.matchops.BaseTest;
import com.sankuai.algoplatform.matchops.domain.model.testtool.PredictorCache1Config;
import com.sankuai.algoplatform.matchops.domain.model.testtool.PredictorCache234Config;
import com.sankuai.algoplatform.matchops.domain.service.testtool.LionHelper;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.List;

@Slf4j
public class LionTest extends BaseTest {

    private final String env = "test";

    @Test
    public void getConfig(){
        String cache1Switch = LionHelper.getPredictorCache1Switch(env);
        log.info("{}", cache1Switch);

        List<PredictorCache1Config> predictorCache1Config = LionHelper.getPredictorCache1Config(env);
        log.info("{}", JSON.toJSONString(predictorCache1Config));

        PredictorCache234Config cache234Config = LionHelper.getPredictorCache234Config(env, "default");
        log.info("{}", JSON.toJSONString(cache234Config));
    }

    @Test
    public void setConfig(){
        LionHelper.getPredictorCache1Switch("env");
    }

}
