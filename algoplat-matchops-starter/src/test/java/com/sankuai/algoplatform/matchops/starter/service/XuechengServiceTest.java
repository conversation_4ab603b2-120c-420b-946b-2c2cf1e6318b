package com.sankuai.algoplatform.matchops.starter.service;

import com.alibaba.druid.sql.dialect.sqlserver.ast.SQLServerOutput;
import com.alibaba.fastjson.JSON;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.dianping.zebra.util.StringUtils;
import com.meituan.talos.commons.domain.User;
import com.sankuai.algoplatform.matchops.BaseTest;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.CookieConfigDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.CookieConfig;
import com.sankuai.algoplatform.matchops.infrastructure.dal.po.XmAddResult;
import com.sankuai.algoplatform.matchops.infrastructure.dal.po.XmContentResult;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.XuechengService;
import com.sankuai.algoplatform.matchops.infrastructure.util.ContextUtil;
import com.sankuai.ead.citadel.document.node.concept.Block;
import com.sankuai.ead.citadel.document.node.impl.node.Doc;
import com.sankuai.ead.citadel.document.node.impl.node.Heading;
import com.sankuai.ead.citadel.document.node.impl.node.TableRow;
import com.sankuai.ead.citadel.document.parser.DocumentParsingException;
import com.sankuai.ead.citadel.document.parser.Serializer;
import com.sankuai.ead.citadel.document.processor.impl.DocValidateResult;
import com.sankuai.ead.citadel.document.processor.impl.DocValidator;
import com.sankuai.ead.citadel.document.util.Builder;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.List;

import static com.sankuai.ead.citadel.document.util.Builder.*;

@Slf4j
public class XuechengServiceTest extends BaseTest {
    @Resource
    private XuechengService xuechengService;

    @Resource
    private CookieConfigDao cookieConfigDao;

    @Test
    @Ignore
    public void testAddCollaborationContent() {
        List<Block> blocks = Lists.newArrayList();
        blocks.addAll(createOneBlock());
        Block[] blockArray = blocks.toArray(new Block[blocks.size()]);
        String title = "无标题";
        Doc doc = Builder.doc(title(title), blockArray);
        DocValidateResult valid = DocValidator.valid(doc);
        try {
            String serialize = Serializer.serialize(doc);
            XmAddResult xmAddResult = xuechengService.addCollaborationContentByApp( "test", "test");
            log.info("{}", JSON.toJSONString(xmAddResult));
        } catch (DocumentParsingException e) {
            throw new RuntimeException(e);
        }


    }

    private List<Block> createOneBlock() {
        List<Block> blocks = Lists.newArrayList();
        blocks.add(heading(2, "这是个TODO"));
        TableRow tableHeaderRow = tableRow(
                tableHeader("日期").colwidth(130),
                tableHeader("事项").colwidth(400),
                tableHeader("跟进人").colwidth(130),
                tableHeader("状态").colwidth(130),
                tableHeader("进展说明").colwidth(200)
        );
        TableRow tableRow = tableRow(
                tableCell(paragraph(" ")).colwidth(130),
                tableCell(paragraph(" ")).colwidth(400),
                tableCell(paragraph(" ")).colwidth(130),
                tableCell(paragraph(" ")).colwidth(130),
                tableCell(paragraph(" ")).colwidth(200)
        );
        blocks.add(table(tableHeaderRow, tableRow));
        return blocks;
    }

    @Test
    public void testGetCollaborationContent() {
        CookieConfig config = cookieConfigDao.selectByMis("zhoumi10");
        XmContentResult collaborationContent = xuechengService.getCollaborationContent("**********",  "zhoumi10");
        log.info("{}", collaborationContent);
    }

//    @Test
    public void testGetCollaborationContent2() {
        CookieConfig config = cookieConfigDao.selectByMis("zhoumi10");
        XmContentResult collaborationContent = xuechengService.getCollaborationContent("**********","zhoumi10");
        log.info("{}", collaborationContent);
    }

//    @Test
    public void testAddCollaborationContent2() {
        CookieConfig config = cookieConfigDao.selectByMis("chenping11");
        XmContentResult content = xuechengService.getCollaborationContent("**********",  "zhoumi10");
        XmAddResult addResult = xuechengService.addCollaborationContentByApp(content.getContent(), "测试创建4");
        log.info("{}", JSON.toJSONString(addResult));
    }


}