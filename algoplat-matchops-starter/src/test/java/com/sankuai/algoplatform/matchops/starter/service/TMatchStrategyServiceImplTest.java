package com.sankuai.algoplatform.matchops.starter.service;

import com.alibaba.fastjson.JSON;
import com.sankuai.algoplatform.matchops.BaseTest;
import com.sankuai.algoplatform.matchops.api.request.matchStrategy.TAddEditMatchStrategyRequest;
import com.sankuai.algoplatform.matchops.api.request.matchStrategy.TDeleteMatchStrategyRequest;
import com.sankuai.algoplatform.matchops.api.request.matchStrategy.TMatchStrategyListRequest;
import com.sankuai.algoplatform.matchops.api.request.matchStrategy.TQueryMatchStrategyRequest;
import com.sankuai.algoplatform.matchops.api.response.matchStrategy.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class TMatchStrategyServiceImplTest extends BaseTest {
    @Autowired
    private TMatchStrategyServiceImpl TMatchStrategyServiceImpl;
    @Test
    public void testGetMatchStrategyList() {
        TMatchStrategyListRequest tMatchStrategyListRequest = new TMatchStrategyListRequest(); 
        tMatchStrategyListRequest.setCurrent(1);
        tMatchStrategyListRequest.setBizLineId(1L);
        log.info("{}", JSON.toJSONString(tMatchStrategyListRequest));
        TMatchStrategyListResponse tMatchStrategyListResponse =  TMatchStrategyServiceImpl.getMatchStrategyList(tMatchStrategyListRequest);
        log.info("{}", JSON.toJSONString(tMatchStrategyListResponse));
    }

    @Test
    public void testAddMatchStrategy() {
        String str = "{\"matchStrategyName\":\"托尔斯泰\",\"sceneId\":\"1\",\"algoCodeInfo\":[{}],\"predictorCache\":{\"setName\":\"手动方式\",\"cache1\":1,\"cache3\":1,\"cache2\":0,\"cache4\":0},\"llmInfo\":[{}],\"octoInfo\":[{\"appkey\":\"发的v方便\",\"setName\":\"我的二哥\",\"branch\":\"asdfgn\",\"lionConfig\":\"{\\\"g\\\":1}\"}],\"bizLineId\":\"1\"}";
        TAddEditMatchStrategyRequest request = JSON.parseObject(str, TAddEditMatchStrategyRequest.class);
        log.info("{}", JSON.toJSONString(request));
        TAddMatchStrategyResponse tBaseResponse = TMatchStrategyServiceImpl.addMatchStrategy(request);
        log.info("{}", JSON.toJSONString(tBaseResponse));
    }

    @Test
    public void testEditMatchStrategy() {
        String str = "{\n" + "    \"matchStrategyId\":2,\n" + "    \"matchStrategyName\": \"10月精准匹配迭代1\",\n"
                + "    \"algoCodeInfo\": [\n" + "        {\n" + "            \"algoBizCode\": \"test1\",\n"
                + "            \"prodAlgoBizCode\": \"zb_dealMatching1\"\n" + "        }\n" + "    ],\n"
                + "    \"predictorCache\": {\n" + "        \"setName\": \"default1\",\n"
                + "        \"cache1\": \"0\",\n" + "        \"cache2\": \"0\",\n" + "        \"cache3\": \"0\", \n"
                + "        \"cache4\": \"0\"\n" + "    },\n" + "    \"llmInfo\": [\n" + "        {\n"
                + "            \"algoBizCode\": \"test1\",\n"
                + "            \"prodAlgoBizCode\": \"zb_dealMatching1\" \n" + "        }\n" + "    ],\n"
                + "    \"octoInfo\": [ \n" + "        {\n"
                + "            \"appkey\": \"com.sankuai.algoplatform.bmlserver1\",\n"
                + "            \"setName\": \"default1\",\n"
                + "            \"lionConfig\": \"{\\\"k11\\\":\\\"v11\\\",\\\"k21\\\":\\\"v21\\\"}\",\n"
                + "            \"branch\": \"featture/xxxxxxx1\"\n" + "        }\n" + "    ],\n"
                + "      \"owner\":\"xxx1\"\n" + "}";
        TAddEditMatchStrategyRequest request = JSON.parseObject(str, TAddEditMatchStrategyRequest.class);
        log.info("{}", JSON.toJSONString(request));
        TEditMatchStrategyResponse tBaseResponse = TMatchStrategyServiceImpl.editMatchStrategy(request);
        log.info("{}", JSON.toJSONString(tBaseResponse));
    }
    @Test
    public void testQueryMatchStrategy() {
        TQueryMatchStrategyRequest request = new TQueryMatchStrategyRequest();
        request.setMatchStrategyId(2L);
        TQueryMatchStrategyResponse tBaseResponse = TMatchStrategyServiceImpl.queryMatchStrategy(request);
        log.info("{}", JSON.toJSONString(tBaseResponse));
    }
    @Test
    public void testDeleteMatchStrategy() {
        TDeleteMatchStrategyRequest request = new TDeleteMatchStrategyRequest();
        request.setMatchStrategyId(2L);
//        request.setOwner("siweichen");
        TDeleteMatchStrategyResponse tBaseResponse = TMatchStrategyServiceImpl.deleteMatchStrategy(request);
        log.info("{}", JSON.toJSONString(tBaseResponse));
    }
}