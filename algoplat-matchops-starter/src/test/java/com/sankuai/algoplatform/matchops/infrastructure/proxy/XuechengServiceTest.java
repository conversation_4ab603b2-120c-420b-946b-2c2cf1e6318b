package com.sankuai.algoplatform.matchops.infrastructure.proxy;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.algoplatform.matchops.BaseTest;
import com.sankuai.algoplatform.matchops.infrastructure.config.OAuthConfig;
import com.sankuai.algoplatform.matchops.infrastructure.dal.po.XmAddResult;
import com.sankuai.algoplatform.matchops.infrastructure.dal.po.XmContentResult;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.CitadelService;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.req.AddCollaborationContentByAppReq;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.resp.AddCollaborationContentByAppResp;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.resp.GetCollaborationContentByAppResp;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class XuechengServiceTest extends BaseTest {

    @Autowired
    private XuechengService xuechengService;

    @Autowired
    private CitadelService citadelService;

    @Autowired
    private DxService dxService;

    @Autowired
    private OAuthConfig oAuthConfig;


    @Test
    public void testAddCollaborationContentByApp() {

        String content ="{\"type\":\"doc\",\"content\":[{\"type\":\"title\",\"attrs\":{\"nodeId\":\"b655c03bcbee4fc5abbfea540f82a208\"},\"content\":[{\"type\":\"text\",\"text\":\"数据分析报告产生出测试2\"}]},{\"type\":\"heading\",\"attrs\":{\"id\":\"445c473f-0aae-4c8d-84ac-f0ce9d3d1c68\",\"level\":1,\"indent\":0,\"align\":\"left\",\"dataDiffId\":null,\"nodeId\":\"791653c3d0094ebc8c6a0f17494d5713\"},\"content\":[{\"type\":\"text\",\"text\":\"数据分析报告\"}]},{\"type\":\"heading\",\"attrs\":{\"id\":\"6ea2ffd7-1dd3-41c5-9516-17828cb1469a\",\"level\":2,\"indent\":0,\"align\":\"left\",\"dataDiffId\":null,\"nodeId\":\"f7ccf590809044d49915f9faed5ca06b\"},\"content\":[{\"type\":\"text\",\"text\":\"1. 整体汇总表格\"}]},{\"type\":\"table\",\"attrs\":{\"indent\":0,\"responsive\":true,\"borderStyle\":\"solid\",\"borderWidth\":1,\"borderColor\":\"#dddddd\",\"dataDiffId\":null,\"nodeId\":\"cf57ec8741fb49be8d253cfc2c1419e3\"},\"content\":[{\"type\":\"table_row\",\"attrs\":{\"dataRowDiffId\":null,\"nodeId\":\"d8fd04a033b44629bc3786bdcd6c558d\"},\"content\":[{\"type\":\"table_header\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[68],\"textAlign\":null,\"verticalAlign\":null,\"bgColor\":null,\"color\":null,\"numCell\":null,\"dataCellDiffId\":null,\"nodeId\":\"2ac2a34567f74095a5ea18b75ea48dff\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"left\",\"dataDiffId\":null,\"nodeId\":\"c3772b6db1dd4282a3b81ba6c3926eb6\"},\"content\":[{\"type\":\"text\",\"text\":\"日期\"}]}]},{\"type\":\"table_header\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[68],\"textAlign\":null,\"verticalAlign\":null,\"bgColor\":null,\"color\":null,\"numCell\":null,\"dataCellDiffId\":null,\"nodeId\":\"6102178b488246c1a85a2277836754ba\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"left\",\"dataDiffId\":null,\"nodeId\":\"a323f0591f264d808488d18a723c3708\"},\"content\":[{\"type\":\"text\",\"text\":\"指标值\"}]}]},{\"type\":\"table_header\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[68],\"textAlign\":null,\"verticalAlign\":null,\"bgColor\":null,\"color\":null,\"numCell\":null,\"dataCellDiffId\":null,\"nodeId\":\"1f5260a317c844dc80d7575dd4530bd7\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"left\",\"dataDiffId\":null,\"nodeId\":\"4f81e5cfe4444b3c8e76034f9244eee3\"},\"content\":[{\"type\":\"text\",\"text\":\"WoW变化值\"}]}]}]},{\"type\":\"table_row\",\"attrs\":{\"dataRowDiffId\":null,\"nodeId\":\"5a6bc5b249b644e0824b56ae84e83597\"},\"content\":[{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[68],\"textAlign\":null,\"verticalAlign\":null,\"bgColor\":null,\"color\":null,\"numCell\":null,\"dataCellDiffId\":null,\"nodeId\":\"4b72b04b09344d5b87e424442c0870e9\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"left\",\"dataDiffId\":null,\"nodeId\":\"9127baa323e241119a14615d6568db74\"},\"content\":[{\"type\":\"text\",\"text\":\"2025-06-05\"}]}]},{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[68],\"textAlign\":null,\"verticalAlign\":null,\"bgColor\":null,\"color\":null,\"numCell\":null,\"dataCellDiffId\":null,\"nodeId\":\"b125a35d24d04e3bb4161660d20dee65\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"left\",\"dataDiffId\":null,\"nodeId\":\"74d9eb86db94494989861c8ec1edf4e8\"},\"content\":[{\"type\":\"text\",\"text\":\"37.05%\"}]}]},{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[68],\"textAlign\":null,\"verticalAlign\":null,\"bgColor\":null,\"color\":null,\"numCell\":null,\"dataCellDiffId\":null,\"nodeId\":\"d89092c0832145f28fd68f690f600277\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"left\",\"dataDiffId\":null,\"nodeId\":\"94fb6363753d4cfe98badcaaf616f079\"},\"content\":[{\"type\":\"text\",\"text\":\"1.49pp\"}]}]}]},{\"type\":\"table_row\",\"attrs\":{\"dataRowDiffId\":null,\"nodeId\":\"4bfbc7d32d89452ea1b7a668a2e03ce1\"},\"content\":[{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[68],\"textAlign\":null,\"verticalAlign\":null,\"bgColor\":null,\"color\":null,\"numCell\":null,\"dataCellDiffId\":null,\"nodeId\":\"54e6b59bb44a42feafe438e12cf4c827\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"left\",\"dataDiffId\":null,\"nodeId\":\"2fbfd15aaf9a4c67b71e1473dc941476\"},\"content\":[{\"type\":\"text\",\"text\":\"2025-05-29\"}]}]},{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[68],\"textAlign\":null,\"verticalAlign\":null,\"bgColor\":null,\"color\":null,\"numCell\":null,\"dataCellDiffId\":null,\"nodeId\":\"206206e5431a47b9b57e0265689c82d0\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"left\",\"dataDiffId\":null,\"nodeId\":\"e42ba48232d34ebdac1ebfb3a6229588\"},\"content\":[{\"type\":\"text\",\"text\":\"35.56%\"}]}]},{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[68],\"textAlign\":null,\"verticalAlign\":null,\"bgColor\":null,\"color\":null,\"numCell\":null,\"dataCellDiffId\":null,\"nodeId\":\"2a3d18a27376427aad2d8ef4b1f92bf0\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"left\",\"dataDiffId\":null,\"nodeId\":\"3effbcf3caa84339ae434e2158bfa45d\"},\"content\":[{\"type\":\"text\",\"text\":\"0.87pp\"}]}]}]},{\"type\":\"table_row\",\"attrs\":{\"dataRowDiffId\":null,\"nodeId\":\"354f058ae66e43e38fa680c08621f198\"},\"content\":[{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[68],\"textAlign\":null,\"verticalAlign\":null,\"bgColor\":null,\"color\":null,\"numCell\":null,\"dataCellDiffId\":null,\"nodeId\":\"2824dc1540754ffeb2a1a172cfc25f54\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"left\",\"dataDiffId\":null,\"nodeId\":\"f5835fded3234c45b711361fafb7d653\"},\"content\":[{\"type\":\"text\",\"text\":\"2025-05-22\"}]}]},{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[68],\"textAlign\":null,\"verticalAlign\":null,\"bgColor\":null,\"color\":null,\"numCell\":null,\"dataCellDiffId\":null,\"nodeId\":\"683ecae3550a4baa88d74205c8ef8446\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"left\",\"dataDiffId\":null,\"nodeId\":\"57746aad23804aa9bd7d7d587d349c1c\"},\"content\":[{\"type\":\"text\",\"text\":\"34.69%\"}]}]},{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[68],\"textAlign\":null,\"verticalAlign\":null,\"bgColor\":null,\"color\":null,\"numCell\":null,\"dataCellDiffId\":null,\"nodeId\":\"5a8f3702c3074c93b01622ad759da113\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"left\",\"dataDiffId\":null,\"nodeId\":\"640e1c8fa7594962a9bd72875a42d0f5\"},\"content\":[{\"type\":\"text\",\"text\":\"-0.22pp\"}]}]}]},{\"type\":\"table_row\",\"attrs\":{\"dataRowDiffId\":null,\"nodeId\":\"850e71293af3451595e204d266d71276\"},\"content\":[{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[68],\"textAlign\":null,\"verticalAlign\":null,\"bgColor\":null,\"color\":null,\"numCell\":null,\"dataCellDiffId\":null,\"nodeId\":\"b679958044864581b0e6a973c1f1abf4\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"left\",\"dataDiffId\":null,\"nodeId\":\"b9d03054954f4baab999066398e5f4e4\"},\"content\":[{\"type\":\"text\",\"text\":\"2025-05-15\"}]}]},{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[68],\"textAlign\":null,\"verticalAlign\":null,\"bgColor\":null,\"color\":null,\"numCell\":null,\"dataCellDiffId\":null,\"nodeId\":\"4fbca658b0be4ce78cb584d1ccf80b11\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"left\",\"dataDiffId\":null,\"nodeId\":\"90910d1ff0c848039223f0b9a754d00f\"},\"content\":[{\"type\":\"text\",\"text\":\"34.90%\"}]}]},{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[68],\"textAlign\":null,\"verticalAlign\":null,\"bgColor\":null,\"color\":null,\"numCell\":null,\"dataCellDiffId\":null,\"nodeId\":\"656c22bd7d9d466d909b9fedcbb8a4c0\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"left\",\"dataDiffId\":null,\"nodeId\":\"117194c6988948729002c89910a1d323\"},\"content\":[{\"type\":\"text\",\"text\":\"-1.20pp\"}]}]}]}]},{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"left\",\"dataDiffId\":null,\"nodeId\":\"3130ee74235f4ed7a61aee0452b13c00\"},\"content\":[{\"type\":\"text\",\"marks\":[{\"type\":\"strong\"}],\"text\":\"分析\"},{\"type\":\"text\",\"text\":\"：D+高销独品率在2025-06-05达到37.05%，较前一周增长1.49pp，呈现上升趋势。\"}]},{\"type\":\"horizontal_rule\",\"attrs\":{\"dataDiffId\":null,\"nodeId\":\"67263b9c11804012a7c8dbd6c4c5c592\"}},{\"type\":\"heading\",\"attrs\":{\"id\":\"27b59971-1917-4bec-b724-ca294be80251\",\"level\":2,\"indent\":0,\"align\":\"left\",\"dataDiffId\":null,\"nodeId\":\"2e38b440ee08471f9cb73edc8e540466\"},\"content\":[{\"type\":\"text\",\"text\":\"下载地址\"}]},{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"left\",\"dataDiffId\":null,\"nodeId\":\"fb7d194e58124ceda917c02190047abb\"},\"content\":[{\"type\":\"text\",\"text\":\"PDF报告下载地址： \"},{\"type\":\"link\",\"attrs\":{\"id\":\"\",\"href\":\"https://mss.vip.sankuai.com/mcpserver-s3/%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90%E6%8A%A5%E5%91%8A.pdf?AWSAccessKeyId=SRV_nnp36zQYTPQMgIH0qlHlmDw5v0KWRbLB&Expires=1783048386&Signature=8ksKN2soLx045a2erUN6jK2d1kc%3D\",\"title\":\"\",\"autoUpdate\":false,\"nodeId\":\"14fbbbdf82d64b91b904dfeec2f5604b\"},\"content\":[{\"type\":\"text\",\"text\":\"点击下载PDF报告\"}]}]},{\"type\":\"blockquote\",\"attrs\":{\"dataDiffId\":null,\"nodeId\":\"78a1c667fdb44d4f80bc47d62a38124d\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"left\",\"dataDiffId\":null,\"nodeId\":\"58662f591d5c40ceb9729c870a357c38\"},\"content\":[{\"type\":\"text\",\"text\":\"💡 提示：点击上方下载地址可下载完整的PDF格式报告，包含所有图表和详细分析内容。\"}]}]},{\"type\":\"horizontal_rule\",\"attrs\":{\"dataDiffId\":null,\"nodeId\":\"46006afee7cf46c18be97fcb22c11f28\"}},{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"left\",\"dataDiffId\":null,\"nodeId\":\"fd6883eee3a84b818041ef86bcf192c7\"},\"content\":[{\"type\":\"text\",\"marks\":[{\"type\":\"em\"}],\"text\":\"报告生成时间：2025-07-03 11:13:06\"}]}]}";
        String title = "测试标题";
        XmAddResult result = xuechengService.addCollaborationContentByApp(content, title);

        Assert.assertNotNull(result);
        Assert.assertEquals(Integer.valueOf(0), result.getCode());
        log.info("测试addCollaborationContentByApp方法结果: {}", JSON.toJSONString(result));
    }
    @Test
    public void readCollaborationContentByApp() {
        String docId = "4301928488";
        XmContentResult result = xuechengService.getCollaborationContent(docId, "zhouzehao02");
        Assert.assertNotNull(result);
        log.info("测试readCollaborationContentByApp方法结果: {}", JSON.toJSONString(result));
    }


}

