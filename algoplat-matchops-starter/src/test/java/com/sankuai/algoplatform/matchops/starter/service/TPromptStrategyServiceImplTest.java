package com.sankuai.algoplatform.matchops.starter.service;

import com.alibaba.fastjson.JSON;
import com.sankuai.algoplatform.matchops.BaseTest;
import com.sankuai.algoplatform.matchops.api.request.prompt.TAddEditPromptBizCodeRequest;
import com.sankuai.algoplatform.matchops.api.request.prompt.TAddEditPromptStrategyRequest;
import com.sankuai.algoplatform.matchops.api.request.prompt.TDeletePromptBizCodeRequest;
import com.sankuai.algoplatform.matchops.api.request.prompt.TPromptListRequest;
import com.sankuai.algoplatform.matchops.api.response.prompt.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class TPromptStrategyServiceImplTest extends BaseTest {

    @Autowired
    private TPromptStrategyServiceImpl TPromptStrategyServiceImpl;
    @Test
    public void testGetPromptList() {
        TPromptListRequest tPromptListRequest = new TPromptListRequest();
        tPromptListRequest.setCurrent(1);
        tPromptListRequest.setBizLineId(1L);
        TPromptListResponse tPromptListResponse = TPromptStrategyServiceImpl.getPromptList(tPromptListRequest);
        log.info("{}", JSON.toJSONString(tPromptListResponse));
    }

    @Test
    public void testAddPromptStrategy() {
        String str = "{\"bizLineId\":\"1\",\"llmBizCode\":\"bdsj\",\"llmStrategyName\":\"hjsbjh\",\"llmModelName\":\"csacsa\",\"llmServiceName\":\"csacas\",\"batchSize\":1,\"isTemplate\":1,\"appId\":\"1\",\"isDebug\":1,\"prompt\":\"1\",\"topP\":1,\"topK\":1,\"temperature\":1,\"maxTokens\":1,\"note\":\"1\"}\n";
        TAddEditPromptStrategyRequest request = JSON.parseObject(str, TAddEditPromptStrategyRequest.class);
        log.info("{}", JSON.toJSONString(request));
        TAddPromptStrategyResponse result = TPromptStrategyServiceImpl.addPromptStrategy(request);
        log.info("{}", JSON.toJSONString(result));
    }

    @Test
    public void testEditPromptStrategy() {
        String str = "{\"llmStrategyId\":\"51\",\"note\":\"说明说明\",\"llmServiceName\":\"测试工具匹配\",\"llmBizCode\":\"dc_testtool_match\",\"llmModelName\":\"dc_testtool_match\",\"isTemplate\":1,\"appId\":\"23132134224243414321\",\"maxTokens\":\"500\",\"temperature\":\"0.95\",\"batchSize\":\"50\",\"llmStrategyName\":\"说明说明\",\"isDebug\":1,\"topK\":\"1.0\",\"topP\":\"0.7\"}\n";
        TAddEditPromptStrategyRequest request = JSON.parseObject(str, TAddEditPromptStrategyRequest.class);
        log.info("{}", JSON.toJSONString(request));
        TEditPromptStrategyResponse result = TPromptStrategyServiceImpl.editPromptStrategy(request);
        log.info("{}", JSON.toJSONString(result));
    }
    @Test
    public void testAddPromptBizCode() {
        String str = "{\n" + "    \"llmBizCode\": \"dz_qipai_match11\", \n" + "    \"abTestName\": \"xxxx\",\n"
                + "    \"strategy\": {\n" + "        \"strategyName\": \"schema\",\n"
                + "        \"distributions\": \"[\n" + "            {\n"
                + "                \\\"strategyId\\\": \\\"17\\\", \n" + "                \\\"quota\\\": 20\n"
                + "            },\n" + "            {\n" + "                \\\"strategyId\\\": \\\"19\\\",\n"
                + "                \\\"quota\\\": 80\n" + "            }\n" + "        ]\"\n" + "    },\n"
                + "    \"note\": \"xxxxxx\",\n" + "    \"bizLineId\": 1,\n" + "       \"owner\":\"xxx\"\n" + "}";
        TAddEditPromptBizCodeRequest request = JSON.parseObject(str, TAddEditPromptBizCodeRequest.class);
        log.info("{}", JSON.toJSONString(request));
        TAddPromptBizCodeResponse tBaseResponse = TPromptStrategyServiceImpl.addPromptBizCode(request);
        log.info("{}", JSON.toJSONString(tBaseResponse));
    }
    @Test
    public void testEditPromptBizCode() {
        String str = "{\"bizLineId\":\"1\",\"note\":\"手动v方便\",\"llmBizCode\":\"额服务\",\"strategy\":{\"strategyName\":\"擦完\",\"distributions\":\"[{\\\"quota\\\":\\\"100\\\",\\\"strategyId\\\":\\\"52\\\"}]\"}}";
        TAddEditPromptBizCodeRequest request = JSON.parseObject(str, TAddEditPromptBizCodeRequest.class);
        log.info("{}", JSON.toJSONString(request));
        TEditPromptBizCodeResponse tBaseResponse = TPromptStrategyServiceImpl.editPromptBizCode(request);
        log.info("{}", JSON.toJSONString(tBaseResponse));
    }

    @Test
    public void testDeletePromptBizCode() {
        TDeletePromptBizCodeRequest request = new TDeletePromptBizCodeRequest();
        request.setLlmStrategyId(62L);
        request.setOwner("siweichen");
        TDeletePromptBizCodeResponse tBaseResponse = TPromptStrategyServiceImpl.deletePromptBizCode(request);
        log.info("{}", JSON.toJSONString(tBaseResponse));
    }
}