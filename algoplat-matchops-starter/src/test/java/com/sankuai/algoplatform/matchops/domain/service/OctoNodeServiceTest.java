package com.sankuai.algoplatform.matchops.domain.service;

import com.sankuai.algoplatform.matchops.BaseTest;
import com.sankuai.algoplatform.matchops.infrastructure.model.OctoNode;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.OctoNodeService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
public class OctoNodeServiceTest extends BaseTest {
    @Resource
    private OctoNodeService octoNodeService;


    //    @Test
    public void testQueryOctoNode() {
        List<OctoNode> nodes = octoNodeService.getOctoNodeStatus("com.sankuai.llm.alzbddpt.1024meifaps2706");
        log.info("{}", nodes);
    }

//    @Test
    public void getAppKeys() {
        List<String> appKeys = octoNodeService.getAppKeys("com.sankuai.tdcinside.relation");
        log.info("{}", appKeys);
    }


}
