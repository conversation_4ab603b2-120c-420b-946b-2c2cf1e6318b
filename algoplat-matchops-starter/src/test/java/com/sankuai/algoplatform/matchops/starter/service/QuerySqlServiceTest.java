package com.sankuai.algoplatform.matchops.starter.service;

import com.alibaba.fastjson.JSON;
import com.sankuai.algoplatform.matchops.BaseTest;
import com.sankuai.algoplatform.matchops.infrastructure.model.SqlResult;
import com.sankuai.algoplatform.matchops.domain.service.QuerySqlService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;

@Slf4j
public class QuerySqlServiceTest extends BaseTest {

    @Resource
    private QuerySqlService querySqlService;

//    @Test
    public void testQuerySqlService() throws Exception {
//        String sql = "select * from al_catering.app_algoplatform_predictor_result limit 10;";
//        String sql = "select count(*) from al_catering.app_dj_deal_match_full_all_d where partition_date = '2024-12-26';";
        String sql = "SELECT distinct dj_poi_id,\n" +
                "       dj_deal_id,\n" +
                "       mt_deal_id,\n" +
                "       mt_shop_id,\n" +
                "       mt_product_type_name,\n" +
                "       source,\n" +
                "       channel\n" +
                "  FROM log.com_sankuai_wenchang_infer_service_mid_result\n" +
                " WHERE dt = '20250107'\n" +
                "   AND _mt_client_env_ = 'staging'\n" +
                "   AND data_version = '大模型V1'\n" +
                "   AND dim_cat1_id = 38\n" +
                "   AND _mt_datetime BETWEEN '2025-01-07 15:53:00' AND '2025-01-07 15:58:00'";
        SqlResult sqlResult = querySqlService.executeSql(sql);
        log.info("{}", JSON.toJSON(sqlResult));
    }

  
}