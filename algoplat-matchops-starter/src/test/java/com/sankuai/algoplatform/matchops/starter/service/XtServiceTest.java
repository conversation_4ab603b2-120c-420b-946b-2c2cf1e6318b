package com.sankuai.algoplatform.matchops.starter.service;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.sankuai.algoplatform.matchops.BaseTest;
import com.sankuai.algoplatform.matchops.domain.model.testtool.XtInfo;
import com.sankuai.algoplatform.matchops.domain.service.XtService;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.CookieConfigDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.CookieConfig;
import com.sankuai.algoplatform.matchops.infrastructure.dal.po.XmAddResult;
import com.sankuai.algoplatform.matchops.infrastructure.dal.po.XmContentResult;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.XuechengService;
import com.sankuai.ead.citadel.document.node.concept.Block;
import com.sankuai.ead.citadel.document.node.impl.node.Doc;
import com.sankuai.ead.citadel.document.node.impl.node.TableRow;
import com.sankuai.ead.citadel.document.parser.DocumentParsingException;
import com.sankuai.ead.citadel.document.parser.Serializer;
import com.sankuai.ead.citadel.document.processor.impl.DocValidateResult;
import com.sankuai.ead.citadel.document.processor.impl.DocValidator;
import com.sankuai.ead.citadel.document.util.Builder;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static com.sankuai.ead.citadel.document.util.Builder.*;

@Slf4j
public class XtServiceTest extends BaseTest {
//    @Resource
//    private XtService xtService;
//
//    private final String taskId = "846968";
//    private final String version = "19513815";

////    @Test
//    public void queryCodeByVersion() {
//        XtInfo info = new XtInfo("846968", "19513815");
//        xtService.queryCodeByVersion(taskId, version);
//        log.info("{}", info.getRawCode());
//    }
//
////    @Test
//    public void submitXtTask() {
//        XtInfo info = new XtInfo("846968", "19513815");
////        XtInfo info = new XtInfo("846968", "19513815");
//        Map<String, Object> map = xtService.queryCodeByVersion(taskId, version);
//        xtService.submitXtTask(map.get("rawCode"), "-v --cd 2025-06-20");
//        log.info("{}", info.getRawCode());
//    }
//
////    @Test
//    public void queryXtTaskStatus() {
//        XtInfo info = new XtInfo("846968", "19513815");
//        info.setSubmitId("9964044");
//        xtService.queryXtTaskStatus(info);
//        log.info("{}", info.getRawCode());
//    }

}