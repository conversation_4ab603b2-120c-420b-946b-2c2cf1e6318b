package com.sankuai.algoplatform.matchops.starter.service;

import com.alibaba.fastjson.JSON;
import com.sankuai.algoplatform.matchops.BaseTest;
import com.sankuai.algoplatform.matchops.api.request.resourceGroup.TAddEditResourceGroupRequest;
import com.sankuai.algoplatform.matchops.api.request.resourceGroup.TDeleteResourceGroupRequest;
import com.sankuai.algoplatform.matchops.api.request.resourceGroup.TResourceGroupListRequest;
import com.sankuai.algoplatform.matchops.api.response.resourceGroup.TAddResourceGroupResponse;
import com.sankuai.algoplatform.matchops.api.response.resourceGroup.TDeleteResourceGroupResponse;
import com.sankuai.algoplatform.matchops.api.response.resourceGroup.TEditResourceGroupResponse;
import com.sankuai.algoplatform.matchops.api.response.resourceGroup.TRSourceGroupListResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class TResourceGroupServiceImplTest extends BaseTest {
    @Autowired
    private TResourceGroupServiceImpl TResourceGroupServiceImpl;
    @Test
    public void testGetResourceGroupList() {
        TResourceGroupListRequest request = new TResourceGroupListRequest();
        request.setBizLineId(1L);
        request.setCurrent(1);
        TRSourceGroupListResponse response = TResourceGroupServiceImpl.getResourceGroupList(request);
        log.info("{}", JSON.toJSONString(response));
    }

    @Test
    public void testAddResourceGroupList() {
        String str = "{\n" + "    \"bizLineId\": \"1\",\n" + "    \"resourceGroupName\": \"test2\",\n"
                + "    \"cpuResourceInfo\": [\n" + "        {\n" + "            \"appkey\": \"test2-1\",\n"
                + "            \"set\": \"1\",\n" + "            \"instanceNum\": 1,\n"
                + "            \"retainTime\": 1\n" + "        }\n" + "    ],\n" + "    \"mlpResourceInfo\": [\n"
                + "        {\n" + "            \"appkey\": \"test2-2\",\n" + "            \"set\": \"2\",\n"
                + "            \"group\": \"2\",\n" + "            \"retainTime\": 2,\n"
                + "            \"instanceNum\": 2\n" + "        }\n" + "    ],\n" + "    \"llmResourceInfo\": [\n"
                + "        {\n" + "            \"serviceName\": \"test2-3\",\n" + "            \"instanceNum\": 3\n"
                + "        }\n" + "    ],\n" + "    \"owner\":\"xxx\"\n" + "}";
        TAddEditResourceGroupRequest request = JSON.parseObject(str, TAddEditResourceGroupRequest.class);
        log.info("{}", JSON.toJSONString(request));
        TAddResourceGroupResponse result = TResourceGroupServiceImpl.addResourceGroupList(request);
        log.info("{}", JSON.toJSONString(result));
    }
    @Test
    public void testEditResourceGroupList() {
        String str = "{\n" + "    \"resourceGroupId\":1,\n" + "    \"resourceGroupName\": \"精准匹配跑数1\",\n"
                + "    \"cpuResourceInfo\": [\n" + "        {\n"
                + "            \"appkey\": \"com.sankuai.algoplatform.predictor1\", \n"
                + "            \"set\": \"default1\", \n" + "            \"instanceNum\": \"2001\", \n"
                + "            \"retainTime\": \"241\" \n" + "        }\n" + "    ],\n" + "    \"mlpResourceInfo\": [\n"
                + "        {\n" + "            \"appkey\": \"com.sankuai.algoplatform.predictor1\", \n"
                + "            \"set\": \"default1\", \n" + "            \"retainTime\": \"241\",\n"
                + "            \"instanceInfo\": \"json串1\" \n" + "        }\n" + "    ],\n"
                + "    \"llmResourceInfo\": [\n" + "        {\n"
                + "            \"serviceName\": \"com.sankuai.algoplatform.predictor1\", \n"
                + "            \"instanceNum\": \"201\"\n" + "        }\n" + "    ], \n" + "      \"owner\":\"xxx1\"\n"
                + "}";
        TAddEditResourceGroupRequest request = JSON.parseObject(str, TAddEditResourceGroupRequest.class);
        log.info("{}", JSON.toJSONString(request));
        TEditResourceGroupResponse result = TResourceGroupServiceImpl.editResourceGroupList(request);
        log.info("{}", JSON.toJSONString(result));
    }
    @Test
    public void testDelResourceGroupList() {
        TDeleteResourceGroupRequest request = new TDeleteResourceGroupRequest();
        request.setResourceGroupId(1L);
        TDeleteResourceGroupResponse tBaseResponse = TResourceGroupServiceImpl.delResourceGroupList(request);
        log.info("{}", JSON.toJSONString(tBaseResponse));
    }
}