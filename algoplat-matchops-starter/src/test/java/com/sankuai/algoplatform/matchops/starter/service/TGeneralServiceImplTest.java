package com.sankuai.algoplatform.matchops.starter.service;

import com.alibaba.fastjson.JSON;
import com.sankuai.algoplatform.matchops.BaseTest;
import com.sankuai.algoplatform.matchops.api.request.general.TDropdownRequest;
import com.sankuai.algoplatform.matchops.api.response.general.TAppResponse;
import com.sankuai.algoplatform.matchops.api.response.general.TDropdownResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class TGeneralServiceImplTest extends BaseTest {
    @Autowired
    private TGeneralServiceImpl TGeneralServiceImpl;

    @Test
    public void testGetDropdown() {
        TDropdownRequest request = new TDropdownRequest();
        //request.setEnv("st");
        request.setType("8");
        request.setBizLineId(1L);
        log.info("{}", JSON.toJSONString(request));
        TDropdownResponse dropdown = TGeneralServiceImpl.getDropdown(request);
        log.info("{}", JSON.toJSONString(dropdown));
    }

    @Test
    public void getApps() {
        TAppResponse apps = TGeneralServiceImpl.getApps("com.sankuai.algoplatform");
        log.info("{}", JSON.toJSONString(apps));
    }

    @Test
    public void getCells() {
        TAppResponse apps = TGeneralServiceImpl.getCells("com.sankuai.algoplatform.predictor");
        log.info("{}", JSON.toJSONString(apps));
    }

}