<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sankuai.algoplatform</groupId>
        <artifactId>algoplat-matchops</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>algoplat-matchops-domain</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>
    <name>algoplat-matchops-domain</name>

    <dependencies>
        <!-- Project module -->
        <dependency>
            <groupId>com.sankuai.algoplatform</groupId>
            <artifactId>algoplat-matchops-infrastructure</artifactId>
        </dependency>
    </dependencies>

</project>
