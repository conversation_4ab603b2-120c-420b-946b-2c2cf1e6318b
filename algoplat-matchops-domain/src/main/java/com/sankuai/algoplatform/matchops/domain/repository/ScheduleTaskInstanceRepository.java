package com.sankuai.algoplatform.matchops.domain.repository;

import com.alibaba.fastjson.JSON;
import com.sankuai.algoplatform.matchops.domain.ability.gpuschedule.ScheduleTaskInstance;
import com.sankuai.algoplatform.matchops.domain.enums.ScheduleTaskInstanceStatus;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.ScheduleTaskInstanceExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.ScheduleTaskInstanceMapper;
import com.sankuai.algoplatform.matchops.infrastructure.util.ZebraForceMasterUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Repository
public class ScheduleTaskInstanceRepository {
    @Resource
    private ScheduleTaskInstanceMapper scheduleTaskInstanceMapper;

    public List<String> insertScheduleTaskInstance(List<ScheduleTaskInstance> scheduleTaskInstances) {
        if (CollectionUtils.isEmpty(scheduleTaskInstances)) {
            return Collections.emptyList();
        }
        List<String> successIds = new ArrayList<>();
        return ZebraForceMasterUtil.queryInMaster(() -> {
            Set<String> uniqKey = scheduleTaskInstances.stream().map(ScheduleTaskInstance::getUniqInstanceKey).collect(Collectors.toSet());

            ScheduleTaskInstanceExample example = new ScheduleTaskInstanceExample();
            example.createCriteria().andInstanceKeyIn(new ArrayList<>(uniqKey));
            List<com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ScheduleTaskInstance> instancesInDB =
                    scheduleTaskInstanceMapper.selectByExample(example);
            Set<String> uniqKeyInDb = instancesInDB.stream().map(com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ScheduleTaskInstance::getInstanceKey)
                    .collect(Collectors.toSet());

            for (ScheduleTaskInstance instance : scheduleTaskInstances) {
                if (uniqKeyInDb.contains(instance.getUniqInstanceKey())) {
                    continue;
                }
                com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ScheduleTaskInstance toDB = new com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ScheduleTaskInstance();
                toDB.setTaskBindingId(instance.getTaskBinding().getId());
                toDB.setInstanceKey(instance.getUniqInstanceKey());
                toDB.setStatus(instance.getStatus().getCode());
                toDB.setMessage(instance.getMessage() != null ? StringUtils.truncate(instance.getMessage(), 2000) : null);
                toDB.setAction(JSON.toJSONString(instance.getAction()));
                int num = scheduleTaskInstanceMapper.insert(toDB);
                if (num > 0) {
                    successIds.add(instance.getUniqInstanceKey());
                }
            }
            return successIds;
        });
    }


    public List<com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ScheduleTaskInstance> queryScheduleTaskInstancesByStatus(ScheduleTaskInstanceStatus status) {
        return ZebraForceMasterUtil.queryInMaster(() -> {
            ScheduleTaskInstanceExample example = new ScheduleTaskInstanceExample();
            example.createCriteria().andStatusEqualTo(status.getCode());
            return scheduleTaskInstanceMapper.selectByExample(example);
        });
    }

    public boolean updateInstanceStatus(Long taskId, ScheduleTaskInstanceStatus status, String message) {
        ScheduleTaskInstanceExample example = new ScheduleTaskInstanceExample();
        example.createCriteria().andIdEqualTo(taskId);
        com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ScheduleTaskInstance instance = new com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ScheduleTaskInstance();
        instance.setStatus(status.getCode());
        instance.setMessage(message);
        return scheduleTaskInstanceMapper.updateByExampleSelective(instance, example) > 0;
    }
}
