package com.sankuai.algoplatform.matchops.domain.ability.offlinetask.utils;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.algoplatform.matchops.infrastructure.config.LionConfig;
import com.sankuai.algoplatform.matchops.infrastructure.enums.OctoNodeStatus;
import com.sankuai.algoplatform.matchops.infrastructure.model.OctoNode;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.OctoNodeService;
import com.sankuai.algoplatform.matchops.infrastructure.util.RetryUtil;
import com.sankuai.algoplatform.matchops.infrastructure.util.SpringContextUtils;
import com.sankuai.inf.octo.mns.model.HostEnv;
import com.sankuai.inf.octo.mns.util.ProcessInfoUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.zip.CRC32;
@Slf4j
public class IpLocateUtils {
    public static int locateIpMod(String appkey, String cellName){
        List<OctoNode> octoNodes = SpringContextUtils.getBean(OctoNodeService.class).getOctoNodeStatus(appkey, cellName, HostEnv.PROD, OctoNodeStatus.NORMAL);
//        List<OctoNode> octoNodes = SpringContextUtils.getBean(OctoNodeService.class).getOctoNodeStatusByCurrentEnv(appkey, cellName, OctoNodeStatus.NORMAL);
        List<String> ips = octoNodes.stream().map(OctoNode::getIp).sorted((ip1, ip2) -> {
            CRC32 crc32_1 = new CRC32();
            crc32_1.update(ip1.getBytes());
            long value1 = crc32_1.getValue();

            CRC32 crc32_2 = new CRC32();
            crc32_2.update(ip2.getBytes());
            long value2 = crc32_2.getValue();

            return Long.compare(value1, value2);
        }).collect(Collectors.toList());

        // 按照CRC32值从小到大排序
        Map<String, Integer> modMap = new HashMap<>();
        // 构建序号到IP的映射
        for (int i = 0; i < ips.size(); i++) {
            modMap.put(ips.get(i), i);
        }
        String localIpV4 = RetryUtil.retry(ProcessInfoUtil::getLocalIpV4, ((ip, e) -> ip == null || modMap.get(ip) == null), 3, 100);
        Integer integer = modMap.get(localIpV4);
        if(Objects.isNull(integer)) {
            log.error("ipLocate fail, current ip = {}, modMap = {}, ips = {}",  localIpV4, JSON.toJSONString(modMap), JSON.toJSONString(ips));
            Cat.logEvent("ipLocate", "fail");
            throw new RuntimeException("ipLocate fail, current ip = " + localIpV4 + ", modMap = " + JSON.toJSONString(modMap) + ", ips = " + JSON.toJSONString(ips));
        }
        return integer;
    }
}
