package com.sankuai.algoplatform.matchops.domain.service.testtool.reporter;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestSubTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * 从参数中提取数据
 */
@Service
@Slf4j
public class RunParamDataFetch {

    //    param@cd
    public static String query(String[] ruleText, TestSubTask testSubTask) {
        String cd = ruleText[1];
        JSONObject jsonObject = JSONObject.parseObject(testSubTask.getRunParam());
        return jsonObject.getString(cd);
    }

}
