package com.sankuai.algoplatform.matchops.domain.ability.gpuschedule;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.sankuai.algoplatform.matchops.domain.ability.gpuschedule.rule.ScheduleRuleExecutor;
import com.sankuai.algoplatform.matchops.domain.enums.ScheduleTaskActionType;
import com.sankuai.algoplatform.matchops.domain.enums.ScheduleTaskStatus;
import com.sankuai.algoplatform.matchops.domain.enums.ScheduleTaskType;
import com.sankuai.algoplatform.matchops.domain.model.ScheduleTaskCheckResult;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ScheduleTaskConfig;
import com.sankuai.algoplatform.matchops.infrastructure.monitor.RaptorTrack;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static java.util.Objects.requireNonNull;

@Slf4j
@Data
public class ScheduleTask {

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 状态（0：正常，-1禁用）
     */
    private ScheduleTaskStatus status;

    /**
     * 任务类型（定时、监控）
     */
    private ScheduleTaskType taskType;

    /**
     * 调度动作类型0增加，1减少
     */
    private ScheduleTaskActionType actionType;

    /**
     * 规则配置
     */
    private ScheduleRuleExecutor ruleExecutor;

    /**
     * 实例数
     */
    private Integer quota;

    /**
     * 操作人id
     */
    private String operatorMis;

    public static boolean isValidConfig(ScheduleTaskConfig cfg) {
        boolean r = cfg != null && StringUtils.isNoneBlank(cfg.getTaskName())
                && cfg.getStatus() != null && ScheduleTaskStatus.getByCode(cfg.getStatus()) != null
                && cfg.getTaskType() != null && ScheduleTaskType.getByCode(cfg.getTaskType()) != null
                && cfg.getActionType() != null && ScheduleTaskActionType.getByCode(cfg.getActionType()) != null
                && StringUtils.isNoneBlank(cfg.getRuleConfig())
                && cfg.getQuota() != null && cfg.getQuota() > 0;
        if (!r) {
            log.error("ScheduleTask config invalid:{}", JSON.toJSONString(cfg));
            RaptorTrack.Sys_UnexpectedVisitNum.report("InvalidScheduleTask_" + (cfg != null ? cfg.getId() : null));
        }
        return r;
    }

    public static ScheduleTask from(ScheduleTaskConfig scheduleTaskConfig) {
        ScheduleTask scheduleTask = new ScheduleTask();
        scheduleTask.setId(scheduleTaskConfig.getId());
        scheduleTask.setTaskName(scheduleTaskConfig.getTaskName());
        scheduleTask.setStatus(requireNonNull(ScheduleTaskStatus.getByCode(scheduleTaskConfig.getStatus())));
        scheduleTask.setTaskType(requireNonNull(ScheduleTaskType.getByCode(scheduleTaskConfig.getTaskType())));
        scheduleTask.setActionType(requireNonNull(ScheduleTaskActionType.getByCode(scheduleTaskConfig.getActionType())));
        scheduleTask.setRuleExecutor(ScheduleRuleExecutor.from(scheduleTask.getTaskType(), scheduleTask, scheduleTaskConfig.getRuleConfig()));
        scheduleTask.setQuota(scheduleTaskConfig.getQuota());
        scheduleTask.setOperatorMis(scheduleTaskConfig.getOperatorMis());
        return scheduleTask;
    }

    public ScheduleTaskCheckResult check() {
        ScheduleTaskCheckResult res = null;
        try {
            List<ScheduleTaskCheckResult> result = ruleExecutor.generateCheckResult(Collections.emptyMap());
            res = result.get(0);
        } finally {
            log.info("ScheduleTask check: task:{}-{}, result:{}", this.id, this.taskName,
                    Optional.ofNullable(res).map(ScheduleTaskCheckResult::isNeedExecute).orElse(null));
        }
        return res;
    }

    public List<ScheduleTaskCheckResult> generateCheckResult(Date beforeTime, Date afterTime) {
        List<ScheduleTaskCheckResult> res = null;
        try {
            res = ruleExecutor.generateCheckResult(ImmutableMap.of("beforeTime", beforeTime, "afterTime", afterTime));
        } finally {
            log.info("ScheduleTask check: task:{}-{}, result:{}", this.id, this.taskName,
                    Optional.ofNullable(res).map(m -> m.stream().map(n -> JSON.toJSONString(n.getAction())).collect(Collectors.toList())).orElse(null));
        }
        return res;
    }
}
