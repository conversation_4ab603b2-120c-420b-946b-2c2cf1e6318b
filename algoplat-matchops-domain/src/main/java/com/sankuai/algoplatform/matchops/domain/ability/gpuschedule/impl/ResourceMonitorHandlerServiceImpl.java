package com.sankuai.algoplatform.matchops.domain.ability.gpuschedule.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.google.common.collect.ImmutableMap;
import com.sankuai.algoplatform.matchops.domain.ability.gpuschedule.*;
import com.sankuai.algoplatform.matchops.domain.ability.gpuschedule.util.HandlerUtil;
import com.sankuai.algoplatform.matchops.domain.enums.ScheduleTaskActionType;
import com.sankuai.algoplatform.matchops.infrastructure.model.GPUQueueResourceDetail;
import com.sankuai.algoplatform.matchops.domain.model.ScheduleTaskAction;
import com.sankuai.algoplatform.matchops.domain.model.ScheduleTaskCheckResult;
import com.sankuai.algoplatform.matchops.domain.repository.*;
import com.sankuai.algoplatform.matchops.infrastructure.config.LionConfig;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ResourcePoolConfig;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ScheduleServiceConfig;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ScheduleTaskConfig;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TaskBindingConfig;
import com.sankuai.algoplatform.matchops.infrastructure.monitor.RaptorTrack;
import com.sankuai.algoplatform.matchops.infrastructure.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ResourceMonitorHandlerServiceImpl implements ResourceMonitorHandlerService {
    @Resource
    private ScheduleTaskRepository scheduleTaskRepository;
    @Resource
    private TaskBindingRepository taskBindingRepository;
    @Resource
    private ScheduleServiceRepository scheduleServiceRepository;
    @Resource
    private ResourcePoolRepository resourcePoolRepository;
    @Resource
    private ScheduleTaskInstanceRepository scheduleTaskInstanceRepository;

    @Override
    public void generateTaskInstance() {
        // 查询全部ScheduleTask
        List<ScheduleTaskConfig> scheduleTaskConfigs = scheduleTaskRepository.queryValidScheduleTasks();
        if (CollectionUtils.isEmpty(scheduleTaskConfigs)) {
            log.info("generateTaskInstance:未配置调度任务.");
            return;
        }
        List<ScheduleTask> scheduleTasks = scheduleTaskConfigs.stream()
                .filter(ScheduleTask::isValidConfig).map(ScheduleTask::from).collect(Collectors.toList());

        List<ScheduleTaskCheckResult> need2ExecuteTasks = scheduleTasks.stream()
                .flatMap(m -> m.generateCheckResult(new Date(), DateUtils.addHours(new Date(), LionConfig.SCHEDULE_RESOURCE_MONITOR_TIMEWINDOW_HOUR)).stream())
                .filter(ScheduleTaskCheckResult::isNeedExecute).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(need2ExecuteTasks)) {
            log.info("generateTaskInstance:没有需要执行的调度任务.");
            return;
        }

        // 简单策略:过滤出需要新增的调度任务
        need2ExecuteTasks = need2ExecuteTasks.stream()
                .filter(f -> ScheduleTaskActionType.ADD.equals(f.getAction().getActionType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(need2ExecuteTasks)) {
            log.info("generateTaskInstance:没有需要触发的调度任务.");
            return;
        }

        List<TaskBindingConfig> taskBindingConfigs = taskBindingRepository
                .queryValidBindingsByTaskIds(need2ExecuteTasks.stream()
                        .map(ScheduleTaskCheckResult::getScheduleTask)
                        .map(ScheduleTask::getId).distinct().collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(taskBindingConfigs)) {
            log.info("generateTaskInstance:待执行的调度任务未绑定服务.");
            return;
        }
        Map<Long, ScheduleTaskCheckResult> scheduleTaskCheckResultMap = need2ExecuteTasks.stream().collect(Collectors.toMap(m -> m.getScheduleTask().getId(), Function.identity()));

        List<ScheduleServiceConfig> serviceConfigs = scheduleServiceRepository.queryValidScheduleServiceByIds(taskBindingConfigs.stream().map(TaskBindingConfig::getScheduleServiceId)
                .distinct().collect(Collectors.toList()));
        List<ResourcePoolConfig> resourcePoolConfigs = resourcePoolRepository.queryValidResourcePoolByIds(taskBindingConfigs.stream()
                .flatMap(f -> JSON.parseArray(f.getResourcePoolIds()).toJavaList(Long.class).stream())
                .distinct().collect(Collectors.toList()));
        // 构建映射关系
        Map<Long, ScheduleService> scheduleServiceMap = serviceConfigs.stream()
                .filter(ScheduleService::isValidConfig)
                .map(ScheduleService::from).collect(Collectors.toMap(ScheduleService::getId, Function.identity()));
        Map<Long, ResourcePool> resourcePoolMap = resourcePoolConfigs.stream()
                .filter(ResourcePool::isValidConfig)
                .map(ResourcePool::from).collect(Collectors.toMap(ResourcePool::getId, Function.identity()));
        Map<Long, ScheduleTask> scheduleTaskMap = scheduleTasks.stream().collect(Collectors.toMap(ScheduleTask::getId, Function.identity()));
        // TaskBinding
        Map<Long, TaskBinding> taskBindingMap = taskBindingConfigs.stream()
                .map(m -> {
                    try {
                        return TaskBinding.from(m,
                                scheduleTaskMap.get(m.getScheduleTaskId()),
                                scheduleServiceMap.get(m.getScheduleServiceId()),
                                resourcePoolMap.values().stream().filter(f -> JSON.parseArray(m.getResourcePoolIds()).toJavaList(Long.class).contains(f.getId())).collect(Collectors.toList()));
                    } catch (Exception e) {
                        log.error("构建TaskBinding异常:{}, e:", m, e);
                        RaptorTrack.Sys_UnexpectedVisitNum.report("ResourceMonitorHandlerService.generateTaskInstance_TaskBindingBuildError");
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(TaskBinding::getId, Function.identity()));
        // ResourcePool to TaskBinding List
        Map<Long, List<TaskBinding>> resourceId2Bindings = taskBindingMap.values().stream()
                .flatMap(b -> b.getResourcePool().stream().map(r -> Pair.of(r.getId(), b)).collect(Collectors.toList()).stream())
                .collect(Collectors.groupingBy(Pair::getLeft, Collectors.mapping(Pair::getRight, Collectors.toList())));

        Map<String, ScheduleTaskInstance> candiInstances = new LinkedHashMap<>();
        resourceId2Bindings.forEach((resourceId, bindings) -> {
            ResourcePool resourcePool = resourcePoolMap.get(resourceId);

            int addInstanceNum = 0;
            for (TaskBinding binding : bindings) {
                ScheduleTask scheduleTask = binding.getScheduleTask();
                ScheduleTaskCheckResult scheduleTaskCheckResult = scheduleTaskCheckResultMap.get(scheduleTask.getId());

                addInstanceNum += scheduleTaskCheckResult.getAction().getQuotaNum();
            }
            Integer remainResourceNum = resourcePool.queryRemainResourceNum();
            int redundancy = LionConfig.SCHEDULE_RESOURCE_MONITOR_REDUNDANCY;
            if (remainResourceNum != null) {
                if (remainResourceNum - addInstanceNum - redundancy <= 0) {
                    // 该resource下全部任务需要扩容
                    for (TaskBinding binding : bindings) {
                        ScheduleTaskAction action = scheduleTaskCheckResultMap.get(binding.getScheduleTask().getId()).getAction();
                        ScheduleTaskInstance scheduleTaskInstance = binding.generateInstances(Objects.requireNonNull(action));
                        candiInstances.put(scheduleTaskInstance.getUniqInstanceKey(), scheduleTaskInstance);
                    }
                    RaptorTrack.Sys_GpuSchedule_InsufficientResourcePool.report(String.format("UrgentScheduling_%s", resourcePool.getId()));

                    log.info("monitorResourcePool insufficient pool: ResourcePool:{}, TaskBinding:{}, remainResourceNum:{}, addInstanceNum:{}, redundancy:{}",
                            resourcePool.getId(), bindings.stream().map(TaskBinding::getId).collect(Collectors.toList()), remainResourceNum, addInstanceNum, redundancy);
                }
            } else {
                RaptorTrack.Sys_UnexpectedVisitNum.report("ResourceMonitorHandlerService.ResourcePoolQueryRemainResourceNumError_" + resourcePool.getId());
            }
        });

        if (MapUtils.isEmpty(candiInstances)) {
            log.info("generateTaskInstance:资源充足无需提前调度, ScheduleTask:{}", scheduleServiceMap.keySet());
            return;
        }

        String triggerDateTimeStr = DateUtil.toDateTimeString(DateUtils.addMinutes(new Date(), LionConfig.SCHEDULE_RESOURCE_MONITOR_TRIGGER_DELAY));
        candiInstances.forEach((f, g) -> {
            String beforeTriggerDateTimeStr = g.getAction().getTriggerDatetime();
            g.getAction().setTriggerDatetime(triggerDateTimeStr);
            g.setMessage(String.format("调整触发时间:%s -> %s", beforeTriggerDateTimeStr, triggerDateTimeStr));
        });

        List<String> successTaskInstances = scheduleTaskInstanceRepository.insertScheduleTaskInstance(new ArrayList<>(candiInstances.values()));
        log.info("generateTaskInstance:成功生成待执行的调度任务:{}", JSON.toJSONString(successTaskInstances));
        successTaskInstances.forEach(f -> {
            ScheduleTaskInstance instance = candiInstances.get(f);
            HandlerUtil.reportRaptorEvent(instance);
        });
        log.info("generateTaskInstance:需要提前执行的调度任务:{}", JSON.toJSONString(need2ExecuteTasks));
    }

    @Override
    public void monitorResourcePool() {
        List<ResourcePoolConfig> resourcePoolConfigs = resourcePoolRepository.queryValidResourcePools();
        if (CollectionUtils.isEmpty(resourcePoolConfigs)) {
            log.info("monitorResourcePool:未配置资源池.");
            return;
        }
        for (ResourcePoolConfig cfg : resourcePoolConfigs) {
            if (!ResourcePool.isValidConfig(cfg)) {
                continue;
            }
            ResourcePool resourcePool = ResourcePool.from(cfg);

            ImmutableMap<String, String> tag = ImmutableMap.of("queue", resourcePool.getQueue(), "resourceType", resourcePool.getResourceType().getName());
            Cat.logMetricForCount("ResourcePool_quotaLimit", resourcePool.getQuotaLimit(), tag);
            GPUQueueResourceDetail detail = resourcePool.queryCurrentQueueResource();
            if (detail == null) {
                continue;
            }

            Cat.logMetricForCount("ResourcePool_quotaNum", detail.getQuotaNum(), tag);
            Cat.logMetricForCount("ResourcePool_availableNum", detail.getAvailableNum(), tag);
            Cat.logMetricForCount("ResourcePool_allocatedNum", detail.getAllocatedNum(), tag);
            Cat.logMetricForCount("ResourcePool_pendingNum", detail.getPendingNum(), tag);
            Cat.logMetricForCount("ResourcePool_remainNum", detail.getRemainNum(), tag);
        }

    }

    @Override
    public void monitorServiceNode() {
        List<ScheduleServiceConfig> serviceConfigs = scheduleServiceRepository.queryValidScheduleService();
        if (CollectionUtils.isEmpty(serviceConfigs)) {
            log.info("monitorServiceNode:未配置服务.");
            return;
        }
        for (ScheduleServiceConfig cfg : serviceConfigs) {
            if (!ScheduleService.isValidConfig(cfg)) {
                continue;
            }
            ScheduleService scheduleService = ScheduleService.from(cfg);
            int nodeCount = scheduleService.queryOnlineOctoNodeCount();
            ImmutableMap<String, String> tag = ImmutableMap.of("appkey", scheduleService.getAppkey(), "group", scheduleService.getGroupName());
            Cat.logMetricForCount("ScheduleServiceNode_online", nodeCount, tag);
        }
    }

}
