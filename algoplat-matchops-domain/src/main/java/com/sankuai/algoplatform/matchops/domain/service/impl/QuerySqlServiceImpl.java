package com.sankuai.algoplatform.matchops.domain.service.impl;

import com.sankuai.algoplatform.matchops.domain.service.QuerySqlService;
import com.sankuai.algoplatform.matchops.infrastructure.model.SqlResult;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.TalosSqlQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
@Slf4j
public class QuerySqlServiceImpl implements QuerySqlService {

    @Autowired
    private TalosSqlQueryService talosSqlQueryService;

    @Override
    public SqlResult executeSql(String sql) throws Exception {
        return talosSqlQueryService.executeSql(sql);
    }
}
