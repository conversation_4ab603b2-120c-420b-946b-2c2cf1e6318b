package com.sankuai.algoplatform.matchops.domain.service.testtool.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.sankuai.algoplatform.matchops.domain.service.testtool.DocHandler;
import com.sankuai.algoplatform.matchops.domain.service.testtool.ReporterService;
import com.sankuai.algoplatform.matchops.domain.service.testtool.reporter.*;
import com.sankuai.algoplatform.matchops.infrastructure.cache.TairClient;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.CookieConfigDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.TestSubTaskDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.TestTaskDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestSubTask;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestTask;
import com.sankuai.algoplatform.matchops.infrastructure.dal.po.XmAddResult;
import com.sankuai.algoplatform.matchops.infrastructure.dal.po.XmContentResult;
import com.sankuai.algoplatform.matchops.infrastructure.monitor.RaptorTrack;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.EmployService;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.XuechengService;
import com.sankuai.algoplatform.matchops.infrastructure.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.*;

import static com.sankuai.algoplatform.matchops.domain.constant.TestToolConstants.SYSTEM_OPS_MIS_ID_LIST;
import static com.sankuai.algoplatform.matchops.infrastructure.config.LionConfig.MCPSERVER_CREATE_XUECHENG_DOC_URL;


@Slf4j
@Service
public class ReporterServiceImpl implements ReporterService {

    @Autowired
    private XuechengService xcService;

    @Autowired
    private SqlDataFetch sqlDataFetch;

    @Autowired
    private CatDataFetch catDataFetch;

    @Autowired
    private TestTaskDao testTaskDao;


    @Autowired
    private TairClient tairClient;

    @Value("${xuecheng.key}")
    private String xuechengCacheKey;

    @Override
    public Pair<Boolean, String> buildReporterByContentId(String contentId, TestSubTask subTask) throws Exception {
//        TestSubTask subTask = testSubTaskDao.selectById(subTaskId);
//        List<String> systemOpsMisIdList = SYSTEM_OPS_MIS_ID_LIST;

        TestTask testTask = testTaskDao.selectById(subTask.getTaskId());
        String title = String.format("%s_%s_任务ID=%s", DateUtil.toDateTimeString_yyyyMMdd(new Date()), testTask.getName(), subTask.getId());
        //读取模板
        XmContentResult contentResult = xcService.getCollaborationContent(contentId, subTask.getCreator());
        if (contentResult.getCode() != 0) {
            log.error("任务报告生成失败， subTaskId：{}，报错：{}", subTask.getId(), JSON.toJSONString(contentResult));
            return Pair.of(false, contentResult.getMessage());
        }

        //解析参数、数据查询
        String content = contentResult.getContent();
        List<String> param = RegexUtil.extractDollar(content);
        Map<String, String> queryDataMap = queryData(param, subTask);
        List<Map<String, String>> list = DocHandler.filterMapByTable(queryDataMap);


        //模板填充
        JSONObject doc = JSON.parseObject(content);
        DocHandler.replaceDocText(doc, list.get(0));
        DocHandler.replaceDocTable(doc, list.get(1));
        String reporterContent = JSON.toJSONString(doc);

        //报告生成
        XmAddResult addResult = xcService.addCollaborationContentByApp(reporterContent, title);
        if (addResult.getCode() == 0) {
            return Pair.of(true, addResult.getXmId());
        } else {
            return Pair.of(false, addResult.getMessage());
        }

    }


    private Map<String, String> queryData(List<String> rules, TestSubTask subTask) {

        ThreadPoolExecutor pool = ThreadPoolFactory.getTemplateQueryThreadPool();


        Map<String, Future<String>> queryHiveDataFutureMap = new HashMap<>();
        Map<String, String> jsonParseMap = new HashMap<>();
        List<Triple<String, String, String>> needParseParams = new ArrayList<>();

        Map<String, String> queryDataMap = new HashMap<>();
        Map<String, String> needParseMap = new HashMap<>();
        Set<String> needClearKeys = new HashSet<>();
        Map<String, String> replaceMap = new HashMap<>();

        for (String originRule : rules) {
            String rule = originRule;
            rule = StringUtils.replace(rule, "${", "");
            rule = StringUtils.replace(rule, "}", "");

            String[] ruleArr = StringUtils.split(rule, "|||");
            if (StringUtils.contains(ruleArr[0], "∫")) {
                //需要查询的参数
                String[] queryDataRuleTexts = StringUtils.split(ruleArr[0], "∫");
                //hive查询慢，并发查询，其他串行，防止打挂Raptor服务器
                if (StringUtils.equals(queryDataRuleTexts[0], "hive")) {
                    queryHiveDataFutureMap.put(ruleArr[0], pool.submit(() -> queryDataByType(queryDataRuleTexts, subTask, ruleArr.length >= 2)));
                } else {
                    queryDataMap.put(ruleArr[0], queryDataByType(queryDataRuleTexts, subTask, ruleArr.length >= 2));
                }

                if (ruleArr.length >= 2) {
                    needClearKeys.add(originRule);
                    jsonParseMap.put(ruleArr[1], ruleArr[0]);
                }

            } else {
                //需要从查询结果中解析的参数，${t1|||$.data[0][0]}
                needParseParams.add(Triple.of(originRule, ruleArr[0], ruleArr[1]));
            }
        }
        for (Map.Entry<String, Future<String>> entry : queryHiveDataFutureMap.entrySet()) {
            Future<String> future = entry.getValue();
            try {
                String value = future.get(1, TimeUnit.MINUTES);
                queryDataMap.put(entry.getKey(), value);
            } catch (InterruptedException | ExecutionException | TimeoutException e) {
                log.error("数据查询错误", e);
            }
        }

        for (Triple<String, String, String> triple : needParseParams) {
            String queryRes = jsonParseMap.get(triple.getMiddle());
            if (StringUtils.isEmpty(queryRes)) {
                continue;
            }
            String jsonRes = queryDataMap.get(queryRes);
            if (StringUtils.isEmpty(jsonRes)) {
                continue;
            }
            needParseMap.put(triple.getLeft(), JsonPathUtil.extractString(jsonRes, triple.getRight()));
        }
        queryDataMap.forEach((k, v) -> replaceMap.put("${" + k + "}", v));
        replaceMap.putAll(needParseMap);

        //空字符生成文档会报错，不替换
        replaceMap.replaceAll((k, v) -> StringUtils.defaultIfEmpty(v, k));

        if (CollectionUtils.isNotEmpty(needClearKeys)) {
            needClearKeys.forEach(key -> replaceMap.put(key, " "));//替换为空
        }

        return replaceMap;

    }

    public String queryDataByType(String[] ruleText, TestSubTask subTask, boolean isMiddleRes) {
        String queryType = ruleText[0];
        try {
            switch (queryType) {
                case "hive":
                    return sqlDataFetch.query(ruleText, subTask);
                case "raptor":
                    return catDataFetch.query(ruleText, subTask, isMiddleRes);
                case "resource":
                    return ResourceDataFetch.query(ruleText, subTask);
                case "strategy":
                    return MatchStrategyDataFetch.query(ruleText, subTask);
                case "param":
                    return RunParamDataFetch.query(ruleText, subTask);
                case "result":
                    return ResultDataFetch.query(ruleText, subTask);
                default:
                    throw new RuntimeException("无法解析参数类型：" + queryType);
            }
        } catch (Exception e) {
            log.error("数据获取失败, subTask:{} ruleText:{}", JSON.toJSONString(subTask), JSON.toJSONString(ruleText), e);
            Cat.logError("数据获取失败", e);
            RaptorTrack.Sys_UnexpectedVisitNum.report("GetReporterDataFail");
        }
        return "解析失败";
    }
}
