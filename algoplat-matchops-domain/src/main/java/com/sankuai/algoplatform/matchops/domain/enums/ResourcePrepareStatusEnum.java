package com.sankuai.algoplatform.matchops.domain.enums;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum ResourcePrepareStatusEnum {
    UNKNOWN(-1, "未知状态"),

    INIT(0, "待准备"),

    ING(1, "准备中"),

    SUCCESS(2, "环境准备成功"),

    FAIL(3, "环境准备失败");

    int code;
    String name;

    ResourcePrepareStatusEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ResourcePrepareStatusEnum getByCode(int code) {
        return Arrays.stream(ResourcePrepareStatusEnum.values()).filter(statusEnum -> statusEnum.getCode() == code).findFirst().orElse(UNKNOWN);
    }
}
