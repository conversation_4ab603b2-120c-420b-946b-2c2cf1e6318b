package com.sankuai.algoplatform.matchops.domain.service.testtool;


import com.sankuai.algoplatform.matchops.domain.enums.TestSubTaskStatusEnum;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestSubTask;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestTask;

public interface TaskExecuteService {

    void runSubTask(TestSubTask subTaskId);

    void stopSubTask(TestSubTask subTaskId, TestSubTaskStatusEnum stop);

    void doneSubTask(TestSubTask subTaskId);

    void handleTestingSubTask(TestTask task);

    void checkTestingTaskException(TestTask task);

    void generateReporter(TestSubTask subTask) throws Exception;

    void handleTaskStatusBySubTask(TestSubTask testSubTask);
}
