package com.sankuai.algoplatform.matchops.domain.repository;

import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TaskBindingConfig;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.TaskBindingConfigExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.TaskBindingConfigMapper;
import com.sankuai.algoplatform.matchops.infrastructure.util.ZebraForceMasterUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Repository
public class TaskBindingRepository {
    @Resource
    private TaskBindingConfigMapper taskBindingConfigMapper;

    public List<TaskBindingConfig> queryValidBindingsByTaskIds(List<Long> taskIds) {
        if (CollectionUtils.isEmpty(taskIds)) {
            return Collections.emptyList();
        }
        return ZebraForceMasterUtil.queryInMaster(() -> {
            TaskBindingConfigExample example = new TaskBindingConfigExample();
            example.createCriteria().andScheduleTaskIdIn(taskIds).andStatusEqualTo(0);
            return taskBindingConfigMapper.selectByExample(example);
        });
    }

    public List<TaskBindingConfig> queryValidBindingsByIds(List<Long> bindingIds) {
        if (CollectionUtils.isEmpty(bindingIds)) {
            return Collections.emptyList();
        }
        return ZebraForceMasterUtil.queryInMaster(() -> {
            TaskBindingConfigExample example = new TaskBindingConfigExample();
            example.createCriteria().andIdIn(bindingIds).andStatusEqualTo(0);
            return taskBindingConfigMapper.selectByExample(example);
        });
    }
}
