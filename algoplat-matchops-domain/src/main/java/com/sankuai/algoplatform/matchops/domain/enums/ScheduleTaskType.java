package com.sankuai.algoplatform.matchops.domain.enums;

public enum ScheduleTaskType {
    // 任务类型（定时、监控）
    TIMING_TASK(1, "定时任务"),
    MONITOR_TASK(2, "监控任务");

    private Integer code;
    private String desc;

    ScheduleTaskType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ScheduleTaskType getByCode(Integer code) {
        for (ScheduleTaskType type : ScheduleTaskType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
