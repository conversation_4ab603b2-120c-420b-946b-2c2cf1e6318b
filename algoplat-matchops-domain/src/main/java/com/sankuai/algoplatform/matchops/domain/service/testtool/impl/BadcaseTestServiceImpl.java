package com.sankuai.algoplatform.matchops.domain.service.testtool.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import com.google.common.collect.ImmutableMap;
import com.sankuai.algoplatform.matchops.domain.enums.IndustryTypeEnum;
import com.sankuai.algoplatform.matchops.domain.service.testtool.BadcaseTestService;
import com.sankuai.algoplatform.matchops.domain.service.testtool.DaozongTestService;
import com.sankuai.algoplatform.matchops.infrastructure.config.LionConfig;
import com.sankuai.algoplatform.matchops.infrastructure.enums.OctoNodeStatus;
import com.sankuai.algoplatform.matchops.infrastructure.model.OctoNode;
import com.sankuai.algoplatform.matchops.infrastructure.model.SqlResult;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.ExcelGenService;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.ExcelReadService;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.OctoNodeService;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.TalosSqlQueryService;
import com.sankuai.algoplatform.matchops.infrastructure.util.CompressUtil;
import com.sankuai.algoplatform.matchops.infrastructure.util.HttpUtil;
import com.sankuai.algoplatform.matchops.infrastructure.util.RetryUtil;
import com.sankuai.inf.octo.mns.model.HostEnv;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;


@Service
@Slf4j
public class BadcaseTestServiceImpl implements BadcaseTestService {

    @Autowired
    private OctoNodeService octoNodeService;

    @Autowired
    private ExcelGenService excelGenService;

    @Autowired
    private ExcelReadService excelReadService;

    @Autowired
    private TalosSqlQueryService talosSqlQueryService;

    @Autowired
    private DaozongTestService daozongTestService;

    @Override
    public Map<String, String> test(String param) {
        JSONObject req = JSONObject.parseObject(param);
        try {
            IndustryTypeEnum industryTypeEnum = IndustryTypeEnum.getByCode(req.getString("industryType"));
            if (Objects.equals(industryTypeEnum, IndustryTypeEnum.DAOZONG)) {
                return daozongTestService.badCaseTest(req.toJSONString());
            }
            String s3Link = req.getString("file");
            List<List<String>> list = excelReadService.readService(s3Link);
            if (CollectionUtils.isEmpty(list)) {
                throw new RuntimeException("文件链接读取错误");
            }
            if (Objects.equals(industryTypeEnum, IndustryTypeEnum.DAOCAN)) {
                String bizCode = req.getString("bizCode");
                String partitionDate = req.getString("partitionDate");
                switch (bizCode) {
                    case "test":
                    case "zb_dealMatching":
                        String s3LinkResult = daocanTest(bizCode, list, partitionDate);
                        return ImmutableMap.of("code", "0", "data", s3LinkResult);
                    default:
                        return ImmutableMap.of("code", "500", "message", "bizCode不存在");
                }
            }
        } catch (Exception e) {
            Cat.logError(e.getMessage(), e);
            return ImmutableMap.of("code", "500", "message", String.valueOf(e.getMessage()));
        }
        return ImmutableMap.of("code", "500", "message", "参数异常");
    }

    private String daocanTest(String bizCode, List<List<String>> list, String partitionDate) throws Exception {
        List<String> column = list.get(0);
        Map<String, Integer> columnMap = column2Map(column);
        Set<String> mtPoiIds = new HashSet<>();

        //excel表头校验
        if (columnMap.get("mt_poi_id") == null ||
                columnMap.get("mt_deal_id") == null ||
                columnMap.get("dj_deal_id") == null) {
            throw new RuntimeException("评测集文件表头错误");
        }

        //1. 查询入参
        for (int i = 1; i < list.size(); i++) {
            String mtPoiId = list.get(i).get(columnMap.get("mt_poi_id"));
            mtPoiIds.add(mtPoiId);
        }

        String sql = "select mt_poi_id, match_feature from mart_zb_catering.bml_dj_mt_poi_match_features " +
                "where partition_date = '" + partitionDate + "' and mt_poi_id in (" + StringUtils.join(mtPoiIds, ", ") + ")";
        SqlResult sqlResult = talosSqlQueryService.executeSql(sql);


        //2. 调用匹配结果
        List<List<Object>> data = sqlResult.getData();

        List<String> poiError = new ArrayList<>();
        List<String> poiRes = new ArrayList<>();
        for (List<Object> row : data) {
            String mtPoiId = (String) row.get(0);
            String matchFeature = (String) row.get(1);

            //调用匹配接口
            Map<String, Object> param = new HashMap<>();
            Map req = JSONObject.parseObject(matchFeature, Map.class);
            req.put("source", "");
            param.put("bizCode", bizCode);
            param.put("req", req);
            param.put("extra", ImmutableMap.of("type", "evaluateSetTest"));
            String url = "http://" + getPredictorIp() + ":8080/test/predictThrift";

//            String respStr = HttpUtil.httpPostJson(url, JSON.toJSONString(param), null);//重试
            String respStr = RetryUtil.retry(() -> {
                return HttpUtil.httpPostJson(url, JSON.toJSONString(param), null);
            }, (result, ex) -> result == null || ex != null, LionConfig.BADCASERETRYTIME, 100);
            JSONObject jsonObject = JSONObject.parseObject(respStr);
            if (jsonObject != null && jsonObject.getInteger("code") == 0) {
                String compressData = jsonObject.getJSONObject("data").getString("compressed_match");
                if (StringUtils.isNotEmpty(compressData)) {
                    String result = CompressUtil.decompress(compressData, "gzip");
                    poiRes.add(result);
                }
            } else {
                poiError.add(mtPoiId);
            }
        }

        //3. 解析成poi*deal粒度
        Map<String, String> realMatchMap = new HashMap<>();
        for (String matchResult : poiRes) {
            List<Map<String, String>> pairResult = JSON.parseObject(matchResult, new TypeReference<List<Map<String, String>>>() {
            });
            for (Map<String, String> pair : pairResult) {
                String key = pair.get("mt_poi_id") + "||||"
                        + pair.get("mt_deal_id") + "||||"
                        + pair.get("dj_deal_id");
                String match = pair.get("is_match");
                realMatchMap.put(key, match);
            }
        }

        //4.生成结果文件
        List<String> header = list.get(0);
        header.add("result");
        List<List<String>> rows = list.subList(1, list.size());
        for (List<String> row : rows) {
            String mtPoiId = row.get(columnMap.get("mt_poi_id"));
            String mtDealId = row.get(columnMap.get("mt_deal_id"));
            String djDealId = row.get(columnMap.get("dj_deal_id"));
            if (poiError.contains(mtPoiId)) {
                row.add("匹配报错");
                continue;
            }
            String key = mtPoiId + "||||" + mtDealId + "||||" + djDealId;
            String match = realMatchMap.get(key);
            int diff = header.size() - row.size();
            //填充空单元格
            for (int i = 0; i < diff - 1; i++) {
                row.add(" ");
            }
            row.add(match);
        }
        return excelGenService.export2S3(header, rows, "badcase测试结果" + System.currentTimeMillis() + ".xlsx");
    }

    private Map<String, Integer> column2Map(List<String> column) {
        Map<String, Integer> columnMap = new HashMap<>();
        for (int j = 0; j < column.size(); j++) {
            columnMap.put(column.get(j), j);
        }
        return columnMap;
    }

    private String getPredictorIp() {
        List<OctoNode> octoNodeStatus = octoNodeService.getOctoNodeStatus("com.sankuai.algoplatform.predictor", "default",
                HostEnv.STAGING, OctoNodeStatus.NORMAL);
        OctoNode octoNode = octoNodeStatus.stream().findFirst().get();
        return octoNode.getIp();
    }
}
