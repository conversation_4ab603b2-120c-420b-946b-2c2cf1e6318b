package com.sankuai.algoplatform.matchops.domain.enums;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum TestSubTaskStatusEnum {
    UNKNOWN(-1, "未知状态"),

    INIT(0, "初始状态"),

    TESTING(1, "测试中"),

    TEST_SUCCESS(2, "测试成功"),

    TEST_FAIL(3, "测试失败"),


    STOP_MANUAL(98, "任务被手动停止"),

    STOP_SYSTEM(99, "任务被系统停止");

    int code;
    String name;

    TestSubTaskStatusEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static TestSubTaskStatusEnum getByCode(int code) {
        return Arrays.stream(TestSubTaskStatusEnum.values()).filter(statusEnum -> statusEnum.getCode() == code)
                .findFirst().orElse(UNKNOWN);
    }

    public static boolean isCanBeRun(int code) {
        TestSubTaskStatusEnum statusEnum = getByCode(code);
        return statusEnum == INIT || statusEnum == TEST_FAIL || statusEnum == STOP_MANUAL || statusEnum == STOP_SYSTEM;
    }

    public static boolean isCanBeStop(int code) {
        TestSubTaskStatusEnum statusEnum = getByCode(code);
        return statusEnum == TESTING;
    }

    public static boolean isFailed(Integer code) {
        return code == TEST_FAIL.getCode() || code == STOP_MANUAL.getCode() || code == STOP_SYSTEM.getCode() || code == UNKNOWN.getCode();
    }

    public static boolean isSuccess(Integer code) {
        return code == TEST_SUCCESS.getCode();
    }

    public static boolean isRunning(Integer code) {
        return code == TESTING.getCode();
    }

    public static boolean isWaiting(Integer code) {
        return code == INIT.getCode();
    }

}
