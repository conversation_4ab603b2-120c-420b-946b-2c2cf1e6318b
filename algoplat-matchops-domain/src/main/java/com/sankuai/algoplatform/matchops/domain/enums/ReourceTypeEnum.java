package com.sankuai.algoplatform.matchops.domain.enums;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum ReourceTypeEnum {

    OCTO(0),

    MLP(1),

    FRIDAY(2);


    final int code;

    ReourceTypeEnum(int code) {
        this.code = code;
    }

    public static ReourceTypeEnum getByCode(int code) {
        return Arrays.stream(ReourceTypeEnum.values()).filter(statusEnum -> statusEnum.getCode() == code).findFirst().orElse(null);
    }
}
