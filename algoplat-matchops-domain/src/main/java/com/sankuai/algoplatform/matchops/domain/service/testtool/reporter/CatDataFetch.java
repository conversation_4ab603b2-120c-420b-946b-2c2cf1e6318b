package com.sankuai.algoplatform.matchops.domain.service.testtool.reporter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableList;
import com.sankuai.algoplatform.matchops.domain.model.testtool.RunTime;
import com.sankuai.algoplatform.matchops.domain.service.testtool.RaptorHelper;
import com.sankuai.algoplatform.matchops.infrastructure.config.LionConfig;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestSubTask;
import com.sankuai.algoplatform.matchops.infrastructure.enums.OctoNodeStatus;
import com.sankuai.algoplatform.matchops.infrastructure.model.OctoNode;
import com.sankuai.algoplatform.matchops.infrastructure.model.RaptorData;
import com.sankuai.algoplatform.matchops.infrastructure.model.SqlResult;
import com.sankuai.algoplatform.matchops.infrastructure.monitor.RaptorTrack;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.OctoNodeService;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.RaptorDataService;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.XuechengService;
import com.sankuai.algoplatform.matchops.infrastructure.util.DateUtil;
import com.sankuai.algoplatform.matchops.infrastructure.util.JsonPathUtil;
import com.sankuai.algoplatform.matchops.infrastructure.util.ParserUtil;
import com.sankuai.ead.citadel.document.node.impl.node.Table;
import com.sankuai.ead.citadel.document.parser.DocumentParsingException;
import com.sankuai.ead.citadel.document.parser.Serializer;
import com.sankuai.inf.octo.mns.model.HostEnv;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 打点数据查询类
 */
@Service
@Slf4j
public class CatDataFetch {
    @Autowired
    private RaptorDataService raptorDataService;

    @Autowired
    private OctoNodeService octoNodeService;

    public String query(String[] ruleText, TestSubTask testSubTask, boolean isMiddleRes) {
        if (ruleText.length < 2) {
            RaptorTrack.Sys_UnexpectedVisitNum.report("parseTemplateError" + testSubTask.getTaskId());
            return "";
        }

        Map<String, String> queryMap = ParserUtil.queryStringToMap(ruleText[1]);
        String ttype = queryMap.get("ttype");
        String appkey = queryMap.get("appkey");
        String cell = queryMap.get("cell");
        String type = queryMap.get("type");
        String item = queryMap.get("item");
        String group = queryMap.get("group");
        String metrics = queryMap.get("metrics");


        RunTime runTime = JSON.parseObject(testSubTask.getRunTime(), RunTime.class);
        Pair<Date, Date> pair = getReqExecuteTime(runTime, appkey + "#" + (StringUtils.isEmpty(group) ? cell : group));
        Date startTime = pair.getLeft();
        Date endTime = pair.getRight();

        List<Pair<Date, Date>> times = DateUtil.splitTimeByHour(startTime, endTime);
        Pair<Date, Date> needCalTime = getNeedCalTimes(testSubTask, times);

        HostEnv hostEnv = RaptorHelper.getHostEnv();
        if (StringUtils.contains(appkey, "udf")) {
            hostEnv = HostEnv.PROD;
        }

        queryMap.remove("ttype");
        queryMap.remove("appkey");
        queryMap.remove("type");
        queryMap.remove("item");
        queryMap.remove("cell");
        queryMap.remove("metrics");
        queryMap.remove("group");


        String raptorRes = "";
        switch (ttype) {
            case "transaction":
                RaptorData.Transaction.Root transactionRes = raptorDataService.queryTransactionHourly(hostEnv, appkey, needCalTime.getKey(), type, group, null);
                RaptorData.Transaction.Root transactionGraphsRes = null;
                try {
                    transactionGraphsRes = raptorDataService.queryTransactionHourlyGraphs(hostEnv, appkey, needCalTime.getKey(), type, group, queryMap.get("name"),null);
                } catch (Exception e) {
                    log.error("queryTransactionHourlyGraphs error.", e);
                }

                Map<String, RaptorData.Transaction.Report> reportMap = RaptorHelper.extraTransactionReport(transactionRes, transactionGraphsRes, queryMap.get("name"));
                if (isMiddleRes) {
                    return JSON.toJSONString(reportMap);
                }
                if (ruleText.length < 3) {
                    return transactionDefaultReturn(reportMap, type, queryMap.get("name"));
                }
                raptorRes = JSON.toJSONString(reportMap);
                break;
            case "event":
                RaptorData.Event.Root eventRes = raptorDataService.queryEventHourly(hostEnv, appkey, needCalTime.getKey(), type, group, queryMap);
                Map<String, RaptorData.Event.Report> eventReportMap = RaptorHelper.extraEventReport(eventRes);
                if (isMiddleRes) {
                    return JSON.toJSONString(eventReportMap);
                }
                if (ruleText.length < 3) {
                    return eventDefaultReturn(eventReportMap, type, queryMap.get("name"));
                }
                raptorRes = JSON.toJSONString(eventReportMap);
                break;
            case "problem":
                RaptorData.Problem.Root problemRes = raptorDataService.queryProblemHourly(hostEnv, appkey, needCalTime.getKey(), queryMap);
                Map<String, Long> errNameAndCount = RaptorHelper.extraError(problemRes);
                if (isMiddleRes) {
                    return JSON.toJSONString(errNameAndCount);
                }
                if (ruleText.length < 3) {
                    return problemDefaultReturn(errNameAndCount);
                }
                raptorRes = JSON.toJSONString(errNameAndCount);
                break;
            case "business":
                RaptorData.Business.Root businessRes = raptorDataService.queryBusiness(hostEnv, appkey, startTime, endTime, item, queryMap);
                Map<String, RaptorData.Business.Statistics> businessStatistics = RaptorHelper.extraBusinessStatistics(businessRes);
                if (isMiddleRes) {
                    return JSON.toJSONString(businessStatistics);
                }
                if (ruleText.length < 3) {
                    return businessDefaultReturn(businessStatistics);
                }
                raptorRes = JSON.toJSONString(businessStatistics);
                break;
            case "host":
                List<String> metricList = Arrays.asList(StringUtils.split(metrics, ","));
                List<String> hostIps = getHostIps(appkey, cell, hostEnv);
                if (CollectionUtils.isEmpty(hostIps)) {
                    throw new RuntimeException("host指标查询失败，原因：机器为空");
                }
                RaptorData.Host.Root hostRes = raptorDataService.queryHost(hostEnv, startTime, endTime, metricList, hostIps, queryMap);
                Map<String, Map<String, Double>> hostFormatRes = RaptorHelper.extraHostMetricAvgMax(hostRes);
                if (isMiddleRes) {
                    return JSON.toJSONString(hostFormatRes);
                }
                if (ruleText.length < 3) {
                    return hostDefaultReturn(hostFormatRes);
                }
                raptorRes = JSON.toJSONString(hostFormatRes);
                break;
            default:
                RaptorTrack.Sys_UnexpectedVisitNum.report("ReporterTemplateConfigError");
                throw new RuntimeException("报告模板配置错误");
        }

        // 特殊处理business类型中的Predict_SlowRequest指标
        if ("business".equals(ttype) && "Predict_SlowRequest".equals(item)) {
            try {
                // 检查JSON中是否包含对应的键，如果不包含，直接返回0
                JSONObject jsonObj = JSON.parseObject(raptorRes);
                if (!jsonObj.containsKey("Predict_SlowRequest")) {
                    log.warn("业务指标 Predict_SlowRequest 不存在，默认返回0");
                    return "0";
                }
            } catch (Exception e) {
                log.error("解析 Predict_SlowRequest 业务指标失败", e);
                return "0";
            }
        }
        
        // 特殊处理含冒号的服务名称，例如com.sankuai.persona.feature:FeatureService.query
        if ("transaction".equals(ttype) && StringUtils.isNotEmpty(queryMap.get("name")) && queryMap.get("name").contains(":")) {
            try {
                String serviceName = queryMap.get("name");
                JSONObject jsonObj = JSON.parseObject(raptorRes);
                
                // 检查JSON中是否包含这个带冒号的服务名
                if (!jsonObj.containsKey(serviceName)) {
                    log.warn("服务 {} 不存在，返回默认值", serviceName);
                    if (ruleText.length >= 3 && ruleText[2].endsWith(".qps")) {
                        return "0";
                    } else {
                        return "";
                    }
                }
            } catch (Exception e) {
                log.error("解析服务名称 {} 失败", queryMap.get("name"), e);
                return "0";
            }
        }
        
        try {
            String result = JsonPathUtil.extractString(raptorRes, ruleText[2]);
            if (NumberUtils.isNumber(result)) {
                if (LionConfig.HOST_METRIC_PERCENT.contains(item)) {
                    result = String.format("%.2f%%", Double.parseDouble(result));
                } else {
                    result = String.format("%.2f", Double.parseDouble(result));
                }
            }
            return StringUtils.isEmpty(result) ? "0" : result;
        } catch (Exception e) {
            log.error("提取数据时出现错误，ruleText: {}, raptorRes: {}", JSON.toJSONString(ruleText), raptorRes, e);
            // 对于数值型指标，返回0；其他类型返回空字符串
            if (ruleText.length >= 3 && (ruleText[2].contains(".qps") || ruleText[2].contains(".COUNT"))) {
                return "0";
            }
            return "";
        }
    }

    private List<String> getHostIps(String appkey, String cell, HostEnv hostEnv) {
        List<String> res = new ArrayList<>();
        if (StringUtils.equals(cell, "default") || StringUtils.equals(cell, "default_cell")) {
            cell = "";
        }
        List<OctoNode> octoNodeStatus = octoNodeService.getOctoNodeStatus(appkey, cell, hostEnv, OctoNodeStatus.NORMAL);
        if (CollectionUtils.isNotEmpty(octoNodeStatus)) {
            res = octoNodeStatus.stream().map(OctoNode::getName).collect(Collectors.toList());
        }

        if (res.size() <= LionConfig.RAPTOR_QUERY_HOST_NUM) {
            return new ArrayList<>(res);
        }
        List<String> copy = new ArrayList<>(res);
        Collections.shuffle(copy);
        return copy.subList(0, LionConfig.RAPTOR_QUERY_HOST_NUM);
    }

    private String transactionDefaultReturn(Map<String, RaptorData.Transaction.Report> reportMap, String type, String name) {
        RaptorData.Transaction.Report report = reportMap.getOrDefault(name, reportMap.get("TOTAL"));
        return String.format("指标%s#%s，QPS：%.0f，失败数：%d，失败率：%.4f%%，平均响应时间：%.0fms，最大响应时长：%.0fms，TP99：%.0fms，TP90：%.0fms",
                type, report.getName(), report.getQps(), report.getFails(),
                report.getFailPercent() * 100, report.getAvg(), report.getMax(), report.getTp99(), report.getTp90());
    }

    private String eventDefaultReturn(Map<String, RaptorData.Event.Report> eventReportMap, String type, String name) {
        RaptorData.Event.Report report = eventReportMap.getOrDefault(name, eventReportMap.get("TOTAL"));
        return String.format("指标%s#%s，上报数量：%d", type, name, report.getCount());
    }

    private String problemDefaultReturn(Map<String, Long> errNameAndCount) {
        if (MapUtils.isEmpty(errNameAndCount)) {
            return " ";
        }
        return errNameAndCount.entrySet().stream().map(entry -> String.format("问题%s，报错数量：%d", entry.getKey(), entry.getValue())).collect(Collectors.joining("。"));
    }

    private String businessDefaultReturn(Map<String, RaptorData.Business.Statistics> statistics) {
        if (MapUtils.isEmpty(statistics)) {
            return "无打点数据";
        }

        List<List<Object>> data = new ArrayList<>();
        List<String> column = new ArrayList<>();
        column.add("指标");
        statistics.forEach((k, v) -> {
            List<Object> row = new ArrayList<>();
            row.add(k);
            if (v.getCOUNT() != null) {
                row.add(v.getCOUNT());
                if (!column.contains("COUNT")) {
                    column.add("COUNT");
                }
            }
            if (v.getAVG() != null) {
                row.add(v.getAVG());
                if (!column.contains("AVG")) {
                    column.add("AVG");
                }
            }
            data.add(row);
        });


        SqlResult sqlResult = new SqlResult();
        sqlResult.setColumn(column);
        sqlResult.setData(data);
        try {
            Table table = XuechengService.buildTable(sqlResult.getColumn(), sqlResult.getData());
            return Serializer.serialize(table);
        } catch (Exception e) {
            log.error("business表格生成失败", e);
        }
        return "business数据解析失败";
    }

    //指标名，平均值，最大值
    private String hostDefaultReturn(Map<String, Map<String, Double>> pairs) {
        return pairs.entrySet().stream()
                .map(entry -> {
                    if (LionConfig.HOST_METRIC_PERCENT.contains(entry.getKey())) {
                        return String.format("指标%s，均值：%.2f%%，最大值：%.2f%%", entry.getKey(), entry.getValue().get("avg"), entry.getValue().get("max"));
                    } else {
                        return String.format("指标%s，均值：%.2f，最大值：%.2f", entry.getKey(), entry.getValue().get("avg"), entry.getValue().get("max"));
                    }
                }).collect(Collectors.joining("。"));
    }

    public static Pair<Date, Date> getReqExecuteTime(RunTime runTime, String key) {
        List<RunTime.ReqExecuteTime> reqTimes = runTime.getReqTimes();
        if (CollectionUtils.isEmpty(reqTimes)) {
            return Pair.of(DateUtil.parseDate(runTime.getTaskStartTime()), DateUtil.parseDate(runTime.getTaskEndTime()));
        }
        Optional<RunTime.ReqExecuteTime> first = reqTimes.stream().filter(f -> StringUtils.equals(f.getKey(), key)).findFirst();
        RunTime.ReqExecuteTime reqExecuteTime = first.orElse(reqTimes.get(0));
        return Pair.of(DateUtil.parseDate(reqExecuteTime.getStartTime()), DateUtil.parseDate(reqExecuteTime.getEndTime()));
    }

    private Pair<Date, Date> getNeedCalTimes(TestSubTask testSubTask, List<Pair<Date, Date>> times) {
        int size = times.size();
        if (size == 1) {
            return times.get(0);
        }

        //如果执行时间超过3h，上报
        if (times.size() > 3) {
            RaptorTrack.Sys_UnexpectedVisitNum.report("TaskExecuteTooLong#" + testSubTask.getId());
        }

        // 如果列表长度为奇数，直接取中间的三个元素
        if (size % 2 == 1) {
            return times.get(size / 2);
        } else {
            return times.get((size / 2) - 1);
        }
    }


}
