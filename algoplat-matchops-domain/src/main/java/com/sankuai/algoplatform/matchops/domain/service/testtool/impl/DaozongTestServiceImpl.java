package com.sankuai.algoplatform.matchops.domain.service.testtool.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.sankuai.algoplatform.matchops.domain.service.testtool.DaozongTestService;
import com.sankuai.algoplatform.matchops.infrastructure.config.LionConfig;
import com.sankuai.algoplatform.matchops.infrastructure.enums.OctoNodeStatus;
import com.sankuai.algoplatform.matchops.infrastructure.model.OctoNode;
import com.sankuai.algoplatform.matchops.infrastructure.model.SqlResult;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.ExcelGenService;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.ExcelReadService;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.OctoNodeService;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.TalosSqlQueryService;
import com.sankuai.algoplatform.matchops.infrastructure.util.HttpUtil;
import com.sankuai.inf.octo.mns.model.HostEnv;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/27
 */
@Service
@Slf4j
public class DaozongTestServiceImpl implements DaozongTestService {

    @Autowired
    private OctoNodeService octoNodeService;

    @Autowired
    private ExcelGenService excelGenService;

    @Autowired
    private ExcelReadService excelReadService;

    @Autowired
    private TalosSqlQueryService talosSqlQueryService;

    private static final List<String> daozongColumns = Lists.newArrayList(
            "dj_deal_id",
            "dj_deal_name",
            "dj_poi_id",
            "dj_poi_name",
            "mt_shop_id",
            "mt_shop_name",
            "dp_shop_id",
            "mt_poi_cate1_id",
            "mt_poi_cate1_name",
            "mt_poi_cate2_id",
            "mt_poi_cate2_name",
            "mt_poi_cate3_id",
            "mt_poi_cate3_name",
            "open_time",
            "deal_price",
            "deal_market_price",
            "goods_info",
            "purchase_info",
            "product_overview",
            "standard_goods_info",
            "standard_purchase_info",
            "new_standard_goods_info"
    );

    private static final String DAOZONG_FILE_NAME_FORMAT = "badcase测试结果-%d-%d.xlsx";

    @Override
    public Map<String, String> badCaseTest(String param) {
        Map<String, String> result = new HashMap<>();
        try {
            JSONObject req = JSON.parseObject(param);
            String s3Link = req.getString("file");
            List<List<String>> list = excelReadService.readService(s3Link);
            if (CollectionUtils.isEmpty(list)) {
                throw new RuntimeException("文件链接读取错误");
            }
            String s3LinkResult = doBadCaseTest(list, req.getString("bu"));
            result.put("code", "0");
            result.put("data", s3LinkResult);

        } catch (Exception e) {
            log.error("DaozongTestService.badCaseTest error, param={}", param, e);
            Cat.logError(e.getMessage(), e);
            result.put("code", "500");
            result.put("message", String.valueOf(e.getMessage()));
        }
        return result;
    }


    private String doBadCaseTest(List<List<String>> rowList, String bu) throws Exception {
        int dimCate1Id = convertDzDimCate1Id(bu);
        List<String> header = rowList.get(0);
        Map<String, Integer> columnMap = column2Map(header);
        Set<String> djPoiIdSet = new HashSet<>();
        //1. 查询入参
        for (int i = 1; i < rowList.size(); i++) {
            String djPoiId = rowList.get(i).get(columnMap.get("dj_poi_id"));
            djPoiIdSet.add(djPoiId);
        }
        List<String> djPoiIds = djPoiIdSet.stream().map(id -> "'" + id + "'").collect(Collectors.toList());
        String djPartitionDate = rowList.get(1).get(columnMap.get("dj_partition_date"));
        String mtPartitionDate = rowList.get(1).get(columnMap.get("mt_partition_date"));
        if (djPartitionDate.length() > 10) {
            djPartitionDate = djPartitionDate.substring(0, 10);
        }
        if (mtPartitionDate.length() > 10) {
            mtPartitionDate = mtPartitionDate.substring(0, 10);
        }
        String sql = "select " + StringUtils.join(daozongColumns, ", ") + " from mart_al_zb_ddpt.bml_dz_dj_mt_poi_deal_test_features " +
                "where partition_date = '" + djPartitionDate + "' and dj_poi_id in (" + StringUtils.join(djPoiIds, ", ") + ")";
        SqlResult sqlResult = talosSqlQueryService.executeSql(sql);
        if (CollectionUtils.isEmpty(sqlResult.getData())) {
            throw new RuntimeException("DJ特征为空, sql= " + sql);
        }
        //2. 调用匹配结果
        List<List<Object>> data = sqlResult.getData();
        Map<String, Integer> dataColumnMap = column2Map(daozongColumns);
        Map<String, List<List<Object>>> poiDeals = data.stream()
                .collect(Collectors.groupingBy(l -> String.valueOf(l.get(dataColumnMap.get("dj_poi_id")))));

        Map<String, Object> extraMap = new HashMap<>();
        extraMap.put("djPartitionDate", djPartitionDate);
        extraMap.put("mtPartitionDate", mtPartitionDate);
        extraMap.put("dimCat1Id", dimCate1Id);
        String smoothSwitchConfig = MapUtils.getString(LionConfig.DAOZONG_SMOOTH_SWITCH_CONFIG, String.valueOf(dimCate1Id));
        if (StringUtils.isNotBlank(smoothSwitchConfig)) {
            extraMap.put("smoothSwitchConfig", smoothSwitchConfig);
        }

        List<String> poiError = new ArrayList<>();
        List<String> poiUnMatch = new ArrayList<>();
        List<String> matchPair = new ArrayList<>();
        Map<String, String> poiMatchModelType = new HashMap<>();

        for (Map.Entry<String, List<List<Object>>> entry : poiDeals.entrySet()) {
            List<List<Object>> deals = entry.getValue();
            String djPoiId = getStringValue(deals.get(0).get(dataColumnMap.get("dj_poi_id")));

            Map<String, Object> dPlusShop = new HashMap<>();
            putMap(dPlusShop, "dPlusPoiId", djPoiId);
            putMap(dPlusShop, "dPlusPoiName", getStringValue(deals.get(0).get(dataColumnMap.get("dj_poi_name"))));
            putMap(dPlusShop, "mtShopId", getLongValue(deals.get(0).get(dataColumnMap.get("mt_shop_id"))));
            putMap(dPlusShop, "mtShopName", getStringValue(deals.get(0).get(dataColumnMap.get("mt_shop_name"))));
            putMap(dPlusShop, "dPlusPoiCat1Name", getStringValue(deals.get(0).get(dataColumnMap.get("mt_poi_cate1_name"))));
            putMap(dPlusShop, "dPlusPoiCat2Name", getStringValue(deals.get(0).get(dataColumnMap.get("mt_poi_cate2_name"))));
            putMap(dPlusShop, "dPlusPoiCat3Name", getStringValue(deals.get(0).get(dataColumnMap.get("mt_poi_cate3_name"))));
            List<Map<String, Object>> products = deals.stream().map(deal -> {
                Map<String, Object> product = new HashMap<>();
                putMap(product, "productId", getStringValue(deal.get(dataColumnMap.get("dj_deal_id"))));
                putMap(product, "productName", getStringValue(deal.get(dataColumnMap.get("dj_deal_name"))));
                putMap(product, "salePrice", getDoubleValue(deal.get(dataColumnMap.get("deal_price")), 0d));
                putMap(product, "marketPrice", getDoubleValue(deal.get(dataColumnMap.get("deal_market_price")), 0d));
                Map<String, Object> ext = new HashMap<>();
                putMap(ext, "opent_time_range", getStringValue(deal.get(dataColumnMap.get("open_time")), ""));
                putMap(ext, "goodsInfo", getStringValue(deal.get(dataColumnMap.get("goods_info"))));
                putMap(ext, "purchase_info", getStringValue(deal.get(dataColumnMap.get("purchase_info"))));
                putMap(ext, "standardGoodsInfo", getStringValue(deal.get(dataColumnMap.get("standard_goods_info"))));
                putMap(ext, "standardPurchaseInfo", getStringValue(deal.get(dataColumnMap.get("standard_purchase_info"))));
                putMap(ext, "productOverview", getStringValue(deal.get(dataColumnMap.get("product_overview"))));
                putMap(ext, "newStandardGoodsInfo", getStringValue(deal.get(dataColumnMap.get("new_standard_goods_info"))));
                putMap(product, "ext", ext);
                return product;
            }).collect(Collectors.toList());
            putMap(dPlusShop, "products", products);
            putMap(dPlusShop, "extraMap", extraMap);

            String url = getBadCaseTestUrl();
            String requestBody = JSON.toJSONString(ImmutableMap.of("dPlusShop", JSON.toJSONString(dPlusShop)));
            // 大模型处理慢，超时时间设长点
            String respStr = HttpUtil.httpPostWithTimeout(url, requestBody, 600, Collections.EMPTY_MAP);
            if (StringUtils.isBlank(respStr)) {
                poiError.add(djPoiId);
                continue;
            }
            JSONObject jsonObject = JSONObject.parseObject(respStr);
            if (jsonObject == null || jsonObject.getInteger("code") != 0) {
                poiError.add(djPoiId);
                continue;
            }
            String useBigModel = jsonObject.getString("useBigModel");
            if ("true".equals(useBigModel)) {
                poiMatchModelType.put(djPoiId, "大模型");
            } else if ("false".equals(useBigModel)) {
                poiMatchModelType.put(djPoiId, "小模型");
            }
            String dataStr = jsonObject.getString("data");
            if (StringUtils.isEmpty(dataStr)) {
                poiUnMatch.add(djPoiId);
                continue;
            }
            JSONArray matchResults = JSON.parseArray(dataStr);
            for (int i = 0; i < matchResults.size(); i++) {
                JSONObject matchResult = matchResults.getJSONObject(i);

                String mtPoiId = matchResult.getString("mtShopId");
                String djPoiId1 = matchResult.getString("dplusPoiId");
                String mtDealId = matchResult.getString("mtProductId");
                String djDealId = matchResult.getString("dplusProductId");

                matchPair.add(String.join("||||", djPoiId1, mtPoiId, djDealId, mtDealId));
            }
        }

        header.add("result");
        header.add("model_type");
        List<List<String>> rows = rowList.subList(1, rowList.size());
        for (List<String> row : rows) {
            String mtPoiId = row.get(columnMap.get("mt_shop_id"));
            String djPoiId = row.get(columnMap.get("dj_poi_id"));
            String mtDealId = row.get(columnMap.get("mt_deal_id"));
            String djDealId = row.get(columnMap.get("dj_deal_id"));

            if (poiError.contains(djPoiId)) {
                row.add("执行报错");
            } else if (poiUnMatch.contains(djPoiId)) {
                row.add("未执行完匹配流程");
            } else {
                String key = String.join("||||", djPoiId, mtPoiId, djDealId, mtDealId);
                String match = matchPair.contains(key) ? "匹配" : "不匹配";
                row.add(match);
            }
            row.add(poiMatchModelType.getOrDefault(djPoiId, ""));
        }
        String fileName = String.format(DAOZONG_FILE_NAME_FORMAT, dimCate1Id, System.currentTimeMillis());
        return excelGenService.export2S3(header, rows, fileName);
    }

    private String getStringValue(Object cellValue) {
        return getStringValue(cellValue, null);
    }

    private String getStringValue(Object cellValue, String defaultValue) {
        if (cellValue == null) {
            return defaultValue;
        }
        String strVal = cellValue.toString();
        if ("NULL".equals(strVal)) {
            return defaultValue;
        }
        return strVal;
    }

    private Long getLongValue(Object cellValue) {
        String strVal = getStringValue(cellValue);
        if (StringUtils.isBlank(strVal)) {
            return null;
        }
        return Long.valueOf(strVal);
    }

    private Double getDoubleValue(Object cellValue, Double defaultValue) {
        String strVal = getStringValue(cellValue);
        if (StringUtils.isBlank(strVal)) {
            return defaultValue;
        }
        return Double.valueOf(strVal);
    }

    private void putMap(Map<String, Object> map, String key, Object value) {
        if (value != null) {
            map.put(key, value);
        }
    }

    private Map<String, Integer> column2Map(List<String> column) {
        Map<String, Integer> columnMap = new HashMap<>();
        for (int j = 0; j < column.size(); j++) {
            columnMap.put(column.get(j), j);
        }
        return columnMap;
    }

    private int convertDzDimCate1Id(String bu) {
        switch (bu) {
            case "美发":
                return 38;
            case "美甲":
                return 39;
            case "美容美体":
                return 42;
            case "口腔医疗机构":
                return 46;
            case "按摩/足疗":
                return 48;
            case "KTV":
                return 51;
            case "游戏厅":
                return 54;
            case "健身中心":
                return 367;
            case "球类运动":
                return 585;
            case "亲子游乐":
                return 740;
            case "棋牌":
                return 2139;
            case "养发":
                return 2539;
            case "洗浴":
                return 2635;
            default:
                throw new RuntimeException("unsupported bu!!!");
        }
    }

    private String getBadCaseTestUrl() {
        return "http://" + getWenchangServiceIp() + ":8080/dealMatchSync";
    }

    private String getWenchangServiceIp() {
        List<OctoNode> octoNodeStatus = octoNodeService.getOctoNodeStatus("com.sankuai.wenchang.infer.service", "gray-release-daozong-bml",
                HostEnv.STAGING, OctoNodeStatus.NORMAL);
        OctoNode octoNode = octoNodeStatus.stream().findFirst().get();
        return octoNode.getIp();
//        return "***********";
    }
}