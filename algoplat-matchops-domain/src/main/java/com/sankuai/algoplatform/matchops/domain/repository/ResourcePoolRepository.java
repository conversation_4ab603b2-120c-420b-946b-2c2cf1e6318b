package com.sankuai.algoplatform.matchops.domain.repository;

import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ResourcePoolConfig;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.ResourcePoolConfigExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.ResourcePoolConfigMapper;
import com.sankuai.algoplatform.matchops.infrastructure.util.ZebraForceMasterUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Repository
public class ResourcePoolRepository {
    @Resource
    private ResourcePoolConfigMapper resourcePoolConfigMapper;

    public List<ResourcePoolConfig> queryValidResourcePoolByIds(List<Long> resourcePoolIds) {
        if (CollectionUtils.isEmpty(resourcePoolIds)) {
            return Collections.emptyList();
        }
        return ZebraForceMasterUtil.queryInMaster(() -> {
            ResourcePoolConfigExample example = new ResourcePoolConfigExample();
            example.createCriteria().andIdIn(resourcePoolIds).andStatusEqualTo(0);
            return resourcePoolConfigMapper.selectByExample(example);
        });
    }

    public List<ResourcePoolConfig> queryValidResourcePools() {
        ResourcePoolConfigExample example = new ResourcePoolConfigExample();
        example.createCriteria().andStatusEqualTo(0);
        return resourcePoolConfigMapper.selectByExample(example);
    }

    public List<ResourcePoolConfig> queryResourcePoolConfigByResourceType(String resourceType){
        ResourcePoolConfigExample example = new ResourcePoolConfigExample();
        example.createCriteria().andResourceTypeEqualTo(resourceType).andStatusEqualTo(0);
        return resourcePoolConfigMapper.selectByExample(example);
    }
}
