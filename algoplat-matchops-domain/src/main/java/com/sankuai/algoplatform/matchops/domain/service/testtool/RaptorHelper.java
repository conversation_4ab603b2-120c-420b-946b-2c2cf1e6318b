package com.sankuai.algoplatform.matchops.domain.service.testtool;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.sankuai.algoplatform.matchops.infrastructure.model.RaptorData;
import com.sankuai.inf.octo.mns.model.HostEnv;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.Min;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

public class RaptorHelper {

    public static Map<String, RaptorData.Transaction.Report> extraTransactionReport(RaptorData.Transaction.Root transactionRes, RaptorData.Transaction.Root transactionGraphsRes, String name) {
        Map<String, RaptorData.Transaction.Report> result = transactionRes.getReport().stream().collect(Collectors.toMap(RaptorData.Transaction.Report::getName, Function.identity(), (k1, k2) -> k2));


        Optional<RaptorData.Transaction.Report> first = transactionRes.getReport().stream().filter(r -> StringUtils.equals(name, r.getName())).findFirst();
        RaptorData.Transaction.Report report = first.orElseGet(() -> transactionRes.getReport().get(0));


        if (Objects.isNull(transactionGraphsRes) || Objects.isNull(transactionGraphsRes.getGraphs())
                || Objects.isNull(transactionGraphsRes.getGraphs().getHit()) || Objects.isNull(transactionGraphsRes.getGraphs().getHit().getRows())) {
            return result;
        }

        //qps必须按照分钟级计算
        AtomicInteger minScale = new AtomicInteger(100);
        AtomicInteger maxScale = new AtomicInteger(0);
        Optional<RaptorData.Transaction.Row> max = transactionGraphsRes.getGraphs().getHit().getRows().stream().peek(row -> {
            if (row.getHits() == null) {
                row.setHits(0.0);
            } else {
                minScale.set(Math.min(minScale.get(), Integer.parseInt(row.getXScale())));
                maxScale.set(Math.max(maxScale.get(), Integer.parseInt(row.getXScale())));
            }
        }).max(Comparator.comparingDouble(RaptorData.Transaction.Row::getHits));
        max.ifPresent(row -> {
            report.setQps(row.getHits() / 60);
            report.setQpm(row.getHits());
            report.setCost(maxScale.get() - minScale.get());
        });

        return result;
    }

    public static Map<String, RaptorData.Event.Report> extraEventReport(RaptorData.Event.Root eventRes) {
        return eventRes.getReport().stream().collect(Collectors.toMap(RaptorData.Event.Report::getName, Function.identity(), (k1, k2) -> k2));
    }


    public static Map<String, Long> extraError(RaptorData.Problem.Root raptorRes) {
        Map<String, Long> errNameAndCount = new HashMap<>();
        List<RaptorData.Problem.Report> report = raptorRes.getReport();
        report.stream().filter(repo -> StringUtils.equals(repo.getType(), "error")).forEach(repo -> errNameAndCount.put(repo.getName(), repo.getCount()));
        return errNameAndCount;
    }

    public static Map<String, RaptorData.Business.Statistics> extraBusinessStatistics(RaptorData.Business.Root businessRes) {
        Map<String, RaptorData.Business.Statistics> statistics = businessRes.getBusinessDatas().get(0).getStatistics();
        List<RaptorData.Business.DataEntry> datas = businessRes.getBusinessDatas().get(0).getDatas();
        for (RaptorData.Business.DataEntry entry : datas) {
            if (!statistics.containsKey(entry.getKey())) {
                RaptorData.Business.Statistics tmp = new RaptorData.Business.Statistics();
                tmp.setAVG(0.00000);
                tmp.setCOUNT(0d);
                statistics.put(entry.getKey(), tmp);
            }
        }
        return statistics;
    }

    public static Map<String, Map<String, Double>> extraHostMetricAvgMax(RaptorData.Host.Root raptorRes) {
        Map<String, Map<String, Double>> res = new HashMap<>();
        //过滤<10%，的先取单机取平均值、最大值，再多机器取平均值、最大值
        Map<String, RaptorData.Host.MetricTrend> metricTrends = raptorRes.getMetricTrends();
        metricTrends.forEach((k, v) -> {
            Map<String, RaptorData.Host.EndpointEntry> hostMetrics = v.getEndpointsEntries();

            Map<String, DoubleSummaryStatistics> hostStats = hostMetrics.entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().getTimeTrend().values().stream().filter(f -> {
                        //cpu指标过滤<10%的
                        if (StringUtils.contains(k, "cpu.busy") || StringUtils.contains(k, "proc.cpu")) {
                            return f > 10;
                        }
                        return true;
                    }).collect(Collectors.summarizingDouble(Double::doubleValue))));

            DoubleSummaryStatistics overallStats = hostStats.values().stream().collect(Collectors.summarizingDouble(DoubleSummaryStatistics::getAverage));


            double totalAvg = overallStats.getAverage();
            double totalMax = hostStats.values().stream().mapToDouble(DoubleSummaryStatistics::getMax).max().orElse(0.0);
            res.put(k, ImmutableMap.of("avg", totalAvg, "max", totalMax));
        });
        return res;
    }

    public static HostEnv getHostEnv() {
        return HostEnv.STAGING;
    }
}
