package com.sankuai.algoplatform.matchops.domain.enums;

public enum OfflineTaskStatus {
    INIT(0, "未开始"),
    RUNNING(1, "运行中"),
    FINISH(2, "已完成");
    private Integer code;
    private String desc;
    OfflineTaskStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    public Integer getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }
}
