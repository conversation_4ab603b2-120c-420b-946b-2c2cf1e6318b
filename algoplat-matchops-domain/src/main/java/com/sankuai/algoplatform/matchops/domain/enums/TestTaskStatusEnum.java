package com.sankuai.algoplatform.matchops.domain.enums;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum TestTaskStatusEnum {
    UNKNOWN(-1, "未知状态"),

    INIT(0, "初始状态"),

    PREPARE_ENV_ING(1, "环境准备中"),

    TOBE_TEST(2, "环境准备成功，待测试"),

    ENV_FAIL(3, "环境准备失败"),

    TESTING(4, "测试中"),

    TEST_SUCCESS(5, "测试成功"),

    TEST_FAIL(6, "测试失败");

    int code;
    String name;

    TestTaskStatusEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static TestTaskStatusEnum getByCode(int code) {
        return Arrays.stream(TestTaskStatusEnum.values()).filter(statusEnum -> statusEnum.getCode() == code).findFirst().orElse(UNKNOWN);
    }

    public static boolean isCanBePrepareEnv(int code) {
        TestTaskStatusEnum statusEnum = getByCode(code);
        return statusEnum == INIT || statusEnum == ENV_FAIL;
    }

    public static boolean isCanBeRunByTask(int code) {
        TestTaskStatusEnum statusEnum = getByCode(code);
        return statusEnum != INIT && statusEnum != ENV_FAIL;
    }

}
