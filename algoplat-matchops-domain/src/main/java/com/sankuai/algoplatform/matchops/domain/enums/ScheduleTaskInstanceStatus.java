package com.sankuai.algoplatform.matchops.domain.enums;

public enum ScheduleTaskInstanceStatus {
    INITIAL(0, "初始化"),
    SUBMITTED(1, "已提交"),
    SUCCESS(3, "成功"),
    FAILED(-1, "失败");

    private int code;
    private String desc;

    ScheduleTaskInstanceStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ScheduleTaskInstanceStatus getByCode(Integer code) {
        for (ScheduleTaskInstanceStatus value : ScheduleTaskInstanceStatus.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
