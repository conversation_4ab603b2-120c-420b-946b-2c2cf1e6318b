package com.sankuai.algoplatform.matchops.domain.service.testtool;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.google.common.collect.ImmutableList;
import com.sankuai.algoplatform.matchops.infrastructure.util.JsonPathUtil;
import com.sankuai.algoplatform.matchops.infrastructure.util.StringReplacerUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 增强版文档处理器，专门处理学城文档JSON格式
 */
@Slf4j
public class EnhancedDocHandler {

    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\$\\{([^}]+)\\}");
    
    // 表格相关节点类型
    private static final List<String> TABLE_TYPES = Arrays.asList("table", "table_row", "table_cell", "table_header");
    
    /**
     * 替换文档中的文本变量
     * @param doc 文档JSON对象
     * @param replacements 替换映射
     * @return 处理后的文档JSON对象
     */
    public static JSONObject processDoc(JSONObject doc, Map<String, String> replacements) {
        if (MapUtils.isEmpty(replacements)) {
            return doc;
        }
        
        // 深度复制防止修改原对象
        JSONObject processedDoc = JSON.parseObject(doc.toJSONString());
        
        // 处理文本替换
        replaceDocText(processedDoc, replacements);
        
        // 处理表格
        processDocTables(processedDoc, replacements);
        
        return processedDoc;
    }
    
    /**
     * 替换文档中的文本
     */
    private static void replaceDocText(JSONObject obj, Map<String, String> replacements) {
        if (obj == null) {
            return;
        }
        
        // 处理文档内容数组
        JSONArray contentArr = obj.getJSONArray("content");
        if (CollectionUtils.isNotEmpty(contentArr)) {
            for (int i = 0; i < contentArr.size(); i++) {
                Object item = contentArr.get(i);
                if (item instanceof JSONObject) {
                    JSONObject contentObj = (JSONObject) item;
                    
                    // 递归处理内容
                    replaceContentObject(contentObj, replacements);
                }
            }
        }
    }
    
    /**
     * 递归处理内容对象
     */
    private static void replaceContentObject(JSONObject contentObj, Map<String, String> replacements) {
        if (contentObj == null) {
            return;
        }
        
        // 处理文本节点
        if (contentObj.containsKey("text") && contentObj.containsKey("type") && "text".equals(contentObj.getString("type"))) {
            String text = contentObj.getString("text");
            if (StringUtils.contains(text, "${")) {
                String replacedText = replaceVariables(text, replacements);
                contentObj.put("text", replacedText);
            }
        }
        
        // 递归处理子内容
        JSONArray nestedContent = contentObj.getJSONArray("content");
        if (CollectionUtils.isNotEmpty(nestedContent)) {
            for (int i = 0; i < nestedContent.size(); i++) {
                Object item = nestedContent.get(i);
                if (item instanceof JSONObject) {
                    replaceContentObject((JSONObject) item, replacements);
                }
            }
        }
    }
    
    /**
     * 处理文档中的表格
     */
    private static void processDocTables(JSONObject doc, Map<String, String> replacements) {
        // 查找所有表格节点
        List<JSONObject> tables = findAllObjects(doc, "table");
        
        for (JSONObject table : tables) {
            processTable(table, replacements);
        }
    }
    
    /**
     * 处理单个表格
     */
    private static void processTable(JSONObject table, Map<String, String> replacements) {
        // 处理表格行
        JSONArray rows = table.getJSONArray("content");
        if (CollectionUtils.isEmpty(rows)) {
            return;
        }
        
        for (int i = 0; i < rows.size(); i++) {
            JSONObject row = rows.getJSONObject(i);
            
            // 处理行中的单元格
            JSONArray cells = row.getJSONArray("content");
            if (CollectionUtils.isNotEmpty(cells)) {
                for (int j = 0; j < cells.size(); j++) {
                    JSONObject cell = cells.getJSONObject(j);
                    
                    // 递归处理单元格内容
                    replaceContentObject(cell, replacements);
                }
            }
        }
    }
    
    /**
     * 替换变量字符串
     */
    private static String replaceVariables(String text, Map<String, String> replacements) {
        if (StringUtils.isEmpty(text) || MapUtils.isEmpty(replacements)) {
            return text;
        }
        
        Matcher matcher = VARIABLE_PATTERN.matcher(text);
        StringBuffer sb = new StringBuffer();
        
        while (matcher.find()) {
            String variable = matcher.group(0); // 完整的变量，如 ${hive∫...}
            String key = matcher.group(1); // 变量内容，如 hive∫...
            
            String replacement = replacements.getOrDefault(variable, "");
            
            // 如果没有直接匹配，尝试使用变量内容作为键
            if (StringUtils.isEmpty(replacement)) {
                replacement = replacements.getOrDefault(key, "");
            }
            
            // 特殊处理resource类型变量
            if (StringUtils.isEmpty(replacement) && key.startsWith("resource")) {
                log.info("处理resource类型变量: {}", key);
                // 尝试不同的键格式
                for (Map.Entry<String, String> entry : replacements.entrySet()) {
                    if (entry.getKey().contains("resource") && 
                        !StringUtils.isEmpty(entry.getValue()) &&
                        !"解析失败".equals(entry.getValue())) {
                        replacement = entry.getValue();
                        log.info("找到resource变量替换值: {}", replacement);
                        break;
                    }
                }
            }
            
            // 特殊处理包含冒号或特殊字符的路径变量
            if (StringUtils.isEmpty(replacement) && (key.contains(":") || key.contains(".") || key.contains("_"))) {
                log.info("处理特殊路径变量: {}", key);
                // 尝试查找部分匹配的键
                String keyLower = key.toLowerCase();
                for (Map.Entry<String, String> entry : replacements.entrySet()) {
                    String entryKeyLower = entry.getKey().toLowerCase();
                    // 检查键的各部分是否匹配
                    if ((entryKeyLower.contains(keyLower) || keyLower.contains(entryKeyLower)) && 
                        !StringUtils.isEmpty(entry.getValue()) &&
                        !"解析失败".equals(entry.getValue())) {
                        replacement = entry.getValue();
                        log.info("找到特殊路径变量替换值，原始变量: {}, 匹配键: {}, 值: {}", key, entry.getKey(), replacement);
                        break;
                    }
                }
            }
            
            // 默认替换为空字符串，避免"解析失败"
            if (StringUtils.isEmpty(replacement) || "解析失败".equals(replacement) || replacement.contains("解析错误")) {
                log.warn("变量 {} 无法找到替换值，将替换为空字符串", variable);
                replacement = ""; // 将无法解析的变量替换为空字符串
            }
            
            // 替换特殊字符防止干扰正则
            replacement = replacement.replace("$", "\\$");
            matcher.appendReplacement(sb, replacement);
        }
        
        matcher.appendTail(sb);
        return sb.toString();
    }
    
    /**
     * 查找指定类型的所有对象
     */
    private static List<JSONObject> findAllObjects(JSONObject root, String type) {
        List<JSONObject> results = new ArrayList<>();
        findObjectsWithType(root, type, results);
        return results;
    }
    
    /**
     * 递归查找特定类型的对象
     */
    private static void findObjectsWithType(JSONObject obj, String type, List<JSONObject> results) {
        if (obj == null) {
            return;
        }
        
        if (type.equals(obj.getString("type"))) {
            results.add(obj);
        }
        
        JSONArray contentArr = obj.getJSONArray("content");
        if (CollectionUtils.isNotEmpty(contentArr)) {
            for (int i = 0; i < contentArr.size(); i++) {
                Object item = contentArr.get(i);
                if (item instanceof JSONObject) {
                    findObjectsWithType((JSONObject) item, type, results);
                }
            }
        }
    }
    
    /**
     * 过滤和处理用于替换的Map
     * @param inputMap 输入的替换映射
     * @return 两个Map的列表，第一个为普通文本替换，第二个为表格替换
     */
    public static List<Map<String, String>> processReplacements(Map<String, String> inputMap) {
        Map<String, String> textReplacements = new HashMap<>();
        Map<String, String> tableReplacements = new HashMap<>();
        Map<String, String> jsonPathValues = new HashMap<>();

        // 第一次遍历：提取JSON路径相关变量值
        for (Map.Entry<String, String> entry : inputMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            
            // 检查是否包含JSON路径信息 (例如 ${h|||$['cpu.busy'].avg})
            if (key.contains("|||")) {
                String[] parts = key.split("\\|\\|\\|");
                if (parts.length >= 2) {
                    // 存储原始键和JSON路径用于后续查找
                    jsonPathValues.put(key, value);
                }
            }
        }

        // 第二次遍历：处理所有替换
        for (Map.Entry<String, String> entry : inputMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            
            // 跳过JSON路径相关的键，这些会在最后特殊处理
            if (key.contains("|||")) {
                continue;
            }

            // 检查值是否为表格JSON
            if (StringUtils.isNotEmpty(value) && 
                value.contains("\"type\"") && 
                value.contains("\"content\"") &&
                (value.contains("\"table\"") || value.contains("\"table_row\""))) {
                try {
                    // 确保是有效的表格JSON
                    JSONObject tableObj = JSON.parseObject(value);
                    if (tableObj.containsKey("type") && 
                        TABLE_TYPES.contains(tableObj.getString("type"))) {
                        tableReplacements.put(key, value);
                        continue;
                    }
                } catch (Exception e) {
                    log.error("Failed to parse potential table JSON: {}", value, e);
                }
            }
            
            // 普通文本替换
            if (!value.equals("解析失败")) {
                textReplacements.put(key, value);
            } else {
                // 对于解析失败的值，使用空字符串替代
                textReplacements.put(key, "");
            }
        }
        
        // 最后处理JSON路径相关变量
        for (Map.Entry<String, String> entry : jsonPathValues.entrySet()) {
            String key = entry.getKey();
            String[] parts = key.split("\\|\\|\\|");
            
            if (parts.length >= 2) {
                String sourceKey = parts[0];
                String jsonPath = parts[1];
                
                // 查找源变量的值
                String sourceValue = textReplacements.get("${" + sourceKey + "}");
                if (StringUtils.isNotEmpty(sourceValue)) {
                    try {
                        // 从源值中提取JSON路径值
                        String extractedValue = JsonPathUtil.extractString(sourceValue, jsonPath);
                        if (StringUtils.isNotEmpty(extractedValue)) {
                            textReplacements.put(key, extractedValue);
                        } else {
                            textReplacements.put(key, ""); // 路径不存在使用空字符串
                        }
                    } catch (Exception e) {
                        Cat.logError(e);
                        log.warn("Failed to extract JSON path value: {}", jsonPath, e);
                        textReplacements.put(key, "");
                    }
                }
            }
        }
        
        return ImmutableList.of(textReplacements, tableReplacements);
    }
}
