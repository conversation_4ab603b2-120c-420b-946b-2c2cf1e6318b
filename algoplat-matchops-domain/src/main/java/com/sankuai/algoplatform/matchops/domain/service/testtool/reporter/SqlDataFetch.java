package com.sankuai.algoplatform.matchops.domain.service.testtool.reporter;

import com.alibaba.fastjson.JSON;
import com.sankuai.algoplatform.matchops.domain.service.QuerySqlService;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestSubTask;
import com.sankuai.algoplatform.matchops.infrastructure.model.SqlResult;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.XuechengService;
import com.sankuai.algoplatform.matchops.infrastructure.util.JsonPathUtil;
import com.sankuai.ead.citadel.document.node.impl.node.Table;
import com.sankuai.ead.citadel.document.parser.Serializer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.mortbay.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * hive表数据查询类
 */
@Service
@Slf4j
public class SqlDataFetch {

    @Autowired
    private QuerySqlService querySqlService;


    public String query(String[] ruleText, TestSubTask testSubTask) throws Exception {
        String sql = ruleText[1];
        log.info("original SQL:{}", sql);
        String cd = RunParamDataFetch.query(new String[]{"param", "cd"}, testSubTask);
        if (StringUtils.isNotEmpty(cd)) {
            sql = StringUtils.replace(sql, "#cd#", cd);
        }
        String hour = RunParamDataFetch.query(new String[]{"param", "hour"}, testSubTask);
        if (StringUtils.isNotEmpty(hour)) {
            sql = StringUtils.replace(sql, "#hour#", hour);
        }
        String dt = RunParamDataFetch.query(new String[]{"param", "dt"}, testSubTask);
        if (StringUtils.isNotEmpty(dt)) {
            sql = StringUtils.replace(sql, "#dt#", dt);
        }
        log.info("replaced SQL:{}", sql);

        SqlResult sqlResult = querySqlService.executeSql(sql);

//        String mock = "{\"code\":0,\"column\":[\"total\",\"cache_cnt\"],\"data\":[[\"1153256\",\"655371\"]],\"message\":\"success\"}";
//        SqlResult sqlResult = JSONObject.parseObject(mock, SqlResult.class);

        if (sqlResult.getCode() != 0) {
            throw new RuntimeException(sqlResult.getMessage());
        }
        if (ruleText.length < 3) {
            Table table = XuechengService.buildTable(sqlResult.getColumn(), sqlResult.getData());
            return Serializer.serialize(table);
        }
        return JsonPathUtil.extractString(JSON.toJSONString(sqlResult), ruleText[2]);
    }

}
