//package com.sankuai.algoplatform.matchops.domain.model.testtool;
//
//import com.alibaba.fastjson.annotation.JSONField;
//import com.sankuai.algoplatform.matchops.domain.constant.TestToolConstants;
//import com.sankuai.algoplatform.matchops.domain.enums.ReqPostTypeEnum;
//import lombok.Data;
//
//@Data
//public class RunParam {
//
//    @JSONField(name = TestToolConstants.ORIGIN_RUN_PARAM)
//    private String originRunParam;
//
//    @JSONField(name = TestToolConstants.POST_TYPE)
//    private int postType;
//
//    //http参数
//    @JSONField(name = TestToolConstants.IP)
//    private String ip;
//
//    @JSONField(name = TestToolConstants.ADDR)
//    private String addr;
//
//    @JSONField(name = TestToolConstants.COOKIE)
//    private String cookie;
//
//    @JSONField(name = TestToolConstants.REQUEST_BODY)
//    private String requestBody;
//
//    @JSONField(name = TestToolConstants.SQL_PARAM)
//    private String sqlParam;
//
//    @JSONField(name = TestToolConstants.TIMEOUT)
//    private int timeout;
//
//    //XT参数
//    @JSONField(name = TestToolConstants.REQ_POST_LINK)
//    private String reqPostLink;//跑数链接
//
//    @JSONField(name = TestToolConstants.CD)
//    private String cd;//日期
//
//    @JSONField(name = TestToolConstants.DT)
//    private String dt;//日期
//
//    @JSONField(name = TestToolConstants.CLEAR_CACHE)
//    private String clearCache;//是否清楚一级缓存
//
//    @Override
//    public String toString() {
//        StringBuilder sb = new StringBuilder("{");
//        boolean first = true;
//        if (requestBody != null && !requestBody.isEmpty()) {
//            sb.append("requestBody='").append(requestBody).append("'");
//            first = false;
//        }
//        if (sqlParam != null && !sqlParam.isEmpty()) {
//            if (!first) sb.append(", ");
//            sb.append("sqlParam='").append(sqlParam).append("'");
//            first = false;
//        }
//        if (cd != null && !cd.isEmpty()) {
//            if (!first) sb.append(", ");
//            sb.append("cd='").append(cd).append("'");
//            first = false;
//        }
//        if (clearCache != null && !clearCache.isEmpty()) {
//            if (!first) sb.append(", ");
//            sb.append("clearCache='").append(clearCache).append("'");
//        }
//        sb.append("}");
//        return sb.toString();
//    }
//
//}
