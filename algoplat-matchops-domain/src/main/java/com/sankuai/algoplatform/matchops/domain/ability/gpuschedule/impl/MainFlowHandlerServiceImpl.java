package com.sankuai.algoplatform.matchops.domain.ability.gpuschedule.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.algoplatform.matchops.domain.ability.gpuschedule.*;
import com.sankuai.algoplatform.matchops.domain.ability.gpuschedule.util.HandlerUtil;
import com.sankuai.algoplatform.matchops.domain.enums.ScheduleTaskInstanceStatus;
import com.sankuai.algoplatform.matchops.domain.model.ScheduleTaskCheckResult;
import com.sankuai.algoplatform.matchops.domain.repository.*;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ResourcePoolConfig;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ScheduleServiceConfig;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ScheduleTaskConfig;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TaskBindingConfig;
import com.sankuai.algoplatform.matchops.infrastructure.monitor.RaptorTrack;
import com.sankuai.algoplatform.matchops.infrastructure.util.ThreadPoolFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.algoplatform.matchops.domain.enums.ScheduleTaskInstanceStatus.FAILED;
import static com.sankuai.algoplatform.matchops.domain.enums.ScheduleTaskInstanceStatus.SUCCESS;

@Slf4j
@Service
public class MainFlowHandlerServiceImpl implements MainFlowHandlerService {
    @Resource
    private ScheduleTaskRepository scheduleTaskRepository;
    @Resource
    private TaskBindingRepository taskBindingRepository;
    @Resource
    private ScheduleServiceRepository scheduleServiceRepository;
    @Resource
    private ResourcePoolRepository resourcePoolRepository;
    @Resource
    private ScheduleTaskInstanceRepository scheduleTaskInstanceRepository;

    @Override
    public void generateTaskInstance() {
        // 查询全部ScheduleTask
        List<ScheduleTaskConfig> scheduleTaskConfigs = scheduleTaskRepository.queryValidScheduleTasks();
        if (CollectionUtils.isEmpty(scheduleTaskConfigs)) {
            log.info("generateTaskInstance:未配置调度任务.");
            return;
        }
        List<ScheduleTask> scheduleTasks = scheduleTaskConfigs.stream()
                .filter(ScheduleTask::isValidConfig)
                .map(ScheduleTask::from).collect(Collectors.toList());
        // 调用ScheduleTask的check
        List<ScheduleTaskCheckResult> need2ExecuteTasks = scheduleTasks.stream()
                .map(ScheduleTask::check).filter(ScheduleTaskCheckResult::isNeedExecute).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(need2ExecuteTasks)) {
            log.info("generateTaskInstance:没有需要执行的调度任务.");
            return;
        }
        // 如果true，找到对应的taskbinding，生成ScheduleTaskInstance
        List<TaskBindingConfig> taskBindingConfigs = taskBindingRepository
                .queryValidBindingsByTaskIds(need2ExecuteTasks.stream()
                        .map(ScheduleTaskCheckResult::getScheduleTask)
                        .map(ScheduleTask::getId).distinct().collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(taskBindingConfigs)) {
            log.info("generateTaskInstance:待执行的调度任务未绑定服务.");
            return;
        }
        List<ScheduleServiceConfig> serviceConfigs = scheduleServiceRepository.queryValidScheduleServiceByIds(taskBindingConfigs.stream().map(TaskBindingConfig::getScheduleServiceId)
                .distinct().collect(Collectors.toList()));
        Map<Long, ScheduleService> scheduleServiceMap = serviceConfigs.stream()
                .filter(ScheduleService::isValidConfig)
                .map(ScheduleService::from).collect(Collectors.toMap(ScheduleService::getId, Function.identity()));
        List<ResourcePoolConfig> resourcePoolConfigs = resourcePoolRepository.queryValidResourcePoolByIds(taskBindingConfigs.stream()
                .flatMap(f -> JSON.parseArray(f.getResourcePoolIds()).toJavaList(Long.class).stream())
                .distinct().collect(Collectors.toList()));
        Map<Long, ResourcePool> resourcePoolMap = resourcePoolConfigs.stream()
                .filter(ResourcePool::isValidConfig)
                .map(ResourcePool::from).collect(Collectors.toMap(ResourcePool::getId, Function.identity()));

        Map<String, ScheduleTaskInstance> candiInstances = new LinkedHashMap<>();
        // 遍历需要执行的ScheduleTaskCheckResult
        for (ScheduleTaskCheckResult scheduleTaskCheckResult : need2ExecuteTasks) {
            ScheduleTask scheduleTask = scheduleTaskCheckResult.getScheduleTask();

            // 找到对应的taskBinding，生成ScheduleTaskInstance
            for (TaskBindingConfig taskBindingConfig : taskBindingConfigs) {
                if (!Objects.equals(scheduleTask.getId(), taskBindingConfig.getScheduleTaskId())) {
                    continue;
                }
                try {
                    List<ResourcePool> resourcePools = JSON.parseArray(taskBindingConfig.getResourcePoolIds()).toJavaList(Long.class)
                            .stream().map(resourcePoolMap::get).collect(Collectors.toList());

                    //为ScheduleService添加用于MLP的相关参数：getResourceType和queue
                    ScheduleService scheduleService = scheduleServiceMap.get(taskBindingConfig.getScheduleServiceId());
                    if (resourcePools.get(0) != null) {
                        scheduleService.setResourceType(resourcePools.get(0).getResourceType());
                        scheduleService.setQueue(resourcePools.get(0).getQueue());
                    }

                    TaskBinding taskBinding = TaskBinding.from(taskBindingConfig, scheduleTask,
                            scheduleServiceMap.get(taskBindingConfig.getScheduleServiceId()),
                            resourcePools);

                    ScheduleTaskInstance taskInstance = taskBinding.generateInstances(scheduleTaskCheckResult.getAction());
                    candiInstances.put(taskInstance.getUniqInstanceKey(), taskInstance);
                } catch (Exception e) {
                    log.error("generateTaskInstance error: taskBindingConfig:{}, ex:", JSON.toJSONString(taskBindingConfig), e);
                    RaptorTrack.Sys_GpuSchedule_FailEvent.report(String.format("GenTaskError:Task:%s,Binding:%s", scheduleTask.getTaskName(), taskBindingConfig.getId()));
                }
            }
        }
        // 落库前去重
        if (MapUtils.isEmpty(candiInstances)) {
            log.info("generateTaskInstance:未生成有效的调度任务.");
            RaptorTrack.Sys_UnexpectedVisitNum.report("MainFlowHandlerService.generateTaskInstance_Empty");
            return;
        }
        List<String> successTaskInstances = scheduleTaskInstanceRepository.insertScheduleTaskInstance(new ArrayList<>(candiInstances.values()));
        log.info("generateTaskInstance:成功生成待执行的调度任务:{}", JSON.toJSONString(successTaskInstances));
        successTaskInstances.forEach(f -> {
            ScheduleTaskInstance instance = candiInstances.get(f);
            HandlerUtil.reportRaptorEvent(instance);
        });
    }

    @Override
    public void invokeTaskInstance() {
        // 从表中获取全部的ScheduleTaskInstance
        List<com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ScheduleTaskInstance> scheduleTaskInstancesInDbs =
                scheduleTaskInstanceRepository.queryScheduleTaskInstancesByStatus(ScheduleTaskInstanceStatus.INITIAL);
        if (CollectionUtils.isEmpty(scheduleTaskInstancesInDbs)) {
            log.info("invokeTaskInstance:没有待执行的调度任务.");
            return;
        }
        List<ScheduleTaskInstance> taskInstances = buildScheduleTaskInstance(scheduleTaskInstancesInDbs);
        if (CollectionUtils.isEmpty(taskInstances)) {
            log.info("invokeTaskInstance:未生成有效的调度任务.");
            RaptorTrack.Sys_UnexpectedVisitNum.report("MainFlowHandlerService.NoValidTaskInstance");
            return;
        }

        // 调用ScheduleTaskInstance的invoke, 且更新状态
        for (ScheduleTaskInstance taskInstance : taskInstances) {
            ThreadPoolFactory.getInvokeTaskThreadPool().submit(() -> {
                try {
                    taskInstance.invoke();
                    scheduleTaskInstanceRepository.updateInstanceStatus(taskInstance.getId(), taskInstance.getStatus(), taskInstance.getMessage());
                } catch (Exception e) {
                    log.error("invokeTaskInstance error: taskInstance:{}, ex:", JSON.toJSONString(taskInstance), e);
                    scheduleTaskInstanceRepository.updateInstanceStatus(taskInstance.getId(), FAILED, e.getMessage());
                }
                if (Lists.newArrayList(SUCCESS, FAILED).contains(taskInstance.getStatus())) {
                    HandlerUtil.reportRaptorEvent(taskInstance);
                }
            });
        }
        log.info("invokeTaskInstance:成功触发调度任务执行:{}", taskInstances.stream().map(ScheduleTaskInstance::getUniqInstanceKey).collect(Collectors.toList()));
    }

    @Override
    public void updateTaskInstance() {
        // 从表中获取全部的ScheduleTaskInstance
        List<com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ScheduleTaskInstance> scheduleTaskInstancesInDbs =
                scheduleTaskInstanceRepository.queryScheduleTaskInstancesByStatus(ScheduleTaskInstanceStatus.SUBMITTED);
        if (CollectionUtils.isEmpty(scheduleTaskInstancesInDbs)) {
            log.info("updateTaskInstance:没有待更新状态的调度任务.");
            return;
        }
        List<ScheduleTaskInstance> taskInstances = buildScheduleTaskInstance(scheduleTaskInstancesInDbs);
        if (CollectionUtils.isEmpty(taskInstances)) {
            log.info("updateTaskInstance:未生成有效的调度任务.");
            RaptorTrack.Sys_UnexpectedVisitNum.report("MainFlowHandlerService.NoValidTaskInstance");
            return;
        }
        Map<String, ScheduleTaskInstanceStatus> originStatus = taskInstances.stream().collect(Collectors.toMap(ScheduleTaskInstance::getUniqInstanceKey, ScheduleTaskInstance::getStatus));

        // 调用ScheduleTaskInstance的invoke, 且更新状态
        for (ScheduleTaskInstance taskInstance : taskInstances) {
            ThreadPoolFactory.getInvokeTaskThreadPool().submit(() -> {
                try {
                    taskInstance.updateStatus();
                    scheduleTaskInstanceRepository.updateInstanceStatus(taskInstance.getId(), taskInstance.getStatus(), taskInstance.getMessage());
                } catch (Exception e) {
                    log.error("updateTaskInstance error: taskInstance:{}, ex:", JSON.toJSONString(taskInstance), e);
                    scheduleTaskInstanceRepository.updateInstanceStatus(taskInstance.getId(), FAILED, e.getMessage());
                }
                HandlerUtil.reportRaptorEvent(taskInstance);
            });
        }
        log.info("updateTaskInstance:调度任务状态更新成功. key:{}, status:{}", taskInstances.stream().map(ScheduleTaskInstance::getUniqInstanceKey).collect(Collectors.toList()),
                taskInstances.stream().map(m -> Pair.of(m.getUniqInstanceKey(), String.format("%s->%s", originStatus.get(m.getUniqInstanceKey()), m.getStatus()))).collect(Collectors.toList()));
    }


    @NotNull
    private List<ScheduleTaskInstance> buildScheduleTaskInstance(List<com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ScheduleTaskInstance> scheduleTaskInstancesInDbs) {
        // 查询对应的taskBinding、service、task、resourcePool
        List<TaskBindingConfig> taskBindingConfigs = taskBindingRepository.queryValidBindingsByIds(scheduleTaskInstancesInDbs.stream()
                .map(com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ScheduleTaskInstance::getTaskBindingId).distinct().collect(Collectors.toList()));
        List<ScheduleServiceConfig> scheduleServiceConfigs = scheduleServiceRepository.queryValidScheduleServiceByIds(taskBindingConfigs.stream().map(TaskBindingConfig::getScheduleServiceId).distinct().collect(Collectors.toList()));
        List<ScheduleTaskConfig> scheduleTaskConfigs = scheduleTaskRepository.queryValidScheduleTasksByIds(taskBindingConfigs.stream().map(TaskBindingConfig::getScheduleTaskId).distinct().collect(Collectors.toList()));
        List<ResourcePoolConfig> resourcePoolConfigs = resourcePoolRepository.queryValidResourcePoolByIds(taskBindingConfigs.stream().flatMap(f -> JSON.parseArray(f.getResourcePoolIds()).toJavaList(Long.class).stream()).distinct().collect(Collectors.toList()));
        Map<Long, TaskBindingConfig> taskBindingConfigMap = taskBindingConfigs.stream().collect(Collectors.toMap(TaskBindingConfig::getId, Function.identity()));
        Map<Long, ScheduleServiceConfig> scheduleServiceConfigMap = scheduleServiceConfigs.stream().collect(Collectors.toMap(ScheduleServiceConfig::getId, Function.identity()));
        Map<Long, ScheduleTaskConfig> scheduleTaskConfigMap = scheduleTaskConfigs.stream().collect(Collectors.toMap(ScheduleTaskConfig::getId, Function.identity()));
        Map<Long, ResourcePoolConfig> resourcePoolConfigMap = resourcePoolConfigs.stream().collect(Collectors.toMap(ResourcePoolConfig::getId, Function.identity()));

        List<ScheduleTaskInstance> taskInstances = new ArrayList<>();
        for (com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ScheduleTaskInstance scheduleTaskInstancesInDb : scheduleTaskInstancesInDbs) {
            TaskBindingConfig taskBindingConfig = taskBindingConfigMap.get(scheduleTaskInstancesInDb.getTaskBindingId());
            try {
                if (Objects.isNull(taskBindingConfig)) {
                    log.warn("buildScheduleTaskInstance: taskBindingConfig is null:{}", scheduleTaskInstancesInDb.getTaskBindingId());
                    RaptorTrack.Sys_GpuSchedule_FailEvent.report(String.format("TaskBindingIsNull:%s", scheduleTaskInstancesInDb.getTaskBindingId()));
                    continue;
                }
                ScheduleServiceConfig scheduleServiceConfig = scheduleServiceConfigMap.get(taskBindingConfig.getScheduleServiceId());
                ScheduleTaskConfig scheduleTaskConfig = scheduleTaskConfigMap.get(taskBindingConfig.getScheduleTaskId());
                List<ResourcePoolConfig> resourcePoolConfigsOfOneBinding = JSON.parseArray(taskBindingConfig.getResourcePoolIds()).toJavaList(Long.class).stream()
                        .map(resourcePoolConfigMap::get).peek(Objects::requireNonNull)
                        .collect(Collectors.toList());
                // 构建taskBinding
                ScheduleService scheduleService = ScheduleService.from(scheduleServiceConfig);
                ScheduleTask scheduleTask = ScheduleTask.from(scheduleTaskConfig);
                List<ResourcePool> resourcePools = resourcePoolConfigsOfOneBinding.stream()
                        .filter(ResourcePool::isValidConfig)
                        .map(ResourcePool::from).collect(Collectors.toList());
                if (resourcePools.get(0) != null) {
                    scheduleService.setResourceType(resourcePools.get(0).getResourceType());
                    scheduleService.setQueue(resourcePools.get(0).getQueue());
                }
                TaskBinding taskBinding = TaskBinding.from(taskBindingConfig, scheduleTask, scheduleService, resourcePools);
                ScheduleTaskInstance scheduleTaskInstance = ScheduleTaskInstance.from(scheduleTaskInstancesInDb, taskBinding);
                taskInstances.add(scheduleTaskInstance);
            } catch (Exception e) {
                log.error("ScheduleTaskInstance error: ScheduleTaskInstance:{}, ex:", JSON.toJSONString(scheduleTaskInstancesInDb), e);
                RaptorTrack.Sys_GpuSchedule_FailEvent.report(String.format("GenTaskError:Key:%s,Binding:%s", scheduleTaskInstancesInDb.getInstanceKey(), taskBindingConfig.getId()));
            }
        }
        return taskInstances;
    }
}
