package com.sankuai.algoplatform.matchops.domain.ability.gpuschedule.rule;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.algoplatform.matchops.domain.ability.gpuschedule.ScheduleTask;
import com.sankuai.algoplatform.matchops.domain.model.ScheduleTaskAction;
import com.sankuai.algoplatform.matchops.domain.model.ScheduleTaskCheckResult;
import com.sankuai.algoplatform.matchops.domain.model.TimedScheduleRuleConfig;
import com.sankuai.algoplatform.matchops.infrastructure.config.LionConfig;
import com.sankuai.algoplatform.matchops.infrastructure.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class TimedScheduleRuleExecutor extends ScheduleRuleExecutor {
    private TimedScheduleRuleConfig config;

    public TimedScheduleRuleExecutor(ScheduleTask scheduleTask, String initJsonConfig) {
        super(scheduleTask);
        this.config = JSON.parseObject(initJsonConfig, TimedScheduleRuleConfig.class);
    }

    @Override
    public List<ScheduleTaskCheckResult> generateCheckResult(Map<String, Object> params) {
        if (params.containsKey("beforeTime") && params.containsKey("afterTime")) {
            // 校验beforeTime和afterTime，不能为空，beforeTime要小于等于afterTime
            Date beforeTime = (Date) params.get("beforeTime");
            Date afterTime = (Date) params.get("afterTime");
            if (beforeTime == null || afterTime == null) {
                throw new IllegalArgumentException("beforeTime and afterTime must not be null");
            }
            if (beforeTime.after(afterTime)) {
                throw new IllegalArgumentException("beforeTime must be before afterTime");
            }
            return generateCheckResultByRange(beforeTime, afterTime);
        } else {
            ScheduleTaskAction action = generateCurrentAction(new Date());
            if (action != null) {
                return Lists.newArrayList(new ScheduleTaskCheckResult(this.scheduleTask, action));
            }
            return Lists.newArrayList(ScheduleTaskCheckResult.noAction(this.scheduleTask));
        }
    }

    private ScheduleTaskAction generateCurrentAction(Date currentTime) {
        Date currentTimeToMinute = DateUtils.truncate(currentTime, Calendar.MINUTE);

        String scheduleDateTime = config.getScheduleDateTime();
        if (TimedScheduleRuleConfig.TimedScheduleType.DAILY.equals(config.getScheduleType())) {
            scheduleDateTime = DateUtil.toDateString(currentTime) + " " + scheduleDateTime;
        }
        Date date = DateUtils.truncate(DateUtil.parseDate(scheduleDateTime), Calendar.MINUTE);
        // date 和 currentTimeToMinute在正负10分钟内
        int minuteDiff = LionConfig.SCHEDULE_TIMED_SCHEDULE_TIMEWINDOW * 60 * 1000;
        if (Math.abs(date.getTime() - currentTimeToMinute.getTime()) < minuteDiff) {
            return new ScheduleTaskAction(this.scheduleTask.getActionType(), this.scheduleTask.getQuota(), DateUtil.toDateTimeString(date));
        }
        return null;
    }

    private List<ScheduleTaskCheckResult> generateCheckResultByRange(Date beforeTime, Date afterTime) {
        List<ScheduleTaskCheckResult> result = Lists.newArrayList();
        String scheduleDateTimeStr = config.getScheduleDateTime();
        if (TimedScheduleRuleConfig.TimedScheduleType.ONCE.equals(config.getScheduleType()) &&
                (beforeTime.before(DateUtil.parseDate(scheduleDateTimeStr))
                        && afterTime.after(DateUtil.parseDate(scheduleDateTimeStr))
                        || DateUtil.parseDate(scheduleDateTimeStr).equals(beforeTime)
                        || DateUtil.parseDate(scheduleDateTimeStr).equals(afterTime))) {
            // scheduleDateTime在beforeTime和afterTime范围内
            result.add(new ScheduleTaskCheckResult(this.scheduleTask, new ScheduleTaskAction(this.scheduleTask.getActionType(), this.scheduleTask.getQuota(), scheduleDateTimeStr)));
        } else if (TimedScheduleRuleConfig.TimedScheduleType.DAILY.equals(config.getScheduleType())) {
            // 在beforeTime和afterTime时间范围内，scheduleDateTimeStr为时分秒，生成范围内每一天的date
            List<String> dates = new ArrayList<>(DateUtil.getDateRangeString(beforeTime, afterTime));
            result = dates.stream()
                    .map(d -> {
                        String datetime = d + " " + scheduleDateTimeStr;
                        return DateUtil.parseDate(datetime);
                    })
                    .filter(d -> d.after(beforeTime) && d.before(afterTime) || d.equals(beforeTime) || d.equals(afterTime))
                    .map(f -> new ScheduleTaskCheckResult(this.scheduleTask,
                            new ScheduleTaskAction(this.scheduleTask.getActionType(), this.scheduleTask.getQuota(), DateUtil.toDateTimeString(f))))
                    .collect(Collectors.toList());
        }
        return result;
    }
}
