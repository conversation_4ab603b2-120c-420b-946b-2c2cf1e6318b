package com.sankuai.algoplatform.matchops.domain.ability.gpuschedule;

import com.alibaba.fastjson.JSON;
import com.sankuai.algoplatform.matchops.domain.enums.ScheduleTaskActionType;
import com.sankuai.algoplatform.matchops.domain.enums.ScheduleTaskInstanceStatus;
import com.sankuai.algoplatform.matchops.domain.model.ScheduleTaskAction;
import com.sankuai.algoplatform.matchops.infrastructure.config.LionConfig;
import com.sankuai.algoplatform.matchops.infrastructure.util.DateUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Date;

import static java.util.Objects.requireNonNull;

@Slf4j
@Data
public class ScheduleTaskInstance {
    private Long id;
    private String uniqInstanceKey; // 规则？id列表+动作+数量
    private TaskBinding taskBinding;
    private ScheduleTaskInstanceStatus status;
    private String message;
    private ScheduleTaskAction action;
    private Date addTime;
    private Date updateTime;

    public static ScheduleTaskInstance from(TaskBinding taskBinding, ScheduleTaskAction action) {
        ScheduleTaskInstance instance = new ScheduleTaskInstance();
        instance.setUniqInstanceKey(generateInstanceKey(taskBinding, action));
        instance.setTaskBinding(taskBinding);
        instance.setStatus(ScheduleTaskInstanceStatus.INITIAL);
        instance.setMessage(null);
        instance.setAction(action);
        return instance;
    }

    private static String generateInstanceKey(TaskBinding taskBinding, ScheduleTaskAction action) {
        return String.format("%s-%s-%s-%s",
                taskBinding.getId(), action.getActionType().getCode(), action.getQuotaNum(), action.getTriggerTimestamp() / 1000);
    }

    public static ScheduleTaskInstance from(com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ScheduleTaskInstance scheduleTaskInstance, TaskBinding taskBinding) {
        ScheduleTaskInstance instance = new ScheduleTaskInstance();
        instance.setId(scheduleTaskInstance.getId());
        instance.setUniqInstanceKey(requireNonNull(scheduleTaskInstance.getInstanceKey()));
        instance.setTaskBinding(requireNonNull(taskBinding));
        instance.setStatus(requireNonNull(ScheduleTaskInstanceStatus.getByCode(scheduleTaskInstance.getStatus())));
        instance.setMessage(scheduleTaskInstance.getMessage());
        instance.setAction(requireNonNull(JSON.parseObject(scheduleTaskInstance.getAction(), ScheduleTaskAction.class)));
        instance.setAddTime(scheduleTaskInstance.getAddTime());
        instance.setUpdateTime(scheduleTaskInstance.getUpdateTime());
        return instance;
    }

    public void invoke() {
        log.info("invoke: key:{}, status:{}, action:{}", this.getUniqInstanceKey(), this.status, JSON.toJSONString(this.action));
        if (ScheduleTaskInstanceStatus.INITIAL.equals(this.status)) {
            if (new Date().getTime() < this.getAction().getTriggerTimestamp()) {
                log.info("invoke: key:{}, 当前时间未到触发时间", this.getUniqInstanceKey());
                setMessage(String.format("未到触发时间, 当前时间:%s, 预期时间:%s.", DateUtil.toDateTimeString(new Date()), this.getAction().getTriggerDatetime()));
                return;
            }

            ScheduleService service = this.taskBinding.getScheduleService();
            Pair<Boolean, String> pair;
            if (ScheduleTaskActionType.ADD.equals(this.action.getActionType())) {
                pair = service.safetyAddInstance((this.action.getQuotaNum()));
            } else if (ScheduleTaskActionType.DELETE.equals(this.action.getActionType())) {
                pair = service.safetyDeleteInstance(this.action.getQuotaNum());
            } else {
                throw new UnsupportedOperationException("不支持的调度动作");
            }
            this.status = pair.getLeft() ? ScheduleTaskInstanceStatus.SUBMITTED : ScheduleTaskInstanceStatus.FAILED;
            this.message = pair.getRight();
        }
    }

    public void updateStatus() {
        log.info("updateStatus: id:{}, status:{}", this.id, this.status);
        if (ScheduleTaskInstanceStatus.SUBMITTED.equals(this.status)) {
            ScheduleService service = this.taskBinding.getScheduleService();
            Pair<Boolean, String> allNodeRunning = service.queryAllNodeRunning();
            if (allNodeRunning.getLeft()) {
                this.status = ScheduleTaskInstanceStatus.SUCCESS;
                this.message = allNodeRunning.getRight();
            } else {
                this.message = allNodeRunning.getRight();
                // 判断updateTime与addTime相差20分钟，如果有则置为失败
                if (DateUtils.addMinutes(this.addTime, LionConfig.SCHEDULE_STARTING_TIMEOUT / 60)
                        .before(this.updateTime)) {
                    this.status = ScheduleTaskInstanceStatus.FAILED;
                    this.message = String.format("调度服务启动超时: 任务启动时间:%s, 节点状态:%s", DateUtil.toDateTimeString(this.addTime), allNodeRunning.getRight());
                }
            }
            log.info("updateStatus: now: id:{}, status:{}, message:{}", this.id, this.status, this.message);
        }
    }

}
