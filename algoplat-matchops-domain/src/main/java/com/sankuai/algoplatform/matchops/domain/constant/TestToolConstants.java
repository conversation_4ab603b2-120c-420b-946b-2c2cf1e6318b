package com.sankuai.algoplatform.matchops.domain.constant;

import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;

import java.util.List;

public class TestToolConstants {

    public static final String PREDICTOR_APPKEY = "com.sankuai.algoplatform.predictor";

    public static final String MATCH_STRATEGY_NAME = "matchStrategyName";

    public static final String SCENE_NAME = "sceneName";

    public static final String RESOURCE_GROUP_NAME = "resourceGroupName";

    public static final String TASK_CAL_TIME_INFO = "taskCalTimeInfo";

    public static final String PARAM = "param";

    public static final String FILE = "file";

    public static final String CLEAR_CACHE = "clearCache";

    public static final String ORIGIN_RUN_PARAM = "originRunParam";

    public static final String POST_TYPE = "postType";

    public static final String IP = "ip";

    public static final String COOKIE = "cookie";

    public static final String REQUEST_BODY = "requestBody";

    public static final String SQL_PARAM = "sqlParam";

    public static final String TIMEOUT = "timeout";

    public static final String REQ_POST_LINK = "reqPostLink";

    public static final String CD = "cd";

    public static final String VERSION = "version";

    public static final String HOUR = "hour";

    public static final String DT = "dt";

    public static final String ALGO_BIZ_CODE = "algoBizCode";

    public static final String XT_PARAM = "xtParam";

    public static final String PARSE_RESP_LINK = "parsePostLink";

    public static final String PARSE_VERSION = "parseVersion";

    public static final String PARSE_XT_PARAM = "parseXtParam";

    public static final List<String> SYSTEM_OPS_MIS_ID_LIST = Lists.newArrayList("zhouzehao02", "zhoumi10");

    public static final String MISSION_ID = "missionId";

}
