package com.sankuai.algoplatform.matchops.domain.service.testtool.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import com.dianping.rhino.cluster.common.util.StringUtil;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.sankuai.algoplatform.matchops.domain.constant.TestToolConstants;
import com.sankuai.algoplatform.matchops.domain.enums.ReqPostTypeEnum;
import com.sankuai.algoplatform.matchops.domain.enums.TestSubTaskStatusEnum;
import com.sankuai.algoplatform.matchops.domain.enums.TestTaskStatusEnum;
import com.sankuai.algoplatform.matchops.domain.enums.XtTaskStatus;
import com.sankuai.algoplatform.matchops.domain.model.testtool.DOctoResource;
import com.sankuai.algoplatform.matchops.domain.model.testtool.RunTime;
import com.sankuai.algoplatform.matchops.domain.model.testtool.XtInfo;
import com.sankuai.algoplatform.matchops.domain.service.XtService;
import com.sankuai.algoplatform.matchops.domain.service.testtool.RaptorHelper;
import com.sankuai.algoplatform.matchops.infrastructure.model.RaptorData;
import com.sankuai.algoplatform.matchops.infrastructure.model.SqlResult;
import com.sankuai.algoplatform.matchops.domain.service.QuerySqlService;
import com.sankuai.algoplatform.matchops.domain.service.testtool.ReporterService;
import com.sankuai.algoplatform.matchops.domain.service.testtool.TaskExecuteService;
import com.sankuai.algoplatform.matchops.infrastructure.config.LionConfig;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.TestSubTaskDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.TestTaskDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestSubTask;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestTask;
import com.sankuai.algoplatform.matchops.infrastructure.monitor.RaptorTrack;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.DxService;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.RaptorDataService;
import com.sankuai.algoplatform.matchops.infrastructure.util.DateUtil;
import com.sankuai.algoplatform.matchops.infrastructure.util.HttpUtil;
import com.sankuai.algoplatform.matchops.infrastructure.util.RegexUtil;
import com.sankuai.algoplatform.matchops.infrastructure.util.ThreadPoolFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;

import static com.sankuai.algoplatform.matchops.domain.enums.XtTaskStatus.TESTING;
import static com.sankuai.algoplatform.matchops.domain.enums.XtTaskStatus.WAITING_EXECUTION;


@Slf4j
@Service
public class TaskExecuteServiceImpl implements TaskExecuteService {

    @Autowired
    private TestTaskDao testTaskDao;

    @Autowired
    private TestSubTaskDao testSubTaskDao;

    @Autowired
    private QuerySqlService querySqlService;

    @Autowired
    private DxService dxService;

    @Autowired
    private ReporterService reporterService;

    @Autowired
    private RaptorDataService raptorDataService;

    @Autowired
    private XtService xtService;

    @Autowired
    private ApplicationContext applicationContext;

    private final String PARSE_RESULT = "parseResult";

    @Override
    public void runSubTask(TestSubTask subTask) {
        Map<String, String> runParam = JSONObject.parseObject(subTask.getRunParam(), new TypeReference<Map<String, String>>() {
        });
        TestSubTask dbUpdateTask = new TestSubTask();
        dbUpdateTask.setId(subTask.getId());
        ReqPostTypeEnum postType = ReqPostTypeEnum.getByCode(Integer.parseInt(runParam.get(TestToolConstants.POST_TYPE)));

        //跑数时间
        RunTime runTime = new RunTime();
        runTime.setTaskStartTime(DateUtil.toDateTimeString(new Date()));

        try {
            switch (postType) {
                case XT:
                    XtInfo xtInfo = runXTTask(runParam, TestToolConstants.REQ_POST_LINK, TestToolConstants.VERSION, TestToolConstants.XT_PARAM, subTask.getCreator());
                    dbUpdateTask.setRunTime(JSON.toJSONString(runTime));
                    dbUpdateTask.setStatus(TestSubTaskStatusEnum.TESTING.getCode());
                    dbUpdateTask.setTaskResult(JSON.toJSONString(xtInfo));
                    break;
                case HTTP:
                case FUNCTION:
                    TestSubTask tmp = TestSubTask.builder()
                            .id(subTask.getId())
                            .status(TestSubTaskStatusEnum.TESTING.getCode())
                            .runTime(JSON.toJSONString(runTime))
                            .build();
                    testSubTaskDao.update(tmp);
                    String result;
                    if (postType == ReqPostTypeEnum.HTTP) {
                        result = runHttpTask(runParam);
                    } else {
                        result = runFunction(runParam);
                    }
                    log.info("result:{}", result);
                    runTime.setTaskEndTime(DateUtil.toDateTimeString(new Date()));
                    dbUpdateTask.setRunTime(JSON.toJSONString(runTime));
                    dbUpdateTask.setTaskResult(result);
                    if (StringUtils.isNotEmpty(result)) {
                        JSONObject respJson = JSONObject.parseObject(result);
                        if (!StringUtil.equals(respJson.getString("code"), "0")) {
                            throw new RuntimeException("任务执行失败" + respJson);
                        }
                    }
                    dbUpdateTask.setStatus(TestSubTaskStatusEnum.TEST_SUCCESS.getCode());
                    break;
                default:
                    throw new IllegalArgumentException("未知的任务发起类型: " + runParam.get(TestToolConstants.POST_TYPE));
            }

            updateStatus2Success(subTask, dbUpdateTask);
            //同步接口处理完毕直接调用
            if (postType == ReqPostTypeEnum.HTTP || postType == ReqPostTypeEnum.FUNCTION) {
                subTask.setTaskResult(dbUpdateTask.getTaskResult());
                subTask.setStatus(dbUpdateTask.getStatus());
                handleTestSuccessSubTask(subTask);
                handleTaskStatusBySubTask(subTask);
            }
        } catch (Exception e) {
            log.error("任务执行失败, subTask={}", JSON.toJSONString(subTask), e);
            runTime.setTaskEndTime(DateUtil.toDateTimeString(new Date()));
            dbUpdateTask.setRunTime(JSON.toJSONString(runTime));
            dbUpdateTask.setStatus(TestSubTaskStatusEnum.TEST_FAIL.getCode());
            dbUpdateTask.setTaskResult(e.getMessage());
            testSubTaskDao.update(dbUpdateTask);
            testTaskDao.updateTestTaskStatus(subTask.getTaskId(), TestTaskStatusEnum.TEST_FAIL.getCode());

            List<String> users = getPushUsers(subTask.getCreator());
            String info = String.format("任务执行失败，原因：%s。", e.getMessage());
            dxService.sendMsg2Users(info, users);
        }
    }

    //更新任务状态为测试中
    private void updateStatus2Success(TestSubTask subTask, TestSubTask dbUpdateSubTask) {
        testSubTaskDao.update(dbUpdateSubTask);
        if (subTask.getTaskId() == TestTaskStatusEnum.TOBE_TEST.getCode()) {
            testTaskDao.updateTestTaskStatus(subTask.getTaskId(), TestTaskStatusEnum.TESTING.getCode());
        }
    }

    @Override
    public void stopSubTask(TestSubTask subTask, TestSubTaskStatusEnum stop) {
        TestSubTask dbSubTesk = new TestSubTask();
        dbSubTesk.setId(subTask.getId());
        dbSubTesk.setStatus(stop.getCode());
        dbSubTesk.setTaskResult(stop.getName());

        RunTime runTime = new RunTime();
        if (StringUtils.isNotEmpty(subTask.getRunTime())) {
            RunTime dbRunTime = JSON.parseObject(subTask.getRunTime(), RunTime.class);
            if (dbRunTime != null) {
                runTime = dbRunTime;
            }
        }
        runTime.setTaskEndTime(DateUtil.toDateTimeString(new Date()));
        dbSubTesk.setRunTime(JSON.toJSONString(runTime));
        testSubTaskDao.update(dbSubTesk);
        subTask.setStatus(stop.getCode());

        handleTaskStatusBySubTask(subTask);
    }

    @Override
    public void doneSubTask(TestSubTask subTask) {
        TestSubTask dbSubTesk = new TestSubTask();
        dbSubTesk.setId(subTask.getId());
        dbSubTesk.setStatus(TestSubTaskStatusEnum.TEST_SUCCESS.getCode());
        dbSubTesk.setTaskResult("手动置为成功");
        RunTime runTime = JSON.parseObject(subTask.getRunTime(), RunTime.class);
        runTime.setTaskEndTime(DateUtil.toDateTimeString(new Date()));
        dbSubTesk.setRunTime(JSON.toJSONString(runTime));
        testSubTaskDao.update(dbSubTesk);
        subTask.setStatus(TestSubTaskStatusEnum.TEST_SUCCESS.getCode());

        handleTaskStatusBySubTask(subTask);
    }

    @Override
    public void handleTestingSubTask(TestTask task) {
        List<TestSubTask> testSubTasks = testSubTaskDao.selectByTaskId(task.getId());
        for (int i = 0; i < testSubTasks.size(); i++) {
            TestSubTask subTask = testSubTasks.get(i);

            if (TestSubTaskStatusEnum.getByCode(subTask.getStatus()) == TestSubTaskStatusEnum.TESTING) {
                calTaskDone(subTask);
                if (subTask.getStatus() == TestSubTaskStatusEnum.TEST_SUCCESS.getCode()) {
                    handleTestSuccessSubTask(subTask);
                }
            }
            //如果当前任务为最后一个任务，则更新父任务状态
            if (i == testSubTasks.size() - 1) {
                handleTaskStatusBySubTask(subTask);
            }
        }
    }

    @Override
    public void checkTestingTaskException(TestTask task) {
        TestSubTask testSubTask = testSubTaskDao.selectOneByTaskIdAndStatus(task.getId(), TestSubTaskStatusEnum.TESTING.getCode());
        if (testSubTask == null) {
            log.info("当前任务无测试中任务");
            return;
        }
        String octoResources = testSubTask.getOctoResources();
        if (StringUtils.isEmpty(octoResources)) {
            return;
        }
        List<DOctoResource> resources = JSONObject.parseObject(octoResources, new TypeReference<List<DOctoResource>>() {
        });
        for (DOctoResource resource : resources) {
            String set = resource.getSet();
            String appkey = resource.getAppkey();
            if (StringUtils.isEmpty(set) || StringUtils.equals(set, "default")) {
                set = "default_cell";
            }
            Map<String, String> paramMap = ImmutableMap.of("group", set, "ip", set);
            Date date = new Date();
            RaptorData.Problem.Root root = raptorDataService.queryProblemHourly(RaptorHelper.getHostEnv(), appkey, date, paramMap);

            Map<String, Long> error = RaptorHelper.extraError(root);
            String link = String.format("https://raptor-st.mws.sankuai.com/application/problem?reportType=hour&date=%s&startDate=%s&endDate=%s&ip=%s&group=%s&domain=%s",
                    DateUtil.toDateTimeString_yyyyMMddHH(date), DateUtil.toDateTimeShortString(date),
                    DateUtil.toDateTimeString_yyyyMMddHH(date) + "5900", set, set, appkey);
            for (Map.Entry<String, Long> singleError : error.entrySet()) {
                Long errorCnt = singleError.getValue();
                if (errorCnt >= LionConfig.CHECK_EXCEPTION_NUM_THRESHOLD) {
                    dxService.sendMsg2Users("你的任务(subTaskId=" + testSubTask.getId() + ")跑数过程中异常信息过多，[点击查看|" + link + "]", getPushUsers(task.getOwner()));
                    return;
                }
            }
        }

    }

    @Override
    public void handleTaskStatusBySubTask(TestSubTask testSubTask) {
        Long taskId = testSubTask.getTaskId();
        List<Integer> status = ImmutableList.of(TestSubTaskStatusEnum.INIT.getCode(), TestSubTaskStatusEnum.TESTING.getCode());
        List<TestSubTask> testSubTasks = testSubTaskDao.selectByTaskIdAndStatus(taskId, status);
        //如果当前没有测试中/待测试的任务，则更新父任务状态
        if (testSubTasks.isEmpty()) {
            if (TestSubTaskStatusEnum.getByCode(testSubTask.getStatus()) == TestSubTaskStatusEnum.TEST_SUCCESS) {
                testTaskDao.updateTestTaskStatus(testSubTask.getTaskId(), TestTaskStatusEnum.TEST_SUCCESS.getCode());
            }
            if (ImmutableList.of(TestSubTaskStatusEnum.TEST_FAIL, TestSubTaskStatusEnum.STOP_MANUAL, TestSubTaskStatusEnum.STOP_SYSTEM).contains(TestSubTaskStatusEnum.getByCode(testSubTask.getStatus()))) {
                testTaskDao.updateTestTaskStatus(testSubTask.getTaskId(), TestTaskStatusEnum.TEST_FAIL.getCode());
            }
        }

    }

    /**
     * 计算执行中的任务是否执行完毕
     */
    private void calTaskDone(TestSubTask subTask) {
        String runParam = subTask.getRunParam();
        Map<String, String> paramMap = JSONObject.parseObject(runParam, new TypeReference<Map<String, String>>() {
        });
        ReqPostTypeEnum reqPostType = ReqPostTypeEnum.getByCode(Integer.parseInt(paramMap.get(TestToolConstants.POST_TYPE)));
        if (reqPostType != ReqPostTypeEnum.XT) {
            return;
        }

        String extraInfo = subTask.getExtraInfo();
        JSONObject taskCalTimeInfo = JSONObject.parseObject(JSONObject.parseObject(extraInfo).getString("taskCalTimeInfo"));
        if (Objects.isNull(taskCalTimeInfo)) {
            return;
        }
        String reqSql = taskCalTimeInfo.getString("reqTotalCalSql");
        String resSql = taskCalTimeInfo.getString("resTotalCalSql");
        ThreadPoolExecutor taskRunThreadPool = ThreadPoolFactory.getTaskRunThreadPool();

        Future<SqlResult> reqFuture = taskRunThreadPool.submit(() -> querySqlService.executeSql(reqSql));
        Future<SqlResult> resFuture = taskRunThreadPool.submit(() -> querySqlService.executeSql(resSql));
        try {
            SqlResult reqResult = reqFuture.get(5, TimeUnit.MINUTES);
            SqlResult resResult = resFuture.get(5, TimeUnit.MINUTES);
            long reqTotal = 0L, resTotal = 0L;
            if (reqResult.getCode() == 0) {
                reqTotal = Long.parseLong((String) reqResult.getData().get(0).get(0));
            }
            if (resResult.getCode() == 0) {
                resTotal = Long.parseLong((String) resResult.getData().get(0).get(0));
            }
            log.info("reqSql={}, resSql={}, reqTotal={}，resTotal={}", reqSql, resSql, reqTotal, resTotal);
            if (reqTotal > 0 && resTotal >= reqTotal * LionConfig.CHECKDONE_THRESHOLD) {
                if (ifTaskRunning(subTask)) {
                    log.info("解析任务还在执行中，subTestTaskId={}", subTask.getId());
                    return;
                }
                //执行XT
                log.info("开始执行解析任务，subTestTaskId={}", subTask.getId());
                XtInfo xtInfo = runXTTask(paramMap, TestToolConstants.PARSE_RESP_LINK, TestToolConstants.PARSE_VERSION, TestToolConstants.PARSE_XT_PARAM, subTask.getCreator());
                updateParseResult(subTask, xtInfo);
                //查询XT状态
                XtTaskStatus xtTaskStatus = queryTask(xtInfo, subTask.getCreator());
                if (xtTaskStatus.getCode().equals(XtTaskStatus.COMPLETED.getCode())) {
                    updateStatus2Success(subTask, taskCalTimeInfo);
                }
            }
        } catch (Exception e) {
            log.error("TaskExecuteServiceImpl.calTaskDone error", e);
            if (e.getMessage().contains("权限")) {
                List<String> applyUrls = RegexUtil.extractMatches(e.getMessage(), "\"http://[^\\\\s]+\";");
                if (CollectionUtils.isNotEmpty(applyUrls)) {
                    String pushInfo = String.format("hive查询无权限，[点击申请权限|%s]：", applyUrls.get(0));
                    dxService.sendMsg2Users(pushInfo, getPushUsers(subTask.getCreator()));
                } else {
                    dxService.sendMsg2Users("任务状态统计失败，原因：" + e.getMessage(), getPushUsers(subTask.getCreator()));
                }
            }
            RaptorTrack.Sys_UnexpectedVisitNum.report("TestingTaskHandleError#" + subTask.getId());
        }
    }

    //判断是有解析任务正在执行中
    private boolean ifTaskRunning(TestSubTask subTask) throws Exception {
        String taskResult = subTask.getTaskResult();
        JSONObject jsonObject = JSON.parseObject(taskResult);
        if (jsonObject != null && jsonObject.get(PARSE_RESULT) != null) {
            XtInfo xtInfo = JSONObject.toJavaObject(jsonObject.getJSONObject(PARSE_RESULT), XtInfo.class);
            XtTaskStatus xtTaskStatus = queryTask(xtInfo, subTask.getCreator());
            if (xtTaskStatus == WAITING_EXECUTION || xtTaskStatus == TESTING) {
                return true;
            }
        }
        return false;
    }

    private void handleTestSuccessSubTask(TestSubTask subTask) {
        if (subTask.getStatus() != TestSubTaskStatusEnum.TEST_SUCCESS.getCode()) {
            RaptorTrack.Sys_UnexpectedVisitNum.report("StatusError");
            return;
        }

        String reporterInfo = subTask.getReporterInfo();

        String pushInfo;
        if (StringUtils.isNotEmpty(reporterInfo)) {
            pushInfo = String.format("您的子任务（subTaskId=%s）测试成功，测试报告正在生成中，请稍后在平台上查看。", subTask.getId());
            ThreadPoolFactory.getGenerateReporterThreadPool().submit(() -> {
                try {
                    generateReporter(subTask);
                } catch (Exception e) {
                    log.error("generateReporter error, subTask={}", JSON.toJSONString(subTask), e);
                    throw new RuntimeException(e);
                }
            });
        } else {
            pushInfo = String.format("您的子任务（subTaskId=%s）测试成功。", subTask.getId());
        }
        List<String> users = Lists.newArrayList(subTask.getCreator());
        dxService.sendMsg2Users(pushInfo, users);
    }


    @Override
    public void generateReporter(TestSubTask subTask) throws Exception {
        List<String> users = Lists.newArrayList(subTask.getCreator());
        JSONArray reporterTemplates = JSONObject.parseArray(subTask.getReporterInfo());
        for (Object obj : reporterTemplates) {
            JSONObject reporterTemplate = (JSONObject) obj;
            String templateAddr = reporterTemplate.getString("templateAddr");
//            String reporter = reporterTemplate.getString("reporter");

            if (StringUtils.isEmpty(templateAddr)) {
                log.info("当前任务taskId={}未配置报告模板", subTask.getId());
                continue;
            }
            String contentId = StringUtils.substringAfter(templateAddr, "collabpage/");


            Pair<Boolean, String> pair = reporterService.buildReporterByContentId(contentId, subTask);
            if (pair.getKey()) {
                String reporter = templateAddr.replace(contentId, pair.getValue());
                reporterTemplate.put("reporter", reporter);
                String info = String.format("您的子任务（subTaskId=%s）测试报告生成成功，[点击查看|%s]", subTask.getId(), reporter);
                dxService.sendMsg2Users(info, users);
            } else {
                String info = String.format("您的子任务（subTaskId=%s）测试报告生成失败，原因：%s。请处理后点击重新生成", subTask.getId(), pair.getValue());
                dxService.sendMsg2Users(info, users);
                RaptorTrack.Sys_UnexpectedVisitNum.report("GenerateReporterFail#" + templateAddr);
            }

            TestSubTask updateTestSubTask = new TestSubTask();
            updateTestSubTask.setId(subTask.getId());
            updateTestSubTask.setReporterInfo(JSON.toJSONString(reporterTemplates));
            testSubTaskDao.update(updateTestSubTask);
        }
    }

    private XtInfo runXTTask(Map<String, String> runParam, String reqLinkKey, String versionKey, String xtParamKey, String misId) throws UnsupportedEncodingException {
        //https://data.sankuai.com/wanxiang2/al-catering/job/dml/1803779?version=19497422
        String xtAddress = runParam.get(reqLinkKey);
        String xtTaskId = extractTaskId(xtAddress);
        String version = runParam.get(versionKey);
        String xtParam = runParam.get(xtParamKey);

        if (StringUtils.isEmpty(xtTaskId) || StringUtils.isEmpty(version) || StringUtils.isEmpty(xtParam)) {
            throw new RuntimeException("发起XT参数错误");
        }

        XtInfo xtInfo = new XtInfo(xtTaskId, version);
        //1. 查询代码
        Map<String, Object> queryResp = xtService.queryCodeByVersion(xtTaskId, version, misId);
        String rawCode = (String) ((Map) queryResp.get("task_code_version")).get("raw_code");
        xtInfo.setRawCode(rawCode);

        //校验UDF是线上还是线下
        boolean isTestXt = validXtNotOnline(rawCode);
        if (!isTestXt) {
            String url = String.format("https://data.sankuai.com/wanxiang2/al-catering/job/dml/%s?version=%s",
                    xtTaskId, version);
            throw new RuntimeException("不允许发起线上UDF调用，请修改参数重新发起。[点击查看XT代码|%s" + url + "]");
        }

        //2. 初始化任务
        Integer onlineVersion = (Integer) ((Map) queryResp.get("task_code")).get("task_code_version_id");
        Map<String, Object> initResp = xtService.initXtTask(String.valueOf(onlineVersion), version, rawCode, xtParam, misId);
        log.info("init resp:{}", initResp);

        //3. 提交任务
        Map<String, Object> submitResp = xtService.submitXtTask(xtTaskId, version, rawCode, xtParam, misId);
        Object submitId = ((Map) submitResp.get("task_test")).get("id");
        Object execPlanId = ((Map) submitResp.get("task_test")).get("exec_plan_id");
        xtInfo.setSubmitId(String.valueOf(submitId));
        xtInfo.setExecPlanId(String.valueOf(execPlanId));

        String testAddr = "https://data.sankuai.com/wanxiang2/al-catering/job/dml/%s?version=%s&test=%s";
        testAddr = String.format(testAddr, xtTaskId, version, submitId);
        xtInfo.setTestAddr(testAddr);
        return xtInfo;
    }

    private XtTaskStatus queryTask(XtInfo xtInfo, String misId) throws Exception {
        if (StringUtils.isEmpty(xtInfo.getSubmitId()) || StringUtils.isEmpty(xtInfo.getTaskId())) {
            throw new RuntimeException("查询XT状态参数错误");
        }
        XtTaskStatus status = xtService.queryXtTaskStatus(xtInfo.getTaskId(), xtInfo.getSubmitId(), misId);
        TimeUnit.MINUTES.sleep(1);
        while (status == WAITING_EXECUTION || status == XtTaskStatus.TESTING) {
            status = xtService.queryXtTaskStatus(xtInfo.getTaskId(), xtInfo.getSubmitId(), misId);
            TimeUnit.MINUTES.sleep(1);
        }
        return status;
    }


    /**
     * 从万象URL中提取任务ID
     *
     * @param url 万象URL，如 https://data.sankuai.com/wanxiang2/al-catering/job/dml/1803779?version=19497422
     * @return 任务ID，如 1803779
     */
    private String extractTaskId(String url) {
        if (StringUtils.isEmpty(url)) {
            return null;
        }
        // 使用正则表达式提取任务ID
        String pattern = ".*/job/dml/(\\d+)\\?.*";
        java.util.regex.Pattern r = java.util.regex.Pattern.compile(pattern);
        java.util.regex.Matcher m = r.matcher(url);
        if (m.find()) {
            return m.group(1);
        }
        return null;
    }

    /**
     * 从万象URL中提取任务ID
     *
     * @param url 万象URL，如 https://data.sankuai.com/wanxiang2/al-catering/job/dml/1803779?version=19497422
     * @return 任务ID，如 1803779
     */
    private String query(String url) {
        if (StringUtils.isEmpty(url)) {
            return null;
        }
        // 使用正则表达式提取任务ID
        String pattern = ".*/job/dml/(\\d+)\\?.*";
        java.util.regex.Pattern r = java.util.regex.Pattern.compile(pattern);
        java.util.regex.Matcher m = r.matcher(url);
        if (m.find()) {
            return m.group(1);
        }
        return null;
    }

    private String runHttpTask(Map<String, String> runParam) throws Exception {
        String addr = runParam.get(TestToolConstants.REQ_POST_LINK);

        Map<String, Object> param = new HashMap<>();
        String requestBody = runParam.get(TestToolConstants.REQUEST_BODY);
        requestBody = StringUtils.replace(requestBody, "\\\"", "\"");
        if (StringUtils.isNotEmpty(requestBody)) {
            param.putAll(JSONObject.parseObject(requestBody, new TypeReference<Map<String, Object>>() {
            }));
        }

        String sqlParam = runParam.get(TestToolConstants.SQL_PARAM);
        if (StringUtils.isNotEmpty(sqlParam)) {
            //添加limit
            sqlParam = "select * from (" + sqlParam + ") limit" + LionConfig.HIVE_SQL_MAX_COUNT;
            List<List<Object>> sqlQueryParam = getSqlParam(sqlParam);
            param.put("sqlParam", sqlQueryParam);
        }

        String file = runParam.get(TestToolConstants.FILE);
        if (StringUtils.isNotEmpty(file)) {
            param.put("file", file);
        }

        int timeout = Integer.parseInt(runParam.getOrDefault(TestToolConstants.TIMEOUT, "60"));

        Map<String, String> header = new HashMap<>();
        String cookie = runParam.get(TestToolConstants.COOKIE);
        if (StringUtils.isNotEmpty(cookie)) {
            header.put(TestToolConstants.COOKIE, cookie);
        }
//        String result = HttpUtil.httpPostJson(addr, JSON.toJSONString(param), header);
        String result = HttpUtil.httpPostWithTimeout(addr, JSON.toJSONString(param), timeout, header);
        if (StringUtils.isBlank(result)) {
            result = HttpUtil.httpPostJson(addr, JSON.toJSONString(param), header);
            if (StringUtils.isBlank(result)) {
                Cat.logError(new RuntimeException("http请求返回结果为空,addr" + addr));
                throw new RuntimeException("http请求返回结果为空" + addr);
            }
        }
        return result;
    }


    private String runFunction(Map<String, String> runParam) throws Exception {
        String addr = runParam.get(TestToolConstants.REQ_POST_LINK);
        String[] split = StringUtils.split(addr, "#");

        Map<String, Object> param = new HashMap<>();
        String requestBody = runParam.get(TestToolConstants.REQUEST_BODY);
        requestBody = StringUtils.replace(requestBody, "\\\"", "\"");
        if (StringUtils.isNotEmpty(requestBody)) {
            param.putAll(JSONObject.parseObject(requestBody, new TypeReference<Map<String, Object>>() {
            }));
        }

        String file = runParam.get(TestToolConstants.FILE);
        if (StringUtils.isNotEmpty(file)) {
            param.put("file", file);
        }
        Object object = invokeMethod(split[0], split[1], param);

        return JSON.toJSONString(object);
    }

    private Object invokeMethod(String className, String method, Object param) throws Exception {
        // 获取 Bean
        Object beanInstance = applicationContext.getBean(Class.forName(className));
        // 获取 方法
        Method mthd = beanInstance.getClass().getMethod(method, String.class);
        // 调用方法
        return mthd.invoke(beanInstance, JSON.toJSONString(param));
    }

    private List<List<Object>> getSqlParam(String sqlParam) throws Exception {
        SqlResult sqlResult = querySqlService.executeSql(sqlParam);

        if (sqlResult.getCode() != 0) {
            throw new RuntimeException("SQL查询错误: " + sqlResult.getMessage());
        }

        List<List<Object>> data = sqlResult.getData();
        if (CollectionUtils.isEmpty(data)) {
            throw new RuntimeException("SQL查询结果为空");
        }

        return convertToMapList(sqlResult.getColumn(), data);
    }

    private List<List<Object>> convertToMapList(List<String> column, List<List<Object>> data) {
        List<List<Object>> result = new ArrayList<>();
        List<Object> header = new ArrayList<>(column);
        result.add(header);
        result.addAll(data);
        return result;
    }

    private List<String> getPushUsers(String user) {
        List<String> users = new ArrayList<>(LionConfig.TEST_TOOL_DEFAULT_MANAGER);
        Optional.ofNullable(user).filter(StringUtils::isNotEmpty).ifPresent(users::add);
        return users;
    }

    private void updateParseResult(TestSubTask subTask, XtInfo xtInfo) {
        JSONObject jsonObject = JSON.parseObject(subTask.getTaskResult());
        jsonObject.put(PARSE_RESULT, xtInfo);
        TestSubTask dbTestSubTask = new TestSubTask();
        dbTestSubTask.setId(subTask.getId());
        dbTestSubTask.setTaskResult(JSON.toJSONString(jsonObject));
        testSubTaskDao.update(dbTestSubTask);
    }


    private void updateStatus2Success(TestSubTask subTask, JSONObject taskCalTimeInfo) {
        RunTime runTime = JSONObject.parseObject(subTask.getRunTime(), RunTime.class);
        Date taskEndTime = new Date();
        Date taskStartTime = DateUtil.parseDate(runTime.getTaskStartTime());
        runTime.setTaskEndTime(DateUtil.toDateTimeString(taskEndTime));
        runTime.setCost(DateUtil.calSecondDiff(taskStartTime, taskEndTime));
        //计算任务具体耗时
        JSONArray items = taskCalTimeInfo.getJSONArray("taskTimeCalByReq");
        List<RunTime.ReqExecuteTime> reqExecuteTimes = new ArrayList<>();
        for (Object item : items) {
            Map<String, String> itemMap = ((JSONObject) item).toJavaObject(new TypeReference<Map<String, String>>() {
            });
            String appkey = itemMap.get("appkey");
            String set = itemMap.get("set");
            if (StringUtils.isEmpty(set) || StringUtils.equals(set, "default")) {
                set = "default_cell";
            }
            String type = itemMap.get("type");
            String name = itemMap.get("name");
            List<Pair<Date, Date>> times = DateUtil.splitTimeByHour(taskStartTime, taskEndTime);

            LocalDateTime taskStartTimeLocalTime = DateUtil.convertToLocalDateTime(taskStartTime);
            List<LocalDateTime> allTimes = new ArrayList<>();
            for (Pair<Date, Date> pair : times) {
                Date date = pair.getLeft();
                LocalDateTime head = DateUtil.convertToLocalDateTime(date);
                RaptorData.Transaction.Root root = raptorDataService.queryTransactionHourlyGraphs(RaptorHelper.getHostEnv(), appkey, date, type, set, name, null);

                List<RaptorData.Transaction.Row> rows = root.getGraphs().getHit().getRows();

                RaptorData.Transaction.Row first = null;
                RaptorData.Transaction.Row last = null;

                for (RaptorData.Transaction.Row row : rows) {
                    Double hits = row.getHits();
                    if (hits == null) {
                        continue;
                    }
                    //开始时间之前的不统计在内
                    if (head.getHour() == taskStartTimeLocalTime.getHour() && Integer.parseInt(row.getXScale()) < taskStartTimeLocalTime.getMinute()) {
                        continue;
                    }
                    if (first == null) {
                        first = row;
                    }
                    last = row;
                }
                if (Objects.nonNull(first)) {
                    String xScale = first.getXScale();
                    LocalDateTime startTime = LocalDateTime.of(head.getYear(), head.getMonth(), head.getDayOfMonth(), head.getHour(), Integer.parseInt(xScale), 0);
                    allTimes.add(startTime);
                }
                if (Objects.nonNull(last)) {
                    String xScale = last.getXScale();
                    LocalDateTime endTime = LocalDateTime.of(head.getYear(), head.getMonth(), head.getDayOfMonth(), head.getHour(), Integer.parseInt(xScale), 0);
                    allTimes.add(endTime);
                }
            }
            Optional<LocalDateTime> min = allTimes.stream().min(LocalDateTime::compareTo);
            Optional<LocalDateTime> max = allTimes.stream().max(LocalDateTime::compareTo);

            LocalDateTime earliestTime = min.get();
            LocalDateTime latestTime = max.get();

//            LocalDateTime earliestTime = DateUtil.convertToLocalDateTime(taskStartTime);
//            LocalDateTime latestTime = LocalDateTime.now();
            RunTime.ReqExecuteTime reqExecuteTime = new RunTime.ReqExecuteTime();
            reqExecuteTime.setKey(appkey + "#" + set);
            reqExecuteTime.setStartTime(DateUtil.convertLocalDateTimeToDateTimeString(earliestTime));
            reqExecuteTime.setEndTime(DateUtil.convertLocalDateTimeToDateTimeString(latestTime));
            reqExecuteTime.setCost(DateUtil.calSecondDiff(earliestTime, latestTime));//毫秒
            reqExecuteTimes.add(reqExecuteTime);
        }
        runTime.setReqTimes(reqExecuteTimes);
        TestSubTask updateSubTask = new TestSubTask();
        updateSubTask.setId(subTask.getId());
        updateSubTask.setRunTime(JSON.toJSONString(runTime));
        updateSubTask.setStatus(TestSubTaskStatusEnum.TEST_SUCCESS.getCode());
        testSubTaskDao.update(updateSubTask);

        subTask.setStatus(updateSubTask.getStatus());
    }


    private boolean validXtNotOnline(String rawCode) {
        String[] split = StringUtils.split(rawCode, "\\n");
        for (String str : split) {
            //挑选出发起UDF调用的行
            if (str.contains("DealMatchUdf") && str.contains("match_feature")) {
                //过滤掉所有-- DealMatchUdf
                if (str.contains("-- DealMatchUdf")) {
                    continue;
                }
                //剩下的如果不是DealMatchUdfTest则告警
                if (!str.contains("DealMatchUdfTest")) {
                    return false;
                }
            }
        }
        return true;
    }
}