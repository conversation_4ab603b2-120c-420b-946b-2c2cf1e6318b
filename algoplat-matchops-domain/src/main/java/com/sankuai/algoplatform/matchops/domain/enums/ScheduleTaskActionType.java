package com.sankuai.algoplatform.matchops.domain.enums;

public enum ScheduleTaskActionType {
    // 任务类型（定时、监控）
    ADD(0, "增加"),
    DELETE(1, "减少");

    private Integer code;
    private String desc;

    ScheduleTaskActionType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ScheduleTaskActionType getByCode(Integer code) {
        for (ScheduleTaskActionType type : ScheduleTaskActionType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
