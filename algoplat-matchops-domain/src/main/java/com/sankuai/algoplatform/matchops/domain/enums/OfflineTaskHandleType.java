package com.sankuai.algoplatform.matchops.domain.enums;

public enum OfflineTaskHandleType {
    FRIDAY(0, "Friday"),
    PREDICTOR(1, "Predictor"),
    SCRIPT(2, "Script"),;
    private Integer code;
    private String desc;
    OfflineTaskHandleType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    public Integer getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }
    public static OfflineTaskHandleType getByCode(Integer code) {
        for (OfflineTaskHandleType value : OfflineTaskHandleType.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
