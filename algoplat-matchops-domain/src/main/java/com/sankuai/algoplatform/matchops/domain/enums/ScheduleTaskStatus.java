package com.sankuai.algoplatform.matchops.domain.enums;

public enum ScheduleTaskStatus {
    // 状态（0：正常，-1禁用）
    ENABLED(0, "启用"),
    DISABLED(-1, "禁用");

    private int code;
    private String desc;

    ScheduleTaskStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ScheduleTaskStatus getByCode(int code) {
        for (ScheduleTaskStatus status : ScheduleTaskStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }
}
