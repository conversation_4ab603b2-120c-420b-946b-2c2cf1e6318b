package com.sankuai.algoplatform.matchops.domain.service.testtool.reporter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.algoplatform.matchops.domain.model.testtool.RunTime;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestSubTask;
import com.sankuai.algoplatform.matchops.infrastructure.monitor.RaptorTrack;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.XuechengService;
import com.sankuai.algoplatform.matchops.infrastructure.util.DateUtil;
import com.sankuai.algoplatform.matchops.infrastructure.util.JsonPathUtil;
import com.sankuai.algoplatform.matchops.infrastructure.util.ParserUtil;
import com.sankuai.ead.citadel.document.node.impl.node.Table;
import com.sankuai.ead.citadel.document.parser.DocumentParsingException;
import com.sankuai.ead.citadel.document.parser.Serializer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * 从参数中提取数据
 */
@Service
@Slf4j
public class ResultDataFetch {

    //    result{xxxxx}
    public static String query(String[] ruleText, TestSubTask testSubTask) throws DocumentParsingException {
        if (ruleText.length < 2) {
            RaptorTrack.Sys_UnexpectedVisitNum.report("ReporterTemplateConfigError");
            return "";
        }

        Map<String, String> queryMap = ParserUtil.queryStringToMap(ruleText[1]);
        String ttype = queryMap.get("ttype");


        switch (ttype) {
            case "cost":
                if (ruleText.length >= 3) {
                    return JsonPathUtil.extractString(testSubTask.getRunTime(), ruleText[2]);
                }
                RunTime runTime = JSON.parseObject(testSubTask.getRunTime(), RunTime.class);
                long cost = 0;
                String startTime = runTime.getTaskStartTime();
                String endTime = runTime.getTaskEndTime();
                if (CollectionUtils.isNotEmpty(runTime.getReqTimes())) {
                    for (RunTime.ReqExecuteTime reqExecuteTime : runTime.getReqTimes()) {
                        if (reqExecuteTime.getCost() == null) {
                            continue;
                        }
                        if (reqExecuteTime.getCost() > cost) {
                            cost = reqExecuteTime.getCost();
                            startTime = reqExecuteTime.getStartTime();
                            endTime = reqExecuteTime.getEndTime();
                        }
                    }
                } else {
                    cost = runTime.getCost();
                }
                if (cost == 0) {
                    cost = DateUtil.calSecondDiff(DateUtil.parseDate(startTime), DateUtil.parseDate(endTime));
                }
                return String.format("开始时间：%s，结束时间%s，总耗时：%d", startTime, endTime, cost);
            case "result":
            default:
                String taskResult = testSubTask.getTaskResult();
                if (StringUtils.isEmpty(taskResult)) {
                    return " ";
                }

                JSONObject resJson = JSONObject.parseObject(taskResult);
                Integer code = resJson.getInteger("code");
                if (code == 0) {
                    String data = resJson.getString("data");
                    if (ruleText.length >= 3) {
                        if (StringUtils.equals(ruleText[2], "table")) {
                            JSONObject dataJson = JSONObject.parseObject(data);
                            JSONArray column = dataJson.getJSONArray("column");
                            List<String> columnList = column.toJavaList(String.class);

                            JSONArray dd = dataJson.getJSONArray("data");
                            List<?> rawList = dd.toJavaList(List.class);
                            List<List<Object>> dataList = new ArrayList<>();
                            for (Object item : rawList) {
                                dataList.add((List<Object>) item);
                            }
                            Table table = XuechengService.buildTable(columnList, dataList);
                            return Serializer.serialize(table);
                        }
                        return JsonPathUtil.extractString(data, ruleText[2]);
                    } else {
                        return data;
                    }
                } else {
                    return taskResult;
                }
        }
    }

}
