package com.sankuai.algoplatform.matchops.domain.ability.gpuschedule;

import com.google.common.base.Preconditions;
import com.sankuai.algoplatform.matchops.domain.model.ScheduleTaskAction;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TaskBindingConfig;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;

import static java.util.Objects.requireNonNull;

@Data
public class TaskBinding {
    private Long id;
    private ScheduleService scheduleService;
    private ScheduleTask scheduleTask;
    private List<ResourcePool> resourcePool;

    public static TaskBinding from(TaskBindingConfig taskBindingConfig, ScheduleTask scheduleTask, ScheduleService scheduleService, List<ResourcePool> resourcePool) {
        TaskBinding taskBinding = new TaskBinding();
        taskBinding.id = taskBindingConfig.getId();
        taskBinding.scheduleTask = requireNonNull(scheduleTask, "任务不能为空");
        taskBinding.scheduleService = requireNonNull(scheduleService, "调度服务不能为空");
        taskBinding.resourcePool = requireNonNull(resourcePool, "资源池不能为空");
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(taskBinding.resourcePool)
                && resourcePool.stream().noneMatch(Objects::isNull), "资源池不能为空");
        Preconditions.checkState(resourcePool.size() == 1, "当前只支持绑定1个资源池");
        return taskBinding;
    }

    public ScheduleTaskInstance generateInstances(ScheduleTaskAction action) {
        Preconditions.checkArgument(action != null, "调度动作不能为空");
        return ScheduleTaskInstance.from(this, action);
    }

}
