package com.sankuai.algoplatform.matchops.domain.enums;

public enum ScheduleServiceType {
    Friday("Friday"),

    MLP("MLP");

    private String name;

    ScheduleServiceType(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static ScheduleServiceType getByName(String name) {
        for (ScheduleServiceType type : ScheduleServiceType.values()) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }
}
