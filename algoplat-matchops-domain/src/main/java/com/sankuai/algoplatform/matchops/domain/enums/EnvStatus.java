package com.sankuai.algoplatform.matchops.domain.enums;

import com.dianping.cat.util.StringUtils;

public enum EnvStatus {
    TEST(-1, "test"),

    ST(0, "staging"),

    PROD(1, "prod");

    private int code;
    private String name;

    EnvStatus(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static EnvStatus getByCode(int value) {
        for (EnvStatus status : EnvStatus.values()) {
            if (status.getCode() == value) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown value: " + value);
    }

    public static EnvStatus fromName(String name) {
        if (StringUtils.isEmpty(name) || StringUtils.equals(name, "st")) {
            return ST;
        }
        for (EnvStatus status : EnvStatus.values()) {
            if (status.getName().equals(name)) {
                return status;
            }
        }
        return TEST;
    }
}
