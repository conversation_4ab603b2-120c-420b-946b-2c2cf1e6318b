package com.sankuai.algoplatform.matchops.domain.service.testtool.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.aifree.thrift.generation.query.BatchingConfigQuery;
import com.sankuai.aifree.thrift.generation.vo.GroupVo;
import com.sankuai.aifree.thrift.generation.vo.ServingDetailDataVo;
import com.sankuai.algoplatform.matchops.domain.enums.ReourceTypeEnum;
import com.sankuai.algoplatform.matchops.domain.enums.ResourcePrepareStatusEnum;
import com.sankuai.algoplatform.matchops.domain.enums.TestTaskStatusEnum;
import com.sankuai.algoplatform.matchops.domain.model.testtool.*;
import com.sankuai.algoplatform.matchops.domain.service.testtool.EnvPrepareService;
import com.sankuai.algoplatform.matchops.infrastructure.config.LionConfig;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.ServiceElasticConfigDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.TestTaskDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.FridayModelDeployRequest;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ModelDeployRequest;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ServiceElasticConfig;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestTask;
import com.sankuai.algoplatform.matchops.infrastructure.enums.FridayInstanceStatus;
import com.sankuai.algoplatform.matchops.infrastructure.enums.OctoNodeStatus;
import com.sankuai.algoplatform.matchops.infrastructure.model.*;
import com.sankuai.algoplatform.matchops.infrastructure.monitor.RaptorTrack;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.*;
import com.sankuai.inf.octo.mns.model.HostEnv;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@Service
@Slf4j
public class EnvPrepareServiceImpl implements EnvPrepareService {

    @Autowired
    private TestTaskDao testTaskDao;

    @Autowired
    private OctoNodeService octoNodeService;

    @Autowired
    private FridayService fridayService;

    @Autowired
    private MlpTrainingService mlpTrainingService;

    @Autowired
    private ServiceElasticConfigDao serviceElasticConfigDao;

    @Autowired
    private DxService dxService;

    @Autowired
    private MLPDeployService mlpDeployService;

    @Resource
    private FridayDeployService fridayDeployService;


    final String AVATAR_URL = "https://avatar.mws.sankuai.com/#/service/detail/host?appkey=%s&env=staging";

    @Override
    public void prepareResourceGroup(EnvPrepareContext resourceContext, TestTask task) {

        ResourceDeployResp deployResp = new ResourceDeployResp();
        prepareOctoResources(deployResp, resourceContext, task);
        prepareMlpResources(deployResp, resourceContext, task);
        prepareFridayResources(deployResp, resourceContext, task);

        TestTaskStatusEnum taskStatus = TestTaskStatusEnum.getByCode(task.getStatus());

        List<String> users = Lists.newArrayList(task.getOwner());
        if (deployResp.getOctoStatus() == ResourcePrepareStatusEnum.SUCCESS
                && deployResp.getFridayStatus() == ResourcePrepareStatusEnum.SUCCESS
                && deployResp.getMlpStatus() == ResourcePrepareStatusEnum.SUCCESS
        ) {
            if (task.getStatus() == TestTaskStatusEnum.TOBE_TEST.getCode()) {
                return;
            }
            taskStatus = TestTaskStatusEnum.TOBE_TEST;
        }

        StringBuilder failInfo = new StringBuilder("您的测试任务（taskId=" + task.getId() + "）环境准备失败，原因：");
        if (deployResp.getFridayStatus() == ResourcePrepareStatusEnum.FAIL) {
            taskStatus = TestTaskStatusEnum.ENV_FAIL;
            failInfo.append(deployResp.getFridayFailReason());
        }
        if (deployResp.getMlpStatus() == ResourcePrepareStatusEnum.FAIL) {
            taskStatus = TestTaskStatusEnum.ENV_FAIL;
            failInfo.append(deployResp.getMlpFailReason());
        }
        if (deployResp.getOctoStatus() == ResourcePrepareStatusEnum.FAIL) {
            taskStatus = TestTaskStatusEnum.ENV_FAIL;
            failInfo.append(deployResp.getOctoFailReason());
        }

        TestTask dbtask = new TestTask();
        dbtask.setId(task.getId());
        dbtask.setResourcePrepareInfo(JSON.toJSONString(deployResp));
        dbtask.setStatus(taskStatus.getCode());
        testTaskDao.update(dbtask);//todo 变更日志

        if (taskStatus == TestTaskStatusEnum.TOBE_TEST) {
            String info = String.format("您的任务（taskId=%s）环境准备成功，请点击[发起跑数|%s]", task.getId(), getTaskListUrl());
            dxService.sendMsg2Users(info, users);
        }
        if (taskStatus == TestTaskStatusEnum.ENV_FAIL) {
            users.addAll(LionConfig.TEST_TOOL_DEFAULT_MANAGER);
            dxService.sendMsg2Users(failInfo.toString(), users);
        }
    }

    @Override
    public boolean queryEnvReady(EnvPrepareContext resourceGroup, String mis) {
        if (resourceGroup == null ||
                (CollectionUtils.isEmpty(resourceGroup.getOctoResources())
                        && CollectionUtils.isEmpty(resourceGroup.getMlpResources()))
                        && CollectionUtils.isEmpty(resourceGroup.getFridayResources())) {
            return true;
        }
        try {
            //查询Octo服务是否就绪
            List<DOctoResource> octoResources = resourceGroup.getOctoResources();
            if (CollectionUtils.isNotEmpty(octoResources)) {
                for (DOctoResource octoResource : octoResources) {
                    List<OctoNode> octoNodeStatus = octoNodeService.getOctoNodeStatus(octoResource.getAppkey(), octoResource.getSet(), HostEnv.STAGING, OctoNodeStatus.NORMAL);
                    if (octoNodeStatus.size() < octoResource.getInstanceNum()) {
                        log.info("当前：{}，需要：{}", octoNodeStatus.size(), octoResource.getInstanceNum());
                        return false;
                    }
                }
            }


//            查询MLP服务是否就绪
            List<DMlpResource> mlpResources = resourceGroup.getMlpResources();
            if (CollectionUtils.isNotEmpty(mlpResources)) {
                for (DMlpResource mlpResource : mlpResources) {
                    int currentInstanceNum = getMlpRunningInstanceNums(mlpResource.getAppkey(), mlpResource.getInstanceInfo().getGroupName(), mis);
                    if (currentInstanceNum < Integer.valueOf(mlpResource.getInstanceNum())) {
                        log.info("当前：{}，需要：{}", currentInstanceNum, mlpResource.getInstanceNum());
                        return false;
                    }
                }
            }


//            查询Friday服务是否就绪
            List<DFridayResource> fridayResources = resourceGroup.getFridayResources();
            if (CollectionUtils.isNotEmpty(fridayResources)) {
                for (DFridayResource fridayResource : fridayResources) {
                    int currentInstanceNum = getFridayRunningInstanceNums(fridayResource.getModelName());//todo 测试要先屏蔽
                    if (currentInstanceNum < fridayResource.getInstanceNum()) {
                        log.info("当前：{}，需要：{}", currentInstanceNum, fridayResource.getInstanceNum());
                        return false;
                    }
                }
            }
        } catch (Exception e) {
            log.error("queryEnvReady error.", e);
            return false;
        }

        return true;
    }

    private void prepareOctoResources(ResourceDeployResp deployResp, EnvPrepareContext resourceContext, TestTask task) {
        List<String> users = Lists.newArrayList(task.getOwner());
        ResourcePrepareStatusEnum octoStatus = ResourcePrepareStatusEnum.SUCCESS;
        List<DOctoResource> octoResources = resourceContext.getOctoResources();

        try {
            if (CollectionUtils.isNotEmpty(octoResources) && resourceContext.getOctoStatus() != ResourcePrepareStatusEnum.SUCCESS) {
                for (DOctoResource octoResource : octoResources) {
                    List<OctoNode> octoNodeStatus = octoNodeService.getOctoNodeStatus(octoResource.getAppkey(), octoResource.getSet(), HostEnv.STAGING, OctoNodeStatus.NORMAL);
                    if (octoNodeStatus.size() < octoResource.getInstanceNum()) {
                        octoStatus = ResourcePrepareStatusEnum.ING;
                        String url = String.format(AVATAR_URL, octoResource.getAppkey());
                        String info = String.format("服务（appkey=%s，set=%s）机器实例不足，[点击这里|%s]手动添加机器", octoResource.getAppkey(), octoResource.getSet(), url);
                        dxService.sendMsg2Users(info, users);
                    }
                }
            }
        } catch (Exception e) {
            log.error("prepareOctoResources error.", e);
            octoStatus = ResourcePrepareStatusEnum.FAIL;
            deployResp.setOctoFailReason("准备octo资源执行异常："+e.getMessage());
        }
        deployResp.setOctoStatus(octoStatus);
    }

    private void prepareFridayResources(ResourceDeployResp deployResp, EnvPrepareContext resourceContext, TestTask task) {
        List<DFridayResource> fridayResources = resourceContext.getFridayResources();
        if (CollectionUtils.isEmpty(resourceContext.getFridayResources())) {
            deployResp.setFridayStatus(ResourcePrepareStatusEnum.SUCCESS);
            return;
        }
        try {
            for (DFridayResource fridayResource : fridayResources) {
                FridayModelDeployRequest fridayModelDeployRequest = new FridayModelDeployRequest();
                fridayModelDeployRequest.setMisId(task.getOwner());
                fridayModelDeployRequest.setDescription(fridayResource.getModelName());
                fridayModelDeployRequest.setModelParam(convertToMap(fridayResource.getModelParam(), fridayResource.getBatchSize()));
                fridayModelDeployRequest.setModelDeployName(fridayResource.getModelName());
                fridayModelDeployRequest.setModelBaseName(fridayResource.getModelBaseName());
                fridayModelDeployRequest.setRequiredInstanceCount(fridayResource.getInstanceNum());
                fridayModelDeployRequest.setGpuPerInstance(Integer.valueOf(fridayResource.getGpuPerInstance()));
                fridayModelDeployRequest.setTrainingTaskId(fridayResource.getTrainingTaskId());
                fridayModelDeployRequest.setTrainMethod(fridayResource.getTrainMethod());
                if (!fridayDeployService.deployModel(fridayModelDeployRequest)) {
                    deployResp.setFridayStatus(ResourcePrepareStatusEnum.FAIL);
                    deployResp.setFridayFailReason("Friday模型部署失败，模型名称：" + fridayResource.getModelName());
                    return;
                }
            }
            deployResp.setFridayStatus(ResourcePrepareStatusEnum.SUCCESS);
        } catch (Exception e) {
            log.error("prepareFridayResources error", e);
            deployResp.setFridayStatus(ResourcePrepareStatusEnum.FAIL);
            deployResp.setFridayFailReason("准备friday资源执行异常："+e.getMessage());
        }
    }

    private Map<String, String> convertToMap(DFridayModelParam param, int batchSize) {
        if (param == null) {
            return Collections.emptyMap();
        }
        Map<String, String> map = new HashMap<>();
        String maxNewTokens = param.getMaxNewTokens();
        String temperature = param.getTemperature();
        String topP = param.getTopP();
        String topK = param.getTopK();
        String promptPrefix = param.getPromptPrefix();
        String promptSuffix = param.getPromptSuffix();
        String maxSeqLength = param.getMaxSeqLength();
        map.put("top_p", topP);
        map.put("top_k", topK);
        map.put("batch_size", String.valueOf(batchSize));
        map.put("max_new_tokens", maxNewTokens);
        map.put("prompt_prefix", promptPrefix);
        map.put("prompt_suffix", promptSuffix);
        map.put("temperature", temperature);
        map.put("max_seq_length", maxSeqLength);
        map.put("dtype", "");
        map.put("do_sample", "True");
        return map;

    }

//    private void prepareFridayResources(ResourceDeployResp deployResp, EnvPrepareContext resourceContext, TestTask task) {
//
//        List<DFridayResource> fridayResources = resourceContext.getFridayResources();
//        if (CollectionUtils.isEmpty(resourceContext.getFridayResources())) {
//            deployResp.setFridayStatus(ResourcePrepareStatusEnum.SUCCESS);
//            return;
//        }
//
//        ResourcePrepareStatusEnum fridayStatus = ResourcePrepareStatusEnum.SUCCESS;
//        for (DFridayResource fridayResource : fridayResources) {
//
//            if (resourceContext.getFridayStatus() == ResourcePrepareStatusEnum.SUCCESS) {
//                continue;
//            }
//
//            int needInstanceNum = fridayResource.getInstanceNum();
//            int currentInstanceNum = getFridayRunningInstanceNums(fridayResource.getModelName());
//
//            if (needInstanceNum <= currentInstanceNum) {
//                continue;
//            }
//
//            if (resourceContext.getFridayStatus() == ResourcePrepareStatusEnum.ING) {
//                //如果状态为ING，说明还在部署中，直接返回
//                deployResp.setFridayStatus(ResourcePrepareStatusEnum.ING);
//                return;
//            }
//
//            int diff = needInstanceNum - currentInstanceNum;
//
//            //需要扩缩容，如果扩容中报错，更改状态为准备失败
//            Pair<Boolean, String> res = fridayService.addInstance(fridayResource.getModelName(), diff);
//            if (res.getLeft()) {
//                continue;
//            }
//
//            if (!StringUtils.contains(res.getValue(), "资源不足")) {
//                String info = "扩容实例失败。" + res.getRight();
//                deployResp.setFridayStatus(ResourcePrepareStatusEnum.FAIL);
//                deployResp.setFridayFailReason(info);
//                return;
//            }
//
//            //资源不够，按照规则缩容
//            ServiceElasticConfig config = getFridayElasticConfig(fridayResource, task.getBizLineId());
//            if (config == null) {
//                String info = String.format("%s可用实例数据不足，需要实例数：%s，当前已有实例数：%s，无可借用服务配置。", fridayResource.getModelName(), needInstanceNum, currentInstanceNum);
//                deployResp.setFridayStatus(ResourcePrepareStatusEnum.FAIL);
//                deployResp.setFridayFailReason(info);
//                return;
//            }
//
//            List<ElasticRule> fridayElasticRules = JSONObject.parseObject(config.getElasticRule(), new TypeReference<List<ElasticRule>>() {
//            });
//            if (CollectionUtils.isEmpty(fridayElasticRules)) {
//                RaptorTrack.Sys_UnexpectedVisitNum.report("FridayElasticFail" + config.getName());
//                deployResp.setFridayStatus(ResourcePrepareStatusEnum.FAIL);
//                deployResp.setFridayFailReason(config.getName() + "弹性规则配置为空");
//                return;
//            }
//
//            List<Pair<ElasticRule, Integer>> canDelInstanceList = new ArrayList<>();
//            List<Pair<ElasticRule, Integer>> finalDelInstanceList = new ArrayList<>();
//            for (ElasticRule fridayElasticRule : fridayElasticRules) {
//                int onlineRunningInstanceNum = getFridayRunningInstanceNums(fridayElasticRule.getServiceName());
//                int canDelInstanceNum = onlineRunningInstanceNum - fridayElasticRule.getMinRetainInstanceNum();
//                if (canDelInstanceNum > 0) {
//                    canDelInstanceList.add(Pair.of(fridayElasticRule, canDelInstanceNum));
//                }
//            }
//            int canDelInstanceNum = canDelInstanceList.stream().mapToInt(Pair::getRight).sum();
//            if (canDelInstanceNum < diff) {
//                String info = String.format("%s可用实例数据不足，需要实例数：%s，当前已有实例数：%s，可借用实例数：%s。",
//                        fridayResource.getModelName(), needInstanceNum, currentInstanceNum, canDelInstanceNum);
//                deployResp.setFridayStatus(ResourcePrepareStatusEnum.FAIL);
//                deployResp.setFridayFailReason(info);
//                return;
//            }
//            finalDelInstanceList = distribute(canDelInstanceList, diff);
//            //todo 开始扩缩容，发MCM
//            //先缩容 再扩容
//            for (Pair<ElasticRule, Integer> pair : finalDelInstanceList) {
//                ElasticRule rule = pair.getLeft();
//                int num = pair.getRight();
//                Pair<Boolean, String> delRes = fridayService.deleteInstance(rule.getServiceName(), num);
//                if (!delRes.getKey()) {
//                    String info = String.format("服务（%s）缩容失败，原因：%s", JSON.toJSONString(pair), delRes.getRight());
//                    deployResp.setFridayStatus(ResourcePrepareStatusEnum.FAIL);
//                    deployResp.setFridayFailReason(info);
//                    return;
//                }
//            }
//            Pair<Boolean, String> addRes = fridayService.addInstance(fridayResource.getModelName(), diff);
//            if (!addRes.getLeft()) {
//                String info = String.format("服务（%s）扩容失败，原因：%s", JSON.toJSONString(fridayResource), addRes.getRight());
//                deployResp.setFridayStatus(ResourcePrepareStatusEnum.FAIL);
//                deployResp.setFridayFailReason(info);
//                return;
//            }
//            fridayStatus = ResourcePrepareStatusEnum.ING;
//        }
//        deployResp.setFridayStatus(fridayStatus);
//    }

//    private void prepareMlpResources(ResourceDeployResp deployResp, EnvPrepareContext resourceContext, TestTask task) {
//        List<DMlpResource> mlpResources = resourceContext.getMlpResources();
//
//        if (CollectionUtils.isEmpty(resourceContext.getMlpResources())) {
//            deployResp.setMlpStatus(ResourcePrepareStatusEnum.SUCCESS);
//            return;
//        }
//
//        ResourcePrepareStatusEnum mlpStatus = ResourcePrepareStatusEnum.SUCCESS;
//        for (DMlpResource mlpResource : mlpResources) {
//
//            if (resourceContext.getMlpStatus() == ResourcePrepareStatusEnum.SUCCESS) {
//                continue;
//            }
//
//            int needInstanceNum = mlpResource.getInstanceNum();
//            int currentInstanceNum = getMlpRunningInstanceNums(mlpResource.getAppkey(), mlpResource.getGroup(), task.getOwner());
//
//            if (needInstanceNum <= currentInstanceNum) {
//                continue;
//            }
//
//            if (resourceContext.getFridayStatus() == ResourcePrepareStatusEnum.ING) {
//                //如果状态为ING，说明还在部署中，直接返回
//                deployResp.setMlpStatus(ResourcePrepareStatusEnum.ING);
//                return;
//            }
//
//            int diff = needInstanceNum - currentInstanceNum;
//
//            ServiceElasticConfig elasticConfig = getMlpElasticConfig(mlpResource, task.getBizLineId());
//            if (elasticConfig == null) {
//                deployResp.setMlpStatus(ResourcePrepareStatusEnum.FAIL);
//                deployResp.setMlpFailReason("mlp资源不足，且未配置弹性规则");
//                return;
//            }
//            DeployConfig deployConfig = JSONObject.parseObject(elasticConfig.getDeployConfig(), DeployConfig.class);
//            int queueRestNum = getRemainOfQueue(deployConfig);
//
//            List<Pair<ElasticRule, Integer>> finalDelInstanceList = new ArrayList<>();
//            if (diff > queueRestNum) {
//                //剩余卡数量不够，需要从线上借用 diff - queueRestNum 张卡
//                List<ElasticRule> mlpElasticRules = JSONObject.parseObject(elasticConfig.getElasticRule(), new TypeReference<List<ElasticRule>>() {
//                });
//                List<Pair<ElasticRule, Integer>> canDelInstanceList = new ArrayList<>();
//                for (ElasticRule mlpElasticRule : mlpElasticRules) {
//                    int onlineRunningInstanceNum = getMlpRunningInstanceNums(mlpElasticRule.getAppkey(), mlpElasticRule.getGroup(), task.getOwner());
//                    int canDelInstanceNum = onlineRunningInstanceNum - mlpElasticRule.getMinRetainInstanceNum();
//                    if (canDelInstanceNum > 0) {
//                        canDelInstanceList.add(Pair.of(mlpElasticRule, canDelInstanceNum));
//                    }
//                }
//                int canDelInstanceNum = canDelInstanceList.stream().mapToInt(Pair::getRight).sum();
//                int needDelInstanceNum = diff - queueRestNum;
//                if (canDelInstanceNum < needDelInstanceNum) {
//                    String info = String.format("Mlp服务%s可用实例数据不足，需要实例数：%s，当前已有实例数：%s，可借用实例数：%s。",
//                            mlpResource.getAppkey(), needInstanceNum, currentInstanceNum, canDelInstanceNum);
//                    deployResp.setMlpStatus(ResourcePrepareStatusEnum.ING);
//                    deployResp.setMlpFailReason(info);
//                    return;
//                }
//                finalDelInstanceList = distribute(canDelInstanceList, needDelInstanceNum);
//            }
//            //todo 开始扩缩容，发MCM
//            //先缩容 再扩容
//            for (Pair<ElasticRule, Integer> pair : finalDelInstanceList) {
//                ElasticRule rule = pair.getLeft();
//                int num = pair.getRight();
//                Pair<Boolean, String> delRes = mlpTrainingService.deleteInstance(rule.getAppkey(), rule.getGroup(), num, task.getOwner());
//                if (!delRes.getKey()) {
//                    String info = String.format("Mlp服务（%s）缩容失败，原因：%s", JSON.toJSONString(pair), delRes.getRight());
//                    deployResp.setMlpStatus(ResourcePrepareStatusEnum.ING);
//                    deployResp.setMlpFailReason(info);
//                    return;
//                }
//            }
//            Pair<Boolean, String> addRes = mlpTrainingService.addInstance(mlpResource.getAppkey(), mlpResource.getGroup(),
//                    mlpResource.getSet(), deployConfig.getQueue(), diff, deployConfig.getVcores(), deployConfig.getMemory(),
//                    deployConfig.getGcores(), deployConfig.getGcoresType(), task.getOwner());
//            log.info("MLP实例扩容结果：{}", JSON.toJSONString(addRes));
//            if (!addRes.getLeft()) {
//                String info = String.format("Mlp服务（%s）扩容失败，原因：%s", JSON.toJSONString(mlpResource), addRes.getRight());
//                deployResp.setMlpStatus(ResourcePrepareStatusEnum.ING);
//                deployResp.setMlpFailReason(info);
//                return;
//            }
//            mlpStatus = ResourcePrepareStatusEnum.ING;
//        }
//
//        deployResp.setMlpStatus(mlpStatus);
//    }


    private void prepareMlpResources(ResourceDeployResp deployResp, EnvPrepareContext resourceContext, TestTask task) {
        List<DMlpResource> mlpResources = resourceContext.getMlpResources();

        if (CollectionUtils.isEmpty(mlpResources)) {
            deployResp.setMlpStatus(ResourcePrepareStatusEnum.SUCCESS);
            return;
        }
        StringBuffer failReason = new StringBuffer();
        AtomicInteger count = new AtomicInteger(0);
        ResourcePrepareStatusEnum prepareStatus = ResourcePrepareStatusEnum.SUCCESS;
        try {
            for (DMlpResource mlpResource : mlpResources) {
                count.getAndIncrement();
                if (resourceContext.getMlpStatus() == ResourcePrepareStatusEnum.SUCCESS) {
                    continue;
                }

                if (resourceContext.getMlpStatus() == ResourcePrepareStatusEnum.ING) {
                    //如果状态为ING，说明还在部署中，直接返回
                    deployResp.setMlpStatus(ResourcePrepareStatusEnum.ING);
                    return;
                }
                // 执行MLP模型部署
                InstanceInfo instanceInfo = mlpResource.getInstanceInfo();
                ResourceParamConfig resourceConfig = instanceInfo.getResourceConfig();
                ModelDeployRequest  mlpDeployRequest = ModelDeployRequest.builder()
                        .appkey(mlpResource.getAppkey())
                        .group(instanceInfo.getGroupName())
                        .set(instanceInfo.getSet())
                        .batching(instanceInfo.getBatching())
                        .rpcTimeout(instanceInfo.getRpcOutTime())
                        .versionNums(Lists.newArrayList(Long.valueOf(mlpResource.getModelVersion())))
                        .batchingConfigQuery(new BatchingConfigQuery(instanceInfo.getBatchingConfig().getMaxBatchSize(), instanceInfo.getBatchingConfig().getBatchTimeoutMicros(), instanceInfo.getBatchingConfig().getBatchingExtra()))
                        .requireInstanceNum(Integer.valueOf(mlpResource.getInstanceNum()))
                        .env(instanceInfo.getEnv())
                        .optMis(task.getOwner())
                        .memory(resourceConfig.getMemory())
                        .vcores(resourceConfig.getVcores())
                        .gcores(resourceConfig.getGcores())
                        .gcoresType(resourceConfig.getGcoresType())
                        .queueName(mlpResource.getInstanceInfo().getQuqueName())
                        .wxProject(mlpResource.getWxProject())
                        .servingType(mlpResource.getModelType())
                        .serverImage(instanceInfo.getServerImage())
                        .modelName(mlpResource.getModelName())
                        .build();
                Pair<Boolean, String> pair = mlpDeployService.deployModel(mlpDeployRequest);
                if (!Boolean.TRUE.equals(pair.getLeft())) {
                    failReason.append(count.get()).append("：").append(pair.getRight()).append("\n\n");
                    prepareStatus = ResourcePrepareStatusEnum.FAIL;
                }
            }
        } catch (Exception e) {
            log.error("prepareMlpResources error", e);
            failReason.append("准备mlp资源执行异常："+e.getMessage());
            prepareStatus = ResourcePrepareStatusEnum.FAIL;
        }
        deployResp.setMlpStatus(prepareStatus);
        deployResp.setMlpFailReason(failReason.toString());
    }

    private int getFridayRunningInstanceNums(String serviceName) {
        try {
            List<FridayServiceInstance> fridayServiceInstances = fridayService.queryInstanceStatus(serviceName);
            int fridayInstanceNum = 0;
            if (CollectionUtils.isNotEmpty(fridayServiceInstances)) {
                long count = fridayServiceInstances.stream().filter(obj -> obj.getStatus() == FridayInstanceStatus.RUNNING).count();
                fridayInstanceNum = (int) count;
            }
            return fridayInstanceNum;
        } catch (Exception e) {
            log.error("Friday queryInstanceStatus error", e);
            return 0;
        }
    }

    private int getMlpRunningInstanceNums(String appKey, String group, String opUser) {
        int mlpInstanceNum = 0;
        ServingDetailDataVo detail = mlpTrainingService.getServingDetail(appKey, opUser);
        if (CollectionUtils.isNotEmpty(detail.getGroups())) {
            Optional<GroupVo> groupVoOptional = detail.getGroups().stream().filter(g -> StringUtils.equalsIgnoreCase(group, g.getName())).findFirst();
            if (groupVoOptional.isPresent()) {
                GroupVo groupVo = groupVoOptional.get();
                if (CollectionUtils.isNotEmpty(groupVo.getInstances())) {
                    mlpInstanceNum = groupVo.getInstances().size();
                }
            }
        }
        return mlpInstanceNum;
    }

    private ServiceElasticConfig getFridayElasticConfig(DFridayResource fridayResource, Long bizLineId) {
        return serviceElasticConfigDao.selectAllByBizLineIdAndResourceTypeAndName(bizLineId, ReourceTypeEnum.FRIDAY.getCode(), fridayResource.getModelName());
    }

    private ServiceElasticConfig getMlpElasticConfig(DMlpResource mlpResource, Long bizLineId) {
        String name = mlpResource.getAppkey() + "_" + mlpResource.getInstanceInfo().getGroupName() + mlpResource.getInstanceInfo().getSet();
        return serviceElasticConfigDao.selectAllByBizLineIdAndResourceTypeAndName(bizLineId, ReourceTypeEnum.MLP.getCode(), name);
    }

    private int getRemainOfQueue(DeployConfig deployConfig) {
        List<GPUQueueResourceDetail> resourceList = mlpTrainingService.getQueueResource(deployConfig.getQueue());
        int queueRestNum = 0;//队列剩余GPU数量
        if (CollectionUtils.isNotEmpty(resourceList)) {
            Optional<GPUQueueResourceDetail> que = resourceList.stream().filter(g -> deployConfig.getGcoresType() == g.getResourceType()).findFirst();
            if (que.isPresent()) {
                GPUQueueResourceDetail gpuQueueResourceDetail = que.get();
                queueRestNum = gpuQueueResourceDetail.getRemainNum();
            }
        }
        return queueRestNum;
    }

    public static List<Pair<ElasticRule, Integer>> distribute(List<Pair<ElasticRule, Integer>> list, int targetSum) {
        int currentSum = list.stream().mapToInt(Pair::getRight).sum();

        // 如果当前总和小于或等于目标总和，直接返回
        if (currentSum <= targetSum) {
            return list;
        }

        int surplus = currentSum - targetSum;

        List<Pair<ElasticRule, Integer>> result = new ArrayList<>();
        for (Pair<ElasticRule, Integer> pair : list) {
            if (surplus <= 0) {
                break;
            }

            int value = pair.getRight();
            int reduction = Math.min(value, surplus);
            result.add(Pair.of(pair.getKey(), reduction));
            surplus -= reduction;
        }
        return result;
    }

    private String getTaskListUrl() {
        String domain = MdpContextUtils.isOfflineEnv() ? "bml.nibdata.test.sankuai.com" : "bml.sankuai.com";
        return "https://" + domain + "/match_system/testing_tools/test_tasks";
    }
}