package com.sankuai.algoplatform.matchops.domain.model;

import com.sankuai.algoplatform.matchops.domain.ability.gpuschedule.ScheduleTask;
import lombok.Data;

import static java.util.Objects.requireNonNull;

@Data
public class ScheduleTaskCheckResult {
    private ScheduleTask scheduleTask;
    private ScheduleTaskAction action;

    public ScheduleTaskCheckResult(ScheduleTask scheduleTask, ScheduleTaskAction action) {
        this.scheduleTask = requireNonNull(scheduleTask);
        this.action = action;
    }

    public static ScheduleTaskCheckResult noAction(ScheduleTask scheduleTask) {
        return new ScheduleTaskCheckResult(scheduleTask, null);
    }

    public boolean isNeedExecute() {
        return this.action != null;
    }

}
