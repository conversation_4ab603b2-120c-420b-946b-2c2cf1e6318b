package com.sankuai.algoplatform.matchops.domain.service.testtool;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.algoplatform.matchops.domain.constant.TestToolConstants;
import com.sankuai.algoplatform.matchops.domain.enums.EnvStatus;
import com.sankuai.algoplatform.matchops.domain.model.testtool.PredictorCache1Config;
import com.sankuai.algoplatform.matchops.domain.model.testtool.PredictorCache234Config;
import com.sankuai.algoplatform.matchops.infrastructure.model.LionConfigReq;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.LionConfigService;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Data
public class LionHelper {

    public static final String PREDICT_CACHE_SWITCH = "predict_cache_switch";

    public static final String PREDICT_CACHE_CONFIG = "predict_cache_config";

    public static final String PREDICT_MODEL_CACHE_CONFIG = "predict_model_cache_config";

    public static String getPredictorCache1Switch(String env) {
        LionConfigReq req = new LionConfigReq();
        req.setAppkey(TestToolConstants.PREDICTOR_APPKEY);
        req.setEnv(env);
        req.setKey(PREDICT_CACHE_SWITCH);
        return LionConfigService.queryLionConfig(req);
    }

    public static void setPredictorCache1Switch(String env, boolean value) {
        LionConfigReq req = new LionConfigReq();
        req.setAppkey(TestToolConstants.PREDICTOR_APPKEY);
        req.setEnv(env);
        req.setKey(PREDICT_CACHE_SWITCH);
        req.setValue(Boolean.toString(value));
        LionConfigService.setLionConfig(req);
    }

    public static List<PredictorCache1Config> getPredictorCache1Config(String env) {
        LionConfigReq req = new LionConfigReq();
        req.setAppkey(TestToolConstants.PREDICTOR_APPKEY);
        req.setEnv(env);
        req.setKey(PREDICT_CACHE_CONFIG);
        String value = LionConfigService.queryLionConfig(req);
        return JSONObject.parseObject(value, new TypeReference<List<PredictorCache1Config>>() {
        });
    }

    public static void setPredictorCache1Config(String env, String value) {
        LionConfigReq req = new LionConfigReq();
        req.setAppkey(TestToolConstants.PREDICTOR_APPKEY);
        req.setEnv(env);
        req.setKey(PREDICT_CACHE_CONFIG);
        req.setValue(value);
        LionConfigService.setLionConfig(req);
    }

    public static PredictorCache234Config getPredictorCache234Config(String env, String set) {
        LionConfigReq req = new LionConfigReq();
        req.setAppkey(TestToolConstants.PREDICTOR_APPKEY);
        req.setEnv(env);
        req.setKey(PREDICT_MODEL_CACHE_CONFIG);
        if (!StringUtils.equals(set, "default") && !StringUtils.isNotEmpty(set)) {
            req.setSet(set);
        }
        String value = LionConfigService.queryLionConfig(req);
        return JSON.parseObject(value, PredictorCache234Config.class);
    }

    public static void setPredictorCache234Config(String env, String set, String value) {
        LionConfigReq req = new LionConfigReq();
        req.setAppkey(TestToolConstants.PREDICTOR_APPKEY);
        req.setEnv(env);
        req.setValue(value);
        req.setKey(PREDICT_MODEL_CACHE_CONFIG);
        if (!StringUtils.equals(set, "default") && !StringUtils.equals(set, "default_cell") && !StringUtils.isNotEmpty(set)) {
            req.setSet(set);
        }
        LionConfigService.setLionConfig(req);
    }

    public static PredictorCache1Config buildBizCodeCache1Config(String bizCode) {
        PredictorCache1Config config = new PredictorCache1Config();
        config.setBizCode(bizCode);
        config.setVersion("1");
        config.setExpireMinutes(2880);
        return config;
    }

    public static EnvStatus getLionConfigEnv() {
        return MdpContextUtils.isOfflineEnv() ? EnvStatus.TEST : EnvStatus.ST;
    }

    public static String getLionConfigEnvName() {
        return getLionConfigEnv().getName();
    }

}
