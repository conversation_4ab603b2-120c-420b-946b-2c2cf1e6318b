package com.sankuai.algoplatform.matchops.domain.service.testtool.reporter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import com.sankuai.algoplatform.matchops.domain.model.testtool.DFridayResource;
import com.sankuai.algoplatform.matchops.domain.model.testtool.DMlpResource;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestSubTask;
import com.sankuai.algoplatform.matchops.infrastructure.monitor.RaptorTrack;
import com.sankuai.algoplatform.matchops.infrastructure.util.ParserUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 打点数据查询类
 */
@Service
public class ResourceDataFetch {

    public static String query(String[] ruleText, TestSubTask testSubTask) {
        if (ruleText.length < 2) {
            RaptorTrack.Sys_UnexpectedVisitNum.report("ReporterTemplateConfigError");
            return "";
        }

        String filterParam = ruleText[1];
        Map<String, String> queryMap = ParserUtil.queryStringToMap(filterParam);

        String type = queryMap.get("ttype");

        String resourceJson = "";
        switch (type) {
            case "octo":
                resourceJson = testSubTask.getOctoResources();
                break;
            case "mlp":
                resourceJson = testSubTask.getMlpModelResources();
                break;
            case "friday":
                resourceJson = testSubTask.getFridayModelResources();
                break;
            default:
                RaptorTrack.Sys_UnexpectedVisitNum.report("ReporterTemplateConfigError");
                return "";
        }

        if (StringUtils.isEmpty(resourceJson)) {
            //todo 打点
            return "";
        }

        JSONArray arr = JSON.parseArray(resourceJson);
        queryMap.remove("ttype");
        return arr.stream()
                .map(object -> (JSONObject) object)
                .filter(obj -> queryMap.entrySet().stream()
                        .allMatch(entry -> StringUtils.equals(obj.getString(entry.getKey()), entry.getValue())))
                .findFirst()
                .map(obj -> obj.getString("instanceNum"))
                .orElse("");
    }
}
