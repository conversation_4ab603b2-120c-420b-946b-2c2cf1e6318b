package com.sankuai.algoplatform.matchops.domain.model.testtool;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class PredictorCache234Config {

    //策略ID
    @J<PERSON>NField(name = "strategyId")
    private int strategyId;

    //缓存版本
    @JSONField(name = "cacheVersion")
    private String cacheVersion;

    //缓存过期时长分钟
    @JSONField(name = "expireMinutes")
    private int expireMinutes;

    //二级缓存开关
    @JSONField(name = "globalCacheSwitch")
    private boolean globalCacheSwitch;

    //三级缓存开关
    @JSONField(name = "coupleCacheSwitch")
    private boolean coupleCacheSwitch;

    //读缓存batch
    @JSONField(name = "modelCacheBatchNum")
    private int modelCacheBatchNum;

    //四级缓存开关
    @JSONField(name = "modelPredictCacheSwitch")
    private boolean modelPredictCacheSwitch;

    //写四级缓存batch
    @JSONField(name = "modelCacheBatchPutNum")
    private int modelCacheBatchPutNum;

}
