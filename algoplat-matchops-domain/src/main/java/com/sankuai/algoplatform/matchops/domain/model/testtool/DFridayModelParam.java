package com.sankuai.algoplatform.matchops.domain.model.testtool;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 大模型参数配置
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DFridayModelParam {
    
    /**
     * 采样概率阈值
     */
    private String topP;
    
    /**
     * 最大生成token数
     */
    private String maxNewTokens;
    
    /**
     * 提示词前缀
     */
    private String promptPrefix = "";
    
    /**
     * 采样候选数
     */
    private String topK;
    
    /**
     * 提示词后缀
     */
    private String promptSuffix ="";
    
    /**
     * 温度系数
     */
    private String temperature;
    
    /**
     * 最大序列长度
     */
    private String maxSeqLength;
    

}