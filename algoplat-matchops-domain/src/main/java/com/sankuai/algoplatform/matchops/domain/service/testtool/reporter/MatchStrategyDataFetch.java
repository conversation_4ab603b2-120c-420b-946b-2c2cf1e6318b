package com.sankuai.algoplatform.matchops.domain.service.testtool.reporter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.algoplatform.matchops.domain.constant.TestToolConstants;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestSubTask;
import com.sankuai.algoplatform.matchops.infrastructure.monitor.RaptorTrack;
import com.sankuai.algoplatform.matchops.infrastructure.util.JsonPathUtil;
import com.sankuai.algoplatform.matchops.infrastructure.util.ParserUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 打点数据查询类
 */
@Service
public class MatchStrategyDataFetch {

    public static String query(String[] ruleText, TestSubTask testSubTask) {
        if (ruleText.length < 2) {
            RaptorTrack.Sys_UnexpectedVisitNum.report("ReporterTemplateConfigError");
            return "";
        }
        Map<String, String> queryMap = ParserUtil.queryStringToMap(ruleText[1]);
        String type = queryMap.get("ttype");

        String matchJson = "";
        switch (type) {
            case "algoCode":
                matchJson = testSubTask.getAlgoCodeInfo();
                if (StringUtils.isEmpty(matchJson)) {
                    //todo 打点
                    return "";
                }
                if (ruleText.length >= 3){
                    return JsonPathUtil.extractString(matchJson, ruleText[2]);
                }
                return handleAlgoCode(matchJson);
            case "llm":
                matchJson = testSubTask.getLlmInfo();
                if (StringUtils.isEmpty(matchJson)) {
                    //todo 打点
                    return "";
                }
                if (ruleText.length >= 3){
                    return JsonPathUtil.extractString(matchJson, ruleText[2]);
                }
                return handleLlm(matchJson);
            default:
                RaptorTrack.Sys_UnexpectedVisitNum.report("ReporterTemplateConfigError");
                return "";
        }

    }

    private static String handleAlgoCode(String matchJson) {
        return JSON.parseArray(matchJson).stream()
                .map(outerObj -> {
                    JSONObject jsonObject = (JSONObject) outerObj;
                    String bizCode = jsonObject.getString(TestToolConstants.ALGO_BIZ_CODE);
                    JSONArray distributions = jsonObject.getJSONObject("algoStrategyAbtest").getJSONArray("distributions");

                    return "bizCode：" + bizCode + distributions.stream()
                            .map(innerObj -> {
                                JSONObject obj = (JSONObject) innerObj;
                                String commitId = obj.getJSONObject("algoPackage").getString("version");
                                String quota = obj.getString("quota");
                                return String.format("，commitId：%s，流量配额：%s", commitId, quota);
                            })
                            .collect(Collectors.joining());
                })
                .collect(Collectors.joining("\n"));
    }

    private static String handleLlm(String matchJson) {
        return JSON.parseArray(matchJson).stream()
                .map(outerObj -> {
                    JSONObject jsonObject = (JSONObject) outerObj;
                    String bizCode = jsonObject.getString("llmBizCode");
                    JSONArray distributions = jsonObject.getJSONObject("llmStrategyAbtest").getJSONArray("distributions");

                    return "bizCode：" + bizCode + distributions.stream()
                            .map(innerObj -> {
                                JSONObject obj = (JSONObject) innerObj;
                                String serviceName = obj.getJSONObject("modelConfig").getString("modelServiceName");
                                String modelName = obj.getJSONObject("modelConfig").getString("modelName");
                                String quota = obj.getString("quota");
                                return String.format("，服务名称：%s，模型名称：%s，流量配额：%s", serviceName, modelName, quota);
                            })
                            .collect(Collectors.joining());
                })
                .collect(Collectors.joining("\n"));
    }

}
