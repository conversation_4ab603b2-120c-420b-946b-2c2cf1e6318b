package com.sankuai.algoplatform.matchops.domain.enums;

import lombok.Getter;
import org.apache.poi.ss.formula.functions.T;

import java.util.Arrays;

@Getter
public enum MatchStrategyStatusEnum {

    UNKNOWN(-2, "未知类型"),

    INIT(-1, "初始状态"),

    TESTING(0, "测试中"),

    TEST_SUCCESS(1, "测试成功"),

    TEST_FAIL(2, "测试失败"),

    DEPLOYING(3, "上线中"),

    DEPLOY_SUCCESS(4, "上线成功"),

    DEPLOY_FAIL(5, "上线失败"),

    ROLLUPING(6, "回滚中"),

    ROLLUP_SUCCESS(7, "回滚成功"),

    ROLLUP_FAIL(8, "回滚失败"),
    ;

    private final int code;
    private final String name;

    MatchStrategyStatusEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static MatchStrategyStatusEnum getByCode(int code) {
        return Arrays.stream(MatchStrategyStatusEnum.values()).filter(statusEnum -> statusEnum.getCode() == code)
                .findFirst().orElse(UNKNOWN);
    }

    public static boolean canDeploy(int code) {
        MatchStrategyStatusEnum type = getByCode(code);
        return type == TEST_SUCCESS;
    }

    public static boolean matchStrategyCanUpdate(int code) {
        return true;
//        MatchStrategyStatusEnum type = getByCode(code);
//        return type != DEPLOY_SUCCESS && type != TESTING && type != DEPLOYING && type != ROLLUPING;
    }
}
