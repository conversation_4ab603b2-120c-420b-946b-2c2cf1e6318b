package com.sankuai.algoplatform.matchops.domain.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;
import lombok.Data;

import java.lang.reflect.Type;

@Data
public class TimedScheduleRuleConfig {
    @JSONField(deserializeUsing = TimedScheduleTypeDeserializer.class)
    private TimedScheduleType scheduleType;
    private String scheduleDateTime;

    public enum TimedScheduleType {
        DAILY(1, "每日周期"),
        ONCE(2, "单次");

        private int code;
        private String desc;

        TimedScheduleType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static TimedScheduleType getByCode(int code) {
            for (TimedScheduleType value : TimedScheduleType.values()) {
                if (value.getCode() == code) {
                    return value;
                }
            }
            return null;
        }
    }

    public static class TimedScheduleTypeDeserializer implements ObjectDeserializer {
        @Override
        public <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
            int value = parser.parseObject(Integer.class);
            return (T) TimedScheduleType.getByCode(value);
        }

        @Override
        public int getFastMatchToken() {
            return 0;
        }

    }
}
