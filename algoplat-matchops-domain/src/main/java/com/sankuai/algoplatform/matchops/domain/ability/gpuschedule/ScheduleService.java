package com.sankuai.algoplatform.matchops.domain.ability.gpuschedule;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.sankuai.aifree.thrift.generation.MLPModelServingService;
import com.sankuai.aifree.thrift.generation.query.AddInstanceQuery;
import com.sankuai.aifree.thrift.generation.query.InstanceQuery;
import com.sankuai.aifree.thrift.generation.query.ResourceQuery;
import com.sankuai.aifree.thrift.generation.vo.CommonResponseVo;
import com.sankuai.aifree.thrift.generation.vo.GroupVo;
import com.sankuai.aifree.thrift.generation.vo.InstanceVo;
import com.sankuai.aifree.thrift.generation.vo.ServingDetailDataVo;
import com.sankuai.algoplatform.matchops.domain.enums.ScheduleServiceType;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.Extra;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ScheduleServiceConfig;
import com.sankuai.algoplatform.matchops.infrastructure.enums.FridayInstanceStatus;
import com.sankuai.algoplatform.matchops.infrastructure.enums.GPUResourceType;
import com.sankuai.algoplatform.matchops.infrastructure.enums.OctoNodeStatus;
import com.sankuai.algoplatform.matchops.infrastructure.model.FridayServiceInstance;
import com.sankuai.algoplatform.matchops.infrastructure.model.OctoNode;
import com.sankuai.algoplatform.matchops.infrastructure.monitor.RaptorTrack;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.FridayService;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.MlpTrainingService;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.OctoNodeService;
import com.sankuai.algoplatform.matchops.infrastructure.util.SpringContextUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;

import static java.util.Objects.requireNonNull;


@Slf4j
@Data
public class ScheduleService {

    /**
     * 字段: id
     * 说明: 自增主键
     */
    private Long id;

    /**
     * 字段: appkey
     * 说明: appkey
     */
    private String appkey;

    /**
     * 字段: group_name
     * 说明: 分组名称
     */
    private String groupName;

    /**
     * 字段: schedule_type
     * 说明: 服务类型（Friday、MLP）
     */
    private ScheduleServiceType scheduleType;

    /**
     * 字段: gpu_usage_num
     * 说明: 单实例gpu数
     */
    private Integer gpuUsageNum;

    /**
     * 字段: inst_max_num
     * 说明: 配额上限
     */
    private Integer instMaxNum;

    /**
     * 字段: inst_min_num
     * 说明: 最小资源数
     */
    private Integer instMinNum;

    /**
     * 字段: operator_mis
     * 说明: 操作人id
     */
    private String operatorMis;

    /**
     *   字段: queue
     *   说明: 队列
     */
    private String queue;

    /**
     *   字段: resource_type
     *   说明: 资源类型（L40,A100）
     */
    private GPUResourceType resourceType;

    /**
     *   字段: extra
     *   说明: 用于MLP扩缩容 包括核心数、内存大小、显存大小等
     */
    private Extra extra;

    public static boolean isValidConfig(ScheduleServiceConfig cfg) {
        boolean r = cfg != null && StringUtils.isNotBlank(cfg.getAppkey()) && StringUtils.isNotBlank(cfg.getGroupName())
                && cfg.getGpuUsageNum() != null && cfg.getGpuUsageNum() > 0
                && cfg.getInstMaxNum() != null && cfg.getInstMaxNum() > 0
                && cfg.getInstMinNum() != null && cfg.getInstMinNum() > 0
                && cfg.getInstMinNum() <= cfg.getInstMaxNum()
                && ScheduleServiceType.getByName(cfg.getScheduleType()) != null;
        if (!r) {
            log.error("ScheduleService config invalid:{}", JSON.toJSONString(cfg));
            RaptorTrack.Sys_UnexpectedVisitNum.report("InvalidScheduleService_" + (cfg != null ? cfg.getId() : null));
        }
        return r;
    }

    public static ScheduleService from(ScheduleServiceConfig scheduleServiceConfig) {
        ScheduleService scheduleService = new ScheduleService();
        scheduleService.setId(scheduleServiceConfig.getId());
        scheduleService.setAppkey(scheduleServiceConfig.getAppkey());
        scheduleService.setGroupName(scheduleServiceConfig.getGroupName());
        scheduleService.setScheduleType(requireNonNull(ScheduleServiceType.getByName(scheduleServiceConfig.getScheduleType())));
        scheduleService.setGpuUsageNum(scheduleServiceConfig.getGpuUsageNum());
        scheduleService.setInstMaxNum(scheduleServiceConfig.getInstMaxNum());
        scheduleService.setInstMinNum(scheduleServiceConfig.getInstMinNum());
        scheduleService.setOperatorMis(scheduleServiceConfig.getOperatorMis());

        if(scheduleServiceConfig.getExtra() != null) {
            try{
                Extra extra = JSON.parseObject(scheduleServiceConfig.getExtra(), Extra.class);
                scheduleService.setExtra(extra);
            } catch (Exception e) {
                log.warn("Failed to parse extraConfig JSON: {}, error: {}", scheduleServiceConfig.getExtra(), e.getMessage());
                // 可以选择设置为 null 或者设置默认值
                scheduleService.setExtra(null);
            }
        }
        return scheduleService;
    }

    public List<OctoNode> queryOctoNodeStatus() {
        if (ScheduleServiceType.Friday.equals(scheduleType)) {
            return SpringContextUtils.getBean(OctoNodeService.class)
                    .getOctoNodeStatus(appkey, null, null, null);
        }else if(ScheduleServiceType.MLP.equals(scheduleType)){
            return SpringContextUtils.getBean(OctoNodeService.class)
                    .getOctoNodeStatus(appkey, extra.getSet(), null, null);
        }else {
            throw new UnsupportedOperationException("暂不支持的类型");
        }
    }

    public int queryOnlineOctoNodeCount() {
        List<OctoNode> octoNodes = queryOctoNodeStatus();
        if (CollectionUtils.isEmpty(octoNodes)) {
            return 0;
        }
        return (int) octoNodes.stream().filter(f -> OctoNodeStatus.NORMAL.equals(f.getStatus())).count();
    }

    public Pair<Boolean, String> queryAllNodeRunning() {
        int onlineOctoNodeCount = queryOnlineOctoNodeCount();
        if (ScheduleServiceType.Friday.equals(scheduleType)) {
            List<FridayServiceInstance> instances = SpringContextUtils.getBean(FridayService.class)
                    .queryInstanceStatus(groupName);
            int gpuNodeCnt = instances.size();
            int onlineGpuNodeCnt = (int) instances.stream().filter(f -> FridayInstanceStatus.RUNNING.equals(f.getStatus())).count();
            boolean r = onlineOctoNodeCount == gpuNodeCnt && onlineGpuNodeCnt == gpuNodeCnt;
            String s = String.format("%s总节点数:%s, 运行中节点数:%s, OCTO在线数:%s", (r ? "[成功]" : ""), gpuNodeCnt, onlineGpuNodeCnt, onlineOctoNodeCount);
            return Pair.of(r, s);
        } else if (ScheduleServiceType.MLP.equals(scheduleType)) {
            int gpuNodeCnt = 0;
            int onlineGpuNodeCnt = 0;
            ServingDetailDataVo detail = SpringContextUtils.getBean(MlpTrainingService.class)
                    .getServingDetail(appkey, operatorMis);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(detail.getGroups())) {
                Optional<GroupVo> groupVoOptional = detail.getGroups().stream()
                        .filter(g -> StringUtils.equalsIgnoreCase(groupName, g.getName()))
                        .findFirst();
                if (groupVoOptional.isPresent()) {
                    GroupVo groupVo = groupVoOptional.get();
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(groupVo.getInstances())) {
                        List<InstanceVo> instances = groupVo.getInstances();
                        gpuNodeCnt = instances.size();
                        onlineGpuNodeCnt = (int) instances.stream()
                                .filter(node -> node.getOctoStatus() == 2) //octo运行中状态
                                .count();
                    }
                }
            }
            boolean r = onlineOctoNodeCount == gpuNodeCnt;
            String s = String.format("%s总节点数:%s,运行中节点数:%s, OCTO在线数:%s", (r ? "[成功]" : ""), gpuNodeCnt, onlineGpuNodeCnt, onlineOctoNodeCount);
            return Pair.of(r, s);
        } else {
            throw new UnsupportedOperationException("暂不支持的类型");
        }
    }
    public AddInstanceQuery buildMLPAddInstanceQuery(int num) {
        String token = StringUtils.truncate(UUID.randomUUID().toString(), 32);
        Integer expireTime = 1;
        Preconditions.checkState(num > 0);
        AddInstanceQuery req = new AddInstanceQuery();
        req.setToken(token);
        req.setExpireTime(expireTime);
        req.setAppkey(appkey);
        req.setUser(operatorMis);
        req.setName(groupName);

        InstanceQuery inst = new InstanceQuery();
        String set = extra.getSet();
        if (StringUtils.isNotBlank(set) && set.contains("default")) {
            set = "";
        }
        inst.setSetName(set);
        inst.setCluster(extractCluster(queue));
        inst.setQueue(queue);
        inst.setWorker(num);

        ResourceQuery resrc = new ResourceQuery();
        inst.setResource(resrc);
        resrc.setVcores(extra.getVcores());
        resrc.setMemory(extra.getMemory());
        resrc.setGcores(extra.getGcores());
        resrc.setGcoresType(resourceType.getName());
        req.setInstanceQueries(Lists.newArrayList(inst));
        return req;
    }

    public boolean addInstance(int num) {
        if (ScheduleServiceType.Friday.equals(scheduleType)) {
            Pair<Boolean, String> pair = SpringContextUtils.getBean(FridayService.class)
                    .addInstance(groupName, num);
            log.info("addInstance num:{}, result: {}", num, pair);
            return pair.getLeft();
        } else if (ScheduleServiceType.MLP.equals(scheduleType)) {
            try {
                AddInstanceQuery req = buildMLPAddInstanceQuery(num);
                CommonResponseVo resp = SpringContextUtils.getBean(MLPModelServingService.Iface.class).addInstance(req);
                Pair<Boolean, String> pair = Pair.of(resp.getCode() == 0, resp.getMessage());
                log.info("addInstance num:{}, result: {}", num, pair);
                return resp.getCode() == 0;
            } catch (Exception e) {
                log.error("addInstance: {}, ex", appkey, e);
                throw new RuntimeException(e);
            }
        } else {
            throw new UnsupportedOperationException("暂不支持的类型");
        }
    }
    private String extractCluster(String input) {
        int firstDotIndex = input.indexOf('.');
        int secondDotIndex = input.indexOf('.', firstDotIndex + 1);
        if (firstDotIndex != -1 && secondDotIndex != -1) {
            return input.substring(firstDotIndex + 1, secondDotIndex);
        }
        return "";
    }

    public boolean deleteInstance(int num) {
        if (ScheduleServiceType.Friday.equals(scheduleType)) {
            Pair<Boolean, String> pair = SpringContextUtils.getBean(FridayService.class)
                    .deleteInstance(groupName, num);
            log.info("deleteInstance num:{}, result: {}", num, pair);
            return pair.getLeft();
        } else if (ScheduleServiceType.MLP.equals(scheduleType)) {
            Pair<Boolean, String> pair = SpringContextUtils.getBean(MlpTrainingService.class)
                    .deleteInstance(appkey, groupName, num, operatorMis);
            log.info("deleteInstance num:{}, result: {}", num, pair);
            return pair.getLeft();

        } else {
            throw new UnsupportedOperationException("暂不支持的类型");
        }
    }

    public Pair<Boolean, String> safetyAddInstance(int num) {
        int queryOnlineOctoNodeCount = queryOnlineOctoNodeCount();
        if (queryOnlineOctoNodeCount > instMaxNum) {
            return Pair.of(false, String.format("[失败]当前节点数超过最大配额, 在线节点数:%s, 配额:%s", queryOnlineOctoNodeCount, instMaxNum));
        }
        if (ScheduleServiceType.Friday.equals(scheduleType)) {
            Pair<Boolean, String> pair = SpringContextUtils.getBean(FridayService.class)
                    .addInstance(groupName, num);
            if (!pair.getLeft()) {
                pair = ImmutablePair.of(pair.getLeft(), "[失败]Friday结果异常:" + pair.getValue());
            } else {
                if (StringUtils.isBlank(pair.getValue())) {
                    pair = ImmutablePair.of(pair.getLeft(), String.format("已向Friday提交增加%s个实例.%s", num, Optional.ofNullable(pair.getValue()).orElse("")));
                }
            }
            log.info("addInstance num:{}, result: {}", num, pair);
            return pair;
        } else if (ScheduleServiceType.MLP.equals(scheduleType)) {
            try {
                AddInstanceQuery req = buildMLPAddInstanceQuery(num);
                CommonResponseVo resp = SpringContextUtils.getBean(MLPModelServingService.Iface.class).addInstance(req);
                Pair<Boolean, String> pair = Pair.of(resp.getCode() == 0, resp.getMessage());
                if (!pair.getLeft()) {
                    pair = ImmutablePair.of(pair.getLeft(), "[失败]MLP结果异常:" + pair.getValue());
                } else {
                    pair = ImmutablePair.of(pair.getLeft(), String.format("已向MLP提交增加%s个实例.%s", num, Optional.ofNullable(pair.getValue()).orElse("")));
                }
                log.info("addInstance num:{}, result: {}", num, pair);
                return pair;
            }catch (Exception e){
                log.error("addInstance: {}, ex", appkey, e);
                throw new RuntimeException(e);
            }
        } else {
            throw new UnsupportedOperationException("暂不支持的类型");
        }
    }

    public Pair<Boolean, String> safetyDeleteInstance(int num) {
        int onlineOctoNodeCount = queryOnlineOctoNodeCount();
        if (onlineOctoNodeCount - num < instMinNum) {
            return Pair.of(false, String.format("[失败]当前节点数小于最小配额, 当前节点数:%s, 缩容数:%s, 最小配额:%s", onlineOctoNodeCount, num, instMinNum));
        }
        if (ScheduleServiceType.Friday.equals(scheduleType)) {
            Pair<Boolean, String> pair = SpringContextUtils.getBean(FridayService.class)
                    .deleteInstance(groupName, num);
            log.info("deleteInstance num:{}, result: {}", num, pair);
            if (!pair.getLeft()) {
                pair = ImmutablePair.of(pair.getLeft(), "[失败]Friday结果异常:" + pair.getValue());
            } else {
                if (StringUtils.isBlank(pair.getValue())) {
                    pair = ImmutablePair.of(pair.getLeft(), String.format("已向Friday提交删除%s个实例.%s", num, Optional.ofNullable(pair.getValue()).orElse("")));
                }
            }
            return pair;
        } else if (ScheduleServiceType.MLP.equals(scheduleType)) {
            Pair<Boolean, String> pair = SpringContextUtils.getBean(MlpTrainingService.class)
                    .deleteInstance(appkey, groupName, num, operatorMis);
            log.info("deleteInstance num:{}, result: {}", num, pair);
            if (!pair.getLeft()) {
                pair = ImmutablePair.of(pair.getLeft(), "[失败]MLP结果异常:" + pair.getValue());
            } else {
                pair = ImmutablePair.of(pair.getLeft(), String.format("已向MLP提交删除%s个实例.%s", num, Optional.ofNullable(pair.getValue()).orElse("")));
            }
            return pair;
        } else {
            throw new UnsupportedOperationException("暂不支持的类型");
        }
    }
}
