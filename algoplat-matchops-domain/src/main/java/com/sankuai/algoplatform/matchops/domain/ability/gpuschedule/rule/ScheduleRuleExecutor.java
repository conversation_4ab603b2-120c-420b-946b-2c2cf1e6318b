package com.sankuai.algoplatform.matchops.domain.ability.gpuschedule.rule;

import com.sankuai.algoplatform.matchops.domain.ability.gpuschedule.ScheduleTask;
import com.sankuai.algoplatform.matchops.domain.enums.ScheduleTaskType;
import com.sankuai.algoplatform.matchops.domain.model.ScheduleTaskCheckResult;

import java.util.List;
import java.util.Map;

public abstract class ScheduleRuleExecutor {
    protected ScheduleTask scheduleTask;

    public ScheduleRuleExecutor(ScheduleTask scheduleTask) {
        this.scheduleTask = scheduleTask;
    }

    public static ScheduleRuleExecutor from(ScheduleTaskType taskType, ScheduleTask scheduleTask, String initJsonConfig) {
        switch (taskType) {
            case TIMING_TASK: {
                return new TimedScheduleRuleExecutor(scheduleTask, initJsonConfig);
            }
            case MONITOR_TASK: {
                throw new IllegalArgumentException("monitor task not supported");
            }
            default: {
                throw new IllegalArgumentException("invalid task type: " + taskType);
            }
        }
    }

    public abstract List<ScheduleTaskCheckResult> generateCheckResult(Map<String,Object> params);

}
