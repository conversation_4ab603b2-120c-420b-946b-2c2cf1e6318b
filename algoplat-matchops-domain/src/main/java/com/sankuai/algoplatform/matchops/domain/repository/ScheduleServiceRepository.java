package com.sankuai.algoplatform.matchops.domain.repository;

import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ScheduleServiceConfig;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.ScheduleServiceConfigExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.ScheduleServiceConfigMapper;
import com.sankuai.algoplatform.matchops.infrastructure.util.ZebraForceMasterUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Slf4j
@Repository
public class ScheduleServiceRepository {
    @Resource
    private ScheduleServiceConfigMapper scheduleServiceConfigMapper;

    public List<ScheduleServiceConfig> queryValidScheduleServiceByIds(List<Long> scheduleServiceIds) {
        if (CollectionUtils.isEmpty(scheduleServiceIds)) {
            return Collections.emptyList();
        }
        return ZebraForceMasterUtil.queryInMaster(() -> {
            ScheduleServiceConfigExample example = new ScheduleServiceConfigExample();
            example.createCriteria().andIdIn(scheduleServiceIds).andStatusEqualTo(0);
            return scheduleServiceConfigMapper.selectByExample(example);
        });
    }

    public List<ScheduleServiceConfig> queryValidScheduleService() {
        ScheduleServiceConfigExample example = new ScheduleServiceConfigExample();
        example.createCriteria().andStatusEqualTo(0);
        return scheduleServiceConfigMapper.selectByExample(example);
    }
}
