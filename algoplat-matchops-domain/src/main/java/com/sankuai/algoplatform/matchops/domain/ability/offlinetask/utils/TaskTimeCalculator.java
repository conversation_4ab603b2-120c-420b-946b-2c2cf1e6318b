package com.sankuai.algoplatform.matchops.domain.ability.offlinetask.utils;

import java.util.Date;

public class TaskTimeCalculator {
    

    public static int calculateRemainingMinutes(Date startDate, int durationMinutes) {
        if (startDate == null) {
            throw new IllegalArgumentException("startDate cannot be null");
        }
        // 计算结束时间：开始时间 + 持续时间（分钟）
        Date endDate = new Date(startDate.getTime() + durationMinutes * 60 * 1000L);

        // 获取当前时间
        Date currentDate = new Date();

        // 计算结束时间与当前时间的差值（毫秒）
        long timeDifferenceMillis = endDate.getTime() - currentDate.getTime();

        // 将毫秒转换为分钟

        return (int) (timeDifferenceMillis / (60 * 1000));
    }
    public static int calculateDiffMinutes(Date startTime, Date endTime) {
        if (startTime == null || endTime == null) {
            throw new IllegalArgumentException("startTime and endTime cannot be null");
        }

        // 如果结束时间在开始时间之前，返回-1
        if (endTime.before(startTime)) {
            return -1;
        }

        // 计算时间差（毫秒）
        long timeDifferenceMillis = endTime.getTime() - startTime.getTime();

        // 将毫秒转换为分钟并返回
        return (int) (timeDifferenceMillis / (60 * 1000));

    }
}
