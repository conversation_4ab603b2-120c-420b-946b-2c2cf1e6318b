package com.sankuai.algoplatform.matchops.domain.model;

import com.sankuai.algoplatform.matchops.domain.enums.ScheduleTaskActionType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import static com.sankuai.algoplatform.matchops.infrastructure.util.DateUtil.parseDate2Stamp;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScheduleTaskAction {
    private ScheduleTaskActionType actionType;
    private Integer quotaNum;
    private String triggerDatetime;

    public Long getTriggerTimestamp() {
        if (StringUtils.isBlank(triggerDatetime)) {
            return null;
        }
        return parseDate2Stamp(this.triggerDatetime);
    }
}
