package com.sankuai.algoplatform.matchops.domain.enums;

import lombok.Getter;


@Getter
public enum XtTaskStatus {
    WAITING_EXECUTION("0", "等待执行"),
    TESTING("10", "正在测试"),
    SUPER_PERMISSION_TEST("11", "超级权限测试，不受监控"),
    STOPPED("21", "被停止"),
    KILLED_BY_SYSTEM("23", "被系统监控kill掉"),
    COMPLETED("30", "运行完成"),
    ERROR("31", "运行出错"),
    STATUS_NOT_RETURNED("4", "状态未返回"),
    START_MULTI_NODE_TEST("41", "开始多节点测试用例");

    private String code;
    private String description;

    XtTaskStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }


    public static XtTaskStatus fromCode(String code) {
        for (XtTaskStatus status : XtTaskStatus.values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown code: " + code);
    }
}
