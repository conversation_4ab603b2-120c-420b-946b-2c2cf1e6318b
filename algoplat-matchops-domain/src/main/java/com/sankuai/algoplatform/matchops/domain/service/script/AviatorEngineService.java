package com.sankuai.algoplatform.matchops.domain.service.script;

import com.alibaba.fastjson.JSONObject;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.AviatorEvaluatorInstance;
import com.googlecode.aviator.Expression;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorRuntimeJavaType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Map;

@Service
@Slf4j
public class AviatorEngineService {

    public AviatorEvaluatorInstance AVIATOR_EVALUATOR = AviatorEvaluator.getInstance();

    @PostConstruct
    public void init() throws Exception {
        try {
            AVIATOR_EVALUATOR.addModule(org.apache.commons.lang3.tuple.Pair.class);
            AVIATOR_EVALUATOR.addStaticFunctions("JSONObject", com.alibaba.fastjson.JSONObject.class);
            AVIATOR_EVALUATOR.addStaticFunctions("StringUtils", org.apache.commons.lang3.StringUtils.class);
            AVIATOR_EVALUATOR.addStaticFunctions("CollectionUtils", org.apache.commons.collections4.CollectionUtils.class);
            AVIATOR_EVALUATOR.addStaticFunctions("CallFridayService", com.sankuai.algoplatform.matchops.domain.service.call.CallFridayService.class);
            AVIATOR_EVALUATOR.addStaticFunctions("CallPredictorService", com.sankuai.algoplatform.matchops.domain.service.call.CallPredictorService.class);
            AVIATOR_EVALUATOR.addFunction("getJsonString",new GetJsonString());
            AVIATOR_EVALUATOR.addFunction("replaceString",new ReplaceString());

        } catch (Exception e) {
            log.error("AviatorEngineService init error", e);
            throw e;
        }
    }
    private class GetJsonString extends AbstractFunction {
        @Override
        public String getName() {
            return "getJsonString";
        }
        @Override
        public AviatorObject call(Map<String, Object> env, AviatorObject arg1, AviatorObject arg2) {
            JSONObject json = (JSONObject) arg1.getValue(env);
            String key = FunctionUtils.getStringValue(arg2, env);
            String value = json.getString(key);
            return AviatorRuntimeJavaType.valueOf(value);
        }
    }

    private class ReplaceString extends AbstractFunction {
        @Override
        public String getName() {
            return "replaceString";
        }

        @Override
        public AviatorObject call(Map<String, Object> env, AviatorObject arg1, AviatorObject arg2, AviatorObject arg3) {
            String str = FunctionUtils.getStringValue(arg1, env);
            String target = FunctionUtils.getStringValue(arg2, env);
            String replacement = FunctionUtils.getStringValue(arg3, env);

            // 使用Java的字符串替换，不涉及正则表达式
            String result = str.replace(target, replacement);
            return AviatorRuntimeJavaType.valueOf(result);
        }
    };

//    /**
//     * 通过脚本ID执行脚本
//     *
//     * @param scriptId 脚本ID
//     * @param features 特征参数
//     * @return 执行结果
//     */
//    public Object executeScriptById(Long scriptId, Map<String, Object> features) {
//        if (scriptId == null || CollectionUtils.isEmpty(features)) {
//            throw new BMLScriptParsingException("Script ID cannot be null");
//        }
//        BmlMatchScriptConfig scriptConfig = bmlMatchScriptConfigDao.selectByScriptId(scriptId);
//        if (scriptConfig == null) {
//            throw new BMLScriptParsingException("Script not found for id: " + scriptId);
//        }
//        return executeScript(scriptConfig.getScript(), features);
//    }
//
//    /**
//     * 批量执行脚本
//     *
//     * @param batchScriptFeatures 批量脚本ID及其对应的特征参数
//     * @return 批量执行结果
//     */
//    public Map<Long, Object> executeBatchScripts(Map<Long, Map<String, Object>> batchScriptFeatures) {
//        if (CollectionUtils.isEmpty(batchScriptFeatures)) {
//            throw new BMLScriptParsingException("Batch script features cannot be null or empty");
//        }
//        log.info("executeBatchScripts: scriptCount={},batchScriptFeatures:{}", batchScriptFeatures.size(), JSONObject.toJSONString(batchScriptFeatures));
//        // 批量查询脚本配置
//        Map<Long, BmlMatchScriptConfig> scriptConfigs = bmlMatchScriptConfigDao.selectByScriptIds(batchScriptFeatures.keySet());
//        if (CollectionUtils.isEmpty(scriptConfigs)) {
//            throw new BMLScriptParsingException("Script not found for ids: " + batchScriptFeatures.keySet());
//        }
//
//        // 执行批量脚本
//        Map<Long, Object> batchResults = new HashMap<>();
//        for (Map.Entry<Long, Map<String, Object>> entry : batchScriptFeatures.entrySet()) {
//            Long scriptId = entry.getKey();
//            Map<String, Object> features = entry.getValue();
//            BmlMatchScriptConfig scriptConfig = scriptConfigs.get(scriptId);
//            if (scriptConfig == null) {
//                log.warn("Script not found for id: {}", scriptId);
//                continue;
//            }
//            Object result = executeScript(scriptConfig.getScript(), features);
//            batchResults.put(scriptId, result);
//        }
//
//        return batchResults;
//    }

    /**
     * 直接执行脚本代码
     *
     * @param scriptCode 脚本代码
     * @param features   特征参数
     * @return 执行结果
     */
    public Object executeDirectScript(String scriptCode, Map<String, Object> features) {
        // 参数验证
        if (StringUtils.isBlank(scriptCode) || MapUtils.isEmpty(features)) {
            throw new RuntimeException("Aviator Script code Or features cannot be empty");
        }
        // 执行脚本
        return executeScript(scriptCode, features);
    }


    /**
     * 执行Aviator脚本的核心方法
     */
    private Object executeScript(String scriptCode, Map<String, Object> features) {
        if (StringUtils.isBlank(scriptCode)) {
            throw new RuntimeException("Script code cannot be empty");
        }
        try {
            Expression compile = AVIATOR_EVALUATOR.compile(scriptCode, true);
            return compile.execute(features);
        } catch (Exception e) {
            log.error("Execute Aviator script error: scriptCode={}", scriptCode, e);
            throw new RuntimeException("Script execution failed: " + e.getMessage(), e);
        }
    }

}