package com.sankuai.algoplatform.matchops.domain.ability.gpuschedule;

import com.alibaba.fastjson.JSON;
import com.sankuai.algoplatform.matchops.infrastructure.enums.GPUResourceType;
import com.sankuai.algoplatform.matchops.infrastructure.model.GPUQueueResourceDetail;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ResourcePoolConfig;
import com.sankuai.algoplatform.matchops.infrastructure.monitor.RaptorTrack;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.MlpTrainingService;
import com.sankuai.algoplatform.matchops.infrastructure.util.SpringContextUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;

import static java.util.Objects.requireNonNull;


/**
 * 表名: resource_pool_config
 */
@Data
@Slf4j
public class ResourcePool {
    /**
     * 字段: id
     * 说明: 主键
     */
    private Long id;

    /**
     * 字段: tenant
     * 说明: 租户
     */
    private String tenant;

    /**
     * 字段: project_name
     * 说明: 项目名称
     */
    private String projectName;

    /**
     * 字段: queue
     * 说明: 队列
     */
    private String queue;

    /**
     * 字段: resource_type
     * 说明: 资源类型（L40,A100）
     */
    private GPUResourceType resourceType;

    /**
     * 字段: quota_limit
     * 说明: 配额上限
     */
    private int quotaLimit;

    /**
     * 字段: operator_mis
     * 说明: 操作人id
     */
    private String operatorMis;

    public static boolean isValidConfig(ResourcePoolConfig cfg) {
        boolean r = cfg != null && cfg.getQuotaLimit() != null && cfg.getQuotaLimit() > 0
                && cfg.getTenant() != null && cfg.getProjectName() != null && cfg.getQueue() != null
                && cfg.getResourceType() != null && GPUResourceType.getByName(cfg.getResourceType()) != null;
        if (!r) {
            log.error("resourcePool config invalid:{}", JSON.toJSONString(cfg));
            RaptorTrack.Sys_UnexpectedVisitNum.report("InvalidResourcePoolConfig_" + (cfg != null ? cfg.getId() : null));
        }
        return r;
    }

    public static ResourcePool from(ResourcePoolConfig cfg) {
        ResourcePool resourcePool = new ResourcePool();
        resourcePool.setId(cfg.getId());
        resourcePool.setTenant(requireNonNull(cfg.getTenant()));
        resourcePool.setProjectName(requireNonNull(cfg.getProjectName()));
        resourcePool.setQueue(requireNonNull(cfg.getQueue()));
        resourcePool.setResourceType(requireNonNull(GPUResourceType.getByName(cfg.getResourceType())));
        resourcePool.setQuotaLimit(requireNonNull(cfg.getQuotaLimit()));
        resourcePool.setOperatorMis(cfg.getOperatorMis());
        return resourcePool;
    }

    public GPUQueueResourceDetail queryCurrentQueueResource() {
        return SpringContextUtils.getBean(MlpTrainingService.class).getQueueResource(queue).stream()
                .filter(f -> resourceType.equals(f.getResourceType())).findFirst().orElse(null);
    }

    public Integer queryRemainResourceNum() {
        //mock, 上线前删掉
//        Integer mock = Lion.getConfigRepository().getIntValue("mock_gpu_remain_num", null);
//        if (mock != null) {
//            log.info("queryRemainResourceNum using mock:{}, queue:{}", this.queue, mock);
//            return mock;
//        }
        GPUQueueResourceDetail details = queryCurrentQueueResource();
        return Optional.ofNullable(details).map(GPUQueueResourceDetail::getRemainNum).orElse(null);
    }
}
