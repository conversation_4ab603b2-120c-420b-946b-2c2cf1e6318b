package com.sankuai.algoplatform.matchops.domain.service.call;

import com.alibaba.fastjson.JSON;
import com.sankuai.algoplatform.matchops.domain.service.script.AviatorEngineService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Service
public class CallScriptService {
    @Resource
    private AviatorEngineService aviatorEngineService;
    public String callScript(Map<String, Object> scriptRequest, String script) {
        Object o = aviatorEngineService.executeDirectScript(script, scriptRequest);
        return (String)o;
    }
}
