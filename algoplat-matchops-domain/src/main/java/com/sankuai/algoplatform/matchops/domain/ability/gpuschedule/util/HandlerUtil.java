package com.sankuai.algoplatform.matchops.domain.ability.gpuschedule.util;

import com.sankuai.algoplatform.matchops.domain.ability.gpuschedule.ScheduleTaskInstance;
import com.sankuai.algoplatform.matchops.domain.enums.ScheduleTaskInstanceStatus;
import com.sankuai.algoplatform.matchops.infrastructure.monitor.RaptorTrack;

import static com.sankuai.algoplatform.matchops.domain.enums.ScheduleTaskInstanceStatus.FAILED;

public class HandlerUtil {
    public static void reportRaptorEvent(ScheduleTaskInstance taskInstance) {
        RaptorTrack.Item item = null;
        String message = null;
        if (ScheduleTaskInstanceStatus.SUCCESS.equals(taskInstance.getStatus())) {
            item = RaptorTrack.Sys_GpuSchedule_SuccessEvent;
            message = "Success";
        } else if (ScheduleTaskInstanceStatus.INITIAL.equals(taskInstance.getStatus())) {
            item = RaptorTrack.Sys_GpuSchedule_SuccessEvent;
            message = "Ready";
        } else if (FAILED.equals(taskInstance.getStatus())) {
            item = RaptorTrack.Sys_GpuSchedule_FailEvent;
            message = "Failed";
        }
        if (item != null) {
            item.report(String.format("TaskInstance%s(%s):AppKey:%s,Group:%s,Task:%s,Action:%s,Quota:%s,TiggTime:%s",
                    message,
                    taskInstance.getUniqInstanceKey(),
                    taskInstance.getTaskBinding().getScheduleService().getAppkey(),
                    taskInstance.getTaskBinding().getScheduleService().getGroupName(),
                    taskInstance.getTaskBinding().getScheduleTask().getId(),
                    taskInstance.getAction().getActionType().name(),
                    taskInstance.getAction().getQuotaNum(),
                    taskInstance.getAction().getTriggerDatetime()
            ));
        }
    }
}
