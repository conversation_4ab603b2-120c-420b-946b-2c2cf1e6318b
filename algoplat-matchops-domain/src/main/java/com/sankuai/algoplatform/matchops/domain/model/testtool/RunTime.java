package com.sankuai.algoplatform.matchops.domain.model.testtool;

import lombok.Data;

import java.util.List;

@Data
public class RunTime {
    /**
     * 用户点击任务执行时间
     */
    private String taskStartTime;
    /**
     * 数据同步完成时间
     */
    private String taskEndTime;
    /**
     * 跑数耗时（毫秒）
     */
    private Long cost;

    private List<ReqExecuteTime> reqTimes;

    @Data
    public static class ReqExecuteTime {
        /**
         * 请求appkey和分组
         */
        private String key;
        /**
         * 请求开始执行时间
         */
        private String startTime;
        /**
         * 请求结束执行时间
         */
        private String endTime;
        /**
         * 请求耗时（毫秒）
         */
        private Long cost;
    }
}
