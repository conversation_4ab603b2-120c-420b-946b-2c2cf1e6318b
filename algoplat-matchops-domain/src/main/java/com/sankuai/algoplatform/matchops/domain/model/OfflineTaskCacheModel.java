package com.sankuai.algoplatform.matchops.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OfflineTaskCacheModel {
    private String partitionDate;
    private Long version;
    private String uniqueKey;
    private String modelOriginOutput;
    private String result;

    public boolean isSameUniqueKey(String uniqueKey)  {
        return this.uniqueKey.equals(uniqueKey);
    }
    public void update(String partitionDate){
        this.partitionDate = partitionDate;
        this.version = version + 1;
    }
}
