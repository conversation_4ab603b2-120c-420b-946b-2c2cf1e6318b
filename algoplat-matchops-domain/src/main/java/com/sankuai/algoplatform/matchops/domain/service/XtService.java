package com.sankuai.algoplatform.matchops.domain.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.sankuai.algoplatform.matchops.domain.enums.XtTaskStatus;
import com.sankuai.algoplatform.matchops.infrastructure.cache.TairClient;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.DxService;
import com.sankuai.algoplatform.matchops.infrastructure.util.ContextUtil;
import com.sankuai.algoplatform.matchops.infrastructure.util.HttpUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;

@Service
public class XtService {

    @Autowired
    private DxService dxService;

    private final TairClient tairClient;

    String queryCodeLink = "https://data.sankuai.com/onedev/xt/task/%s/versions/home";

    String initTaskLink = "https://data.sankuai.com/linter/v1/test_rules/stage/init_test";

    String submitTaskLink = "https://data.sankuai.com/onedev/xt/task/%s/test";

    String queryTaskLink = "https://data.sankuai.com/onedev/xt/%s/test/%s";

    public XtService(TairClient tairClient) {
        this.tairClient = tairClient;
    }

    public Map<String, Object> queryCodeByVersion(String taskId, String version, String misId) {
        String url = String.format(queryCodeLink, taskId);
        Map<String, String> header = buildHeader(misId);
        String res = HttpUtil.httpGet(url, ImmutableMap.of("code_version_id", version), header);
        JSONObject jsonObject = JSONObject.parseObject(res);
        handleRespCode(jsonObject, "查询XT代码失败，原因：");
        return jsonObject.getJSONObject("data").toJavaObject(Map.class);
    }

    public Map<String, Object> initXtTask(String onlineVersion, String version, String rawCode, String param, String misId) throws UnsupportedEncodingException {
        Map<String, String> header = buildHeader(misId);
        Map<String, Object> reqParam = new HashMap<>();
        reqParam.put("project_name", "al-catering");
        reqParam.put("task_code_version_id", version);
        reqParam.put("task_name", "");

        HashMap<Object, Object> extra = new HashMap<>();
        extra.put("raw_code", rawCode);
        extra.put("task_online_code_version_id", onlineVersion);
        extra.put("test_params", param);

        reqParam.put("extra_info", extra);
        String res = HttpUtil.httpPostWithTimeout(initTaskLink, JSON.toJSONString(reqParam), 30, header);
        // 如果需要确保UTF-8编码
        String utf8Response = new String(res.getBytes("UTF-8"));
        JSONObject jsonObject = JSONObject.parseObject(utf8Response);
        handleRespCode(jsonObject, "XT任务初始化失败");
        return jsonObject.getJSONObject("data").toJavaObject(Map.class);
    }

    /**
     *
     */
    public Map<String, Object> submitXtTask(String taskId, String version, String rawCode, String param, String misId) throws UnsupportedEncodingException {
        String url = String.format(submitTaskLink, taskId);
        Map<String, String> header = buildHeader(misId);

        Map<String, String> reqParam = new HashMap<>();
        reqParam.put("is_force", "true");
        reqParam.put("task_code_version_id", version);
        reqParam.put("test_params", param);
        reqParam.put("type", "");
        reqParam.put("engine_type", "spark3");
        reqParam.put("raw_code", rawCode);
//        reqParam.put("name", "");
//        reqParam.put("test_case_list", null);
        String res = HttpUtil.httpPostWithTimeout(url, JSON.toJSONString(reqParam), 30, header);
// 如果需要确保UTF-8编码
        String utf8Response = new String(res.getBytes("UTF-8"));
        JSONObject jsonObject = JSONObject.parseObject(utf8Response);
        handleRespCode(jsonObject, "XT任务提交失败");
        return jsonObject.getJSONObject("data").toJavaObject(Map.class);
    }

    public XtTaskStatus queryXtTaskStatus(String taskId, String submitId, String misId) {
        String url = String.format(queryTaskLink, taskId, submitId);
        Map<String, String> header = buildHeader(misId);
        String res = HttpUtil.httpGet(url, null, header);
        JSONObject jsonObject = JSONObject.parseObject(res);
        handleRespCode(jsonObject, "查询任务状态失败，原因：");
        String status = jsonObject.getJSONObject("data").getJSONObject("task_test").getString("status");
        return XtTaskStatus.fromCode(status);
    }

    public void stopTaskStatus(String xtTaskId, String version) {

    }


    private void handleRespCode(JSONObject jsonObject, String msg) {
        int code = jsonObject.getInteger("code");
        if (code != 200 && code != 0) {
            if (code == 401) {
                dxService.sendMsg2Users("XT 登录信息失效，请点击重试", ImmutableList.of(ContextUtil.getLoginUserMis()));
            }
            throw new RuntimeException(msg + jsonObject.getString("msg"));
        }
    }

//    private Map<String, String> buildHeader() {
//        String key = "bml_agent_xt_test_cookie_" + ContextUtil.getLoginUserMis();
//        String cookie = tairClient.get(key);
//        if (StringUtils.isEmpty(cookie)) {
//            throw new RuntimeException("请刷线xt缓存后，点击重试");
//        }
//        return ImmutableMap.of("Cookie", "com.sankuai.data.wanxiang.wanxiang_ssoid=" + cookie);
//    }

    private Map<String, String> buildHeader(String misId) {
        String key = "bml_agent_xt_test_cookie_" + misId;
        String cookie = tairClient.get(key);
        if (StringUtils.isEmpty(cookie)) {
            throw new RuntimeException("请刷线xt缓存后，点击重试");
        }
        Map<String, String> header = new HashMap<>();
        header.put("Cookie", "com.sankuai.data.wanxiang.wanxiang_ssoid=" + cookie);
        // header.put("Cookie", "com.sankuai.data.wanxiang.wanxiang_ssoid=" + "eAGFjrtKA0EUQJ0IkkrEL1jEQlMsc2fuvKxMVoOloCDYyMzd2RCCm2IjsUiVQsRGsNROsLHTT7CxDoKCEPwCKwVbsdDW7lTnnDpbeJ-81ZKrm9vxpxBL1D9MK1_2jnw3zf3Ap0NfHnd92fmDtYQkgUbhlFIcjQsB0DSi5y5qzK3VrQlbXN2LYYdiGUc824S20FxmWqnMtFwbxYYF4E0BlluX3J0-PH6JlZr4V2x_Vtfntp6fpicfYvvl9fx-KsZs5oLVf3OXLI06tyQxavCWCkXW5kXwCBK0JgruAAxKrrQCIaW7ZsvDGEYeOXGucmkkIBgKwDHXaAvigSKq_cQYkUtulVcNRJTSchHIoAZZOGMNjdl8Jw6aRLGqdvu9WJ6x2arqfwMlfm0O**eAEFwYEBwCAIA7CXXFEp58Cg_59gUogzQ_8NbQvovuXzUUsUQx27LrhtRYFHWdrpcK_LOcwHPwIRpw**raM98ThoBcPogDe9JUhwuMp7T0HJrOAPa6ifSrAY_JsHAITSGqwToSPmOMNjEb3fsp6sTzCFtop4ASxuppWDDw**NTc2MTI2MSx6aG91bWkxMCzlkajonJwsemhvdW1pMTBAbWVpdHVhbi5jb20sMSwwMzIyNDc4MywxNzQ5MTMwMzE4NDkx;");
        return header;
    }


}
