package com.sankuai.algoplatform.matchops.domain.enums;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum MatchStrategyOpStepEnum {
    UNKNOWN(0, "", "未知状态"),

    MCM(1, "mcm", "待发送MCM"),

    LION(2, "lion", "待上线Lion配置"),

    ALGO_CODE(3, "algoCode", "待上线算法代码包"),

    LLM(4, "llm", "待上线LLM配置"),

    SERVICE(5, "service", "待上线服务"),

    REFRESH_CACHE(6, "refreshCache", "待刷缓存"),

    DEPLOY_SUCCESS(7, "", "上线完成");

    int code;
    String name;
    String desc;

    MatchStrategyOpStepEnum(int code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public static MatchStrategyOpStepEnum getByCode(int code) {
        return Arrays.stream(MatchStrategyOpStepEnum.values()).filter(statusEnum -> statusEnum.getCode() == code)
                .findFirst().orElse(UNKNOWN);
    }

}
