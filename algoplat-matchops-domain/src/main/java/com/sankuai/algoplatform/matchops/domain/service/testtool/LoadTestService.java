package com.sankuai.algoplatform.matchops.domain.service.testtool;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.AmazonClientException;
import com.amazonaws.AmazonServiceException;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import com.dianping.cat.Cat;
import com.google.common.base.Preconditions;
import com.sankuai.aifree.thrift.generation.vo.GroupVo;
import com.sankuai.aifree.thrift.generation.vo.InstanceVo;
import com.sankuai.aifree.thrift.generation.vo.ServingDetailDataVo;
import com.sankuai.aifree.thrift.generation.vo.ServingModelInfoVo;
import com.sankuai.algoplatform.matchops.domain.enums.EnvStatus;
import com.sankuai.algoplatform.matchops.domain.model.testtool.PredictorCache234Config;
import com.sankuai.algoplatform.matchops.infrastructure.config.LionConfig;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.*;
import com.sankuai.algoplatform.matchops.infrastructure.enums.OctoNodeStatus;
import com.sankuai.algoplatform.matchops.api.request.testTask.LoadTestRequest;
import com.sankuai.algoplatform.matchops.infrastructure.model.OctoNode;
import com.sankuai.algoplatform.matchops.infrastructure.model.RaptorData;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.MlpTrainingService;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.OctoNodeService;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.RaptorDataService;
import com.sankuai.algoplatform.matchops.infrastructure.util.*;
import com.sankuai.inf.octo.mns.model.HostEnv;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.http.HttpVersion;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class LoadTestService {


    @Resource
    private AmazonS3 amazonS3;

    @Resource
    private MlpTrainingService mlpTrainingService;

    private static final String LLM_URL = "https://aigc.sankuai.com/v1/host-model/sankuai/inference";

    private static final String APP_ID = "1780164906713722955";

    private static final int numTimeMetrics = 3;

    private CountDownLatch ctl;

    private static final long timespanMs = 5 * 60 * 1000 + 3 * 1000; // 压测时长

    private static final int reqTimeoutMs = 120 * 1000; // 请求超时时间

    private static final int maxReqPerMinute = 11500; // 每分钟最大请求数

    private static final List<ReqCounter> counterList = new ArrayList<>(32); // 按照分钟索引

    private static List<String> promptContents;

    private static MinuteRateLimiter rateLimiter;

    private static final String RAPTOR_APPKEY = "com.sankuai.algoplatform.predictor";

    private final AtomicInteger errorCounter = new AtomicInteger(0);

    private static final int MAX_ERROR_THRESHOLD = 20; // 最大错误次数阈值

    private static final double THREAD_CONVERGENCE_THRESHOLD = 0.2; // 线程数收敛阈值，20%

    private static final double BOUNDARY_CONVERGENCE_THRESHOLD = 0.1; // 边界收敛阈值，10%

    @Autowired
    private OctoNodeService octoNodeService;

    @Autowired
    private RaptorDataService raptorDataService;


    /**
     * 压测线程执行的任务:
     * 循环发送 HTTP 请求, 直到超出压测时长(15分钟)
     * 成功的响应: 响应不超过超时时间8s、响应状态码为等于200
     * 统计每分钟内的成功响应请求数
     */
    public static class WorkerTask implements Runnable {
        private final CountDownLatch latch;

        private final long testBeginTsMs;

        private final long testTimeSpanMs;

        private final CloseableHttpClient client;

        private final HttpPost httpPostReq;

        private final String model;


        private static final Logger logger = LoggerFactory.getLogger(WorkerTask.class);


        WorkerTask() throws UnsupportedEncodingException {
            this(15 * 60 * 1000L, System.currentTimeMillis(), null, null);
        }

        WorkerTask(long timeSpanMs, long testBeginTsMs, String model, CountDownLatch latch) throws UnsupportedEncodingException {
            this.testTimeSpanMs = timeSpanMs;
            this.testBeginTsMs = testBeginTsMs;
            this.model = model;
            this.latch = latch;

            /* 连接池管理器(服务端需要Connection: keep-alive) */
            PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager();
            connManager.setMaxTotal(30); /* 最大连接数 */
            connManager.setDefaultMaxPerRoute(10);  /* 每个路由的最大连接数 */

            RequestConfig config = RequestConfig.custom()
                    .setConnectionRequestTimeout(5000) /* 从连接池获取连接的超时时间 */
                    .setConnectTimeout(5000) /* 请求连接超时 5s */
                    .setSocketTimeout(reqTimeoutMs) /* 响应超时 */
                    .build();
            this.client = HttpClients.custom()
                    .setDefaultRequestConfig(config)
                    .setConnectionManager(connManager)
                    .build();
            HttpPost req = new HttpPost(LLM_URL);
            req.setProtocolVersion(HttpVersion.HTTP_1_1); // 默认长连接
            req.setHeader("Content-Type", "application/json;charset=UTF-8");
            req.setHeader("appId", APP_ID);
            this.httpPostReq = req;
        }


        @Override
        public void run() {
            long time_1, time_2, time_3, time_4;
            while (true) {
                time_1 = System.currentTimeMillis();

                // 申请请求份额
                boolean success = false;
                try {
                    success = rateLimiter.acquire();
                } catch (InterruptedException e) {
                    logger.error("[run] rateLimiter.acquire failed, exception info: {}", e.getMessage());
                }
                if (!success) break; // 线程被中断 或 限流器停止

                time_2 = System.currentTimeMillis();
                // 记录请求开始时间
                long reqBeginTs = System.currentTimeMillis();
                if (timeout(reqBeginTs)) break;

                // 统计信息所属buckets
                int bucketIdx = statInfoIdx(reqBeginTs);

                // 随机挑选prompt
                LLMReqBody llmReqBody = new LLMReqBody();

                llmReqBody.setModel(model);
                int randIdx = new Random().nextInt(promptContents.size());
                llmReqBody.setMessages(Collections.singletonList(new LLMReqBody.LLMReqMessage("user", promptContents.get(randIdx))));
//                llmReqBody.setStop(Lists.newArrayList("原因", "因为"));
                StringEntity entity = new StringEntity(JSON.toJSONString(llmReqBody), StandardCharsets.UTF_8);
                this.httpPostReq.setEntity(entity);

                time_3 = System.currentTimeMillis();
                // 发送HTTP请求
                try (CloseableHttpResponse resp = client.execute(this.httpPostReq) /* try-with-resources自动释放连接 */) {
                    time_4 = System.currentTimeMillis();
                    String responseBody = EntityUtils.toString(resp.getEntity());
                    //System.out.println(responseBody);
                    String status = JSON.parseObject(responseBody).get("status").toString();
                    if (resp.getStatusLine().getStatusCode() == HttpStatus.SC_OK && "0".equals(status)) {
                        // 请求成功, 更新统计信息
                        counterList.get(bucketIdx).addSuccessCnt();
                        EntityUtils.consume(resp.getEntity()); // 消耗掉响应体, 避免资源泄漏
                        counterList.get(bucketIdx).updateTimeMetrics(Arrays.asList(time_2 - time_1, time_3 - time_2, time_4 - time_3));
                    } else {
                        EntityUtils.consume(resp.getEntity()); // 消耗掉响应体
                        counterList.get(bucketIdx).addFailedCnt();
                        if (resp.getStatusLine().getStatusCode() == HttpStatus.SC_OK)
                            throw new RuntimeException("HTTP状态码200, 但是响应体状态码 = " + status);
                        else
                            throw new RuntimeException("HTTP状态码=" + resp.getStatusLine().getStatusCode());
                    }
                } catch (Throwable e) {
                    // 请求失败, 更新统计信息
                    Cat.logError(e);
                    logger.error("调用大模型失败, 异常信息: {}", e.toString());
                }
            }
            latch.countDown();
        }

        private int statInfoIdx(long reqBeginTsMs) {
            long intervalMs = reqBeginTsMs - testBeginTsMs;
            return (int) CommonUtils.msToMin(intervalMs);
        }

        private boolean timeout(long reqBeginTsMs) {
            long delta = reqBeginTsMs - testBeginTsMs;
            return delta >= testTimeSpanMs;
        }
    }

    public Map<String, Object> runLoadTest(String req) {
        log.info("接收到压测请求: {}", req);
        LoadTestRequest loadTestRequest = JSONObject.parseObject(req, LoadTestRequest.class);
        Map<String, Object> response = new HashMap<>();
        response.put("code", 500);

        try {
            // 获取测试类型
            String testType = loadTestRequest.getModelType();
            if (testType == null) {
                throw new IllegalArgumentException("testType 不能为空，必须为 'tf' 或 'bge' 或 'llm'");
            }

            Map<String, Object> result;

            // 根据测试类型选择不同的测试方法
            switch (testType) {
                case "llm":
                    // Friday压测逻辑
                    String testDataUrl = loadTestRequest.getTestDataUrl();
                    Integer workers = loadTestRequest.getInitialWorkers();
                    String modelName = loadTestRequest.getModelName();

                    // 参数校验
                    if (StringUtils.isEmpty(testDataUrl)) {
                        throw new IllegalArgumentException("testDataUrl 不能为空");
                    }
                    if (workers == null || workers <= 0) {
                        workers = 1;
                    }

                    // 执行Friday压测
                    result = fridayRunLoadTest(testDataUrl, workers, modelName);
                    break;

                case "tf":
                case "bge":
                    // 执行万象模型压测
                    result = wanxiangRunTest(loadTestRequest);
                    break;

                default:
                    throw new IllegalArgumentException("不支持的测试类型: " + testType + "，只支持 'friday' 或 'wanxiang'");
            }

            // 构建响应
            response.put("code", 0);
            response.put("data", result);
            return response;

        } catch (IllegalArgumentException e) {
            log.error("压测参数错误: {}", e.getMessage());
            response.put("message", "压测参数错误" + e.getMessage());
            return response;
        } catch (Exception e) {
            log.error("压测执行异常", e);
            response.put("message", "压测执行失败: " + e.getMessage());
        }
        return response;
    }


    public Map<String, Object> wanxiangRunTest(LoadTestRequest req) throws InterruptedException {

        String setName = getSetName(req);
        //String setName = "gray-release-domestic-room-match";
        String predictorIp = getPredictorIp(setName);
        //关闭对应setName的缓存
        shutdownCache(setName);
        if (StringUtils.isBlank(predictorIp)) {
            log.error("获取压测地址失败");
            return null;
        }
        String targetUrl = "http://" + predictorIp + ":8080/test/predictWithCache";
        List<String> prompts = req.isSimilarityComparison() ?
                getContentFromS3(req.getTestDataUrl(), new TwoLineConverter()) :
                getContentFromS3(req.getTestDataUrl(), null);

        int maxThreads = LionConfig.mlpMaxThreadNum; // 最大线程数上限
        int testDurationMinutes = 5; // 每轮测试时长(分钟)
        //double failureRateThreshold = 0.01; // 失败率判断条件

        List<Integer> batchSizes = new ArrayList<>();
        if (StringUtils.isNotBlank(req.getBatchSizes())) {
            String[] batchSizesArray = req.getBatchSizes().split(",");
            for (String batchSizeStr : batchSizesArray) {
                try {
                    int batchSize = Integer.parseInt(batchSizeStr.trim());
                    if (batchSize > 0) {
                        batchSizes.add(batchSize);
                    }
                } catch (NumberFormatException e) {
                    log.warn("无效的批量大小: {}", batchSizeStr);
                }
            }
        }

        Map<String, Map<String, List<TransactionMetrics>>> batchSizeResults = new HashMap<>();

        for (int batchSize : batchSizes) {
            errorCounter.set(0);
            Map<String, List<TransactionMetrics>> batchResult = new HashMap<>();
            List<TransactionMetrics> allMetricsList = new ArrayList<>();

            int currentThreads = req.getInitialWorkers();
            int lowerBound = req.getInitialWorkers();
            int upperBound = maxThreads;
            int lastSuccessfulThreads = 0; // 记录最后一次成功的线程数
            AtomicBoolean continueTest = new AtomicBoolean(true);
            boolean binarySearchMode = false; // 标记是否已经进入二分查找模式

            while (continueTest.get() && currentThreads <= maxThreads) {
                log.info("开始新一轮压测，当前线程数: {}", currentThreads);
                errorCounter.set(0);
                final AtomicBoolean shouldStop = new AtomicBoolean(false);
                // 创建线程池
                ExecutorService executorService = Executors.newFixedThreadPool(currentThreads);
//                ThreadPoolExecutor invokeTaskThreadPool = ThreadPoolFactory.getRunTestThreadPool();
//                List<CompletableFuture<Void>> futures = new ArrayList<>();
                // 创建并提交任务
                for (int i = 0; i < currentThreads; i++) {
                    executorService.submit(() -> {
                        long endTime = System.currentTimeMillis() + testDurationMinutes * 60 * 1000;
                        while (System.currentTimeMillis() < endTime) {
                            try {
                                // 发送请求
                                boolean success = sendRequest(prompts, req, targetUrl, batchSize);
                                if (!success) {
                                    int currentErrors = errorCounter.incrementAndGet();
                                    if (currentErrors >= MAX_ERROR_THRESHOLD) {
                                        // 达到阈值，设置停止标志
                                        shouldStop.set(true);
                                        log.warn("错误计数达到{}，准备停止当前压测", MAX_ERROR_THRESHOLD);
                                        break;
                                    }
                                }
                            } catch (Exception e) {
                                log.error("请求发送失败", e);
                            }
                        }
                    });
                }
                for (int i = 0; i < (testDurationMinutes * 60) / 10; i++) {
                    if (errorCounter.get() >= MAX_ERROR_THRESHOLD || shouldStop.get()) {
                        log.warn("错误计数达到阈值{}或收到停止信号，提前终止压测", MAX_ERROR_THRESHOLD);
                        executorService.shutdownNow();
                        continueTest.set(false);
                        break;
                    }
                    if (executorService.awaitTermination(10, TimeUnit.SECONDS)) {
                        // 所有线程已正常终止
                        break;
                    }
                }

                //添加30秒休眠确保所有错误都已计数完成
                log.info("等待30秒确保所有错误计数完成...");
                try {
                    Thread.sleep(30000);
                } catch (InterruptedException e) {
                    log.warn("等待期间被中断", e);
                    Thread.currentThread().interrupt(); // 重置中断状态
                }
                //确保所有线程都已停止
                if (!executorService.isTerminated()) {
                    log.warn("有任务未在规定时间内完成，强制关闭");
                    executorService.shutdownNow();
                }

                // 判断测试是否成功
                boolean currentTestSucceeded = errorCounter.get() < MAX_ERROR_THRESHOLD;

                lastSuccessfulThreads = currentThreads;
                // 从raptor获取性能指标
                Map<String, String> params = new HashMap<>();
                params.put("ip", predictorIp);
                // 创建一个精确到小时的日期（分秒为0）
                Calendar calendar = Calendar.getInstance();
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                Date hourDate = calendar.getTime();

                //RaptorData.Transaction.Root root = raptorDataService.queryTransactionHourlyGraphs(HostEnv.STAGING, RAPTOR_APPKEY, hourDate, "URL", setName, "/test/predictWithCache", params);
                RaptorData.Transaction.Root root = null;
                int retryCount = 0;
                int maxRetries = 20;

                while (retryCount <= maxRetries) {
                    try {
                        root = raptorDataService.queryTransactionHourlyGraphs(HostEnv.STAGING, RAPTOR_APPKEY, hourDate, "URL", setName, "/test/predictWithCache", params);
                        break; // 查询成功，跳出循环
                    } catch (Exception e) {
                        // 只有在特定错误消息时才重试
                        if (e.getMessage() != null && e.getMessage().contains("query raptor host data failed, resp is blank")) {
                            retryCount++;
                            if (retryCount > maxRetries) {
                                log.error("获取Raptor数据失败，已重试{}次，错误信息：{}", maxRetries, e.getMessage(), e);
                                break;
                            }
                            log.warn("获取Raptor数据失败: {}，正在进行第{}次重试", e.getMessage(), retryCount);
                            try {
                                Thread.sleep(5000);
                            } catch (InterruptedException ie) {
                                Thread.currentThread().interrupt();
                                log.error("重试等待被中断", ie);
                                break;
                            }
                        } else {
                            // 其他错误直接抛出
                            log.error("获取Raptor数据失败，错误信息：{}", e.getMessage(), e);
                            throw e;
                        }
                    }
                }
                TransactionMetrics metrics = getPreviousThreeMinutesAverage(root);
                metrics.setThreads(currentThreads);
                //String gpuMetrics = getGpuMetrics(req, "");
                String gpuMetrics = getGpuMetrics(req, setName);
                metrics.setGpuUtilAvg(gpuMetrics);
                allMetricsList.add(metrics);

                // 根据测试结果决定下一步
                if (!binarySearchMode) {
                    // 仍在使用翻倍策略
                    if (currentTestSucceeded) {
                        if (currentThreads * 2 <= maxThreads) {
                            // 成功且可以翻倍，继续翻倍
                            currentThreads *= 2;
                            log.info("当前测试成功，翻倍线程数至: {}", currentThreads);
                        } else {
                            // 已达到最大值，停止测试
                            continueTest.set(false);
                            log.info("已达到最大线程数上限: {}", maxThreads);
                        }
                    } else {
                        // 失败，进入二分查找模式
                        binarySearchMode = true;
                        upperBound = currentThreads; // 当前失败的线程数作为上界
                        lowerBound = currentThreads / 2; // 上一次成功的线程数作为下界

                        // 如果lowerBound小于初始值，重置为初始值
                        if (lowerBound < req.getInitialWorkers()) {
                            lowerBound = req.getInitialWorkers();
                        }

                        log.info("测试失败，进入二分查找模式，上界: {}, 下界: {}", upperBound, lowerBound);

                        // 设置下一个测试的线程数为上下界中点
                        currentThreads = lowerBound + (upperBound - lowerBound) / 2;
                        log.info("调整线程数至: {}", currentThreads);
                    }
                } else {
                    // 已经在二分查找模式
                    // 更新上下边界
                    if (currentTestSucceeded) {
                        lowerBound = currentThreads;
                    } else {
                        upperBound = currentThreads;
                    }

                    // 计算下一个测试的线程数
                    int nextThreads;
                    if (currentTestSucceeded) {
                        // 如果当前测试成功，往上探测
                        nextThreads = Math.min(maxThreads, currentThreads + (upperBound - currentThreads) / 2);
                    } else {
                        // 如果当前测试失败，往下探测
                        nextThreads = lowerBound + (currentThreads - lowerBound) / 2;
                        // 确保下一个线程数至少比当前少一些
                        if (nextThreads >= currentThreads) {
                            nextThreads = (int) (currentThreads * 0.8);
                        }
                    }

                    // 检查是否达到收敛条件（与当前值相差不超过20%）
                    if (Math.abs(nextThreads - currentThreads) <= currentThreads * THREAD_CONVERGENCE_THRESHOLD || nextThreads == currentThreads) {
                        continueTest.set(false);
                        log.info("线程数已收敛，当前线程数: {}, 下一个线程数: {}", currentThreads, nextThreads);
                    } else {
                        currentThreads = nextThreads;
                        log.info("调整线程数至: {}", currentThreads);
                    }

                    // 特殊情况：如果上下边界相同或者极其接近，也停止测试
                    if (upperBound - lowerBound <= Math.max(1, lowerBound * BOUNDARY_CONVERGENCE_THRESHOLD)) {
                        continueTest.set(false);
                        log.info("边界已收敛，下边界: {}, 上边界: {}", lowerBound, upperBound);
                    }
                }

            }
            // 处理结果：确保我们使用最后一次成功的线程数
            if (lastSuccessfulThreads > 0) {
                log.info("批次 {} 压测完成，最后成功的线程数: {}", batchSize, lastSuccessfulThreads);
            } else {
                log.warn("批次 {} 压测未成功，尝试的最大线程数: {}", batchSize, currentThreads);
            }

            batchResult.put("allMetrics", allMetricsList);
            batchSizeResults.put("batchSize" + batchSize, batchResult);
            log.info("当前batch压测全部完成，batch数：{},最终线程数: {}", batchSize, currentThreads);

        }

        log.info("所有batch压测完成，结果汇总: {}", batchSizeResults);
        Map<String, Object> finalresult = analyzeTestResults(batchSizeResults);
        // 转换为表格格式
        Map<String, Object> tableFormatResult = convertToTableFormat(finalresult);
        return tableFormatResult;
    }

    private boolean sendRequest(List<String> prompts, LoadTestRequest req, String targetUrl, int batchSize) {
        try {

            // 随机选择batchSize个prompt
            List<String> selectedPrompts = new ArrayList<>();
            Random random = new Random();

            // 确保batchSize不超过可用prompt数量
            int actualBatchSize = Math.min(batchSize, prompts.size());

            for (int i = 0; i < actualBatchSize; i++) {
                int randIdx = random.nextInt(prompts.size());
                selectedPrompts.add(prompts.get(randIdx));
            }

            // 构建请求体
            JSONObject requestBody = new JSONObject();

            String modelType = req.getModelType();
            String predictorModelType = StringUtils.equals(modelType, "tf") ? "1" : "2";
            requestBody.put("pre_handle_struct_type", req.getPreHandleStructType());
            requestBody.put("model_name", req.getModelName());
            requestBody.put("model_type", predictorModelType);//1 TF 2 PyTorch
            requestBody.put("max_seq_len", req.getMaxSeqLen());
            requestBody.put("model_index", req.getModelIndex());

            // 如果model_type是1，添加两个额外参数
            if ("tf".equalsIgnoreCase(req.getModelType())) {
                requestBody.put("func_type", req.getFuncType());
                requestBody.put("groupId", "-1");
            }

            // 创建二维数组结构
            JSONArray processedInput = new JSONArray();

            // 处理所有选中的prompt
            for (String selectedPrompt : selectedPrompts) {
                // 为每个prompt创建一个内部数组
                JSONArray innerArray = new JSONArray();
                // 将原始字符串添加到内部数组
                innerArray.add(selectedPrompt);
                // 将内部数组添加到processed_input中
                processedInput.add(innerArray);
            }

            if (req.isSimilarityComparison()) {
                requestBody.put("input", processedInput);
            } else {
                requestBody.put("processed_input", processedInput);
            }

            // 发送请求
            String response = HttpUtil.httpPostJson(targetUrl, requestBody.toJSONString(), new HashMap<>());

            // 检查响应是否为空
            if (response == null) {
                log.error("HTTP请求返回null响应");
                return false;

            }
            return true;
        } catch (Exception e) {
            log.error("调用模型失败，异常信息：{}", e.toString());
            Cat.logError(e);
            return false;
        }
    }

    private String getPredictorIp(String setName) {
        List<OctoNode> octoNodeStatus = octoNodeService.getOctoNodeStatus("com.sankuai.algoplatform.predictor", setName, HostEnv.STAGING, OctoNodeStatus.NORMAL);
        OctoNode octoNode = octoNodeStatus.stream().findFirst().get();
        return octoNode.getIp();
    }

    public Map<String, Object> fridayRunLoadTest(String promptAddress, Integer currWorkers, String modelName) {
        Map<String, Object> finalResults = new HashMap<>();
        int maxWorkers = LionConfig.fridayMaxThreadNum;
        try {
//            int lastWorkers = 0;
            double lastRpm = 0; // 记录上一次的RPM
            promptContents = getContentFromS3(promptAddress, new TestSet2PromptConverter());
            log.info("加载到 {} 条prompt内容", promptContents.size());


            while (true) {
                log.info("开始压测，当前并发数: {}", currWorkers);

                Map<String, Object> results = fridayRunTest(currWorkers, modelName);
                // 获取所有分钟的指标
                List<Map<String, Object>> timeMetrics = (List<Map<String, Object>>) results.get("timeMetrics");
                if (timeMetrics == null || timeMetrics.isEmpty()) {
                    break;
                }
                // 计算平均指标
                Map<String, Object> avgMetrics = calculateAverageMetrics(timeMetrics);


                // 只保存平均指标作为当前线程数的结果
                finalResults.put("workers" + currWorkers, avgMetrics);

                // 获取当前RPM
                double currentRpm = Double.parseDouble((String) avgMetrics.get("rpm"));

                // 计算RPM增长大小
                double difference = currentRpm - lastRpm;

                if (difference <= 50) {
                    break;
                } else {
                    lastRpm = currentRpm;
                    currWorkers = currWorkers + 32;
                    if (currWorkers > maxWorkers) {
                        break;
                    }
                }

//                // 计算平均失败率
//                double totalFailureRate = 0.0;
//                for (Map<String, Object> metric : timeMetrics) {
//                    totalFailureRate += Double.parseDouble(((String) metric.get("failureRate")));
//                }
//                double avgFailureRate = totalFailureRate / timeMetrics.size();
//
//                log.info("平均失败率: {}", String.format("%.4f", avgFailureRate));


//                lastWorkers = currWorkers;
//                // 根据失败率调整并发数
//                if (avgFailureRate > 0.01) { // 失败率>1%
//                    currWorkers = (currWorkers + lastWorkers) / 2;
//                } else {
//                    // 失败率<=1%时
//                    currWorkers = currWorkers * 2;
//                }

//                // 判断是否能跳出循环
//                if (lastWorkers >= currWorkers) {
//                    break;
//                }

                log.info("调整后的并发数: {}", currWorkers);
            }


            return convertLoadTestResult(finalResults);
        } catch (Exception e) {
            log.error("压测执行异常: promptAddress={}, currWorkers={}", promptAddress, currWorkers, e);
            throw new RuntimeException("压测执行失败", e);
        }
    }

    public Map<String, Object> fridayRunTest(Integer currWorkers, String modelName) throws Exception {

        try {
            ctl = new CountDownLatch(currWorkers);

            // 每次测试前清空并重新初始化 counterList
            counterList.clear();
            for (int i = 0; i < 32; i++) {
                counterList.add(new ReqCounter(numTimeMetrics));
            }

            // 启动限流器
            rateLimiter = MinuteRateLimiter.create(maxReqPerMinute);
            rateLimiter.start();
            // 启动定时器, 在超出测试时间后, 停止限流器
            new Thread(() -> {
                try {
                    Thread.sleep(timespanMs - 5 * 1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                rateLimiter.stop(); // 停止限流器
            }).start();

            long testBeginTsMs = System.currentTimeMillis();
            for (int i = 0; i < currWorkers; i++) {
                WorkerTask workerTask = new WorkerTask(timespanMs, testBeginTsMs, modelName, ctl);
                Thread thread = new Thread(workerTask, "stress_test_thread_" + i);
                thread.start();
            }

            // 收集结果
            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>> timeMetrics = new ArrayList<>();


            while (true) {
                try {
                    boolean jumpOut = ctl.await(60, TimeUnit.SECONDS);
                    long interval = System.currentTimeMillis() - testBeginTsMs;
                    long lastMin = CommonUtils.msToMin(interval) - 1;

                    if (lastMin >= 0) {
                        Map<String, Object> metrics = calculateMetrics(counterList.get((int) lastMin));


                        timeMetrics.add(metrics);

                        // 输出当前分钟的指标
                        log.info("当前分钟指标 - RPM: {}, avg: {}, tp99: {}, tp999: {}",
                                metrics.get("rpm"),
                                metrics.get("avg"),
                                metrics.get("tp99"),
                                metrics.get("tp999")
                        );
                    }

                    if (jumpOut) {
                        log.info("压测完成");
                        break;
                    }
                } catch (InterruptedException e) {
                    log.error("压测等待被中断", e);
                    break;
                }
            }


            // 添加结果到返回map
            result.put("timeMetrics", timeMetrics);

            return result;
        } finally {
            rateLimiter.stop();
        }
    }

    /**
     * 将负载测试结果转换为指定格式
     *
     * @param finalResults 原始测试结果
     * @return 转换后的负载测试结果，包含表格数据和结论
     */
    public Map<String, Object> convertLoadTestResult(Map<String, Object> finalResults) {
        if (finalResults == null || finalResults.isEmpty()) {
            return null;
        }

        // 定义结果结构
        Map<String, Object> result = new HashMap<>();
        List<String> columns = Arrays.asList("线程数", "平均响应时间", "tp99响应时间", "tp999响应时间", "rpm");
        result.put("column", columns);

        // 提取数据
        List<List<String>> dataRows = new ArrayList<>();

        // 用于跟踪最大RPM的配置
        String maxRpmKey = null;
        double maxRpm = 0.0;
        Map<String, Object> maxRpmConfig = null;

        // 遍历所有结果
        for (Map.Entry<String, Object> entry : finalResults.entrySet()) {
            String key = entry.getKey();

            // 从key中提取线程数，格式为"workers数字"
            String threadsStr = key.replaceAll("\\D+", ""); // 提取数字部分

            if (threadsStr.isEmpty()) {
                continue; // 如果无法提取线程数，跳过
            }

            Map<String, Object> metrics = (Map<String, Object>) entry.getValue();

            // 提取指标，去掉"ms"后缀
            String avg = ((String) metrics.get("avg")).replace("ms", "");
            String tp99 = ((String) metrics.get("tp99")).replace("ms", "");
            String tp999 = ((String) metrics.get("tp999")).replace("ms", "");
            String rpmStr = (String) metrics.get("rpm");
            double rpmValue = Double.parseDouble(rpmStr);

            // 添加数据行
            List<String> row = Arrays.asList(threadsStr, avg, tp99, tp999, rpmStr);
            dataRows.add(row);

            // 更新最大RPM配置
            if (rpmValue > maxRpm) {
                maxRpm = rpmValue;
                maxRpmKey = key;
                maxRpmConfig = metrics;
            }
        }

        result.put("data", dataRows);

        // 添加结论部分
        Map<String, Object> conclusion = new HashMap<>();
        if (maxRpmKey != null && maxRpmConfig != null) {
            String threadsStr = maxRpmKey.replaceAll("\\D+", "");
            int threads = Integer.parseInt(threadsStr);

            conclusion.put("threads", threads);
            conclusion.put("avg", Double.parseDouble(((String) maxRpmConfig.get("avg")).replace("ms", "")));
            conclusion.put("tp99", Double.parseDouble(((String) maxRpmConfig.get("tp99")).replace("ms", "")));
            conclusion.put("tp999", Double.parseDouble(((String) maxRpmConfig.get("tp999")).replace("ms", "")));
            conclusion.put("rpm", Double.parseDouble((String) maxRpmConfig.get("rpm")));
        } else {
            // 即使没有有效配置也添加默认值的conclusion
            conclusion.put("threads", 0);
            conclusion.put("avg", 0.0);
            conclusion.put("tp99", 0.0);
            conclusion.put("tp999", 0.0);
            conclusion.put("rpm", 0.0);
        }

// 无论如何都添加conclusion字段
        result.put("conclusion", conclusion);

        return result;
    }

    public Map<String, Object> findMaxRpmResult(Map<String, Object> finalResults) {
        if (finalResults == null || finalResults.isEmpty()) {
            return null;
        }

        String maxRpmKey = null;
        double maxRpm = 0.0;

        // 遍历所有结果，找出 RPM 最大的配置
        for (Map.Entry<String, Object> entry : finalResults.entrySet()) {
            Map<String, Object> metrics = (Map<String, Object>) entry.getValue();
            if (metrics.containsKey("rpm")) {
                double rpm = Double.parseDouble(((String) metrics.get("rpm")));
                if (rpm > maxRpm) {
                    maxRpm = rpm;
                    maxRpmKey = entry.getKey();
                }
            }
        }

        if (maxRpmKey != null) {
            Map<String, Object> result = new HashMap<>();
            result.put(maxRpmKey, finalResults.get(maxRpmKey));
            return result;
        }

        return null;
    }

    /**
     * 计算指标
     */
    private Map<String, Object> calculateMetrics(ReqCounter counter) {
        Map<String, Object> metrics = new HashMap<>();

        log.info("开始计算指标，counter: {}", counter);

        // 获取响应时间列表
        List<Long> timeMetrics = counter.getSuccessTimeMetrics();
        log.info("获取到的timeMetrics: {}", timeMetrics);

        if (timeMetrics == null) {
            log.error("timeMetrics为null");
            return metrics;
        }

        if (counter.getSuccessCnt() == 0) {
            log.error("成功请求数为0，无法计算平均响应时间");
            metrics.put("avg", "0.00ms");
            metrics.put("tp99", "0.00ms");
            metrics.put("tp999", "0.00ms");
            metrics.put("rpm", "0.00");
            //metrics.put("failureRate", "0.0000");
            return metrics;
        }

        // 计算平均响应时间 (使用第3个指标，即 time_4 - time_3)
        try {
            double avgResponseTime = timeMetrics.get(2).doubleValue() / counter.getSuccessCnt();
            log.info("计算平均响应时间: {}", avgResponseTime);

            // 计算TP99和TP999
            List<Long> sortedTimes = new ArrayList<>();

            List<Long> individualTimes = counter.getIndividualResponseTimes();
            //log.info("获取到的individualTimes: {}", individualTimes);

            if (individualTimes != null) {
                sortedTimes.addAll(individualTimes);
                Collections.sort(sortedTimes);
                log.info("排序后的响应时间列表大小: {}", sortedTimes.size());
            } else {
                log.error("individualTimes为null");
            }

            int tp99Index = (int) Math.ceil(sortedTimes.size() * 0.99) - 1;
            int tp999Index = (int) Math.ceil(sortedTimes.size() * 0.999) - 1;
            log.info("tp99Index: {}, tp999Index: {}", tp99Index, tp999Index);

            // 计算RPM
            double rpm = (double) counter.getSuccessCnt();

//            // 计算失败率
//            long totalRequests = counter.getSuccessCnt() + counter.getFailedCnt();
//            log.info("总请求数: {}, 成功数: {}, 失败数: {}", totalRequests, counter.getSuccessCnt(), counter.getFailedCnt());
//
//            if (totalRequests == 0) {
//                log.error("总请求数为0，无法计算失败率");
//                metrics.put("failureRate", "0.0000");
//            } else {
//                double failureRate = (double) counter.getFailedCnt() / totalRequests;
//                metrics.put("failureRate", String.format("%.4f", failureRate));
//            }

            metrics.put("avg", String.format("%.2fms", avgResponseTime));
            metrics.put("tp99", String.format("%.2fms", tp99Index >= 0 ? sortedTimes.get(tp99Index).doubleValue() : 0));
            metrics.put("tp999", String.format("%.2fms", tp999Index >= 0 ? sortedTimes.get(tp999Index).doubleValue() : 0));
            metrics.put("rpm", String.format("%.2f", rpm));

        } catch (Exception e) {
            log.error("计算指标时发生异常: {}", e.getMessage(), e);
            metrics.put("avg", "0.00ms");
            metrics.put("tp99", "0.00ms");
            metrics.put("tp999", "0.00ms");
            metrics.put("rpm", String.format("%.2f", (double) counter.getSuccessCnt()));
            //metrics.put("failureRate", "0.0000");
        }

        return metrics;
    }

    /**
     * 计算多个分钟指标的平均值
     */
    private Map<String, Object> calculateAverageMetrics(List<Map<String, Object>> timeMetrics) {
        Map<String, Object> avgMetrics = new HashMap<>();
        double totalAvgResponseTime = 0;
        double totalTp99 = 0;
        double totalTp999 = 0;
        double totalRpm = 0;
        double totalFailureRate = 0;

        for (Map<String, Object> metric : timeMetrics) {
            totalAvgResponseTime += Double.parseDouble(((String) metric.get("avg")).replace("ms", ""));
            totalTp99 += Double.parseDouble(((String) metric.get("tp99")).replace("ms", ""));
            totalTp999 += Double.parseDouble(((String) metric.get("tp999")).replace("ms", ""));
            totalRpm += Double.parseDouble(((String) metric.get("rpm")));
            // totalFailureRate += Double.parseDouble(((String) metric.get("failureRate")));
        }

        int count = timeMetrics.size();
        avgMetrics.put("avg", String.format("%.2fms", totalAvgResponseTime / count));
        avgMetrics.put("tp99", String.format("%.2fms", totalTp99 / count));
        avgMetrics.put("tp999", String.format("%.2fms", totalTp999 / count));
        avgMetrics.put("rpm", String.format("%.2f", totalRpm / count));
        //avgMetrics.put("failureRate", String.format("%.4f", totalFailureRate / count));

        return avgMetrics;
    }


    private List<String> getContentFromS3(String s3Url, Function<String, String> converter) {
        Map<String, String> dataMeta = getObjectInfoAndSetUTF8(s3Url);
        String bucketName = dataMeta.get("bucketName");
        String objectName = dataMeta.get("objectName");
        List<String> contentList = new ArrayList<>();

        try (S3Object s3object = getObject(new GetObjectRequest(bucketName, objectName));
             InputStream content = s3object.getObjectContent();
             BufferedReader reader = new BufferedReader(new InputStreamReader(content))) {
            if (content != null) {
                String line;
                while ((line = reader.readLine()) != null) {
                    // 跳过空行
                    if (StringUtil.isNullOrEmpty(line)) continue;

                    // 使用转换器处理每一行
                    if (converter != null) {
                        line = converter.apply(line);
                    }

                    contentList.add(line);
                }
            }
        } catch (IOException e) {
            log.error("getContentFromS3 error,s3Url is {}", s3Url, e);
            throw new RuntimeException(e);
        }
        return contentList;
    }


    public S3Object getObject(GetObjectRequest getObjectRequest) {
        return amazonS3.getObject(getObjectRequest);
    }

    public Map<String, String> getObjectInfoAndSetUTF8(String inputurl) {
        Map<String, String> map = extractBucketAndObjectName(inputurl);
        String bucketName = map.get("bucketName");
        String objectName = map.get("objectName");
        updateObjectHttpUTF8(bucketName, objectName);
        return map;
    }

    public Map<String, String> extractBucketAndObjectName(String url) {
        try {
            Preconditions.checkNotNull(url, "URL cannot be null");
            // 使用正则表达式提取 bucketName 和 objectName
            String regex = "https?://[^/]+/([^/]+)/(.+?)(\\?|$)";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(url);

            if (matcher.find()) {
                String bucketName = matcher.group(1); // 提取 bucketName
                String encodedObjectName = matcher.group(2); // 提取 objectName（可能是百分号编码的）

                // 解码 objectName
                String objectName = URLDecoder.decode(encodedObjectName, "UTF-8");

                log.info("Extracted bucketName: {}, objectName: {}", bucketName, objectName);

                Map<String, String> result = new HashMap<>();
                result.put("bucketName", bucketName);
                result.put("objectName", objectName);
                return result;
            } else {
                log.error("Failed to extract bucketName and objectName from URL: {}", url);
                return null;
            }
        } catch (Exception e) {
            log.error("Error occurred while extracting bucketName and objectName: {}", e.getMessage(), e);
            return null;
        }
    }

    public void updateObjectHttpUTF8(String bucketName, String objectName) {
        updateObjectHttp(bucketName, objectName, "UTF-8");
    }

    public void updateObjectHttp(String bucketName, String objectName, String encodingName) {
        try {
            // 获取原始对象元数据
            GetObjectMetadataRequest req = new GetObjectMetadataRequest(bucketName, objectName);
            ObjectMetadata objectMeta = amazonS3.getObjectMetadata(req);
            // 更新元数据
            objectMeta.setContentEncoding(encodingName);
            // 创建更新请求
            CopyObjectRequest copyObjRequest = new CopyObjectRequest(bucketName, objectName, bucketName, objectName);
            copyObjRequest.setNewObjectMetadata(objectMeta);
            amazonS3.copyObject(copyObjRequest);
        } catch (AmazonServiceException ase) {
            log.error("AmazonServiceException occurred while updating object metadata: {}", ase.getMessage(), ase);
            throw new RuntimeException("S3 updateObject error: " + objectName, ase);
        } catch (AmazonClientException ace) {
            log.error("AmazonClientException occurred while updating object metadata: {}", ace.getMessage(), ace);
            throw new RuntimeException("S3 client error: " + objectName, ace);
        }
    }

    private String getSetName(LoadTestRequest request) {
        String appkey = null;
        switch (request.getModelType()) {
            case "tf":
                appkey = "com.sankuai.algoplatform.modelserver";
                break;
            case "bge":
                appkey = "com.sankuai.algoplatform.dzmoderserver.pytorch";
                break;
            default:
                throw new IllegalArgumentException("无效的modelType: " + request.getModelType() + "，只支持tf或bge");
        }
        ServingDetailDataVo servingDetail = mlpTrainingService.getServingDetail(appkey, request.getMisId());
        // 遍历所有服务组
        for (GroupVo group : servingDetail.getGroups()) {
            // 检查组是否有模型信息
            if (group.isSetModels() && group.getModels().isSetModels()) {
                // 遍历该组中的所有模型信息
                for (ServingModelInfoVo modelInfo : group.getModels().getModels()) {
                    // 比较模型名称
                    if (modelInfo.isSetModelName() && request.getModelName().equals(modelInfo.getModelName())) {
                        // 找到匹配的模型，检查该组是否有实例
                        if (group.isSetInstances() && !group.getInstances().isEmpty()) {
                            // 获取第一个实例的setName
                            InstanceVo firstInstance = group.getInstances().get(0);
                            if (firstInstance.isSetSetName()) {
                                return firstInstance.getSetName();
                            }
                        }
                        // 找到匹配的模型但没有实例或实例没有setName
                        return null;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 获取当前时间前三分钟的指标数据平均值
     *
     * @param root RaptorData.Transaction.Root对象
     * @return 包含平均指标的TransactionMetrics对象
     */
    public TransactionMetrics getPreviousThreeMinutesAverage(RaptorData.Transaction.Root root) {
        // 获取当前分钟数
        Calendar calendar = Calendar.getInstance();
        int currentMinute = calendar.get(Calendar.MINUTE);

        // 计算前三分钟的分钟数（保持在0-59范围内）
        int minute1 = (currentMinute - 1 + 60) % 60;
        int minute2 = (currentMinute - 2 + 60) % 60;
        int minute3 = (currentMinute - 3 + 60) % 60;

        log.info("获取前三分钟数据: {}, {}, {}", minute1, minute2, minute3);

        // 用于存储找到的数据
        List<Double> tp99Values = new ArrayList<>();
        List<Double> tp999Values = new ArrayList<>();
        List<Double> avgValues = new ArrayList<>();
        List<Double> hitValues = new ArrayList<>();

        // 检查root对象是否有效
        if (root == null || root.getGraphs() == null) {
            log.error("获取指标数据失败：root对象为null或不包含图表数据");
            return createEmptyMetrics();
        }

        // 获取tpLine图表中的tp99和tp999数据
        if (root.getGraphs().getTpLine() != null && root.getGraphs().getTpLine().getRows() != null) {
            for (RaptorData.Transaction.Row row : root.getGraphs().getTpLine().getRows()) {
                String xScale = row.getXScale();
                if (xScale != null && (xScale.equals(String.valueOf(minute1)) ||
                        xScale.equals(String.valueOf(minute2)) ||
                        xScale.equals(String.valueOf(minute3)))) {

                    if (row.getTp99() != null) {
                        tp99Values.add(row.getTp99());
                    }

                    if (row.getTp999() != null) {
                        tp999Values.add(row.getTp999());
                    }
                }
            }
        }

        // 获取avg图表中的avg数据
        if (root.getGraphs().getAvg() != null && root.getGraphs().getAvg().getRows() != null) {
            for (RaptorData.Transaction.Row row : root.getGraphs().getAvg().getRows()) {
                String xScale = row.getXScale();
                if (xScale != null && (xScale.equals(String.valueOf(minute1)) ||
                        xScale.equals(String.valueOf(minute2)) ||
                        xScale.equals(String.valueOf(minute3)))) {

                    if (row.getAvg() != null) {
                        avgValues.add(row.getAvg());
                    }
                }
            }
        }

        // 获取hit图表中的hit数据
        if (root.getGraphs().getHit() != null && root.getGraphs().getHit().getRows() != null) {
            for (RaptorData.Transaction.Row row : root.getGraphs().getHit().getRows()) {
                String xScale = row.getXScale();
                if (xScale != null && (xScale.equals(String.valueOf(minute1)) ||
                        xScale.equals(String.valueOf(minute2)) ||
                        xScale.equals(String.valueOf(minute3)))) {

                    if (row.getHits() != null) {
                        hitValues.add(row.getHits());
                    }
                }
            }
        }

        // 计算平均值
        double avgTp99 = calculateAverage(tp99Values);
        double avgTp999 = calculateAverage(tp999Values);
        double avgAvg = calculateAverage(avgValues);
        double avgHits = calculateAverage(hitValues);
        double qps = avgHits / 60.0; // 将每分钟的平均请求数转换为QPS

        // 创建并返回TransactionMetrics对象
        TransactionMetrics metrics = new TransactionMetrics();
        //metrics.setName("/test/predictWithCache");
        //metrics.setIp("all");
        metrics.setAvg(avgAvg);
        metrics.setTp99(avgTp99);
        metrics.setTp999(avgTp999);
        metrics.setQps(qps);
        //metrics.setFailPercent(0.0);

        return metrics;
    }

    /**
     * 计算列表数值的平均值
     */
    private double calculateAverage(List<Double> values) {
        if (values == null || values.isEmpty()) {
            return 0.0;
        }

        double sum = 0.0;
        for (Double value : values) {
            sum += value;
        }

        return sum / values.size();
    }

    /**
     * 创建空的指标对象
     */
    private TransactionMetrics createEmptyMetrics() {
        TransactionMetrics metrics = new TransactionMetrics();
        //metrics.setName("/test/predictWithCache");
        //metrics.setIp("all");
        metrics.setAvg(0.0);
        metrics.setTp99(0.0);
        metrics.setTp999(0.0);
        metrics.setQps(0.0);
        //metrics.setFailPercent(0.0);
        return metrics;
    }

    /**
     * 分析压测结果并确定最佳配置
     *
     * @param batchSizeResults 不同批处理大小的压测结果
     * @return 包含分析结论和原始数据的结果
     */
    public static Map<String, Object> analyzeTestResults(Map<String, Map<String, List<TransactionMetrics>>> batchSizeResults) {
        Map<String, Object> result = new HashMap<>();

        // 寻找最佳配置
        String bestBatchSize = null;
        Integer bestThreads = null;
        double bestScore = -1;
        TransactionMetrics bestMetrics = null;

        // 找到各指标的最大/最小值用于归一化
        double maxQps = -1;
        double minAvg = Double.MAX_VALUE;
        double minTp99 = Double.MAX_VALUE;
        double minTp999 = Double.MAX_VALUE;

        // 遍历所有批处理大小
        for (Map.Entry<String, Map<String, List<TransactionMetrics>>> batchSizeEntry : batchSizeResults.entrySet()) {
            Map<String, List<TransactionMetrics>> metricsMap = batchSizeEntry.getValue();

            // 通常我们期望这里有一个"allMetrics"的key
            List<TransactionMetrics> metricsList = metricsMap.get("allMetrics");
            if (metricsList == null) continue;

            for (TransactionMetrics metrics : metricsList) {
                maxQps = Math.max(maxQps, metrics.getQps());
                minAvg = Math.min(minAvg, metrics.getAvg());
                minTp99 = Math.min(minTp99, metrics.getTp99());
                minTp999 = Math.min(minTp999, metrics.getTp999());
            }
        }

        // 计算每个配置的综合得分
        for (Map.Entry<String, Map<String, List<TransactionMetrics>>> batchSizeEntry : batchSizeResults.entrySet()) {
            String batchSize = batchSizeEntry.getKey();
            Map<String, List<TransactionMetrics>> metricsMap = batchSizeEntry.getValue();

            List<TransactionMetrics> metricsList = metricsMap.get("allMetrics");
            if (metricsList == null || metricsList.isEmpty()) continue;

            for (TransactionMetrics metrics : metricsList) {
                // 计算归一化分数：高QPS和低延迟更好
                double qpsScore = metrics.getQps() / maxQps;
                double avgScore = minAvg / metrics.getAvg();
                double tp99Score = minTp99 / metrics.getTp99();
                double tp999Score = minTp999 / metrics.getTp999();

                // 综合评分：QPS权重更高
                double score = qpsScore * 0.5 + avgScore * 0.3 + tp99Score * 0.1 + tp999Score * 0.1;

                if (score > bestScore) {
                    bestScore = score;
                    bestBatchSize = batchSize;
                    bestThreads = metrics.getThreads();
                    bestMetrics = metrics;
                }
            }
        }

        // 创建结论对象
        Map<String, Object> conclusionMap = new HashMap<>();
        if (bestMetrics != null) {
            // 从"batchSize20"格式中提取出数字部分并转为整数
            int batchSizeValue = Integer.parseInt(bestBatchSize.replace("batchSize", ""));
            conclusionMap.put("batchSize", batchSizeValue);
            conclusionMap.put("threads", bestThreads);
            conclusionMap.put("avg", bestMetrics.getAvg());
            conclusionMap.put("tp99", bestMetrics.getTp99());
            conclusionMap.put("tp999", bestMetrics.getTp999());
            conclusionMap.put("qps", bestMetrics.getQps());
            conclusionMap.put("qpm",bestMetrics.getQps() * 60);
            conclusionMap.put("gpuUtilAvg", bestMetrics.getGpuUtilAvg());
        }

        result.put("conclusion", conclusionMap);

        // 添加原始数据到输出
        for (Map.Entry<String, Map<String, List<TransactionMetrics>>> entry : batchSizeResults.entrySet()) {
            result.put(entry.getKey(), entry.getValue());
        }

        return result;
    }

    public String getGpuMetrics(LoadTestRequest request, String setName) {
        //String setName = getSetName(request);
        if (setName == null) {
            return null;
        }
        String appkey = null;
        switch (request.getModelType()) {
            case "tf":
                appkey = "com.sankuai.algoplatform.modelserver";
                break;
            case "bge":
                appkey = "com.sankuai.algoplatform.dzmoderserver.pytorch";
                break;
            default:
                throw new IllegalArgumentException("无效的modelType: " + request.getModelType() + "，只支持tf或bge");
        }

        // 获取当前时间
        Date now = new Date();

        // 创建startTime（当前时间前3分钟）
        Calendar startCalendar = Calendar.getInstance();
        startCalendar.setTime(now);
        startCalendar.add(Calendar.MINUTE, -3);
        Date startTime = startCalendar.getTime();
        List<OctoNode> octoNodeStatus = octoNodeService.getOctoNodeStatus(appkey, setName, HostEnv.STAGING, OctoNodeStatus.NORMAL);
        // 从节点列表构建endpoints
        List<String> endpoints = new ArrayList<>();
        for (OctoNode node : octoNodeStatus) {
            // 使用IP字段构建endpoint格式
            if (node.getName() != null && !node.getName().isEmpty()) {
                endpoints.add(node.getName());
            }
        }

        // 如果没有找到有效的IP，记录并返回
        if (endpoints.isEmpty()) {
            log.warn("没有找到有效的endpoints，无法获取GPU指标");
            return null;
        }

        log.info("获取到的endpoints: {}", endpoints);
        ArrayList<String> metrics = new ArrayList<>();
        metrics.add("gpu.util.avg");
        RaptorData.Host.Root root = raptorDataService.queryHost(HostEnv.STAGING, startTime, now, metrics, endpoints, null);
        // 检查root是否为空
        if (root == null || root.getMetricTrends() == null) {
            log.warn("未获取到GPU指标数据");
            return "0.00%";
        }
        // 从metricTrends中获取"gpu.util.avg"指标数据
        RaptorData.Host.MetricTrend gpuUtilTrend = root.getMetricTrends().get("gpu.util.avg");
        if (gpuUtilTrend == null || gpuUtilTrend.getEndpointsEntries() == null) {
            log.warn("未获取到gpu.util.avg指标数据");
            return "0.00%";
        }

        // 遍历所有endpoints，计算平均利用率
        double totalUtilization = 0.0;
        int validEndpointCount = 0;

        for (Map.Entry<String, RaptorData.Host.EndpointEntry> endpointEntry : gpuUtilTrend.getEndpointsEntries().entrySet()) {
            RaptorData.Host.EndpointEntry entry = endpointEntry.getValue();
            if (entry != null && entry.getTimeTrend() != null && !entry.getTimeTrend().isEmpty()) {
                // 计算每个endpoint的平均利用率
                double endpointSum = 0.0;
                for (Double utilValue : entry.getTimeTrend().values()) {
                    if (utilValue != null) {
                        endpointSum += utilValue;
                    }
                }

                double endpointAvg = endpointSum / entry.getTimeTrend().size();
                totalUtilization += endpointAvg;
                validEndpointCount++;

                log.info("节点 {} 的平均GPU利用率: {}%", endpointEntry.getKey(), endpointAvg);
            }
        }

        // 计算所有endpoint的平均值
        double averageUtilization = validEndpointCount > 0 ? totalUtilization / validEndpointCount : 0.0;
        log.info("所有节点的平均GPU利用率: {}%", averageUtilization);

        // 返回格式化的结果
        return String.format("%.2f%%", averageUtilization);
    }

    /**
     * 将压测结果转换为表格格式
     *
     * @param originalResult 原始压测结果
     * @return 转换后的表格格式结果
     */
    private Map<String, Object> convertToTableFormat(Map<String, Object> originalResult) {
        Map<String, Object> result = new HashMap<>();

        // 修改列名 - 添加batch数列
        List<String> columns = Arrays.asList("batch数", "线程数", "平均响应时间", "tp99响应时间", "tp999响应时间", "qps", "qpm", "gpu平均利用率");
        result.put("column", columns);

        // 创建统一的数据列表
        List<List<String>> allRows = new ArrayList<>();

        // 遍历原始结果，查找所有batchSize开头的键
        for (Map.Entry<String, Object> entry : originalResult.entrySet()) {
            String key = entry.getKey();

            // 跳过conclusion键
            if ("conclusion".equals(key)) {
                continue;
            }

            // 处理batchSize开头的键
            if (key.startsWith("batchSize")) {
                // 从键名中提取batchSize值 (例如"batchSize1" -> "1")
                String batchSizeStr = key.substring("batchSize".length());

                Map<String, Object> batchData = (Map<String, Object>) entry.getValue();
                List<TransactionMetrics> allMetrics = (List<TransactionMetrics>) batchData.get("allMetrics");

                if (allMetrics != null) {
                    for (TransactionMetrics metrics : allMetrics) {
                        List<String> row = new ArrayList<>();
                        // 添加batch数作为第一列
                        row.add(batchSizeStr);
                        // 添加其他指标
                        row.add(String.valueOf(metrics.getThreads()));
                        row.add(String.valueOf(metrics.getAvg()));
                        row.add(String.valueOf(metrics.getTp99()));
                        row.add(String.valueOf(metrics.getTp999()));
                        row.add(String.valueOf(metrics.getQps()));
                        // 计算并添加QPM（每分钟查询数）
                        double qpm = metrics.getQps() * 60;
                        row.add(String.format("%.2f", qpm));
                        // 添加GPU利用率列
                        row.add(metrics.getGpuUtilAvg() != null ? metrics.getGpuUtilAvg() : "0.00%");

                        allRows.add(row);
                    }
                }
            }
        }

        // 添加统一的数据结果
        result.put("data", allRows);

        // 处理conclusion部分 - 如果原始结果中有conclusion
        if (originalResult.containsKey("conclusion")) {
            result.put("conclusion", originalResult.get("conclusion"));
        }

        return result;
    }


    private void shutdownCache(String setName) {
        //读取缓存配置
        PredictorCache234Config cache234Config = LionHelper.getPredictorCache234Config(EnvStatus.ST.getName(), setName);
        //关闭缓存开关
        cache234Config.setGlobalCacheSwitch(false);
        cache234Config.setCoupleCacheSwitch(false);
        cache234Config.setModelPredictCacheSwitch(false);
        LionHelper.setPredictorCache234Config(EnvStatus.ST.getName(), setName, JSON.toJSONString(cache234Config));
    }

}
