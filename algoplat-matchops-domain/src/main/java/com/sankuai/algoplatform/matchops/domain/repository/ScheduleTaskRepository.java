package com.sankuai.algoplatform.matchops.domain.repository;

import com.sankuai.algoplatform.matchops.domain.enums.ScheduleTaskInstanceStatus;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ScheduleTaskConfig;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.ScheduleTaskConfigExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.ScheduleTaskConfigMapper;
import com.sankuai.algoplatform.matchops.infrastructure.util.ZebraForceMasterUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Repository
public class ScheduleTaskRepository {
    @Resource
    private ScheduleTaskConfigMapper scheduleTaskConfigMapper;

    public List<ScheduleTaskConfig> queryValidScheduleTasks() {
        return ZebraForceMasterUtil.queryInMaster(() -> {
            ScheduleTaskConfigExample example = new ScheduleTaskConfigExample();
            example.createCriteria().andStatusEqualTo(0);
            return scheduleTaskConfigMapper.selectByExample(example);
        });
    }

    public List<ScheduleTaskConfig> queryValidScheduleTasksByIds(List<Long> taskIds) {
        if (CollectionUtils.isEmpty(taskIds)) {
            return Collections.emptyList();
        }
        return ZebraForceMasterUtil.queryInMaster(() -> {
            ScheduleTaskConfigExample example = new ScheduleTaskConfigExample();
            example.createCriteria().andIdIn(taskIds).andStatusEqualTo(0);
            return scheduleTaskConfigMapper.selectByExample(example);
        });
    }


}
