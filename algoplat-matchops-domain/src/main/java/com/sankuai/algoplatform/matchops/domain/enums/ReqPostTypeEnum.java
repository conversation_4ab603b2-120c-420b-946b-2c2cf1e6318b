package com.sankuai.algoplatform.matchops.domain.enums;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum ReqPostTypeEnum {

    UNKNOWN(-1, "未知类型"),

    XT(0, "XT任务"),

    HTTP(1, "http接口任务"),

    FUNCTION(2, "函数调用")
    ;

    private final int code;
    private final String name;

    ReqPostTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ReqPostTypeEnum getByCode(int code) {
        return Arrays.stream(ReqPostTypeEnum.values()).filter(statusEnum -> statusEnum.getCode() == code)
                .findFirst().orElse(UNKNOWN);
    }

}
