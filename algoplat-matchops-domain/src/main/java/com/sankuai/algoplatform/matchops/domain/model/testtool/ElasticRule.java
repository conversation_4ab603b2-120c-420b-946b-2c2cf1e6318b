package com.sankuai.algoplatform.matchops.domain.model.testtool;

import com.alibaba.fastjson.annotation.JSONField;
import com.sankuai.algoplatform.matchops.domain.enums.EnvStatus;
import lombok.Data;

@Data
public class ElasticRule {

    /**
     * 大模型服务名称
     */
    @JSONField(name = "serviceName")
    private String serviceName;

    /**
     * 服务appkey
     */
    @JSONField(name = "appkey")
    private String appkey;

    /**
     * mlp分组名称
     */
    @JSONField(name = "group")
    private String group;

    /**
     * 当前服务线上使用还是ST环境使用，如果是线上使用，缩容需要发送MCM
     */
    @JSONField(name = "env")
    private EnvStatus env; //参考EnvStatus

    /**
     * 最少保留实例数
     */
    @JSONField(name = "minRetainInstanceNum")
    private int minRetainInstanceNum;
}
