package com.sankuai.algoplatform.matchops.domain.enums;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/6/11
 */
public enum IndustryTypeEnum {
    DAOCAN("1", "到餐"),
    DAOZONG("2", "到综"),
    ZHUSU("3", "住宿"),
    ;

    private final String code;
    private final String desc;

    IndustryTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static IndustryTypeEnum getByCode(String code) {
        return Arrays.stream(IndustryTypeEnum.values()).filter(statusEnum -> Objects.equals(statusEnum.getCode(), code))
                .findFirst().orElse(null);
    }
}
