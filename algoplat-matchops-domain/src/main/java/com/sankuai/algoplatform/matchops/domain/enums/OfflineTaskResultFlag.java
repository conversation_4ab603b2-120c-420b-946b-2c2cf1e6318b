package com.sankuai.algoplatform.matchops.domain.enums;

public enum OfflineTaskResultFlag {
    INIT(0, "初始化"),
    CACHED(1, "已缓存"),
    NEED_PROCESS(2, "待处理");
    private Integer code;
    private String desc;
    OfflineTaskResultFlag(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    public Integer getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }
}
