package com.sankuai.algoplatform.matchops.domain.service.testtool;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableList;
import com.sankuai.algoplatform.matchops.infrastructure.util.StringReplacerUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class DocHandler {

    public static void replaceDocText(JSONObject obj, Map<String, String> replacements) {
        if (MapUtils.isEmpty(replacements)) {
            return;
        }
        JSONArray contentArr = obj.getJSONArray("content");
        replaceDocText(contentArr, replacements);
    }


    public static void replaceDocText(JSONArray contentArr, Map<String, String> replacements) {
        if (CollectionUtils.isEmpty(contentArr)) {
            return;
        }

        for (Object contentObj : contentArr) {
            JSONObject content = (JSONObject) contentObj;
            JSONArray nextContent = content.getJSONArray("content");
            if (CollectionUtils.isEmpty(nextContent)) {
                String txt = content.getString("text");
                if (StringUtils.contains(txt, "$")) {
                    String replace = StringReplacerUtil.replace(txt, replacements);
                    content.put("text", replace);
                }
            } else {
                replaceDocText(nextContent, replacements);
            }

        }
    }

    public static void replaceDocTable(JSONObject obj, Map<String, String> replacements) {
        if (MapUtils.isEmpty(replacements)) {
            return;
        }
        for (Map.Entry<String, String> entry : replacements.entrySet()) {
            JSONObject originObj = replaceTable(obj, entry.getKey());
            JSONObject replaceObj = JSONObject.parseObject(entry.getValue());
            if (Objects.nonNull(originObj)) {
                originObj.put("type", replaceObj.getString("type"));
                originObj.put("content", replaceObj.getJSONArray("content"));
            }
        }
    }

    public static JSONObject replaceTable(JSONObject obj, String search) {
        JSONArray contentArr = obj.getJSONArray("content");
        if (CollectionUtils.isEmpty(contentArr)) {
            return null;
        }

        for (Object contentObj : contentArr) {
            JSONObject content = (JSONObject) contentObj;
            JSONArray nextContent = content.getJSONArray("content");

            if (CollectionUtils.isEmpty(nextContent)) {
                String txt = content.getString("text");
                if (StringUtils.contains(txt, search)) {
                    return obj;
                }
            } else {
                JSONObject jsonObject = replaceTable(content, search);
                if (jsonObject != null) {
                    return jsonObject;
                }
            }

        }
        return null;
    }

    public static List<Map<String, String>> filterMapByTable(Map<String, String> inputMap) {
        Map<String, String> resultMap = new HashMap<>();
        Map<String, String> filterMap = new HashMap<>();

        for (Map.Entry<String, String> entry : inputMap.entrySet()) {
            String value = entry.getValue();
            if (value.contains("type") && value.contains("content")) {
                filterMap.put(entry.getKey(), value);
            } else {
                resultMap.put(entry.getKey(), value);
            }
        }
        return ImmutableList.of(resultMap, filterMap);
    }
}
