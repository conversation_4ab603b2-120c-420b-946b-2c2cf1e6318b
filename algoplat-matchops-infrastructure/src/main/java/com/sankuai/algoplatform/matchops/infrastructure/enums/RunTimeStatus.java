package com.sankuai.algoplatform.matchops.infrastructure.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
public enum RunTimeStatus {
    PYTHON37(0, "python3.7", "python3.7_common"),
    PYTHON39(1, "python3.11", "python3.11_common");

    private int code;
    private String name;
    private String desc;

    RunTimeStatus(int code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static RunTimeStatus fromValue(int value) {
        for (RunTimeStatus status : RunTimeStatus.values()) {
            if (status.getCode() == value) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown value: " + value);
    }

    public static RunTimeStatus fromDesc(String desc) {
        for (RunTimeStatus status : RunTimeStatus.values()) {
            if (StringUtils.equals(status.getDesc(), desc)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown value: " + desc);
    }

    public static RunTimeStatus fromName(String name) {
        for (RunTimeStatus status : RunTimeStatus.values()) {
            if (status.getName().equals(name)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown Name: " + name);
    }
}
