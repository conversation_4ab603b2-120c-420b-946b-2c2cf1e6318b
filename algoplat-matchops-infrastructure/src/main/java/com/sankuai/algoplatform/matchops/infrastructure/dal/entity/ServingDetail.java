package com.sankuai.algoplatform.matchops.infrastructure.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ServingDetail {
    private Boolean groupExist;        // 分组是否存在
    private Boolean modelDeployed;     // 模型是否部署
    private Integer instanceCount;      // 当前实例数
    private Integer groupId;           // 分组ID
    private Integer servingId;         // 服务ID
}