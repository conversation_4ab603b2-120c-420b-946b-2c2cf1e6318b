package com.sankuai.algoplatform.matchops.infrastructure.util;

import io.netty.util.internal.StringUtil;

import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class TestSet2PromptConverter implements Function<String, String> {

    // (.*?): '.'匹配任意字符, *? 懒惰匹配, 即尽可能少的匹配字符
    // (.*): 贪婪匹配
    // \\s*: 零或多个空格; \\d+: 一或多个数字
    private final Pattern pattern = Pattern.compile("USER:(.*?)ASSISTANT:");

    @Override
    public String apply(String s) {
        if(StringUtil.isNullOrEmpty(s)) return "";

        Matcher matcher = pattern.matcher(s);
        if(matcher.find()){
            return matcher.group(1);
        }else
            return s;
    }

}
