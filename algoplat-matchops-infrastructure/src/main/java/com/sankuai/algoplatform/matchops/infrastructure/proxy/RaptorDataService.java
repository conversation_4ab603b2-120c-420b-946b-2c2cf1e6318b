package com.sankuai.algoplatform.matchops.infrastructure.proxy;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import com.meituan.mdp.boot.starter.mdpcache.anno.Cached;
import com.meituan.mdp.boot.starter.mdpcache.core.CacheType;
import com.sankuai.algoplatform.matchops.infrastructure.config.LionConfig;
import com.sankuai.algoplatform.matchops.infrastructure.model.RaptorData;
import com.sankuai.algoplatform.matchops.infrastructure.util.DateUtil;
import com.sankuai.algoplatform.matchops.infrastructure.util.HttpUtil;
import com.sankuai.algoplatform.matchops.infrastructure.util.RetryUtil;
import com.sankuai.inf.octo.mns.model.HostEnv;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class RaptorDataService {

    /**
     * transaction指标
     *
     * @param env         环境
     * @param date        时间,20241204150000格式
     * @param otherParams 其它参数
     * @return data
     */
    @Cached(name = "queryTransactionHourly", key = "#env+#appkey+#date+#type+#group", cacheType = CacheType.LOCAL, timeUnit = TimeUnit.MINUTES, expire = 1, localLimit = 100)
    public RaptorData.Transaction.Root queryTransactionHourly(HostEnv env, String appkey, Date date, String type, String group, Map<String, String> otherParams) {
        //url: https://raptor-st.mws.sankuai.com/cat/r/t/hourly?reportType=hour&date=2024120415&startDate=20241204150000&endDate=20241204155900&r=16539&ip=All&group=&domain=com.sankuai.algoplatform.predictor&type=KmsManage
        try {
            return RetryUtil.retry(() -> {
                String url = String.format("http://%s/%s", getRaptorUrl(env), "cat/r/t/hourly");
                Map<String, String> params = new LinkedHashMap<>();
                params.put("reportType", "hour");
                params.put("date", DateUtil.toDateTimeString_yyyyMMddHH(date));
                params.put("startDate", DateUtil.toDateTimeShortString(date));
                params.put("endDate", DateUtil.toDateTimeString_yyyyMMddHH(date) + "5900");
                //params.put("ip", group);
                params.put("group", group);
                params.put("domain", appkey);
                Optional.ofNullable(type).ifPresent(t -> params.put("type", t));
                if (MapUtils.isNotEmpty(otherParams)) {
                    params.putAll(otherParams);
                }
                if (!params.containsKey("ip")) {
                    params.put("ip", group);
                }
                return queryRaptor(url, params, RaptorData.Transaction.Root.class);
            }, (result, ex) -> result == null || ex != null, 6, 10 * 1000);
        } catch (Exception e) {
            Cat.logError(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    public RaptorData.Transaction.Root queryTransactionHourlyGraphs(HostEnv env, String appkey, Date date, String type, String group, String name, Map<String, String> otherParams) {
        //url: https://raptor-st.mws.sankuai.com/cat/r/t/hourlyGraphs?reportType=hour&date=2024120611&startDate=20241206110000&endDate=20241206115900&r=55094&ip=default_cell&group=default_cell&domain=com.sankuai.algoplatform.predictor&type=OctoCall&ShowViewTab=summary&fold=Octo&query=&showId=com.sankuai.algoplatform.modelserver%3AOnlineService.GetResponse&name=com.sankuai.algoplatform.modelserver%3AOnlineService.GetResponse&isSecond=false
        String url = String.format("http://%s/%s", getRaptorUrl(env), "cat/r/t/hourlyGraphs");
        Map<String, String> params = new LinkedHashMap<>();
        params.put("reportType", "hour");
        params.put("date", DateUtil.toDateTimeString_yyyyMMddHH(date));
        params.put("startDate", DateUtil.toDateTimeShortString(date));
        params.put("endDate", DateUtil.toDateTimeString_yyyyMMddHH(date) + "5900");
        //params.put("ip", group);
        params.put("group", group);
        params.put("domain", appkey);
        Optional.ofNullable(type).ifPresent(t -> params.put("type", t));
        params.put("ShowViewTab", "summary");
        params.put("showId", name);
        params.put("name", name);
        params.put("isSecond", "false");
        if (MapUtils.isNotEmpty(otherParams)) {
            params.putAll(otherParams);
        }
        if (!params.containsKey("ip")) {
            params.put("ip", group);
        }
        return queryRaptor(url, params, RaptorData.Transaction.Root.class);
    }

    /**
     * event指标
     *
     * @param env         环境
     * @param appkey      appkey
     * @param date        时间,20241204150000格式
     * @param type        type
     * @param otherParams 其它参数
     * @return data
     */
    public RaptorData.Event.Root queryEventHourly(HostEnv env, String appkey, Date date, String type, String group, Map<String, String> otherParams) {
        // url: https://raptor-st.mws.sankuai.com/cat/r/e/hourly?domain=com.sankuai.algoplatform.predictor&ip=All&reportType=hour&date=2024120415&type=SQL.Database
        try {
            return RetryUtil.retry(() -> {
                String url = String.format("http://%s/%s", getRaptorUrl(env), "cat/r/e/hourly");
                Map<String, String> params = new LinkedHashMap<>();
                params.put("domain", appkey);
                params.put("ip", group);
                params.put("group", group);
                params.put("reportType", "hour");
                params.put("date", DateUtil.toDateTimeString_yyyyMMddHH(date));
                Optional.ofNullable(type).ifPresent(t -> params.put("type", t));
                if (MapUtils.isNotEmpty(otherParams)) {
                    params.putAll(otherParams);
                }
                return queryRaptor(url, params, RaptorData.Event.Root.class);
            }, (result, ex) -> result == null || ex != null, 6, 10 * 1000);
        } catch (Exception e) {
            Cat.logError(e.getMessage(), e);
            throw new RuntimeException(e);
        }

    }

    /**
     * problem指标
     *
     * @param env
     * @param date
     * @param otherParams 其他参数
     * @return
     */
    public RaptorData.Problem.Root queryProblemHourly(HostEnv env, String appkey, Date date, Map<String, String> otherParams) {
        // url:https://raptor-st.mws.sankuai.com/cat/r/p/hourly?reportType=hour&date=2024120411&startDate=20241204110000&endDate=20241204115900&r=94504&ip=All&group=&domain=com.sankuai.algoplatform.predictor&type=error&urlThreshold=5000&serviceThreshold=5000&callThreshold=5000&sqlThreshold=5000&cacheThreshold=500&mqThreshold=3000&bladeSqlThreshold=5000
        try {
            return RetryUtil.retry(() -> {
                String url = String.format("http://%s/%s", getRaptorUrl(env), "cat/r/p/hourly");
                Map<String, String> params = new LinkedHashMap<>();
                params.put("reportType", "hour");
                params.put("date", DateUtil.toDateTimeString_yyyyMMddHH(date));
                params.put("startDate", DateUtil.toDateTimeShortString(date));
                params.put("endDate", DateUtil.toDateTimeString_yyyyMMddHH(date) + "5900");
                params.put("ip", "All");
                params.put("group", "");
                params.put("domain", appkey);
                params.put("type", "error");
                params.put("urlThreshold", "5000");
                params.put("serviceThreshold", "5000");
                params.put("callThreshold", "5000");
                params.put("sqlThreshold", "5000");
                params.put("cacheThreshold", "500");
                params.put("mqThreshold", "3000");
                params.put("bladeSqlThreshold", "5000");
                if (MapUtils.isNotEmpty(otherParams)) {
                    params.putAll(otherParams);
                }
                return queryRaptor(url, params, RaptorData.Problem.Root.class);
            }, (result, ex) -> result == null || ex != null, 6, 10 * 1000);
        } catch (Exception e) {
            Cat.logError(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * business指标
     *
     * @param env
     * @param appkey
     * @param startDate
     * @param endDate
     * @param item
     * @param otherParams
     * @return data
     */
    public RaptorData.Business.Root queryBusiness(HostEnv env, String appkey, Date startDate, Date endDate, String item, Map<String, String> otherParams) {
        // url: https://raptor-st.mws.sankuai.com/cat/r/business/businessMinuteData?type=COUNT&start=202412040800&end=202412041059&domain=com.sankuai.algoplatform.predictor&item=BridgeRunningTaskCnt&tagValues=&isSecond=false&showHistoryData=false
        try {
            return RetryUtil.retry(() -> {
                String url = String.format("http://%s/%s", getRaptorUrl(env), "cat/r/business/businessMinuteData");
                Map<String, String> params = new LinkedHashMap<>();
                params.put("type", "COUNT");
                params.put("start", DateUtil.toDateTimeString_yyyyMMddHHmm(startDate));
                params.put("end", DateUtil.toDateTimeString_yyyyMMddHHmm(endDate));
                params.put("domain", appkey);
                Optional.ofNullable(item).ifPresent(i -> params.put("item", i));
                params.put("tagValues", "");
                params.put("isSecond", "false");
                params.put("showHistoryData", "false");
                if (MapUtils.isNotEmpty(otherParams)) {
                    params.putAll(otherParams);
                }
                return queryRaptor(url, params, RaptorData.Business.Root.class);
            }, (result, ex) -> result == null || ex != null, 6, 10 * 1000);
        } catch (Exception e) {
            Cat.logError(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * host指标
     *
     * @param env
     * @param startDate
     * @param endDate
     * @param metrics
     * @param endpoints
     * @param otherParams
     * @return
     */
    public RaptorData.Host.Root queryHost(HostEnv env, Date startDate, Date endDate, List<String> metrics, List<String> endpoints, Map<String, String> otherParams) {

        try {
            return RetryUtil.retry(() -> {
                Map<String, String> cfg = Objects.requireNonNull(JSON.parseObject(LionConfig.RAPTOR_DATA_HOST_URL, new TypeReference<HashMap<String, Map<String, String>>>() {
                })).get(env.name());
                String host = Objects.requireNonNull(cfg.get("url"));
                String token = Objects.requireNonNull(cfg.get("token"));
                String url = String.format("http://%s/%s", host, "/raptor/openapi/hosts/minute");
                url += "?token=" + token;
                Map<String, Object> params = new LinkedHashMap<>();
                params.put("startDate", DateUtil.toDateTimeString_yyyyMMddHHmm(startDate));
                params.put("endDate", DateUtil.toDateTimeString_yyyyMMddHHmm(endDate));
                params.put("metrics", metrics);
                params.put("endpoints", endpoints);
                params.put("sample", "AVG");
                if (MapUtils.isNotEmpty(otherParams)) {
                    params.putAll(otherParams);
                }
                String resp = HttpUtil.httpPostJson(url, JSON.toJSONString(params), null);
                if (StringUtils.isBlank(resp)) {
                    throw new IllegalStateException("query raptor host data failed, resp is blank");
                }
                JSONObject json = JSON.parseObject(resp);
                if (!Integer.valueOf(10000).equals(Optional.ofNullable(json.get("code")).map(m -> ((Number) m).intValue()).orElse(null))) {
                    throw new IllegalStateException(String.format("query raptor data failed, code:%s, message:%s", json.get("code"), json.get("message")));
                }
                return json.getObject("result", RaptorData.Host.Root.class);
            }, (result, ex) -> result == null || ex != null, 6, 10 * 1000);
        } catch (Exception e) {
            Cat.logError(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    private <T> T queryRaptor(String url, Map<String, String> params, Class<T> clazz) {
        String resp = HttpUtil.httpGet(url, params, null);
        if (StringUtils.isBlank(resp)) {
            throw new IllegalStateException("query raptor host data failed, resp is blank");
        }
        JSONObject json = JSON.parseObject(resp);
        if (!Integer.valueOf(200).equals(Optional.ofNullable(json.get("code")).map(m -> ((Number) m).intValue()).orElse(null))) {
            throw new IllegalStateException(String.format("query raptor data failed, code:%s, message:%s", json.get("code"), json.get("message")));
        }
        return json.getObject("data", clazz);
    }

    private String getRaptorUrl(HostEnv env) {
        return Objects.requireNonNull(LionConfig.RAPTOR_DATA_URL.get(env.name()));
    }

}
