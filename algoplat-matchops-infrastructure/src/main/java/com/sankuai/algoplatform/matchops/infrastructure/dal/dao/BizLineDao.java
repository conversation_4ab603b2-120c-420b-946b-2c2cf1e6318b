package com.sankuai.algoplatform.matchops.infrastructure.dal.dao;

import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.BizLine;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.BizLineExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.BizLineMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
@Service
public class BizLineDao {
    @Autowired
    BizLineMapper bizLineMapper;

    public List<BizLine> selectAll() {
        BizLineExample example = new BizLineExample();
        return bizLineMapper.selectByExample(example);
    }

    public List<BizLine> selectAllNotDelete() {
        BizLineExample example = new BizLineExample();
        example.createCriteria().andIsDelEqualTo(false);
        return bizLineMapper.selectByExample(example);
    }

    public BizLine selectById(Long id) {
        BizLineExample example = new BizLineExample();
        example.createCriteria().andIdEqualTo(id);
        return bizLineMapper.selectByExample(example).get(0);
    }

    public BizLine selectByBizName(String bizName) {
        BizLineExample example = new BizLineExample();
        example.createCriteria().andBizLineNameEqualTo(bizName);
        return bizLineMapper.selectByExample(example).get(0);
    }

    public void insert(BizLine bizLine) {
        bizLineMapper.insertSelective(bizLine);
    }

    public void update(BizLine bizLine) {
        bizLineMapper.updateByPrimaryKeySelective(bizLine);
    }

    public void softDelete(Long id) {
        BizLine bizLine = selectById(id);
        bizLine.setIsDel(true);
        update(bizLine);
    }

    public void delete(Long id) {
        bizLineMapper.deleteByPrimaryKey(id);
    }
}
