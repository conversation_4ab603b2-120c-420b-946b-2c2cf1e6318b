package com.sankuai.algoplatform.matchops.infrastructure.enums;

public enum FridayInstanceStatus {
    SUBMITTING("提交中"),
    PREPARING("实例准备中"),
    RUNNING("运行中"), // 稳定状态
    FAILED("失败"),
    SUCCESS("成功"),
    UNKNOWN("未知"),
    DELETING("下线中"),
    DELETED("已下线");

    private final String name;

    FridayInstanceStatus(String description) {
        this.name = description;
    }

    public String getName() {
        return name;
    }

    public static FridayInstanceStatus getByName(String text) {
        for (FridayInstanceStatus b : FridayInstanceStatus.values()) {
            if (b.getName().equalsIgnoreCase(text)) {
                return b;
            }
        }
        return null;
    }
}
