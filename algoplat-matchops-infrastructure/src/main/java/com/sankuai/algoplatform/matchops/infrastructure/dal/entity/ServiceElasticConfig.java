package com.sankuai.algoplatform.matchops.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: service_elastic_config
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ServiceElasticConfig {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: biz_line_id
     *   说明: 业务线ID
     */
    private Long bizLineId;

    /**
     *   字段: resource_type
     *   说明: 资源类型 0octo 1mlp 2friday
     */
    private Integer resourceType;

    /**
     *   字段: name
     */
    private String name;

    /**
     *   字段: deploy_config
     *   说明: 服务部署配置
     */
    private String deployConfig;

    /**
     *   字段: elastic_rule
     *   说明: 实例弹性规则
     */
    private String elasticRule;

    /**
     *   字段: owner
     *   说明: 负责人
     */
    private String owner;

    /**
     *   字段: create_time
     *   说明: 创建时间
     */
    private Date createTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}