package com.sankuai.algoplatform.matchops.infrastructure.util;

import com.google.common.collect.ImmutableList;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.lang.reflect.Array;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ParamUtil {

    public static Pair<Boolean, String> checkNotNull(Map<String, Object> paramMap) {
        for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
            Object obj = entry.getValue();
            if (isNullOrEmpty(obj)) {
                String info = String.format("参数 %s 不能为空", entry.getKey());
                return Pair.of(false, info);
            }
        }
        return Pair.of(true, "");
    }


    public static boolean isNullOrEmpty(Object obj) {
        if (obj == null) {
            return true;
        } else if (obj instanceof String) {
            return ((String) obj).isEmpty();
        } else if (obj instanceof List) {
            return CollectionUtils.isEmpty(((List<?>) obj).stream().
                    filter(o -> o != null && !StringUtils.isEmpty(o.toString()))
                    .collect(Collectors.toList()));
        } else if (obj.getClass().isArray()) {
            return Array.getLength(obj) == 0;
        } else if (obj instanceof Map) {
            return MapUtils.isEmpty((Map<?, ?>) obj);
        } else if (obj instanceof Integer || obj instanceof Double || obj instanceof Long) {
            return obj.equals(0);
        } else {
            return false;
        }
    }

}
