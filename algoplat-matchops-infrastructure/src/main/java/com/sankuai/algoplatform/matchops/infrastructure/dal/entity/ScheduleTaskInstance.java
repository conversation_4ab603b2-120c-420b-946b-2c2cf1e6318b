package com.sankuai.algoplatform.matchops.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: schedule_task_instance
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScheduleTaskInstance {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: task_binding_id
     *   说明: 绑定任务配置id
     */
    private Long taskBindingId;

    /**
     *   字段: instance_key
     *   说明: 实例key
     */
    private String instanceKey;

    /**
     *   字段: status
     *   说明: 执行状态
     */
    private Integer status;

    /**
     *   字段: message
     *   说明: 关键信息
     */
    private String message;

    /**
     *   字段: action
     *   说明: 执行动作
     */
    private String action;

    /**
     *   字段: add_time
     *   说明: 添加时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}