package com.sankuai.algoplatform.matchops.infrastructure.config;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/5/19
 */

import com.sankuai.meituan.org.opensdk.client.RemoteServiceClient;
import com.sankuai.meituan.org.opensdk.service.*;
import com.sankuai.meituan.org.opensdk.service.impl.*;
import com.sankuai.meituan.org.queryservice.domain.param.DataScope;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;

@Configuration
public class OpenSdkConfig {

    private final static String REMOTE_APP_KEY = "com.sankuai.hrmdm.org.gateway";

    @Value("${org.appId}")
    private String CLIENT_APP_KEY ;
    private final static Integer APP_TENANT_ID = 1;//如需查询其它租户数据，请设置成其它租户ID
    private final static String APP_SOURCE = "MT";//设置租户下对应的source.如果设置为"ALL"，则查tenantId下所有sources。


    @Bean
    public RemoteServiceClient orgRemoteServiceClient() throws Exception {

        // 设置App默认的数据访问范围。如下设置，App默认所有的请求是针对美团租户下“MT”数据域的ORG数据
        DataScope dataScope = new DataScope();
        dataScope.setTenantId(APP_TENANT_ID);
        dataScope.setSources(Arrays.asList(APP_SOURCE));
        //如果不传超时时间我们默认60000，传了，就按照你们的超时时间为准  timeout 为时间设置
        //public RemoteServiceClient(String orgAppKey, String appSecret, String remoteAppKey, DataScope dataScope, String serverIpPorts, Integer timeout)
        String APP_SECRET = KmsConfigs.getKmsValueByKey("org.appSecret");
        RemoteServiceClient remoteServiceClient = new RemoteServiceClient(CLIENT_APP_KEY, APP_SECRET, REMOTE_APP_KEY, dataScope);
        return remoteServiceClient;
    }

//    @Bean
//    public CompService compService(RemoteServiceClient orgRemoteServiceClient) {
//        CompServiceImpl compService = new CompServiceImpl(remoteServiceClient);
//        return compService;
//    }
//
//    @Bean
//    public DictService dictService(RemoteServiceClient orgRemoteServiceClient) {
//        DictServiceImpl dictService = new DictServiceImpl(remoteServiceClient);
//        return dictService;
//    }

    @Bean
    public EmpService empService(RemoteServiceClient orgRemoteServiceClient) {
        EmpServiceImpl empService = new EmpServiceImpl(orgRemoteServiceClient);
        return empService;
    }

//    @Bean
//    public JobCodeService jobCodeService(RemoteServiceClient orgRemoteServiceClient) {
//        JobCodeServiceImpl jobCodeService = new JobCodeServiceImpl(orgRemoteServiceClient);
//        return jobCodeService;
//    }
//
//    @Bean
//    public OrgService orgService(RemoteServiceClient orgRemoteServiceClient) {
//        OrgServiceImpl orgService = new OrgServiceImpl(orgRemoteServiceClient);
//        return orgService;
//    }
//
//    @Bean
//    public SiteCodeService siteCodeService(RemoteServiceClient orgRemoteServiceClient) {
//        SiteCodeServiceImpl siteCodeService = new SiteCodeServiceImpl(orgRemoteServiceClient);
//        return siteCodeService;
//    }
}
