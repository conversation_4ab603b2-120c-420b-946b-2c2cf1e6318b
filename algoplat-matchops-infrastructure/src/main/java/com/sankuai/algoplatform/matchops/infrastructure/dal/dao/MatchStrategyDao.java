package com.sankuai.algoplatform.matchops.infrastructure.dal.dao;

import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.MatchStrategy;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.MatchStrategyExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.MatchStrategyMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service

public class MatchStrategyDao {

    @Autowired
    private MatchStrategyMapper matchStrategyMapper;

    public MatchStrategy selectById(Long id) {
        return matchStrategyMapper.selectByPrimaryKey(id);
    }

    public List<MatchStrategy> selectByIds(List<Long> ids) {
        MatchStrategyExample example = new MatchStrategyExample();
        example.createCriteria().andIdIn(ids);
        return matchStrategyMapper.selectByExample(example);
    }

    public List<MatchStrategy> selectAll() {
        MatchStrategyExample example = new MatchStrategyExample();
        return matchStrategyMapper.selectByExample(example);
    }

    public List<MatchStrategy> selectByBizLine(Long bizLineId) {
        MatchStrategyExample example = new MatchStrategyExample();
        example.createCriteria().andBizLineIdEqualTo(bizLineId).andIsDelEqualTo(false);
        return matchStrategyMapper.selectByExample(example);
    }

    public List<MatchStrategy> selectByStatus(List<Integer> status) {
        MatchStrategyExample example = new MatchStrategyExample();
        example.createCriteria().andStatusIn(status);
        return matchStrategyMapper.selectByExample(example);
    }

    public List<MatchStrategy> selectByOwnerId(String owner) {
        MatchStrategyExample example = new MatchStrategyExample();
        example.createCriteria().andOwnerEqualTo(owner);
        return matchStrategyMapper.selectByExample(example);
    }

    public List<MatchStrategy> selectByName(String name) {
        MatchStrategyExample example = new MatchStrategyExample();
        example.createCriteria().andNameEqualTo(name).andIsDelEqualTo(false);
        example.setOrderByClause("id desc");
        return matchStrategyMapper.selectByExample(example);
    }

    public Long insert(MatchStrategy matchStrategy) {
        matchStrategyMapper.insertSelective(matchStrategy);
        return matchStrategy.getId();
    }

    public int update(MatchStrategy matchStrategy) {
        return matchStrategyMapper.updateByPrimaryKeySelective(matchStrategy);
    }

    public int updateStatus(Long matchStrategyId, int status) {
        MatchStrategy matchStrategy = new MatchStrategy();
        matchStrategy.setId(matchStrategyId);
        matchStrategy.setStatus(status);
        return matchStrategyMapper.updateByPrimaryKeySelective(matchStrategy);
    }

    public int delete(Long id) {
        return matchStrategyMapper.deleteByPrimaryKey(id);
    }

    public List<MatchStrategy> selectByPageAndBizLineId(int page, int pageSize, long bizLineId) {
        MatchStrategyExample example = new MatchStrategyExample();
        example.limit((page - 1) * pageSize, pageSize);
        example.createCriteria().andBizLineIdEqualTo(bizLineId).andIsDelEqualTo(false);
        return matchStrategyMapper.selectByExample(example);
    }

    public Long selectCountByBizLineId(long bizLineId) {
        MatchStrategyExample example = new MatchStrategyExample();
        example.createCriteria().andBizLineIdEqualTo(bizLineId).andIsDelEqualTo(false);
        return matchStrategyMapper.countByExample(example);
    }

    public int softDelete(Long id, String owner) {
        MatchStrategy matchStrategy = selectById(id);
        if (matchStrategy != null) {
            matchStrategy.setIsDel(true);
            matchStrategy.setOwner(owner);
            return update(matchStrategy);
        }
        return 0;
    }

    public List<MatchStrategy> findByAlgoBizCode(String algoBizCode) {
        return matchStrategyMapper.findByAlgoBizCode(algoBizCode);
    }

    public List<MatchStrategy> findByLlmBizCode(String llmBizCode) {
        return matchStrategyMapper.findByLlmBizCode(llmBizCode);
    }
}
