package com.sankuai.algoplatform.matchops.infrastructure.util;

import org.apache.commons.cli.BasicParser;
import org.apache.commons.cli.CommandLine;
import org.apache.commons.cli.Options;
import org.apache.commons.cli.ParseException;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ParserUtil {

    public static Map<String, String> getValue(String[] args) {
        Map<String, String> argumentMap = new HashMap<>();
        String key = null;

        for (String arg : args) {
            if (arg.startsWith("-")) {
                if (key != null) {
                    argumentMap.put(key, "");
                }
                key = arg.substring(1); // 去掉前面的 '-'
            } else {
                if (key != null) {
                    argumentMap.put(key, arg);
                    key = null;
                }
            }
        }

        if (key != null) {
            argumentMap.put(key, "");
        }

        return argumentMap;
    }

    public static Map<String, String> getValue(String[] args, List<String> keys) {
        Options options = new Options();
        for (String key : keys) {
            options.addOption(key, true, "");
        }
        BasicParser parser = new BasicParser();
        Map<String, String> result = new HashMap<>();
        for (String key : keys) {
            try {
                CommandLine commandLine = parser.parse(options, args);
                result.put(key, commandLine.getOptionValue(key));
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        }
        return result;
    }

    public static String[] splitCommandLine(String commandLine) {
        List<String> tokens = new ArrayList<>();
        Pattern pattern = Pattern.compile("('[^']*'|\"[^\"]*\"|\\S+)");
        Matcher matcher = pattern.matcher(commandLine);

        while (matcher.find()) {
            String token = matcher.group(1);
            tokens.add(token.replaceAll("^['\"]|['\"]$", "")); // Remove surrounding quotes if present
        }

        return tokens.toArray(new String[0]);
    }

    /**
     * 将查询字符串转换为Map
     *
     * @param queryString 查询字符串
     * @return 包含键值对的Map
     */
    public static Map<String, String> queryStringToMap(String queryString) {
        Map<String, String> map = new HashMap<>();

        // 检查输入字符串是否为空
        if (queryString == null || queryString.isEmpty()) {
            return map;
        }

        // 拆分字符串为键值对
        String[] pairs = queryString.split("&");
        for (String pair : pairs) {
            // 拆分键和值
            String[] keyValue = pair.split("=", 2); // 限制为2个，避免值中有'='号导致的问题
            if (keyValue.length == 2) {
                map.put(keyValue[0], keyValue[1]);
            } else if (keyValue.length == 1) {
                // 处理没有值的情况
                map.put(keyValue[0], "");
            }
        }

        return map;
    }

    public static String mapToCmdArgs(Map<String, String> map) {
        // 构建命令行参数
        StringBuilder cmdArgs = new StringBuilder();
        for (String key : map.keySet()) {
            cmdArgs.append("-").append(key).append(" ").append(map.get(key)).append(" ");
        }

        // 去除末尾多余的空格
        return cmdArgs.toString().trim();
    }

//    public static void main(String[] args) {
//        String cmdLine = "-cookie1 xxxxxx -requestBody '{\"bizCode\":\"abc\", \"req\":\"xxxx\"}' -sqlParam 'select a,b from abc'";
//        String[] param = splitCommandLine(cmdLine);
//        Map<String, String> value = getValue(param);
//        System.out.println(value);
//
//        cmdLine = "-version 17098883 -cd 2024-10-01,2024-10-02";
//        param = splitCommandLine(cmdLine);
//        value = getValue(param, ImmutableList.of("version", "cd"));
//        System.out.println(value);
//
//        System.out.println(queryStringToMap("type=octo&appkey=xxxxx&set=xxx"));
//    }

}