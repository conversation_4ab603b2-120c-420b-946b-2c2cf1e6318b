package com.sankuai.algoplatform.matchops.infrastructure.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FridayModelDeployRequest {
    /**
     * 模型部署名称
     */
    private String modelDeployName;
    
    /**
     * 所需实例数量
     */
    private Integer requiredInstanceCount;
    
    /**
     * 模型基础名称
     */
    private String modelBaseName;
    
    /**
     * GPU类型
     */
    @Builder.Default
    private String gpuType = "L40-48G";
    

    /**
     * 每个实例的GPU数量
     */
    private Integer gpuPerInstance;
    
    /**
     * 训练方法
     */
    private String trainMethod;
    
    /**
     * 训练任务ID
     */
    private String trainingTaskId;
    
    /**
     * 项目组名称
     */
    @Builder.Default
    private String groupName = "al-zb-ddpt";
    
    /**
     * 模型描述
     */
    @Builder.Default
    private String description = "modelDeployName";


    /**
     * MIS账号
     */
    private String misId;
    
    /**
     * 模型参数
     */
    private Map<String, String> modelParam;
}