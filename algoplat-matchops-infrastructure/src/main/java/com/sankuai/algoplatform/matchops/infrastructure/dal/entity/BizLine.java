package com.sankuai.algoplatform.matchops.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: biz_line
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BizLine {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: biz_line_name
     *   说明: 业务线名称
     */
    private String bizLineName;

    /**
     *   字段: is_del
     *   说明: 是否删除
     */
    private Boolean isDel;

    /**
     *   字段: create_time
     *   说明: 创建时间
     */
    private Date createTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}