package com.sankuai.algoplatform.matchops.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: schedule_service_config
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScheduleServiceConfig {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: appkey
     *   说明: appkey
     */
    private String appkey;

    /**
     *   字段: group_name
     *   说明: 分组名称
     */
    private String groupName;

    /**
     *   字段: schedule_type
     *   说明: 服务类型（Friday、MLP）
     */
    private String scheduleType;

    /**
     *   字段: gpu_usage_num
     *   说明: 单实例gpu数
     */
    private Integer gpuUsageNum;

    /**
     *   字段: inst_max_num
     *   说明: 配额上限
     */
    private Integer instMaxNum;

    /**
     *   字段: inst_min_num
     *   说明: 最小资源数
     */
    private Integer instMinNum;

    /**
     *   字段: operator_mis
     *   说明: 操作人id
     */
    private String operatorMis;

    /**
     * 字段: extra
     * 说明 扩展信息：用于MLP相关的set、gpu数量、cpu数量、内存大小
     */
    private String extra;

    /**
     *   字段: status
     *   说明: 状态（0：正常，-1禁用）
     */
    private Integer status;

    /**
     *   字段: add_time
     *   说明: 添加时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}