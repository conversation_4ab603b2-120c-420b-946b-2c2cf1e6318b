package com.sankuai.algoplatform.matchops.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: biz_llmpredict_prompt_template
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BizLlmpredictPromptTemplate {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: note
     *   说明: 描述
     */
    private String note;

    /**
     *   字段: status
     *   说明: 状态，1：启用，0：关闭
     */
    private Boolean status;

    /**
     *   字段: owner
     *   说明: 编辑此模板的人员名字
     */
    private String owner;

    /**
     *   字段: add_time
     *   说明: 创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: prompt_template
     *   说明: 提示模板内容，可能包含长达6000字符以上的字符串
     */
    private String promptTemplate;
}