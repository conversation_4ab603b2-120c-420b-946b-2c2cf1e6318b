package com.sankuai.algoplatform.matchops.infrastructure.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.time.Duration;
import java.util.List;
import java.util.Map;

public class RaptorData {

    public static class Transaction {
        @Data
        public static class Root {
            private List<Host> hosts;
            private List<String> groupIps;
            private List<Report> report;
            private Graphs graphs;
            private Map<String, Object> foldItems;
            private Boolean second;
            private Boolean showLoad;
        }

        @Data
        public static class Host {
            private String ip;
            private String hostname;
            private String set;
        }

        @Data
        public static class Report {
            private String ip;
            private String hostname;
            private String type;
            private String name;
            private Long count;
            private Long fails;
            private Double failPercent;
            private String successMessageUrl;
            private String longestMessageUrl;
            private Double sum;
            private Double avg;
            private Double max;
            private Double tp50;
            private Double tp90;
            private Double tp95;
            private Double tp99;
            private Double tp999;
            private Double tp9999;
            private Double qps;
            private Double qpm;
            private String foldedItemId;
            private String set;
            private int cost;
        }

        @Data
        public static class Graphs {
            private DistributionGraph distributionGraph;
            private DistributionGraph hit;//请求个数
            private DistributionGraph avg;//平均个数
            private DistributionGraph successRate;//成功率
            private DistributionGraph max;//最大耗时
            private DistributionGraph failure;//失败个数
            private DistributionGraph tpLine;//耗时分布
        }

        @Data
        public static class DistributionGraph {
            private String type;
            private String title;
            private List<String> columns;
            private List<Row> rows;
            private Map<String, Object> labelMap;
            private List<String> xaxisNames;
            private List<String> yaxisNames;
        }

        @Data
        public static class Row {
            private String name;
            private Long value;
            private String xScale;
            @JSONField(name = "Hits")
            private Double Hits;
            @JSONField(name = "Avg")
            private Double Avg;
            @JSONField(name = "Max")
            private Double Max;
            private Double tp50;
            private Double tp90;
            private Double tp99;
            private Double tp999;
            private Double tp9999;
            private Double tp95;
            private Double Failures;
            private Double SuccessRate;


        }
    }

    public static class Event {
        @Data
        public static class Root {
            private List<Host> hosts;
            private List<String> groupIps;
            private List<Report> report;
            private Graphs graphs;
            private Map<String, Object> foldItems;
            private Boolean second;
            private Boolean showLoad;
        }

        @Data
        public static class Host {
            private String ip;
            private String hostname;
            private String set;
        }

        @Data
        public static class Report {
            private String ip;
            private String hostname;
            private String type;
            private String name;
            private Long count;
            private Long fails;
            private Double failPercent;
            private String successMessageUrl;
            private Double qps;
            private String foldedItemId;
            private String set;
        }

        @Data
        public static class Graphs {
            private DistributionGraph distributionGraph;
        }

        @Data
        public static class DistributionGraph {
            private String type;
            private String title;
            private List<String> columns;
            private List<Row> rows;
            private Map<String, Object> labelMap;
            private List<String> xaxisNames;
            private List<String> yaxisNames;
        }

        @Data
        public static class Row {
            private String name;
            private Double value;
        }
    }

    public static class Problem {
        @Data
        public static class Root {
            private List<Host> hosts;
            private List<Object> groupIps;
            private List<Report> report;
            private List<Object> threads;
            private List<Object> minuteDistribution;
            private Map<String, Object> graphs;
        }

        @Data
        public static class Host {
            private String ip;
            private String hostname;
            private String set;
        }

        @Data
        public static class Report {
            private String type;
            private String category;
            private String name;
            private Integer countType;
            private Integer countCategory;
            private Long count;
            private Integer minute;
            private List<Message> messages;
        }

        @Data
        public static class Message {
            private String id;
            private Long timestamp;
        }
    }

    public static class Business {
        @Data
        public static class Root {
            private Long current;
            private List<BusinessData> businessDatas;
        }

        @Data
        public static class BusinessData {
            private Boolean dataComplete;
            private Integer indexOfSecondYAxis;
            private List<DataEntry> datas;
            private Map<String, Statistics> statistics;
        }

        @Data
        public static class DataEntry {
            private String key;
            private Map<Long, Double> data;
        }

        @Data
        public static class Statistics {
            @JSONField(name = "COUNT")
            private Double COUNT;
            @JSONField(name = "AVG")
            private Double AVG;
        }
    }

    public static class Host {
        @Data
        public static class Root {
            private Map<String, MetricTrend> metricTrends;
        }

        @Data
        public static class MetricTrend {
            private Map<String, EndpointEntry> endpointsEntries;
        }

        @Data
        public static class EndpointEntry {
            private Map<Long, Double> timeTrend;
        }
    }
}

