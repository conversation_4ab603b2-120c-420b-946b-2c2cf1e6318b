package com.sankuai.algoplatform.matchops.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: task_run_record
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TaskRunRecord {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: task_id
     *   说明: 任务ID
     */
    private Long taskId;

    /**
     *   字段: run_param
     *   说明: 运行参数
     */
    private String runParam;

    /**
     *   字段: algo_commit_id
     *   说明: 算法提交ID
     */
    private String algoCommitId;

    /**
     *   字段: predictor1_level_cache_switch
     *   说明: 预测器一级缓存开关
     */
    private Integer predictor1LevelCacheSwitch;

    /**
     *   字段: predictor2_level_cache_switch
     *   说明: 预测器二级缓存开关
     */
    private Integer predictor2LevelCacheSwitch;

    /**
     *   字段: predictor3_level_cache_switch
     *   说明: 预测器三级缓存开关
     */
    private Integer predictor3LevelCacheSwitch;

    /**
     *   字段: predictor4_level_cache_switch
     *   说明: 预测器四级缓存开关
     */
    private Integer predictor4LevelCacheSwitch;

    /**
     *   字段: lion_config
     *   说明: 其他Lion配置
     */
    private String lionConfig;

    /**
     *   字段: service_branch
     *   说明: 服务分支
     */
    private String serviceBranch;

    /**
     *   字段: octo_resources
     *   说明: OCTO资源
     */
    private String octoResources;

    /**
     *   字段: mlp_model_resources
     *   说明: MLP模型资源
     */
    private String mlpModelResources;

    /**
     *   字段: friday_model_resources
     *   说明: Friday模型资源
     */
    private String fridayModelResources;

    /**
     *   字段: reporter_template_id
     *   说明: 报告模板ID
     */
    private String reporterTemplateId;

    /**
     *   字段: status
     *   说明: 任务状态
     */
    private Integer status;

    /**
     *   字段: reporters
     *   说明: 生成报告地址
     */
    private String reporters;

    /**
     *   字段: creator
     *   说明: 创建者
     */
    private String creator;

    /**
     *   字段: create_time
     *   说明: 创建时间
     */
    private Date createTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}