package com.sankuai.algoplatform.matchops.infrastructure.proxy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.sankuai.algoplatform.matchops.infrastructure.enums.OctoNodeStatus;
import com.sankuai.algoplatform.matchops.infrastructure.model.OctoNode;
import com.sankuai.algoplatform.matchops.infrastructure.util.ContextUtil;
import com.sankuai.algoplatform.matchops.infrastructure.util.HttpUtil;
import com.sankuai.algoplatform.matchops.infrastructure.util.RetryUtil;
import com.sankuai.inf.octo.mns.model.HostEnv;
import com.sankuai.inf.octo.mns.util.ProcessInfoUtil;
import com.sankuai.oceanus.http.internal.HttpHeader;
import com.sankuai.oceanus.http.internal.TargetRequestEnv;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.inf.octo.mns.model.HostEnv.*;

@Slf4j
@Service
public class OctoNodeService {
    public static String OCTO_APP_KEY = "com.sankuai.octo.openapi";
    public static String OCTO_URL = "https://octoopenapi.vip.sankuai.com/v1/provider";
    public static String OCTO_URL_OFFLINE = "https://octoopenapi.inf.test.sankuai.com/v1/provider";


    public List<OctoNode> getOctoNodeStatus(String appKey) {
        return getOctoNodeStatus(appKey, null, null, null);
    }

    /**
     * 测试环境获取测试环境节点，线上环境获取线上节点
     * 线上不要使用，测试环境使用
     */
    public List<OctoNode> getOctoNodeStatusByCurrentEnv(String appKey, String cell, OctoNodeStatus status) {
        if (StringUtils.equals(cell, "default") ||StringUtils.equals(cell, "default_cell")){
            cell = "";
        }
        HostEnv env = ProcessInfoUtil.getHostEnv();
        boolean isOnlineEnv = ProcessInfoUtil.isLocalHostOnline();
        // 接口文档：https://km.sankuai.com/collabpage/**********
        Map<String, String> header = ImmutableMap.of(HttpHeader.OCEANUS_RMOTE_APPKEY_HEADER, OCTO_APP_KEY,
                HttpHeader.OCEANUS_TARGET_ENV_HEADER, isOnlineEnv? TargetRequestEnv.ONLINE.getDesc(): TargetRequestEnv.OFFLINE.getDesc());
        //1: dev; 2: test; 3: stage; 4: prod。默认4PROD
        Map<String, String> param = ImmutableMap.of(
                "appkey", appKey,
                "type", "1",
                "env", ImmutableMap.of(DEV, "1", TEST, "2", STAGING, "3", PROD, "4")
                        .getOrDefault(Optional.ofNullable(env).orElse(PROD), "4"),
                "status", Optional.ofNullable(status).map(m -> String.valueOf(m.getCode())).orElse("-1"));

        String finalCell = cell;
        String resp = RetryUtil.retry(
                () -> {
                    String respStr = HttpUtil.octoHttpGet(isOnlineEnv ? OCTO_URL: OCTO_URL_OFFLINE, param, header);
                    log.info("getOctoNodeStatus: appKey:{}, cell:{}, env:{}, status:{}, respStr:{}", appKey, finalCell, env, status, respStr);
                    return respStr;
                },
                (r, e) -> {
                    if (StringUtils.isBlank(r)) {
                        return true;
                    }
                    JSONObject object = JSON.parseObject(r);
                    Boolean isSuccess = object.getBoolean("success");
                    return e != null || isSuccess == null || !isSuccess;
                }, 2, 1000
        );
        if (StringUtils.isBlank(resp)) {
            return Collections.emptyList();
        }

        List<OctoNode> nodes = JSON.parseObject(resp).getJSONObject("data")
                .getJSONArray("providers").toJavaList(OctoNode.class);
        if (cell != null) {
            String finalCell1 = cell;
            nodes = nodes.stream().filter(node -> StringUtils.equals(node.getCell(), finalCell1)).collect(Collectors.toList());
        }
        return nodes;
    }

    /**
     * 该接口只返回线上服务的节点
     */
    public List<OctoNode> getOctoNodeStatus(String appKey, String cell, HostEnv env, OctoNodeStatus status) {
        if (StringUtils.equals(cell, "default") ||StringUtils.equals(cell, "default_cell")){
            cell = "";
        }
        // 接口文档：https://km.sankuai.com/collabpage/**********
        Map<String, String> header = ImmutableMap.of(HttpHeader.OCEANUS_RMOTE_APPKEY_HEADER, OCTO_APP_KEY,
                HttpHeader.OCEANUS_TARGET_ENV_HEADER, TargetRequestEnv.ONLINE.getDesc());
        //1: dev; 2: test; 3: stage; 4: prod。默认4PROD
        Map<String, String> param = ImmutableMap.of(
                "appkey", appKey,
                "type", "1",
                "env", ImmutableMap.of(DEV, "1", TEST, "2", STAGING, "3", PROD, "4")
                        .getOrDefault(Optional.ofNullable(env).orElse(PROD), "4"),
                "status", Optional.ofNullable(status).map(m -> String.valueOf(m.getCode())).orElse("-1"));

        String finalCell = cell;
        String resp = RetryUtil.retry(
                () -> {
                    String respStr = HttpUtil.octoHttpGet(OCTO_URL, param, header);
                    log.info("getOctoNodeStatus: appKey:{}, cell:{}, env:{}, status:{}, respStr:{}", appKey, finalCell, env, status, respStr);
                    return respStr;
                },
                (r, e) -> {
                    if (StringUtils.isBlank(r)) {
                        return true;
                    }
                    JSONObject object = JSON.parseObject(r);
                    Boolean isSuccess = object.getBoolean("success");
                    return e != null || isSuccess == null || !isSuccess;
                }, 2, 1000
        );
        if (StringUtils.isBlank(resp)) {
            return Collections.emptyList();
        }

        List<OctoNode> nodes = JSON.parseObject(resp).getJSONObject("data")
                .getJSONArray("providers").toJavaList(OctoNode.class);
        if (cell != null) {
            String finalCell1 = cell;
            nodes = nodes.stream().filter(node -> StringUtils.equals(node.getCell(), finalCell1)).collect(Collectors.toList());
        }
        return nodes;
    }

    public List<String> getAppKeys(String keyword) {
        // 接口文档：https://a.sankuai.com/index/#/papi/project/0f4b8d5b-a409-482f-90be-109b4c6ecf1b?apiId=db00316e-d64a-4c2b-a8f7-e1628977e6c4
        Map<String, String> param = new HashMap<>();
        if (StringUtils.isEmpty(keyword)) {
            param.put("username", ContextUtil.getLoginUserMis());
        } else {
            param.put("keyword", keyword);
        }
        String resp = HttpUtil.octoHttpGet("https://octo.sankuai.com/api/apps", param, null);
        if (StringUtils.isBlank(resp)) {
            return Collections.emptyList();
        }
        return JSON.parseObject(resp).getJSONArray("data").toJavaList(String.class);
    }
}
