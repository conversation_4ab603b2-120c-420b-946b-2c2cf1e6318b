package com.sankuai.algoplatform.matchops.infrastructure.dal.dao;

import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ReporterTemplate;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.ReporterTemplateExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.ReporterTemplateMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service

public class TestReporterTemplateDao {
    @Autowired
    ReporterTemplateMapper reporterTemplateMapper;

    public List<ReporterTemplate> selectAllNotDelete() {
        ReporterTemplateExample example = new ReporterTemplateExample();
        example.createCriteria().andIsDelEqualTo(false);
        return reporterTemplateMapper.selectByExample(example);
    }

    public List<ReporterTemplate> selectAllByBizLineId(Long bizLineId) {
        ReporterTemplateExample example = new ReporterTemplateExample();
        example.createCriteria().andBizLineIdEqualTo(bizLineId);
        return reporterTemplateMapper.selectByExample(example);
    }

    public ReporterTemplate selectById(Long id) {
        ReporterTemplateExample example = new ReporterTemplateExample();
        example.createCriteria().andIdEqualTo(id);
        return reporterTemplateMapper.selectByExample(example).get(0);
    }

    public ReporterTemplate selectByBizId(Long bizLineId) {
        ReporterTemplateExample example = new ReporterTemplateExample();
        example.createCriteria().andBizLineIdEqualTo(bizLineId);
        return reporterTemplateMapper.selectByExample(example).get(0);
    }

    public Long insert(ReporterTemplate reporterTemplate) {
        reporterTemplateMapper.insertSelective(reporterTemplate);
        return reporterTemplate.getId();
    }

    public int update(ReporterTemplate reporterTemplate) {
        return reporterTemplateMapper.updateByPrimaryKeySelective(reporterTemplate);
    }

    public int softDelete(Long id, String mis) {
        ReporterTemplate reporterTemplate = selectById(id);
        if (reporterTemplate != null) {
            reporterTemplate.setIsDel(true);
            reporterTemplate.setCreator(mis);
            return update(reporterTemplate);
        }
        return 0;
    }

    public void delete(Long id) {
        reporterTemplateMapper.deleteByPrimaryKey(id);
    }

    public List<ReporterTemplate> selectByPageAndBizLineId(int page, int pageSize, long bizLineId) {
        ReporterTemplateExample example = new ReporterTemplateExample();
        example.limit((page - 1) * pageSize, pageSize);
        example.createCriteria().andBizLineIdEqualTo(bizLineId).andIsDelEqualTo(false);
        return reporterTemplateMapper.selectByExample(example);
    }

    public Long selectCountByBizLineId(long bizLineId) {
        ReporterTemplateExample example = new ReporterTemplateExample();
        example.createCriteria().andBizLineIdEqualTo(bizLineId).andIsDelEqualTo(false);
        return reporterTemplateMapper.countByExample(example);
    }

}
