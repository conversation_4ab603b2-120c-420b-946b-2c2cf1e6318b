package com.sankuai.algoplatform.matchops.infrastructure.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ResourceGroup;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.ResourceGroupExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ResourceGroupMapper extends MybatisBaseMapper<ResourceGroup, ResourceGroupExample, Long> {
    int batchInsert(@Param("list") List<ResourceGroup> list);
}