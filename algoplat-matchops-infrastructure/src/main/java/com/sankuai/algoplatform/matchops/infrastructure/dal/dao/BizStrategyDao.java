package com.sankuai.algoplatform.matchops.infrastructure.dal.dao;

import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.BizStrategy;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.MatchStrategy;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.BizStrategyExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.MatchStrategyExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.BizStrategyMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@Service

public class BizStrategyDao {
    @Autowired
    BizStrategyMapper bizStrategyMapper;

    public List<BizStrategy> selectAllByBizLineIdAndEnv(Long bizLineId, Integer env) {
        BizStrategyExample example = new BizStrategyExample();
        example.createCriteria().andBizLineIdEqualTo(bizLineId).andEnvEqualTo(env).andStatusEqualTo(0);
        return bizStrategyMapper.selectByExample(example);
    }

    public List<BizStrategy> selectByPageAndBizLineId(int page, int pageSize, long bizLineId) {
        BizStrategyExample example = new BizStrategyExample();
        example.limit((page - 1) * pageSize, pageSize);
        example.createCriteria().andBizLineIdEqualTo(bizLineId).andStatusEqualTo(0);
        return bizStrategyMapper.selectByExample(example);
    }

    public Long selectCountByBizLineId(long bizLineId) {
        BizStrategyExample example = new BizStrategyExample();
        example.createCriteria().andBizLineIdEqualTo(bizLineId).andStatusEqualTo(0);
        return bizStrategyMapper.countByExample(example);
    }

    public BizStrategy selectById(Long id) {
        return bizStrategyMapper.selectByPrimaryKey(id);
    }

    public List<BizStrategy> selectByBizCodes(List<String> bizCodes) {
        BizStrategyExample example = new BizStrategyExample();
        example.createCriteria().andBizCodeIn(bizCodes);
        return bizStrategyMapper.selectByExample(example);
    }

    public BizStrategy selectByBizCode(String bizCode) {
        BizStrategyExample example = new BizStrategyExample();
        example.createCriteria().andBizCodeEqualTo(bizCode);
        List<BizStrategy> bizStrategies = bizStrategyMapper.selectByExample(example);
        return CollectionUtils.isEmpty(bizStrategies) ? null : bizStrategies.get(0);
    }


    public int update(BizStrategy bizStrategy) {
        return bizStrategyMapper.updateByPrimaryKeySelective(bizStrategy);
    }

    public Long insert(BizStrategy bizStrategy) {
        bizStrategyMapper.insertSelective(bizStrategy);
        return bizStrategy.getId();
    }

    public int softDelete(Long id, String mis) {
        BizStrategy bizStrategy = selectById(id);
        if (bizStrategy != null) {
            bizStrategy.setStatus(-1);
            bizStrategy.setOwner(mis);
            return update(bizStrategy);
        }
        return 0;
    }

    public List<BizStrategy> selectByStrategyId(Long strategyId) {
        return bizStrategyMapper.selectByStrategyId(strategyId);
    }
}
