package com.sankuai.algoplatform.matchops.infrastructure.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ScheduleServiceConfigExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ScheduleServiceConfigExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAppkeyIsNull() {
            addCriterion("appkey is null");
            return (Criteria) this;
        }

        public Criteria andAppkeyIsNotNull() {
            addCriterion("appkey is not null");
            return (Criteria) this;
        }

        public Criteria andAppkeyEqualTo(String value) {
            addCriterion("appkey =", value, "appkey");
            return (Criteria) this;
        }

        public Criteria andAppkeyNotEqualTo(String value) {
            addCriterion("appkey <>", value, "appkey");
            return (Criteria) this;
        }

        public Criteria andAppkeyGreaterThan(String value) {
            addCriterion("appkey >", value, "appkey");
            return (Criteria) this;
        }

        public Criteria andAppkeyGreaterThanOrEqualTo(String value) {
            addCriterion("appkey >=", value, "appkey");
            return (Criteria) this;
        }

        public Criteria andAppkeyLessThan(String value) {
            addCriterion("appkey <", value, "appkey");
            return (Criteria) this;
        }

        public Criteria andAppkeyLessThanOrEqualTo(String value) {
            addCriterion("appkey <=", value, "appkey");
            return (Criteria) this;
        }

        public Criteria andAppkeyLike(String value) {
            addCriterion("appkey like", value, "appkey");
            return (Criteria) this;
        }

        public Criteria andAppkeyNotLike(String value) {
            addCriterion("appkey not like", value, "appkey");
            return (Criteria) this;
        }

        public Criteria andAppkeyIn(List<String> values) {
            addCriterion("appkey in", values, "appkey");
            return (Criteria) this;
        }

        public Criteria andAppkeyNotIn(List<String> values) {
            addCriterion("appkey not in", values, "appkey");
            return (Criteria) this;
        }

        public Criteria andAppkeyBetween(String value1, String value2) {
            addCriterion("appkey between", value1, value2, "appkey");
            return (Criteria) this;
        }

        public Criteria andAppkeyNotBetween(String value1, String value2) {
            addCriterion("appkey not between", value1, value2, "appkey");
            return (Criteria) this;
        }

        public Criteria andGroupNameIsNull() {
            addCriterion("group_name is null");
            return (Criteria) this;
        }

        public Criteria andGroupNameIsNotNull() {
            addCriterion("group_name is not null");
            return (Criteria) this;
        }

        public Criteria andGroupNameEqualTo(String value) {
            addCriterion("group_name =", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameNotEqualTo(String value) {
            addCriterion("group_name <>", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameGreaterThan(String value) {
            addCriterion("group_name >", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameGreaterThanOrEqualTo(String value) {
            addCriterion("group_name >=", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameLessThan(String value) {
            addCriterion("group_name <", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameLessThanOrEqualTo(String value) {
            addCriterion("group_name <=", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameLike(String value) {
            addCriterion("group_name like", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameNotLike(String value) {
            addCriterion("group_name not like", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameIn(List<String> values) {
            addCriterion("group_name in", values, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameNotIn(List<String> values) {
            addCriterion("group_name not in", values, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameBetween(String value1, String value2) {
            addCriterion("group_name between", value1, value2, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameNotBetween(String value1, String value2) {
            addCriterion("group_name not between", value1, value2, "groupName");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeIsNull() {
            addCriterion("schedule_type is null");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeIsNotNull() {
            addCriterion("schedule_type is not null");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeEqualTo(String value) {
            addCriterion("schedule_type =", value, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeNotEqualTo(String value) {
            addCriterion("schedule_type <>", value, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeGreaterThan(String value) {
            addCriterion("schedule_type >", value, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeGreaterThanOrEqualTo(String value) {
            addCriterion("schedule_type >=", value, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeLessThan(String value) {
            addCriterion("schedule_type <", value, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeLessThanOrEqualTo(String value) {
            addCriterion("schedule_type <=", value, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeLike(String value) {
            addCriterion("schedule_type like", value, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeNotLike(String value) {
            addCriterion("schedule_type not like", value, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeIn(List<String> values) {
            addCriterion("schedule_type in", values, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeNotIn(List<String> values) {
            addCriterion("schedule_type not in", values, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeBetween(String value1, String value2) {
            addCriterion("schedule_type between", value1, value2, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andScheduleTypeNotBetween(String value1, String value2) {
            addCriterion("schedule_type not between", value1, value2, "scheduleType");
            return (Criteria) this;
        }

        public Criteria andGpuUsageNumIsNull() {
            addCriterion("gpu_usage_num is null");
            return (Criteria) this;
        }

        public Criteria andGpuUsageNumIsNotNull() {
            addCriterion("gpu_usage_num is not null");
            return (Criteria) this;
        }

        public Criteria andGpuUsageNumEqualTo(Integer value) {
            addCriterion("gpu_usage_num =", value, "gpuUsageNum");
            return (Criteria) this;
        }

        public Criteria andGpuUsageNumNotEqualTo(Integer value) {
            addCriterion("gpu_usage_num <>", value, "gpuUsageNum");
            return (Criteria) this;
        }

        public Criteria andGpuUsageNumGreaterThan(Integer value) {
            addCriterion("gpu_usage_num >", value, "gpuUsageNum");
            return (Criteria) this;
        }

        public Criteria andGpuUsageNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("gpu_usage_num >=", value, "gpuUsageNum");
            return (Criteria) this;
        }

        public Criteria andGpuUsageNumLessThan(Integer value) {
            addCriterion("gpu_usage_num <", value, "gpuUsageNum");
            return (Criteria) this;
        }

        public Criteria andGpuUsageNumLessThanOrEqualTo(Integer value) {
            addCriterion("gpu_usage_num <=", value, "gpuUsageNum");
            return (Criteria) this;
        }

        public Criteria andGpuUsageNumIn(List<Integer> values) {
            addCriterion("gpu_usage_num in", values, "gpuUsageNum");
            return (Criteria) this;
        }

        public Criteria andGpuUsageNumNotIn(List<Integer> values) {
            addCriterion("gpu_usage_num not in", values, "gpuUsageNum");
            return (Criteria) this;
        }

        public Criteria andGpuUsageNumBetween(Integer value1, Integer value2) {
            addCriterion("gpu_usage_num between", value1, value2, "gpuUsageNum");
            return (Criteria) this;
        }

        public Criteria andGpuUsageNumNotBetween(Integer value1, Integer value2) {
            addCriterion("gpu_usage_num not between", value1, value2, "gpuUsageNum");
            return (Criteria) this;
        }

        public Criteria andInstMaxNumIsNull() {
            addCriterion("inst_max_num is null");
            return (Criteria) this;
        }

        public Criteria andInstMaxNumIsNotNull() {
            addCriterion("inst_max_num is not null");
            return (Criteria) this;
        }

        public Criteria andInstMaxNumEqualTo(Integer value) {
            addCriterion("inst_max_num =", value, "instMaxNum");
            return (Criteria) this;
        }

        public Criteria andInstMaxNumNotEqualTo(Integer value) {
            addCriterion("inst_max_num <>", value, "instMaxNum");
            return (Criteria) this;
        }

        public Criteria andInstMaxNumGreaterThan(Integer value) {
            addCriterion("inst_max_num >", value, "instMaxNum");
            return (Criteria) this;
        }

        public Criteria andInstMaxNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("inst_max_num >=", value, "instMaxNum");
            return (Criteria) this;
        }

        public Criteria andInstMaxNumLessThan(Integer value) {
            addCriterion("inst_max_num <", value, "instMaxNum");
            return (Criteria) this;
        }

        public Criteria andInstMaxNumLessThanOrEqualTo(Integer value) {
            addCriterion("inst_max_num <=", value, "instMaxNum");
            return (Criteria) this;
        }

        public Criteria andInstMaxNumIn(List<Integer> values) {
            addCriterion("inst_max_num in", values, "instMaxNum");
            return (Criteria) this;
        }

        public Criteria andInstMaxNumNotIn(List<Integer> values) {
            addCriterion("inst_max_num not in", values, "instMaxNum");
            return (Criteria) this;
        }

        public Criteria andInstMaxNumBetween(Integer value1, Integer value2) {
            addCriterion("inst_max_num between", value1, value2, "instMaxNum");
            return (Criteria) this;
        }

        public Criteria andInstMaxNumNotBetween(Integer value1, Integer value2) {
            addCriterion("inst_max_num not between", value1, value2, "instMaxNum");
            return (Criteria) this;
        }

        public Criteria andInstMinNumIsNull() {
            addCriterion("inst_min_num is null");
            return (Criteria) this;
        }

        public Criteria andInstMinNumIsNotNull() {
            addCriterion("inst_min_num is not null");
            return (Criteria) this;
        }

        public Criteria andInstMinNumEqualTo(Integer value) {
            addCriterion("inst_min_num =", value, "instMinNum");
            return (Criteria) this;
        }

        public Criteria andInstMinNumNotEqualTo(Integer value) {
            addCriterion("inst_min_num <>", value, "instMinNum");
            return (Criteria) this;
        }

        public Criteria andInstMinNumGreaterThan(Integer value) {
            addCriterion("inst_min_num >", value, "instMinNum");
            return (Criteria) this;
        }

        public Criteria andInstMinNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("inst_min_num >=", value, "instMinNum");
            return (Criteria) this;
        }

        public Criteria andInstMinNumLessThan(Integer value) {
            addCriterion("inst_min_num <", value, "instMinNum");
            return (Criteria) this;
        }

        public Criteria andInstMinNumLessThanOrEqualTo(Integer value) {
            addCriterion("inst_min_num <=", value, "instMinNum");
            return (Criteria) this;
        }

        public Criteria andInstMinNumIn(List<Integer> values) {
            addCriterion("inst_min_num in", values, "instMinNum");
            return (Criteria) this;
        }

        public Criteria andInstMinNumNotIn(List<Integer> values) {
            addCriterion("inst_min_num not in", values, "instMinNum");
            return (Criteria) this;
        }

        public Criteria andInstMinNumBetween(Integer value1, Integer value2) {
            addCriterion("inst_min_num between", value1, value2, "instMinNum");
            return (Criteria) this;
        }

        public Criteria andInstMinNumNotBetween(Integer value1, Integer value2) {
            addCriterion("inst_min_num not between", value1, value2, "instMinNum");
            return (Criteria) this;
        }

        public Criteria andOperatorMisIsNull() {
            addCriterion("operator_mis is null");
            return (Criteria) this;
        }

        public Criteria andOperatorMisIsNotNull() {
            addCriterion("operator_mis is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorMisEqualTo(String value) {
            addCriterion("operator_mis =", value, "operatorMis");
            return (Criteria) this;
        }

        public Criteria andOperatorMisNotEqualTo(String value) {
            addCriterion("operator_mis <>", value, "operatorMis");
            return (Criteria) this;
        }

        public Criteria andOperatorMisGreaterThan(String value) {
            addCriterion("operator_mis >", value, "operatorMis");
            return (Criteria) this;
        }

        public Criteria andOperatorMisGreaterThanOrEqualTo(String value) {
            addCriterion("operator_mis >=", value, "operatorMis");
            return (Criteria) this;
        }

        public Criteria andOperatorMisLessThan(String value) {
            addCriterion("operator_mis <", value, "operatorMis");
            return (Criteria) this;
        }

        public Criteria andOperatorMisLessThanOrEqualTo(String value) {
            addCriterion("operator_mis <=", value, "operatorMis");
            return (Criteria) this;
        }

        public Criteria andOperatorMisLike(String value) {
            addCriterion("operator_mis like", value, "operatorMis");
            return (Criteria) this;
        }

        public Criteria andOperatorMisNotLike(String value) {
            addCriterion("operator_mis not like", value, "operatorMis");
            return (Criteria) this;
        }

        public Criteria andOperatorMisIn(List<String> values) {
            addCriterion("operator_mis in", values, "operatorMis");
            return (Criteria) this;
        }

        public Criteria andOperatorMisNotIn(List<String> values) {
            addCriterion("operator_mis not in", values, "operatorMis");
            return (Criteria) this;
        }

        public Criteria andOperatorMisBetween(String value1, String value2) {
            addCriterion("operator_mis between", value1, value2, "operatorMis");
            return (Criteria) this;
        }

        public Criteria andOperatorMisNotBetween(String value1, String value2) {
            addCriterion("operator_mis not between", value1, value2, "operatorMis");
            return (Criteria) this;
        }
        public Criteria andExtraIsNull() {
            addCriterion("extra is null");
            return (Criteria) this;
        }

        public Criteria andExtraIsNotNull() {
            addCriterion("extra is not null");
            return (Criteria) this;
        }

        public Criteria andExtraEqualTo(String value) {
            addCriterion("extra =", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraNotEqualTo(String value) {
            addCriterion("extra <>", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraGreaterThan(String value) {
            addCriterion("extra >", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraGreaterThanOrEqualTo(String value) {
            addCriterion("extra >=", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraLessThan(String value) {
            addCriterion("extra <", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraLessThanOrEqualTo(String value) {
            addCriterion("extra <=", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraLike(String value) {
            addCriterion("extra like", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraNotLike(String value) {
            addCriterion("extra not like", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraIn(List<String> values) {
            addCriterion("extra in", values, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraNotIn(List<String> values) {
            addCriterion("extra not in", values, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraBetween(String value1, String value2) {
            addCriterion("extra between", value1, value2, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraNotBetween(String value1, String value2) {
            addCriterion("extra not between", value1, value2, "extra");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}