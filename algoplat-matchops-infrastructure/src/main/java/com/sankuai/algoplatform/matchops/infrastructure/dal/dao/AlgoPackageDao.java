package com.sankuai.algoplatform.matchops.infrastructure.dal.dao;

import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.AlgoPackage;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.AlgoPackageExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.AlgoPackageMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service

public class AlgoPackageDao {
    @Autowired
    AlgoPackageMapper algoPackageMapper;

    public AlgoPackage selectById(Long id) {
        return algoPackageMapper.selectByPrimaryKey(id);
    }

    public List<AlgoPackage> selectByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        AlgoPackageExample example = new AlgoPackageExample();
        example.createCriteria().andIdIn(ids);
        return algoPackageMapper.selectByExample(example);
    }

    public int update(AlgoPackage algoPackage) {
        return algoPackageMapper.updateByPrimaryKeySelective(algoPackage);
    }

    public Long insert(AlgoPackage algoPackage) {
        algoPackageMapper.insertSelective(algoPackage);
        return algoPackage.getId();
    }

    public int softDelete(Long id, String mis) {
        AlgoPackage algoPackage = selectById(id);
        if (algoPackage != null) {
            algoPackage.setStatus(-1);
            algoPackage.setOwnerMis(mis);
            return update(algoPackage);
        }
        return 0;
    }

    public List<AlgoPackage> selectByVersion(String version) {
        AlgoPackageExample example = new AlgoPackageExample();
        example.createCriteria()
                .andVersionEqualTo(version);
        return algoPackageMapper.selectByExample(example);
    }
}
