package com.sankuai.algoplatform.matchops.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: reporter_template
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReporterTemplate {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: biz_line_id
     *   说明: 业务线ID
     */
    private Long bizLineId;

    /**
     *   字段: name
     *   说明: 模板名称
     */
    private String name;

    /**
     *   字段: template_addr
     *   说明: 模板地址
     */
    private String templateAddr;

    /**
     *   字段: creator
     *   说明: 创建者
     */
    private String creator;

    /**
     *   字段: create_time
     *   说明: 创建时间
     */
    private Date createTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: is_del
     */
    private Boolean isDel;
}