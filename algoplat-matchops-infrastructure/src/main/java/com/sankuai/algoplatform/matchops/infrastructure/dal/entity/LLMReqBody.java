package com.sankuai.algoplatform.matchops.infrastructure.dal.entity;

import java.util.List;

public class LLMReqBody {
    private List<LLMReqMessage> messages;

    private String model;

    private Double temperature = 0.01;

    private Integer max_tokens = 10;

    private Double top_p = 0.7;

    private Integer top_k = 1;

    private List<String> stop;

    public List<LLMReqMessage> getMessages() {
        return messages;
    }

    public void setMessages(List<LLMReqMessage> messages) {
        this.messages = messages;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public Double getTemperature() {
        return temperature;
    }

    public void setTemperature(Double temperature) {
        this.temperature = temperature;
    }

    public Integer getMax_tokens() {
        return max_tokens;
    }

    public void setMax_tokens(Integer max_tokens) {
        this.max_tokens = max_tokens;
    }

    public Double getTop_p() {
        return top_p;
    }

    public void setTop_p(Double top_p) {
        this.top_p = top_p;
    }

    public void setStop(List<String> stop) {
        this.stop = stop;
    }

    public List<String> getStop(){
        return this.stop;
    }

    public Integer getTop_k() {
        return top_k;
    }

    public void setTop_k(Integer top_k) {
        this.top_k = top_k;
    }

    public static class LLMReqMessage {
        private String role;

        private String content;

        public LLMReqMessage() {
        }

        public LLMReqMessage(String role, String content) {
            this.role = role;
            this.content = content;
        }

        public String getRole() {
            return role;
        }

        public void setRole(String role) {
            this.role = role;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }
    }
}
