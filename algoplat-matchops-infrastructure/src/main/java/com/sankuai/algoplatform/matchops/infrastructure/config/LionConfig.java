package com.sankuai.algoplatform.matchops.infrastructure.config;


import com.alibaba.fastjson.JSONObject;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Configuration
public class LionConfig {

    @MdpConfig("schedule_starting_timeout:1200")
    public static Integer SCHEDULE_STARTING_TIMEOUT;

    @MdpConfig("schedule_timed_schedule_timewindow:10")
    public static Integer SCHEDULE_TIMED_SCHEDULE_TIMEWINDOW;

    @MdpConfig("schedule_resource_monitor_timewindow_hour:24")
    public static Integer SCHEDULE_RESOURCE_MONITOR_TIMEWINDOW_HOUR;

    @MdpConfig("schedule_resource_monitor_redundancy:1")
    public static Integer SCHEDULE_RESOURCE_MONITOR_REDUNDANCY;

    @MdpConfig("schedule_resource_monitor_trigger_delay:1")
    public static Integer SCHEDULE_RESOURCE_MONITOR_TRIGGER_DELAY;


    @MdpConfig("test_tool_default_manager:[]")
    public static ArrayList<String> TEST_TOOL_DEFAULT_MANAGER;

    @MdpConfig("raptor_data_url:{\"PROD\":\"cat.service.sankuai.com\",\"STAGING\":\"************:8080\",\"TEST\":\"test.cat.service.sankuai.com\",\"DEV\":\"test.cat.service.sankuai.com\"}")
    public static HashMap<String, String> RAPTOR_DATA_URL;

    @MdpConfig("raptor_data_host_url:{}")
    public static String RAPTOR_DATA_HOST_URL;

    @MdpConfig("wiki_parent_id:4298119950")
    public static String WIKI_PARENT_ID;

    @MdpConfig("raptor_query_host_num:50")
    public static int RAPTOR_QUERY_HOST_NUM;

    @MdpConfig("host_metric_percent:[\"cpu.busy\", \"mem.memused.percent\", \"proc.cpu\"]")
    public static ArrayList<String> HOST_METRIC_PERCENT;

    @MdpConfig("checkdone_threshold:0.9999")
    public static double CHECKDONE_THRESHOLD;

    @MdpConfig("check_exception_num_threshold:2000")
    public static long CHECK_EXCEPTION_NUM_THRESHOLD;

    @MdpConfig("agent_xuecheng_user:liyue31")
    public static String AGENT_XUECHENG_USER;

    @MdpConfig("business_metadata:[]")
    public static String BUSINESS_METADATA;

    @MdpConfig("hive_sql_max_count:10000")
    public static int HIVE_SQL_MAX_COUNT;

    @MdpConfig("save_cache_timeout:4320")
    public static Integer SAVE_CACHE_TIMEOUT;

    @MdpConfig("mcpserver_create_xuecheng_doc_url:''")
    public static String MCPSERVER_CREATE_XUECHENG_DOC_URL;

    @MdpConfig("daozong_smooth_switch_config:{}")
    public static HashMap<String, JSONObject> DAOZONG_SMOOTH_SWITCH_CONFIG;

    @MdpConfig("mlp_max_thread:128")
    public static Integer mlpMaxThreadNum;

    @MdpConfig("friday_max_thread:100")
    public static Integer fridayMaxThreadNum;

    @MdpConfig("badcase_retry_time: 1")
    public static Integer BADCASERETRYTIME;

    @MdpConfig("default_backup_auditors:[\"liyue31\", \"zhoumi10\",\"sunrunlai\"]")
    public static ArrayList<String> DEFAULT_BACKUP_AUDITORS;

    @MdpConfig("offline_task_handle_minutes:1080")
    public static Integer OFFLINE_TASK_HANDLE_MINUTES;
}
