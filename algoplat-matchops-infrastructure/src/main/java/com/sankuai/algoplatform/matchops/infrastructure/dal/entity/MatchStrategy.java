package com.sankuai.algoplatform.matchops.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: match_strategy
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MatchStrategy {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: name
     *   说明: 策略名称
     */
    private String name;

    /**
     *   字段: biz_line_id
     *   说明: 业务线ID
     */
    private Long bizLineId;

    /**
     *   字段: test_scene_id
     *   说明: 测试场景ID
     */
    private Long testSceneId;

    /**
     *   字段: algo_biz_codes
     *   说明: 算法bizCode
     */
    private String algoBizCodes;

    /**
     *   字段: predictor_cache
     *   说明: 预测服务缓存配置
     */
    private String predictorCache;

    /**
     *   字段: llm_biz_codes
     *   说明: 大模型bizCode
     */
    private String llmBizCodes;

    /**
     *   字段: octo_service_config
     *   说明: 服务Lion配置、分支信息
     */
    private String octoServiceConfig;

    /**
     *   字段: status
     *   说明: 策略状态
     */
    private Integer status;

    /**
     *   字段: deploy_info
     *   说明: 上线信息
     */
    private String deployInfo;

    /**
     *   字段: owner
     *   说明: 负责人
     */
    private String owner;

    /**
     *   字段: is_del
     *   说明: 是否删除 0删除 1不删除
     */
    private Boolean isDel;

    /**
     *   字段: create_time
     *   说明: 创建时间
     */
    private Date createTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}