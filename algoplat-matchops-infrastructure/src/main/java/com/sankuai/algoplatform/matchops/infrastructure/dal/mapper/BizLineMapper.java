package com.sankuai.algoplatform.matchops.infrastructure.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.BizLine;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.BizLineExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BizLineMapper extends MybatisBaseMapper<BizLine, BizLineExample, Long> {
    int batchInsert(@Param("list") List<BizLine> list);
}