package com.sankuai.algoplatform.matchops.infrastructure.proxy.mcm.custom;

import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.algoplatform.matchops.infrastructure.config.LionConfig;
import com.sankuai.algoplatform.matchops.infrastructure.util.ContextUtil;
import com.sankuai.mcm.client.sdk.annotation.McmComponent;
import com.sankuai.mcm.client.sdk.context.customconfig.CustomConfigProviderAdaptor;
import com.sankuai.mcm.client.sdk.context.customconfig.CustomConfigProviderRequest;
import com.sankuai.mcm.client.sdk.context.handler.AuditConfig;
import com.sankuai.mcm.client.sdk.context.handler.AuditNode;
import com.sankuai.mcm.client.sdk.dto.common.*;
import com.sankuai.mcm.client.sdk.enums.AuditApproveType;
import com.sankuai.mcm.client.sdk.enums.AuditorType;
import com.sankuai.mcm.client.sdk.enums.OnEvent;

import java.util.List;


/**
 * 算法策略更新MCM自定义配置提供者
 */
@McmComponent(events = "UpdateAlgoStrategy")
public class UpdateAlgoStrategyCustomConfigProvider extends CustomConfigProviderAdaptor {
    
    @Override
    public AuditConfig getAuditConfig(CustomConfigProviderRequest request) {
        // 审核节点，可配置多个
        AuditNode node = new AuditNode();
        node.setName("算法策略更新审核");
        // 定义审批人列表
        List<String> auditors = Lists.newArrayList();

        // 根据环境添加审批人
        if (MdpContextUtils.isOfflineEnv()){
            auditors.add(ContextUtil.getLoginUserMis());
        }

        // 添加默认备份审批人
        auditors.addAll(LionConfig.DEFAULT_BACKUP_AUDITORS);

        node.setAuditors(auditors);
        node.setApproveType(AuditApproveType.OR);
        // 通过审核人类型来设置审核人
        List<AuditorType> auditorTypes = Lists.newArrayList(AuditorType.USER_LEADER);
        node.setCustomAuditors(auditorTypes);
        // 当审核人为空时的备份审核人
        node.setBackupAuditors(LionConfig.DEFAULT_BACKUP_AUDITORS);

        // 审核链路
        List<AuditNode> configs = Lists.newArrayList();
        configs.add(node);

        AuditConfig auditConfig = new AuditConfig();
        auditConfig.setConfigs(configs);

        return auditConfig;
    }
    
    //自定义审核周知
    @Override
    public NoticeConfig getAuditNoticeConfig(CustomConfigProviderRequest request) {

        NoticeMsgConfig noticeMsgConfig = NoticeMsgConfig.builder()
                .content("BML平台算法代码策略更改")
                .eventName("算法代码策略更改")
                .accountName("BML平台")
                //.items(items)
                .build();

        // item可以有多个，不同的时机，周知不同的对象
        NoticeConfigItem item = new NoticeConfigItem();
        // 周知时机
        item.setOn(Lists.newArrayList(OnEvent.AFTER_AUDIT_ACCEPT));

        return NoticeConfig.builder()
                // 自定义周知消息
                .msgConfig(noticeMsgConfig)
                .configs(Lists.newArrayList(item))
                .build();
    }
    
    //自定义变更内容
    @Override
    public ChangeConfig getChangeConfig(CustomConfigProviderRequest request) {
        ChangeConfig changeConfig = new ChangeConfig();
        // 使用HTML格式来实现换行和格式化
        StringBuilder content = new StringBuilder();
        content.append("BML平台算法代码策略更改");

        changeConfig.setContent(content.toString());
        return changeConfig;
    }
}