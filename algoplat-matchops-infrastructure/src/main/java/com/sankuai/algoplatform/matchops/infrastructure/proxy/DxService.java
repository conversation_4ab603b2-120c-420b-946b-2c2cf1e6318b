package com.sankuai.algoplatform.matchops.infrastructure.proxy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.algoplatform.matchops.infrastructure.monitor.RaptorTrack;
import com.sankuai.algoplatform.matchops.infrastructure.util.RetryUtil;
import com.sankuai.xm.pubapi.thrift.GroupPushMessageWithUids;
import com.sankuai.xm.pubapi.thrift.PushMessageServiceI;
import com.sankuai.xm.pubapi.thrift.PusherInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class DxService {
    @Resource
    private PusherInfo pusherInfo;
    @Resource
    private PushMessageServiceI.Iface pushMessageService;

    @Resource
    private UserInfoService userInfoService;
    public static final String MESSAGE_TYPE = "text";
    public static final String EXTENSION = null;


    /**
     * 推送消息给多个用户
     *
     * @param misIds  用户mis列表
     * @param message 推送消息
     * @return 是否推送成功
     */

    public boolean sendMsg2Users(String message, List<String> misIds) {
        log.info("DxPusherService.sendMsgByMisIds message:{}, misIds:{}", message, misIds);
        try {

            Map<String, Long> uidByMisId = userInfoService.getUidByMisId(new HashSet<>(misIds));
            if (MapUtils.isEmpty(uidByMisId) || CollectionUtils.isEmpty(uidByMisId.values())) {
                return false;
            }
            List<Long> toUids = new ArrayList<>(uidByMisId.values());
            String resp = pushMessageService.pushExtensionMessageWithUids(System.currentTimeMillis(),
                    MESSAGE_TYPE, buildJsonMessage(message), EXTENSION, toUids, pusherInfo);
            log.info("DxPusherService.sendMsgByMisIds resp:{}", JSON.toJSONString(resp));
            return handlePushResult(resp);
        } catch (TException e) {
            log.error("DxPusherService.sendMsgByMisIds err. message:{}, misIds:{}", message, JSON.toJSONString(misIds), e);
            RaptorTrack.Sys_InternalErrorNum.report("PushFail");
        }
        return false;
    }

    /**
     * 推送消息给群组
     *
     * @param groupId 群组ID
     * @param message 推送消息
     * @return 是否推送成功
     */
    public boolean sendMsg2Group(String message, Long groupId) {
        log.info("DxPusherService.sendMsgByRoomId message:{}，groupId:{}", message, groupId);
        GroupPushMessageWithUids param = new GroupPushMessageWithUids();
        param.setCts(System.currentTimeMillis());
        param.setMessageType(MESSAGE_TYPE);
        param.setMessageBodyJson(buildJsonMessage(message));
        param.setGid(groupId);
        param.setPusherInfo(pusherInfo);
        try {
            String resp = pushMessageService.pushToRoomWithUids(param);
            log.info("DxPusherService.sendMsgByRoomId resp:{}", JSON.toJSONString(resp));
            return handlePushResult(resp);
        } catch (TException e) {
            log.error("DxPusherService.sendMsgByRoomId err. message:{}, groupId:{}", message, groupId, e);
            RaptorTrack.Sys_InternalErrorNum.report("PushFail");
        }
        return false;
    }

    public boolean sendMsgToRoomWithUids(GroupPushMessageWithUids param) {

        try {
            log.info("pushMessageService.pushToRoomWithUids param: {}", JSON.toJSONString(param));
            String resp = pushMessageService.pushToRoomWithUids(param);
            log.info("pushMessageService.pushToRoomWithUids resp: {}", resp);
            return handlePushResult(resp);
        } catch (TException e) {
            log.error("DxPusherService.sendMsgToRoomWithUids err. param={}", JSON.toJSONString(param), e);
            RaptorTrack.Sys_InternalErrorNum.report("sendMsgToRoomWithUidsFail");
            return false;
        }
    }

    private String buildJsonMessage(String message) {
        JSONObject msgJson = new JSONObject();
        msgJson.put(MESSAGE_TYPE, message);
        return msgJson.toString();
    }

    private boolean handlePushResult(String resp) {
        JSONObject jsonObject = JSON.parseObject(resp);
        int resCode = jsonObject.getInteger("rescode");
        JSONObject data = jsonObject.getJSONObject("data");

        if (resCode == 0 && data != null &&
                (!CollectionUtils.isEmpty(data.getJSONArray("mids"))
                        || data.getLong("mid") != null && data.getLong("mid") > 0)) {
            return true;
        }
        return false;
    }

}
