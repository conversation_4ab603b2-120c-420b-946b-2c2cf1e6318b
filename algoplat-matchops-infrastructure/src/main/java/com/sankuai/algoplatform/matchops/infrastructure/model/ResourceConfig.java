package com.sankuai.algoplatform.matchops.infrastructure.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * @program: algoplat-matchops
 * <AUTHOR>
 * @Date 2025/5/25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResourceConfig {

    private List<ServiceConfig> predictor =new ArrayList<>();

    private List<ModelConfig> mlp =new ArrayList<>();

    private List<ModelConfig> friday =new ArrayList<>();

    @Data
    public static class ServiceConfig {
        private String set;
        private String cate;
    }

    @Data
    public class ModelConfig {
        private String wxProject;
        private String appkey;
        private String modelType;
        private List<GroupConfig> group =new ArrayList<>();
    }
        @Data
        public static class GroupConfig {
            private String groupName;
            private String serverImage;
            private int rpcOutTime;
            private boolean batching;
            private BatchingConfig batchingConfig;
            private String env;
            private String set;
            private ResourceParamConfig resourceConfig;
        }


}
