package com.sankuai.algoplatform.matchops.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: resource_group
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResourceGroup {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: biz_line_id
     *   说明: 业务线ID
     */
    private Long bizLineId;

    /**
     *   字段: octo_resources
     *   说明: OCTO资源
     */
    private String octoResources;

    /**
     *   字段: mlp_resources
     *   说明: MLP资源
     */
    private String mlpResources;

    /**
     *   字段: friday_resources
     *   说明: Friday资源
     */
    private String fridayResources;

    /**
     *   字段: creator
     *   说明: 创建者
     */
    private String creator;

    /**
     *   字段: create_time
     *   说明: 创建时间
     */
    private Date createTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: is_del
     */
    private Boolean isDel;

    /**
     *   字段: name
     *   说明: 资源组名称
     */
    private String name;
}