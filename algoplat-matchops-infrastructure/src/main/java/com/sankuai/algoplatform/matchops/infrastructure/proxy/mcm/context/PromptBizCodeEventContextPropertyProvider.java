package com.sankuai.algoplatform.matchops.infrastructure.proxy.mcm.context;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.algoplatform.matchops.api.request.prompt.TAddEditPromptBizCodeRequest;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.BizLlmPredictConfigDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.LlmBizStrategyDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.BizLlmpredictConfig;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.LlmBizStrategy;
import com.sankuai.algoplatform.matchops.infrastructure.util.ContextUtil;
import com.sankuai.mcm.client.sdk.annotation.McmComponent;
import com.sankuai.mcm.client.sdk.context.eventcontext.EventContextPropertyProviderAdaptor;
import com.sankuai.mcm.client.sdk.context.eventcontext.EventContextPropertyProviderRequest;
import com.sankuai.mcm.client.sdk.dto.common.UserIdentity;
import com.sankuai.meituan.auth.vo.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@McmComponent(events = "UpdatePromptBizCode")
public class PromptBizCodeEventContextPropertyProvider extends EventContextPropertyProviderAdaptor {

    @Autowired
    private LlmBizStrategyDao llmBizStrategyDao;

    @Autowired
    BizLlmPredictConfigDao bizLlmPredictConfigDao;

    @Override
    public UserIdentity getUserIdentity(EventContextPropertyProviderRequest request) {
        return UserIdentity.ofUser(ContextUtil.getLoginUserMis());
    }


    @Override
    public User getSsoUser() {
        User user = new User();
        user.setLogin(ContextUtil.getLoginUserMis());
        return user;
    }

    @Override
    public Map<String, Object> getExtraInfo(EventContextPropertyProviderRequest request) {
        Map<String, Object> extraInfo = new HashMap<>();

        try {
            // 获取before信息 - 从数据库查询当前状态
            Map<String, Object> beforeInfo = getBeforeInfo(request);
            extraInfo.put("before", beforeInfo);

            // 获取after信息 - 格式化显示
            Map<String, Object> afterInfo = getAfterInfo(request);
            extraInfo.put("after", afterInfo);

        } catch (Exception e) {
            log.error("获取算法业务代码变更上下文信息失败", e);
            extraInfo.put("before", "获取变更前信息失败: " + e.getMessage());
            extraInfo.put("after", "获取变更后信息失败: " + e.getMessage());
        }

        return extraInfo;
    }

    /**
     * 获取变更前信息 - 格式化显示
     */
    private Map<String, Object> getBeforeInfo(EventContextPropertyProviderRequest request) {
        Map<String, Object> beforeInfo = new HashMap<>();

        // 从请求参数中提取llmBizCode
        Object[] args = request.getArgs();
        if (args == null || args.length == 0) {
            throw new RuntimeException("请求参数为空");
        }

        TAddEditPromptBizCodeRequest requestObj = (TAddEditPromptBizCodeRequest) args[0];
        String llmBizCode = requestObj.llmBizCode;

        if (llmBizCode == null || llmBizCode.isEmpty()) {
            throw new RuntimeException("无法提取llmBizCode参数");
        }

        // 1. 根据bizCode查询llm_biz_strategy表
        LlmBizStrategy llmBizStrategy = llmBizStrategyDao.selectByBizCode(llmBizCode);
        if (llmBizStrategy == null) {
            throw new RuntimeException("未找到对应的LlmBizStrategy记录, bizCode: " + llmBizCode);
        }

        // 基本信息
        beforeInfo.put("BizCode", llmBizStrategy.getBizCode());
        beforeInfo.put("说明", llmBizStrategy.getNote());

        // 2. 解析abtest_config
        String abtestConfig = llmBizStrategy.getAbtestConfig();
        if (abtestConfig == null || abtestConfig.trim().isEmpty()) {
            throw new RuntimeException("LlmBizStrategy中abtestConfig为空");
        }

        JSONObject abtestJson = JSON.parseObject(abtestConfig);
        String strategyName = abtestJson.getString("strategyName");
        if (strategyName == null) {
            throw new RuntimeException("abtestConfig中未找到strategyName");
        }
        beforeInfo.put("实验名称", strategyName);


        // 3. 获取所有distribution信息
        JSONArray distributions = abtestJson.getJSONArray("distributions");
        if (distributions == null || distributions.isEmpty()) {
            throw new RuntimeException("abtestConfig中未找到distributions");
        }

        List<String> strategyConfigList = new ArrayList<>();
        for (int i = 0; i < distributions.size(); i++) {
            JSONObject distribution = distributions.getJSONObject(i);

            // 获取流量配比
            String quota = distribution.getString("quota");
            if (quota == null) {
                continue; // 跳过没有quota的distribution
            }

            // 获取strategyId
            Long strategyId = distribution.getLong("strategyId");
            if (strategyId == null) {
                continue; // 跳过没有strategyId的distribution
            }

            // 根据strategyId查询策略信息
            BizLlmpredictConfig bizLlmpredictConfig = bizLlmPredictConfigDao.selectById(strategyId);
            if (bizLlmpredictConfig != null) {
                // 策略名称格式：note（bizcode）
                String strategyNote = bizLlmpredictConfig.getNote();
                String strategyBizCode = bizLlmpredictConfig.getBizCode();
                String strategyDisplayName = strategyNote + "（" + strategyBizCode + "）";

                // 添加策略名称和流量配比作为独立条目
                strategyConfigList.add("策略名称: " + strategyDisplayName);
                strategyConfigList.add("流量配比: " + quota);
            }
        }

        beforeInfo.put("策略配置", strategyConfigList);

        log.info("成功获取变更前Prompt策略信息, llmBizCode: {}", llmBizCode);
        return beforeInfo;
    }

    /**
     * 获取变更后信息 - 从requestObj中获取所有内容
     */
    private Map<String, Object> getAfterInfo(EventContextPropertyProviderRequest request) {
        Map<String, Object> afterInfo = new HashMap<>();

        Object[] args = request.getArgs();
        if (args == null || args.length == 0) {
            throw new RuntimeException("请求参数为空");
        }

        TAddEditPromptBizCodeRequest requestObj = (TAddEditPromptBizCodeRequest) args[0];

        // 直接从requestObj获取基本信息
        afterInfo.put("BizCode", requestObj.llmBizCode);
        afterInfo.put("说明", requestObj.note);

        // 从strategy中获取实验名称和流量配比
        Map<String, String> strategy = requestObj.strategy;
        if (strategy == null) {
            throw new RuntimeException("请求中strategy为空");
        }

        String strategyName = strategy.get("strategyName");
        String distributions = strategy.get("distributions");

        if (strategyName == null) {
            throw new RuntimeException("strategy中未找到strategyName");
        }
        if (distributions == null) {
            throw new RuntimeException("strategy中未找到distributions");
        }

        afterInfo.put("实验名称", strategyName);

        // 解析distributions获取所有策略信息
        JSONArray distributionsArray = JSON.parseArray(distributions);
        if (distributionsArray == null || distributionsArray.isEmpty()) {
            throw new RuntimeException("distributions解析失败或为空");
        }

        List<String> strategyConfigList = new ArrayList<>();
        for (int i = 0; i < distributionsArray.size(); i++) {
            JSONObject distribution = distributionsArray.getJSONObject(i);

            String quota = distribution.getString("quota");
            String strategyId = distribution.getString("strategyId");

            if (quota == null || strategyId == null) {
                continue;
            }

            // 根据strategyId查询策略名称
            Long strategyIdLong = Long.parseLong(strategyId);
            BizLlmpredictConfig bizLlmpredictConfig = bizLlmPredictConfigDao.selectById(strategyIdLong);
            if (bizLlmpredictConfig != null) {
                // 策略名称格式：note（bizcode）
                String strategyNote = bizLlmpredictConfig.getNote();
                String strategyBizCode = bizLlmpredictConfig.getBizCode();
                String strategyDisplayName = strategyNote + "（" + strategyBizCode + "）";

                // 添加策略名称和流量配比作为独立条目
                strategyConfigList.add("策略名称: " + strategyDisplayName);
                strategyConfigList.add("流量配比: " + quota);
            }
        }

        afterInfo.put("策略配置", strategyConfigList);

        log.info("成功格式化变更后Prompt业务代码信息");
        return afterInfo;
    }
}