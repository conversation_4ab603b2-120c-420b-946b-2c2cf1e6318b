package com.sankuai.algoplatform.matchops.infrastructure.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.BizStrategy;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.MatchStrategy;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.MatchStrategyExample;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface MatchStrategyMapper extends MybatisBaseMapper<MatchStrategy, MatchStrategyExample, Long> {

    @Select("SELECT * FROM match_strategy WHERE JSON_CONTAINS(algo_biz_codes, CONCAT('{\"algoBizCode\":\"', #{algoBizCode}, '\"}'))")
    List<MatchStrategy> findByAlgoBizCode(@Param("algoBizCode") String algoBizCode);

    @Select("SELECT * FROM match_strategy WHERE JSON_CONTAINS(llm_biz_codes, CONCAT('{\"llmBizCode\":\"', #{llmBizCode}, '\"}'))")
    List<MatchStrategy> findByLlmBizCode(@Param("llmBizCode") String llmBizCode);

}