package com.sankuai.algoplatform.matchops.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: test_sub_task
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TestSubTask {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: task_id
     *   说明: 任务ID
     */
    private Long taskId;

    /**
     *   字段: run_param
     *   说明: 运行参数
     */
    private String runParam;

    /**
     *   字段: algo_code_info
     *   说明: 算法提交ID
     */
    private String algoCodeInfo;

    /**
     *   字段: predictor_cache_info
     *   说明: 预测服务4级缓存配置信息和setName
     */
    private String predictorCacheInfo;

    /**
     *   字段: llm_info
     *   说明: 算法提交ID
     */
    private String llmInfo;

    /**
     *   字段: octo_info
     *   说明: 服务lion配置和分支
     */
    private String octoInfo;

    /**
     *   字段: octo_resources
     *   说明: OCTO资源配置
     */
    private String octoResources;

    /**
     *   字段: mlp_model_resources
     *   说明: MLP模型资源配置
     */
    private String mlpModelResources;

    /**
     *   字段: friday_model_resources
     *   说明: Friday模型资源配置
     */
    private String fridayModelResources;

    /**
     *   字段: reporter_info
     *   说明: 报告模板信息
     */
    private String reporterInfo;

    /**
     *   字段: status
     *   说明: 任务状态
     */
    private Integer status;

    /**
     *   字段: run_time
     *   说明: 任务开始执行时间
     */
    private String runTime;

    /**
     *   字段: extra_info
     *   说明: 其他信息
     */
    private String extraInfo;

    /**
     *   字段: creator
     *   说明: 创建者
     */
    private String creator;

    /**
     *   字段: create_time
     *   说明: 创建时间
     */
    private Date createTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: task_result
     *   说明: 任务执行结果，只有http任务才有
     */
    private String taskResult;
}