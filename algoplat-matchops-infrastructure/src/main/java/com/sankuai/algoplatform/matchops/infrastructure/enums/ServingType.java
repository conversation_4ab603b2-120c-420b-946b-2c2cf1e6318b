package com.sankuai.algoplatform.matchops.infrastructure.enums;

import lombok.Getter;

/**
 * 服务类型枚举
 */
@Getter
public enum ServingType {
    /**
     * TensorFlow 服务类型
     */
    TENSORFLOW("tensorflow"),
    
    /**
     * PyTorch 服务类型
     */
    PYTORCH("pytorch"),
    
    /**
     * Triton 服务类型
     */
    TRITON("triton"),
    
    /**
     * PMML 服务类型
     */
    PMML("pmml"),
    
    /**
     * XGBoost 服务类型
     */
    XGB("xgb");
    
    /**
     * 服务类型名称
     */
    private final String name;
    
    /**
     * 构造函数
     * 
     * @param name 服务类型名称
     */
    ServingType(String name) {
        this.name = name;
    }
    
    /**
     * 根据名称获取服务类型
     * 
     * @param name 服务类型名称
     * @return 服务类型枚举，如果不存在则返回null
     */
    public static ServingType getByName(String name) {
        if (name == null) {
            return null;
        }
        
        for (ServingType type : ServingType.values()) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }
}