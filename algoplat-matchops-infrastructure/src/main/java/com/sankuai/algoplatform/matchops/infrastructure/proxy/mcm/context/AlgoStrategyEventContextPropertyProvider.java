package com.sankuai.algoplatform.matchops.infrastructure.proxy.mcm.context;

import com.sankuai.algoplatform.matchops.api.request.algoPackage.TAddEditAlgoStrategyRequest;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.AlgoPackageDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.AlgoStrategyDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.AlgoPackage;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.AlgoStrategy;
import com.sankuai.algoplatform.matchops.infrastructure.enums.RunTimeStatus;
import com.sankuai.algoplatform.matchops.infrastructure.util.ContextUtil;
import com.sankuai.mcm.client.sdk.annotation.McmComponent;
import com.sankuai.mcm.client.sdk.context.eventcontext.EventContextPropertyProviderAdaptor;
import com.sankuai.mcm.client.sdk.context.eventcontext.EventContextPropertyProviderRequest;
import com.sankuai.mcm.client.sdk.dto.common.UserIdentity;
import com.sankuai.meituan.auth.vo.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@McmComponent(events = "UpdateAlgoStrategy")
public class AlgoStrategyEventContextPropertyProvider extends EventContextPropertyProviderAdaptor {

    @Autowired
    private AlgoStrategyDao algoStrategyDao;

    @Autowired
    private AlgoPackageDao algoPackageDao;

    @Override
    public UserIdentity getUserIdentity(EventContextPropertyProviderRequest request) {
        return UserIdentity.ofUser(ContextUtil.getLoginUserMis());
    }


    @Override
    public User getSsoUser() {
        User user = new User();
        user.setLogin(ContextUtil.getLoginUserMis());
        return user;
    }

    @Override
    public Map<String, Object> getExtraInfo(EventContextPropertyProviderRequest request) {
        Map<String, Object> extraInfo = new HashMap<>();

        try {
            // 获取before信息 - 从数据库查询当前状态
            Map<String, Object> beforeInfo = getBeforeInfo(request);
            extraInfo.put("before", beforeInfo);

            // 获取after信息
            Map<String, Object> afterInfo = getAfterInfo(request);
            extraInfo.put("after", afterInfo);

        } catch (Exception e) {
            log.error("获取算法策略变更上下文信息失败", e);
            extraInfo.put("before", "获取变更前信息失败: " + e.getMessage());
            extraInfo.put("after", "获取变更后信息失败: " + e.getMessage());
        }

        return extraInfo;
    }

    /**
     * 获取变更前信息 - 格式化显示
     */
    private Map<String, Object> getBeforeInfo(EventContextPropertyProviderRequest request) {
        Map<String, Object> beforeInfo = new HashMap<>();

        // 从请求参数中提取algoCodeStrategyId
        Object[] args = request.getArgs();
        if (args == null || args.length == 0) {
            throw new RuntimeException("请求参数为空");
        }

        // 直接转换为TAddEditAlgoStrategyRequest对象
        TAddEditAlgoStrategyRequest requestObj = (TAddEditAlgoStrategyRequest) args[0];
        Long algoCodeStrategyId = requestObj.algoCodeStrategyId;

        if (algoCodeStrategyId == null) {
            throw new RuntimeException("无法提取algoCodeStrategyId参数");
        }

        // 根据algoCodeStrategyId查询algo_strategy表
        AlgoStrategy algoStrategy = algoStrategyDao.selectById(algoCodeStrategyId);
        if (algoStrategy == null) {
            throw new RuntimeException("未找到对应的AlgoStrategy记录, algoCodeStrategyId: " + algoCodeStrategyId);
        }

        // 根据package_id查询algo_package表获取策略名称
        Long packageId = algoStrategy.getPackageId();
        if (packageId == null) {
            throw new RuntimeException("AlgoStrategy中packageId为空, strategyId: " + algoCodeStrategyId);
        }

        AlgoPackage algoPackage = algoPackageDao.selectById(packageId);
        if (algoPackage == null) {
            throw new RuntimeException("未找到对应的AlgoPackage记录, packageId: " + packageId);
        }

        // 格式化显示字段 - 第一点修改：策略名称从algo_package表的note字段获取
        beforeInfo.put("说明", algoPackage.getNote());
        beforeInfo.put("调用算法文件", algoStrategy.getEntrancePath());
        beforeInfo.put("调用算法方法", algoStrategy.getEntranceMethod());

        // 第二点修改：根据convert_strategy_id查询文本转向量信息
        Long convertStrategyId = algoStrategy.getConvertStrategyId();
        if (convertStrategyId != null && convertStrategyId == 0) {
            beforeInfo.put("调用文本转向量文件", "");
            beforeInfo.put("调用文本转向量方法", "");
        } else {
            if (convertStrategyId != null) {
                AlgoStrategy convertStrategy = algoStrategyDao.selectById(convertStrategyId);
                if (convertStrategy == null) {
                    throw new RuntimeException("未找到对应的转换策略记录, convertStrategyId: " + convertStrategyId);
                }
                beforeInfo.put("调用文本转向量文件", convertStrategy.getEntrancePath());
                beforeInfo.put("调用文本转向量方法", convertStrategy.getEntranceMethod());
            } else {
                throw new RuntimeException("未找到对应的转换策略记录, convertStrategyId: " + convertStrategyId);
            }
        }

        beforeInfo.put("策略名称", algoStrategy.getNote());
        beforeInfo.put("代码仓库", algoPackage.getCodeRepo());
        beforeInfo.put("算法代码文件夹", algoPackage.getModulePath());
        beforeInfo.put("算法代码版本", algoPackage.getVersion());

        // 根据runtime的desc判断对应的code，再转换为name
        String runtime = algoPackage.getRuntime();
        String algoRuntimeName = "";
        if (runtime != null && !runtime.trim().isEmpty()) {
            RunTimeStatus runTimeStatus = RunTimeStatus.fromDesc(runtime);
            if (runTimeStatus == null) {
                throw new RuntimeException("无法识别的runtime描述: " + runtime);
            }
            algoRuntimeName = runTimeStatus.getName();
        }
        beforeInfo.put("算法环境", algoRuntimeName);

        log.info("成功获取变更前算法策略信息, algoCodeStrategyId: {}", algoCodeStrategyId);
        return beforeInfo;
    }

    /**
     * 获取变更后信息 - 格式化显示
     */
    private Map<String, Object> getAfterInfo(EventContextPropertyProviderRequest request) {
        Map<String, Object> afterInfo = new HashMap<>();

        try {
            Object[] args = request.getArgs();
            if (args != null && args.length > 0) {
                TAddEditAlgoStrategyRequest requestObj = (TAddEditAlgoStrategyRequest) args[0];

                // 格式化显示字段
                afterInfo.put("策略名称", requestObj.algoCodeStrategyName);
                afterInfo.put("算法代码文件夹", requestObj.modulePath);
                afterInfo.put("调用算法文件", requestObj.entrancePath);
                afterInfo.put("调用算法方法", requestObj.entranceMethod);
                afterInfo.put("调用文本转向量文件", requestObj.convertEntrancePath);
                afterInfo.put("调用文本转向量方法", requestObj.convertEntranceMethod);
                afterInfo.put("算法代码版本", requestObj.version);
                afterInfo.put("代码仓库", requestObj.codeRepo);
                afterInfo.put("说明", requestObj.note);

                // 将algoRuntime的code转换为name
                String algoRuntimeName = "";
                if (requestObj.algoRuntime != null && !requestObj.algoRuntime.trim().isEmpty()) {
                    try {
                        int runtimeCode = Integer.parseInt(requestObj.algoRuntime);
                        RunTimeStatus runTimeStatus = RunTimeStatus.fromValue(runtimeCode);
                        algoRuntimeName = runTimeStatus.getName();
                    } catch (NumberFormatException e) {
                        log.error("algoRuntime格式错误: {},NumberFormatException: {}", requestObj.algoRuntime, e);
                        throw new RuntimeException("algoRuntime格式错误", e);
                    } catch (IllegalArgumentException e) {
                        log.error("无法识别的algoRuntime代码: {}, IllegalArgumentException: {}", requestObj.algoRuntime, e);
                        throw new RuntimeException("无法识别的algoRuntime代码", e);
                    }
                }
                afterInfo.put("算法环境", algoRuntimeName);

                log.info("成功格式化变更后算法策略信息");
                return afterInfo;
            } else {
                afterInfo.put("error", "请求参数为空");
                return afterInfo;
            }
        } catch (Exception e) {
            log.error("提取变更后信息失败", e);
            afterInfo.put("error", "提取请求参数失败: " + e.getMessage());
            return afterInfo;
        }
    }
}