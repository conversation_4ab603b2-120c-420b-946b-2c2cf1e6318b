package com.sankuai.algoplatform.matchops.infrastructure.model;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @program: algoplat-matchops
 * <AUTHOR>
 * @Date 2025/5/23
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BusinessMetaData {

    /**
     * 产业类型。1到餐 2到综 3住宿
     */
    private String industryType;
    /**
     * 业务类型。全部 美容美体 境内竞对
     */
    private String bu;

    /**
     * 匹配标准。精准匹配 商品匹配 房型匹配
     */
    private String matchStandard;

    private List<TestSceneInfo> testSceneInfo = Lists.newArrayList();

    /**
     * 算法代码bizCode
     */
    private List<AlgoBizCode> algoBizCodeList = Lists.newArrayList();

    /**
     * 关联的线上bizCode
     */
    private List<LlmBizCode> llmBizCodeList = Lists.newArrayList();

    /**
     * 资源组相关配置
     */
    private ResourceConfig resourceConfigInfo;

    /**
     * 房型匹配策略名称
     */
    private String zsStrategyName;


    @Data
    public static class TestSceneInfo {

        /**
         * 测试类型。1:业务回测（badcase率测试） 2.一致性测试  3.GSB测试  4.整体性能压测
         */
        private String testType;
        private String sceneId;
        private String channel;
        private List<Long> reporterTemplateIds = Lists.newArrayList();
        private String resourceGroupId;
    }


}
