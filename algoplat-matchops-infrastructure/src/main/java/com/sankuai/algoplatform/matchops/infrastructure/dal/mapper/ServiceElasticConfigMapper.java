package com.sankuai.algoplatform.matchops.infrastructure.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ServiceElasticConfig;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.ServiceElasticConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ServiceElasticConfigMapper extends MybatisBaseMapper<ServiceElasticConfig, ServiceElasticConfigExample, Long> {
    int batchInsert(@Param("list") List<ServiceElasticConfig> list);
}