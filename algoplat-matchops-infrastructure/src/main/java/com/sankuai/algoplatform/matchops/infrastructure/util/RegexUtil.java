package com.sankuai.algoplatform.matchops.infrastructure.util;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class RegexUtil {

    final static String pattern = "\\$\\{([^}]+)\\}";

    public static List<String> extractDollar(String input) {
        return extractMatches(input, pattern);
    }

    /**
     * 从输入字符串中提取所有匹配正则表达式的内容。
     *
     * @param input 输入字符串
     * @param regex 正则表达式
     * @return 匹配的内容列表
     */
    public static List<String> extractMatches(String input, String regex) {
        List<String> matches = new ArrayList<>();
        if (StringUtils.isEmpty(input)) {
            return matches;
        }
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);

        while (matcher.find()) {
            matches.add(matcher.group());
        }

        return matches;
    }

    /**
     * 从输入字符串中提取所有匹配正则表达式捕获组的内容。
     *
     * @param input 输入字符串
     * @param regex 正则表达式
     * @param group 捕获组索引
     * @return 匹配的捕获组内容列表
     */
    public static List<String> extractGroupMatches(String input, String regex, int group) {
        List<String> matches = new ArrayList<>();
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);

        while (matcher.find()) {
            matches.add(matcher.group(group));
        }

        return matches;
    }

    /**
     * 检查输入字符串是否与正则表达式匹配。
     *
     * @param input 输入字符串
     * @param regex 正则表达式
     * @return 如果匹配则返回 true，否则返回 false
     */
    public static boolean matches(String input, String regex) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        return matcher.matches();
    }

    /**
     * 使用正则表达式替换输入字符串中的匹配内容。
     *
     * @param input       输入字符串
     * @param regex       正则表达式
     * @param replacement 替换内容
     * @return 替换后的字符串
     */
    public static String replace(String input, String regex, String replacement) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        return matcher.replaceAll(replacement);
    }

//    // 示例测试方法
//    public static void main(String[] args) {
//        String input = "{\"type\":\"doc\",\"content\":[{\"type\":\"title\",\"attrs\":{\"nodeId\":\"26b56b544988459280ac4af6edbc7ba9\"},\"content\":[{\"type\":\"text\",\"text\":\"测试工具解析模板\"}]},{\"type\":\"table\",\"attrs\":{\"indent\":0,\"responsive\":false,\"borderStyle\":\"solid\",\"borderWidth\":1,\"borderColor\":\"#dddddd\",\"dataDiffId\":\"d3236398-2bb4-402b-a4bc-22ad4dbab00d\",\"nodeId\":\"28c0278aa472441899174dde7de60cb5\"},\"content\":[{\"type\":\"table_row\",\"attrs\":{\"dataRowDiffId\":\"bdd957bf-aa52-4638-824b-3df17d35f3f5\",\"nodeId\":\"08322dc798584e7585c5e729d42fd3d7\"},\"content\":[{\"type\":\"table_header\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[126],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"ad70d83e-1496-4375-930e-7f85ec865874\",\"nodeId\":\"1f0509a3b8264b77864a23c7c8b1ed54\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"79d68964-65b3-4944-a0ad-e5d0278ac789\",\"nodeId\":\"f3eb360feaed4a908545d0627a1ef807\"},\"content\":[{\"type\":\"text\",\"text\":\"数据源\"}]}]},{\"type\":\"table_header\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[409],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"df4c5535-a5df-4902-ab3c-221529f96912\",\"nodeId\":\"4af73a8ba1364d138e0ebaf00f4f7fa4\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"454ae0ef-2704-4654-b054-be4d6d0d3952\",\"nodeId\":\"4b27b5bc9dbc456a9c14e98736c38ecd\"},\"content\":[{\"type\":\"text\",\"text\":\"文本解析规则\"}]}]},{\"type\":\"table_header\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[341],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"d272c7d4-8e0a-4dff-a877-196df184aef4\",\"nodeId\":\"1a29f3ef08004be7a4e7dc375e438204\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"d3fc9f97-b76a-49fb-adeb-1e4b9a4779c4\",\"nodeId\":\"0f6421cd19854f0888f601b3a3b179b2\"},\"content\":[{\"type\":\"text\",\"text\":\"取数\"}]}]},{\"type\":\"table_header\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[341],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"2c8a41a4-5a2a-45c0-8339-82e496ae1f29\",\"nodeId\":\"c5dd643fac8e4b10a4094c90242f7146\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"c24beaa9-984f-412c-a75e-e82904484545\",\"nodeId\":\"3150a20e00d34e9988e4aee5545f4d59\"},\"content\":[{\"type\":\"text\",\"text\":\"示例\"}]}]}]},{\"type\":\"table_row\",\"attrs\":{\"dataRowDiffId\":\"55c2feef-a805-4a21-95dd-e3dc28c274e0\",\"nodeId\":\"3c6ded0d62f845dfabfe866d3a292250\"},\"content\":[{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[126],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"107bf28f-5699-44f8-9875-00cda5f53d2d\",\"nodeId\":\"a20d52d4cfd54956a61e89880ab7faa0\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"029ae2bb-ff59-4604-9c45-0c14bc101268\",\"nodeId\":\"8a984dca51ff4c84aec1df39c60f3833\"},\"content\":[{\"type\":\"text\",\"text\":\"业务策略\"}]}]},{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[409],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"76490798-4790-45e8-8877-dc341d88f0de\",\"nodeId\":\"46b42ff0706c48549efa2c8e0c211e2b\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"9f362a20-243e-4b0d-8ca0-1eb94f881914\",\"nodeId\":\"d8125ec03f844e69bea33e7ff318cbf6\"},\"content\":[{\"type\":\"text\",\"text\":\"${strategy@commitId}\"}]}]},{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[341],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"27a43481-f600-4991-a24f-3cfc6457cfa4\",\"nodeId\":\"eb1fffd7aa8146f5a4bbf55683b7b270\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"7d9b9767-b14d-48e5-a1cd-0b2cb4b27d37\",\"nodeId\":\"962afc91b2674cd48b4a2d50d1c0649c\"},\"content\":[{\"type\":\"text\",\"text\":\"从当前跑数任务对应的策略中取算法commitId\"}]}]},{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[341],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"a0361e3e-3957-40cb-8598-d65d713d1107\",\"nodeId\":\"222df79a22484fcc9d7e3f6ce75ee313\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"a5b445b9-ef1a-4a49-bca6-378e956dba22\",\"nodeId\":\"b8ff714192ed4a30afc9c8add3e16ff9\"}}]}]},{\"type\":\"table_row\",\"attrs\":{\"dataRowDiffId\":\"aec980fa-63d7-4aa8-a4a6-a53d830219bb\",\"nodeId\":\"fca996e86efc468f9282a3f5ebdcda09\"},\"content\":[{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[126],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"cfffddb8-dc9b-42f6-9ee4-d0b5a06a14fa\",\"nodeId\":\"f13d40a0936e440fab511d28569df86e\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"b85975d7-3e9e-4472-bfbc-8313df2bf5a0\",\"nodeId\":\"f06240b0e3854178a38daeecd2c5e054\"},\"content\":[{\"type\":\"text\",\"text\":\"跑数入参\"}]}]},{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[409],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"12948484-5160-4463-90cd-94b13f15c665\",\"nodeId\":\"b628e78d70524c37a45fefffcb84d16b\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"0abd9004-22f8-4843-8cd9-18df7f501944\",\"nodeId\":\"0c6af7ce31a54175be0ee8cb3e5302db\"},\"content\":[{\"type\":\"text\",\"text\":\"${param@cd}\"}]}]},{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[341],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"c353daa2-a27b-4368-9780-47e9e57b8d7a\",\"nodeId\":\"fe2558c4bbfc45e994a5f8bd29b81fe2\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"aa048ec8-48f4-4dc2-aabd-5bd80d2fe2c9\",\"nodeId\":\"ee4d4e3dc1ab407d8486372b99d218d4\"},\"content\":[{\"type\":\"text\",\"text\":\"从当前跑数任务对应的策略中取算法commitId\"}]}]},{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[341],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"c0b7f879-760f-413c-b7eb-2cb9b02a7613\",\"nodeId\":\"1f473d063c0d4d48afca4320fd44cb2b\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"021f94f8-6112-4042-8f58-8b2e16dc6110\",\"nodeId\":\"3c8f96999c43453db0e7a2c765e11768\"},\"content\":[{\"type\":\"text\",\"text\":\"入参：-v --cd 2024-08-06\"}]},{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"4fd69de4-f8ea-4125-bcd9-d1dbc14cedbb\",\"nodeId\":\"30e9cacbb68a4144841bb877bc1a95f0\"},\"content\":[{\"type\":\"text\",\"text\":\"匹配：2024-10-10\"}]}]}]},{\"type\":\"table_row\",\"attrs\":{\"dataRowDiffId\":\"4300fe47-a37a-4469-a094-d6ae6d4acf1b\",\"nodeId\":\"bd1a054e8244458a8704cc1c8387c2df\"},\"content\":[{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":3,\"colwidth\":[126],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"800ca68c-fb1e-43b3-9ba0-d997ef746942\",\"nodeId\":\"130608796ab64ac9b1bfa7b4cbbbf64d\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"e71ccbf3-4400-4414-a01b-08fc355c6295\",\"nodeId\":\"23926e4cc20846c4807faab054d78fa1\"},\"content\":[{\"type\":\"text\",\"text\":\"资源组配置\"}]}]},{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[409],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"83d13f49-3d4a-47e0-8651-72913f34130f\",\"nodeId\":\"0ef9a38b66a44a1d8dc605d5d8eaec92\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"c3416fae-fa26-45b6-9bd6-4b3c59d5dd8d\",\"nodeId\":\"9fe7655ac43345d5889cba76038490ec\"},\"content\":[{\"type\":\"text\",\"text\":\"${resource@octo@appkey@set@instanceNum}\"}]}]},{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":3,\"colwidth\":[341],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"e3bd1b62-ac3b-43bd-b6de-3bb4e6af8ed1\",\"nodeId\":\"cf686df2288e48558ab0b08cf1ee1be6\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"f98d074a-72e4-429e-8ee8-512f6c7b1436\",\"nodeId\":\"735b92df92f743b1ae8e4f651b3e3908\"},\"content\":[{\"type\":\"text\",\"text\":\"从当前跑数任务对应的资源组策略取响应服务实例的数量\"}]}]},{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":3,\"colwidth\":[341],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"8a585dd1-dd9a-45cb-9248-d5e068bab9b7\",\"nodeId\":\"814aaef044434e4ab18c66f294372c9b\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"8cdc6236-82d1-4e55-b832-028e45db569e\",\"nodeId\":\"e09aeeaef02b4be194d0c6540d3cc71b\"}}]}]},{\"type\":\"table_row\",\"attrs\":{\"dataRowDiffId\":\"26b0e7d1-040a-4b0e-986b-91cf7d4097f8\",\"nodeId\":\"0743f117d30c4b439e0fc59fa4916abd\"},\"content\":[{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[409],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"0510b3d9-071f-43fe-876e-960fd58c0eab\",\"nodeId\":\"a00a3249d4eb47a6b5908a270d728628\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"56f88f6a-a839-4988-99d9-466b1015199c\",\"nodeId\":\"dd58b1e5b098461799e047832a89f86b\"},\"content\":[{\"type\":\"text\",\"text\":\"${resource@mlp@appkey@set@instanceNum}\"}]}]}]},{\"type\":\"table_row\",\"attrs\":{\"dataRowDiffId\":\"3626b8fb-19ac-4621-868b-ebe8fa35dd3d\",\"nodeId\":\"867607ad9c2f4599b83ba57da4a52787\"},\"content\":[{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[409],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"e9d064f6-dfce-431f-8f50-6772d9a6a563\",\"nodeId\":\"6dd29af6103240ce90a1423b42067d95\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"b4b6c5ac-8339-431d-8ac5-91599698380d\",\"nodeId\":\"fe2259359b0e4ac6bd21f1b01c0c75e2\"},\"content\":[{\"type\":\"text\",\"text\":\"${resource@friday@serviceName@instanceNum}\"}]}]}]},{\"type\":\"table_row\",\"attrs\":{\"dataRowDiffId\":\"7374c4ae-4823-4d8a-b391-0228dc7067f2\",\"nodeId\":\"4ba09956c89b46cdbc1b121fee62df71\"},\"content\":[{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":5,\"colwidth\":[126],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"c16b6b6d-fd5e-446f-9759-50d8c9c1950f\",\"nodeId\":\"3164bd068bcc409db9b47e75bcb9397c\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"caa69d97-89fe-4aa9-bd3e-9f476490a94e\",\"nodeId\":\"b674869b3ecf4e509312d6512442626e\"},\"content\":[{\"type\":\"text\",\"text\":\"rapto打点信息\"}]}]},{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[409],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"97e6c755-d69d-4916-959b-903185df4883\",\"nodeId\":\"62c5feb4b47e46a1a3f2363e070a5248\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"35c8f020-4deb-484c-860e-02dc0c8420d8\",\"nodeId\":\"6174fdc3f5d14e2e820a7d4da843e84a\"},\"content\":[{\"type\":\"text\",\"text\":\"${raptor@transaction@具体参数}\"}]}]},{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":5,\"colwidth\":[341],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"cb3631a5-f9ef-4cd7-954e-af31aebc2834\",\"nodeId\":\"b5dfe60e29d040e29cd17a5ae24830b9\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"537068cb-6787-4118-88c5-8092cc7cad82\",\"nodeId\":\"09c0b009a8c94224a3be4dd965999133\"},\"content\":[{\"type\":\"link\",\"attrs\":{\"id\":\"1619a1df-0a51-4589-8349-4cf22a9a1e9c\",\"href\":\"https://km.sankuai.com/collabpage/1482153948#id-%E6%95%B0%E6%8D%AE%E7%BA%A7%E5%88%AB%E8%A1%A8\",\"title\":\"transaction分钟级别数据查询demo\",\"autoUpdate\":true,\"nodeId\":\"a119fb3896aa40c9a707f43d0165a3c0\"},\"content\":[{\"type\":\"text\",\"text\":\"transaction分钟级别数据查询demo\"}]}]},{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"7112027e-e9c4-48c7-92b1-b74b202ff4dd\",\"nodeId\":\"7838e956396c4c3084f711729b29f734\"}}]},{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[341],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"d4fa33f0-3adf-43f9-97cb-99576943116f\",\"nodeId\":\"e1a3a02ccfb1431ab3f742b555bf862d\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"46559381-0d0f-4e8d-aac7-0859cad59013\",\"nodeId\":\"929847f4dc754973adb9d32d7984b416\"},\"content\":[{\"type\":\"text\",\"text\":\"\u200B\"}]}]}]},{\"type\":\"table_row\",\"attrs\":{\"dataRowDiffId\":\"a837ed5d-8224-49cc-93c0-4a8997e9c168\",\"nodeId\":\"e9b27017d3224ae7b2dd529ecaca4f2c\"},\"content\":[{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[409],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"21f06968-0f76-411f-bb1e-1425bb839627\",\"nodeId\":\"58ed91f0736a4e5d9e55d299d072247b\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"737f11b6-3280-43f8-a3e6-e7a580c8ed4b\",\"nodeId\":\"a9177d69d58b4bb6b1ea9da5cf0e1693\"},\"content\":[{\"type\":\"text\",\"text\":\"${raptor@event@具体参数}\"}]}]},{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[341],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"94b4eb6b-737d-4245-a76c-3e06bca50b8a\",\"nodeId\":\"1fbc3f1f4c5347e2a2f33372716d9b86\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"6c009cbf-f6d9-4ba4-9425-335a375b00ff\",\"nodeId\":\"8f7b9fe8a5784390b5e00c60240a955d\"}}]}]},{\"type\":\"table_row\",\"attrs\":{\"dataRowDiffId\":\"4ab2236f-5e4b-411c-8e5b-3528b56b66b7\",\"nodeId\":\"6b7e3cf48e804fa3af659ba6b3afea35\"},\"content\":[{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[409],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"f8e0e4df-bf28-4f35-a5e8-6cd1471f7135\",\"nodeId\":\"256d631f1765470691dfd560b4842e71\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"770e9e34-ba97-44af-808a-2f33c48434d6\",\"nodeId\":\"8a7f2c1a042742d2a1a5e6bd76f2f554\"},\"content\":[{\"type\":\"text\",\"text\":\"${raptor@problem@具体参数}\"}]}]},{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[341],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"d289cb51-c64b-4a62-be44-5febdeb513af\",\"nodeId\":\"24ef6d97c59d473791a96afd5e164246\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"823069e9-3d02-498c-9404-8823ab8b07a0\",\"nodeId\":\"b9e0f02eedc041128cf43854be32bab9\"}}]}]},{\"type\":\"table_row\",\"attrs\":{\"dataRowDiffId\":\"bcfbdecd-6b2a-478a-8885-69bf9c8e2088\",\"nodeId\":\"5fed6da38a1a438f8e09c4a503b9b2eb\"},\"content\":[{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[409],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"6ccd3c9b-774a-4efa-872d-adf4636b6fe0\",\"nodeId\":\"060a9c79d0af4c839646fa064b36b0df\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"b744cf8c-e497-46db-84cb-a10a5778485e\",\"nodeId\":\"27a2a19680254460bf4c2b61e0cd6d26\"},\"content\":[{\"type\":\"text\",\"text\":\"${raptor@business@具体参数}\"}]}]},{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[341],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"5b6b1a1b-fb71-4cd4-9506-a006ac42f4dc\",\"nodeId\":\"c87c221ba7d94ce58091943265eaa9ab\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"b2426a16-6b35-4413-b61b-b892c8f2c999\",\"nodeId\":\"9d07c9d2e79340f48a87113a805b6792\"}}]}]},{\"type\":\"table_row\",\"attrs\":{\"dataRowDiffId\":\"c7ba6940-a1d3-4459-9d6b-794aa0c1facf\",\"nodeId\":\"27042a9dd75c403aa769154ef667520a\"},\"content\":[{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[409],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"97b9d59f-8452-4318-941b-3569b568c70c\",\"nodeId\":\"fdc1e24ac59442e6b12d5e1ae04ca539\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"3b95d9f7-cbf8-443c-b008-d101d15a0b4e\",\"nodeId\":\"0765dcaa4f024da3b9abf82e8bf3aab9\"},\"content\":[{\"type\":\"text\",\"text\":\"${raptor@hosts@具体参数}\"}]}]},{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[341],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"ee47424f-69f7-426e-a10e-f50afe26a8cb\",\"nodeId\":\"c02d5d976e424ce7af7f12c56af3a786\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"0ddef3e6-9f59-4c52-828b-66b2d9bcec51\",\"nodeId\":\"3a06197e00f745469388a0b42c1501ed\"}}]}]},{\"type\":\"table_row\",\"attrs\":{\"dataRowDiffId\":\"57ecd24d-edfa-481b-8380-8df585796205\",\"nodeId\":\"e62b91c2eaa24b2e8b09f0975d347742\"},\"content\":[{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[126],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"0e6eab78-6ca4-48a5-aa17-bed450fdeae9\",\"nodeId\":\"d8991bf229904477b2b929ff16c663b3\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"bcad4e2d-3631-45a2-8116-ceeae19f0f08\",\"nodeId\":\"2547ad23f2044a698b95e14882935af9\"},\"content\":[{\"type\":\"text\",\"text\":\"hive表\"}]}]},{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[409],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"f9441906-b2be-439c-b1d2-8be1aab74dcc\",\"nodeId\":\"831cfb654b004a3e9231875f92630f01\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"e5d23df0-5a86-4446-bc55-513341bfebe5\",\"nodeId\":\"119c2e4a56e642b4b7b5df2ca664d8f4\"},\"content\":[{\"type\":\"text\",\"text\":\"${hive@sql=xxx}\"}]}]},{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[341],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"b4f4ffdf-334a-4220-b7d1-7ac024716877\",\"nodeId\":\"df8a2d5aea7146e2aca05052d1ab305c\"},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"indent\":0,\"align\":\"\",\"dataDiffId\":\"185b5644-8e69-4cc6-bf7f-a3a128def17b\",\"nodeId\":\"7066b632c5c6433bbd815aa33e22008a\"},\"content\":[{\"type\":\"text\",\"text\":\"查询sql结果替换\"}]}]},{\"type\":\"table_cell\",\"attrs\":{\"colspan\":1,\"rowspan\":1,\"colwidth\":[341],\"textAlign\":\"\",\"verticalAlign\":\"\",\"bgColor\":\"\",\"color\":\"\",\"numCell\":false,\"dataCellDiffId\":\"a54302cd-4791-42d7-b8fc-8b498f39562b\",\"nodeId\":\"ecb1b7825166400dac4c68004d0e1a96\"},\"content\":[{\"type\":\"code_block\",\"attrs\":{\"language\":\"SQL\",\"theme\":\"xq-light\",\"title\":\"代码块\",\"isExpand\":false,\"isCreate\":false,\"dataDiffId\":\"d000fe0c-e1af-4e94-9b6b-72b6abc0c47b\",\"id\":\"632a0b3e-12ed-4b13-8fed-08bb7f798454\",\"nodeId\":\"f75cf0e7dc074ac0a27f148d39da2ec7\"},\"content\":[{\"type\":\"text\",\"text\":\"select count(*) as total,\\n       sum(if(get_json_object(extra, '$.useCache') = 'true', 1, 0)) AS cache_cnt,\\n\\tFROM al_catering.app_algoplatform_predictor_result\\n WHERE dt = '${partitiondate}'\\n   AND HOUR = '${hour}'\\n   AND get_json_object(extra, '$.source') = 'udf'\"}]}]}]}]}]}";
//        String regex = "\\$\\{([^}]+)\\}";
//        ;
//
//        // 提取所有匹配的捕获组内容
//        List<String> matches = extractMatches(input, regex);
//        System.out.println("Extracted content: " + matches);
//    }
}

