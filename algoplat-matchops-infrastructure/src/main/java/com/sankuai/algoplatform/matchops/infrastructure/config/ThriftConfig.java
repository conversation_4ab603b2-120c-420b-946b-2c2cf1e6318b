package com.sankuai.algoplatform.matchops.infrastructure.config;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.S3ClientOptions;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.sankuai.ai.friday.model.deploy.thrift.FridayMLModelDeployService;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.CitadelService;
import com.sankuai.mlp.kub.service.job.JobServiceRPC;
import com.sankuai.xm.pubapi.thrift.PushMessageServiceI;
import com.sankuai.xm.udb.common.UdbServiceI;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ThriftConfig {

    @MdpThriftClient(
            remoteAppKey = "com.sankuai.llmsftinfra.model.autodeploy",
            timeout = 30000)
    private FridayMLModelDeployService.Iface fridayMLModelDeployService;

    @Bean(name = "clientProxy", destroyMethod = "destroy")
    ThriftClientProxy mlpJobService() {
        ThriftClientProxy clientProxy = new ThriftClientProxy();
        clientProxy.setServiceInterface(JobServiceRPC.class);
        clientProxy.setRemoteAppkey("com.sankuai.mlp.training.api");
        clientProxy.setTimeout(20000);
        // test环境和dev环境需要IP直连
        if (MdpContextUtils.isOfflineEnv()) {
            clientProxy.setServerIpPorts("************:9001");
        } else {
            clientProxy.setRemoteUniProto(true);
        }
        return clientProxy;
    }

    @Bean(name = "xmAuthService", destroyMethod = "destroy")
    ThriftClientProxy xmAuthService() {
        ThriftClientProxy clientProxy = new ThriftClientProxy();
        clientProxy.setServiceInterface(com.sankuai.xm.openplatform.auth.service.XmAuthServiceI.class);
        clientProxy.setRemoteAppkey("com.sankuai.dxenterprise.open.gateway");
        clientProxy.setTimeout(8000);
        clientProxy.setRemoteUniProto(true);
        return clientProxy;
    }

    @Bean(name = "xmKmService", destroyMethod = "destroy")
    ThriftClientProxy xmKmService() {
        ThriftClientProxy clientProxy = new ThriftClientProxy();
        clientProxy.setServiceInterface(com.sankuai.xm.openplatform.api.service.open.XmOpenKmServiceI.class);
        clientProxy.setRemoteAppkey("com.sankuai.dxenterprise.open.gateway");
        clientProxy.setTimeout(8000);
        clientProxy.setRemoteUniProto(true);
        return clientProxy;
    }

    @Bean
    public CitadelService citadelService() {
        return new CitadelService();
    }

    @MdpThriftClient(remoteAppKey = "com.sankuai.xm.udb", timeout = 3000)
    private UdbServiceI.Iface udbService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.xm.pubapi", timeout = 5000)
    private PushMessageServiceI.Iface pushMessageService;

    @Bean
    ThriftClientProxy mlpModelServingService() {
        // https://km.sankuai.com/page/2031440239
        ThriftClientProxy clientProxy = new ThriftClientProxy();
        clientProxy.setServiceInterface(com.sankuai.aifree.thrift.generation.MLPModelServingService.class);
        clientProxy.setRemoteAppkey("com.sankuai.mlp.msp");
        clientProxy.setTimeout(8000);
        clientProxy.setRemoteUniProto(true);
        if (MdpContextUtils.isOfflineEnv()) {
            clientProxy.setServerIpPorts("*************:8416");
        }
        clientProxy.setFilterByServiceName(true);
        return clientProxy;
    }

    @Bean
    public AmazonS3 amazonS3(S3Properties s3Properties) {
        AWSCredentials credentials = new BasicAWSCredentials(s3Properties.getAccessKey(), s3Properties.getSecretKey());

        ClientConfiguration configuration = new ClientConfiguration();
        configuration.setProtocol(Protocol.HTTPS);
        AmazonS3 s3client = new AmazonS3Client(credentials, configuration);
        s3client.setEndpoint(s3Properties.getHostname());
        S3ClientOptions s3ClientOptions = new S3ClientOptions();
        s3ClientOptions.setPathStyleAccess(true);
        s3client.setS3ClientOptions(s3ClientOptions);
        return s3client;
    }

}
