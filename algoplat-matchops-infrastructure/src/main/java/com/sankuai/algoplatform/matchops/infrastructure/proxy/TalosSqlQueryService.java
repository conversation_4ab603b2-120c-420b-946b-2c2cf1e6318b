package com.sankuai.algoplatform.matchops.infrastructure.proxy;

import com.meituan.talos.commons.domain.Column;
import com.meituan.talos.commons.exception.TalosException;
import com.sankuai.algoplatform.matchops.infrastructure.model.SqlResult;
import com.sankuai.algoplatform.matchops.infrastructure.monitor.RaptorTrack;
import com.sankuai.data.talos.AsyncQueryResult;
import com.sankuai.data.talos.AsyncTalosClient;
import com.sankuai.data.talos.QueryRequest;
import com.sankuai.data.talos.Talos;
import com.sankuai.data.talos.model.Engine;
import com.sankuai.data.talos.model.QueryInfo;
import com.sankuai.data.talos.model.QueryResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 查询sql
 * https://km.sankuai.com/collabpage/111057708
 */
@Service
@Slf4j
public class TalosSqlQueryService {

    @Autowired
    private AsyncTalosClient asyncTalosClient;

    public void refreshTalosSession() {
        boolean res = false;
        try {
            res = asyncTalosClient.openSession(new Talos.RequestOption(60, 60, 60, TimeUnit.SECONDS));
        } catch (TalosException e) {
            log.error("refreshTalosSession fail", e);
        }
        if (!res) {
            log.error("refreshTalosSession fail");
            RaptorTrack.Sys_UnexpectedVisitNum.report("RefreshTalosSessionFail");
        }
        log.info("refreshTalosSession result：{}", res);
    }

    public SqlResult executeSql(String sql) throws Exception {
        log.info("executeSql sql:{}", sql);
        SqlResult sqlResult = new SqlResult();
        sqlResult.setCode(-1);
        sqlResult.setMessage("unknown reason");
        sqlResult.setData(new ArrayList<>());
        sqlResult.setColumn(new ArrayList<>());
        QueryRequest request = new QueryRequest.Builder()
                .engine(Engine.Presto.getName())
                .statement(sql)
                .build();
        AsyncQueryResult asyncResult = asyncTalosClient.submitAsync(request);

        String qid = asyncResult.getQid();

        int res = asyncTalosClient.waitForFinished(qid);

        if (res == 1) {
            //查询成功，下载查询结果
            QueryResult queryResult = asyncTalosClient.getQueryResult(qid);
            List<String> columns = queryResult.getColumns().stream().map(Column::getName).collect(Collectors.toList());
            List<List<Object>> row = queryResult.fetchAll();
            sqlResult.setCode(0);
            sqlResult.setMessage("success");
            sqlResult.setColumn(columns);
            sqlResult.setData(row);
            return sqlResult;
        }
        String msg = "未知的查询返回值：" + res;
        if (res == -1) {
            //查询失败，获取查询失败原因
            QueryInfo queryInfo = asyncTalosClient.getQueryInfo(qid);
            log.error("Talos Query failed, error: {}", queryInfo.getMessage());
            log.error("Talos Query EngineLog:{} ", queryInfo.getEngineLog());
            msg = queryInfo.getMessage();
            sqlResult.setCode(-1);
            sqlResult.setMessage(msg);
            sqlResult.setData(new ArrayList<>());
            sqlResult.setColumn(new ArrayList<>());
        }
        throw new Exception(msg);
    }
}
