package com.sankuai.algoplatform.matchops.infrastructure.config;

import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.xm.pubapi.thrift.PusherInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class DxPusherConfig {

    @Value("${dx.key}")
    private String key;

    @Value("${dx.pubId}")
    private long fromUid;

    private String token;

    private static final String DX_TOKEN = "dx.token";

    public static final short TO_APPID = 1;
    public static final short CHANNELLD = 0;

    @Bean
    public PusherInfo getPusherInfo() {
        initKeys();
        new PusherInfo();
        PusherInfo pusherInfo;
        pusherInfo = new PusherInfo();
        pusherInfo.setAppkey(key);
        pusherInfo.setToken(token);
        pusherInfo.setFromUid(fromUid);
        pusherInfo.setToAppid(TO_APPID);
        pusherInfo.setChannelId(CHANNELLD);
        return pusherInfo;
    }

    private void initKeys() {
        try {
            this.token = Kms.getByName(MdpContextUtils.getAppKey(), DX_TOKEN);
            log.info("获取大象KMS配置成功");
        } catch (KmsResultNullException e) {
            log.error("获取大象KMS配置失败", e);
        }
    }

}
