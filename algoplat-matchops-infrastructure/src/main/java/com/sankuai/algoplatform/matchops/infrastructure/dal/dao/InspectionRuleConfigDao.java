package com.sankuai.algoplatform.matchops.infrastructure.dal.dao;

import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.BizLine;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.InspectionRuleConfig;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.BizLineExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.InspectionRuleConfigExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.BizLineMapper;
import com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.InspectionRuleConfigMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class InspectionRuleConfigDao {

    @Autowired
    InspectionRuleConfigMapper inspectionRuleConfigMapper;

    public List<InspectionRuleConfig> getAll() {
        return inspectionRuleConfigMapper.selectByExample(new InspectionRuleConfigExample());
    }

}
