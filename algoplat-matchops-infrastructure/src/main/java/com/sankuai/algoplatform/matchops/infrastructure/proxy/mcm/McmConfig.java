package com.sankuai.algoplatform.matchops.infrastructure.proxy.mcm;

import com.sankuai.algoplatform.matchops.infrastructure.proxy.mcm.context.*;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.mcm.custom.*;
import com.sankuai.mcm.client.sdk.config.McmFilterFactoryBean;
import com.sankuai.mcm.client.sdk.config.annotation.EnableMcm;
import com.sankuai.mcm.client.sdk.config.annotation.McmConfigurerAdaptor;
import com.sankuai.mcm.client.sdk.config.annotation.registry.CustomConfigProviderRegistry;
import com.sankuai.mcm.client.sdk.config.annotation.registry.EventContextPropertyProviderRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.DelegatingFilterProxy;

import javax.servlet.DispatcherType;

/**
 * MCM配置类
 */
@EnableMcm
@Configuration
public class McmConfig extends McmConfigurerAdaptor {

    @Autowired
    private CreateNoticeCustomConfigProvider createNoticeCustomConfigProvider;
    @Autowired
    private DefaultEventContextPropertyProvider defaultEventContextPropertyProvider;
    @Autowired
    private ModelDeployCustomConfigProvider modelDeployCustomConfigProvider;
    @Autowired
    private UpdateAlgoCodePackageCustomConfigProvider updateAlgoCodePackageCustomConfigProvider;
    @Autowired
    private UpdatePromptCustomConfigProvider updatePromptCustomConfigProvider;
    @Autowired
    private UpdateRuleCustomConfigProvider updateRuleCustomConfigProvider;
    @Autowired
    private UpdateAlgoStrategyCustomConfigProvider updateAlgoStrategyCustomConfigProvider;
    @Autowired
    private UpdateAlgoBizCodeCustomConfigProvider updateAlgoBizCodeCustomConfigProvider;
    @Autowired
    private UpdatePromptBizCodeCustomConfigProvider updatePromptBizCodeCustomConfigProvider;
    @Autowired
    private UpdatePromptStrategyCustomConfigProvider updatePromptStrategyCustomConfigProvider;
    @Autowired
    private PromptEventContextPropertyProvider promptEventContextPropertyProvider;
    @Autowired
    private PromptBizCodeEventContextPropertyProvider promptBizCodeEventContextPropertyProvider;
    @Autowired
    private AlgoBizCodeEventContextPropertyProvider algoBizCodeEventContextPropertyProvider;
    @Autowired
    private AlgoStrategyEventContextPropertyProvider algoStrategyEventContextPropertyProvider;

    @Bean
    public McmFilterFactoryBean mcmFilterBean() {
        return new McmFilterFactoryBean();
    }

    @Bean
    public FilterRegistrationBean mcmFilter() {
        DelegatingFilterProxy filter = new DelegatingFilterProxy();
        filter.setTargetBeanName("mcmFilterBean");
        filter.setTargetFilterLifecycle(true);

        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(filter);
        registration.addUrlPatterns(
                // 管控的接口
                "/test/mcm",
                // 审核回调的接口，只是一个标识URL,不需要写Controller，只是用于后端路由，只要能确保回调请求路由到变更系统后端即可
                "/testtool/api/mcm/audit/callback");
        registration.setDispatcherTypes(DispatcherType.REQUEST);
        registration.setName("mcmFilterBean");
        registration.setOrder(Integer.MAX_VALUE);

        return registration;
    }
    //MCM自定义内容注册
    @Override
    public void addCustomConfigProvider(CustomConfigProviderRegistry registry) {
        registry.addCustomConfigProvider(updateAlgoStrategyCustomConfigProvider);
        registry.addCustomConfigProvider(updateAlgoBizCodeCustomConfigProvider);
        registry.addCustomConfigProvider(updatePromptBizCodeCustomConfigProvider);
        registry.addCustomConfigProvider(updatePromptStrategyCustomConfigProvider);
    }
    // 更改内容对比
    @Override
    public void addEventContextPropertyProvider(EventContextPropertyProviderRegistry registry) {
        registry.addPropertyProvider(promptEventContextPropertyProvider);
        registry.addPropertyProvider(promptBizCodeEventContextPropertyProvider);
        registry.addPropertyProvider(algoBizCodeEventContextPropertyProvider);
        registry.addPropertyProvider(algoStrategyEventContextPropertyProvider);
    }
}