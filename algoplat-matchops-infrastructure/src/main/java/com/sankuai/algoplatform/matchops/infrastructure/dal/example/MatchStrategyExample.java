package com.sankuai.algoplatform.matchops.infrastructure.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MatchStrategyExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public MatchStrategyExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public MatchStrategyExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public MatchStrategyExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public MatchStrategyExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andBizLineIdIsNull() {
            addCriterion("biz_line_id is null");
            return (Criteria) this;
        }

        public Criteria andBizLineIdIsNotNull() {
            addCriterion("biz_line_id is not null");
            return (Criteria) this;
        }

        public Criteria andBizLineIdEqualTo(Long value) {
            addCriterion("biz_line_id =", value, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdNotEqualTo(Long value) {
            addCriterion("biz_line_id <>", value, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdGreaterThan(Long value) {
            addCriterion("biz_line_id >", value, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdGreaterThanOrEqualTo(Long value) {
            addCriterion("biz_line_id >=", value, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdLessThan(Long value) {
            addCriterion("biz_line_id <", value, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdLessThanOrEqualTo(Long value) {
            addCriterion("biz_line_id <=", value, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdIn(List<Long> values) {
            addCriterion("biz_line_id in", values, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdNotIn(List<Long> values) {
            addCriterion("biz_line_id not in", values, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdBetween(Long value1, Long value2) {
            addCriterion("biz_line_id between", value1, value2, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdNotBetween(Long value1, Long value2) {
            addCriterion("biz_line_id not between", value1, value2, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andTestSceneIdIsNull() {
            addCriterion("test_scene_id is null");
            return (Criteria) this;
        }

        public Criteria andTestSceneIdIsNotNull() {
            addCriterion("test_scene_id is not null");
            return (Criteria) this;
        }

        public Criteria andTestSceneIdEqualTo(Long value) {
            addCriterion("test_scene_id =", value, "testSceneId");
            return (Criteria) this;
        }

        public Criteria andTestSceneIdNotEqualTo(Long value) {
            addCriterion("test_scene_id <>", value, "testSceneId");
            return (Criteria) this;
        }

        public Criteria andTestSceneIdGreaterThan(Long value) {
            addCriterion("test_scene_id >", value, "testSceneId");
            return (Criteria) this;
        }

        public Criteria andTestSceneIdGreaterThanOrEqualTo(Long value) {
            addCriterion("test_scene_id >=", value, "testSceneId");
            return (Criteria) this;
        }

        public Criteria andTestSceneIdLessThan(Long value) {
            addCriterion("test_scene_id <", value, "testSceneId");
            return (Criteria) this;
        }

        public Criteria andTestSceneIdLessThanOrEqualTo(Long value) {
            addCriterion("test_scene_id <=", value, "testSceneId");
            return (Criteria) this;
        }

        public Criteria andTestSceneIdIn(List<Long> values) {
            addCriterion("test_scene_id in", values, "testSceneId");
            return (Criteria) this;
        }

        public Criteria andTestSceneIdNotIn(List<Long> values) {
            addCriterion("test_scene_id not in", values, "testSceneId");
            return (Criteria) this;
        }

        public Criteria andTestSceneIdBetween(Long value1, Long value2) {
            addCriterion("test_scene_id between", value1, value2, "testSceneId");
            return (Criteria) this;
        }

        public Criteria andTestSceneIdNotBetween(Long value1, Long value2) {
            addCriterion("test_scene_id not between", value1, value2, "testSceneId");
            return (Criteria) this;
        }

        public Criteria andAlgoBizCodesIsNull() {
            addCriterion("algo_biz_codes is null");
            return (Criteria) this;
        }

        public Criteria andAlgoBizCodesIsNotNull() {
            addCriterion("algo_biz_codes is not null");
            return (Criteria) this;
        }

        public Criteria andAlgoBizCodesEqualTo(String value) {
            addCriterion("algo_biz_codes =", value, "algoBizCodes");
            return (Criteria) this;
        }

        public Criteria andAlgoBizCodesNotEqualTo(String value) {
            addCriterion("algo_biz_codes <>", value, "algoBizCodes");
            return (Criteria) this;
        }

        public Criteria andAlgoBizCodesGreaterThan(String value) {
            addCriterion("algo_biz_codes >", value, "algoBizCodes");
            return (Criteria) this;
        }

        public Criteria andAlgoBizCodesGreaterThanOrEqualTo(String value) {
            addCriterion("algo_biz_codes >=", value, "algoBizCodes");
            return (Criteria) this;
        }

        public Criteria andAlgoBizCodesLessThan(String value) {
            addCriterion("algo_biz_codes <", value, "algoBizCodes");
            return (Criteria) this;
        }

        public Criteria andAlgoBizCodesLessThanOrEqualTo(String value) {
            addCriterion("algo_biz_codes <=", value, "algoBizCodes");
            return (Criteria) this;
        }

        public Criteria andAlgoBizCodesLike(String value) {
            addCriterion("algo_biz_codes like", value, "algoBizCodes");
            return (Criteria) this;
        }

        public Criteria andAlgoBizCodesNotLike(String value) {
            addCriterion("algo_biz_codes not like", value, "algoBizCodes");
            return (Criteria) this;
        }

        public Criteria andAlgoBizCodesIn(List<String> values) {
            addCriterion("algo_biz_codes in", values, "algoBizCodes");
            return (Criteria) this;
        }

        public Criteria andAlgoBizCodesNotIn(List<String> values) {
            addCriterion("algo_biz_codes not in", values, "algoBizCodes");
            return (Criteria) this;
        }

        public Criteria andAlgoBizCodesBetween(String value1, String value2) {
            addCriterion("algo_biz_codes between", value1, value2, "algoBizCodes");
            return (Criteria) this;
        }

        public Criteria andAlgoBizCodesNotBetween(String value1, String value2) {
            addCriterion("algo_biz_codes not between", value1, value2, "algoBizCodes");
            return (Criteria) this;
        }

        public Criteria andPredictorCacheIsNull() {
            addCriterion("predictor_cache is null");
            return (Criteria) this;
        }

        public Criteria andPredictorCacheIsNotNull() {
            addCriterion("predictor_cache is not null");
            return (Criteria) this;
        }

        public Criteria andPredictorCacheEqualTo(String value) {
            addCriterion("predictor_cache =", value, "predictorCache");
            return (Criteria) this;
        }

        public Criteria andPredictorCacheNotEqualTo(String value) {
            addCriterion("predictor_cache <>", value, "predictorCache");
            return (Criteria) this;
        }

        public Criteria andPredictorCacheGreaterThan(String value) {
            addCriterion("predictor_cache >", value, "predictorCache");
            return (Criteria) this;
        }

        public Criteria andPredictorCacheGreaterThanOrEqualTo(String value) {
            addCriterion("predictor_cache >=", value, "predictorCache");
            return (Criteria) this;
        }

        public Criteria andPredictorCacheLessThan(String value) {
            addCriterion("predictor_cache <", value, "predictorCache");
            return (Criteria) this;
        }

        public Criteria andPredictorCacheLessThanOrEqualTo(String value) {
            addCriterion("predictor_cache <=", value, "predictorCache");
            return (Criteria) this;
        }

        public Criteria andPredictorCacheLike(String value) {
            addCriterion("predictor_cache like", value, "predictorCache");
            return (Criteria) this;
        }

        public Criteria andPredictorCacheNotLike(String value) {
            addCriterion("predictor_cache not like", value, "predictorCache");
            return (Criteria) this;
        }

        public Criteria andPredictorCacheIn(List<String> values) {
            addCriterion("predictor_cache in", values, "predictorCache");
            return (Criteria) this;
        }

        public Criteria andPredictorCacheNotIn(List<String> values) {
            addCriterion("predictor_cache not in", values, "predictorCache");
            return (Criteria) this;
        }

        public Criteria andPredictorCacheBetween(String value1, String value2) {
            addCriterion("predictor_cache between", value1, value2, "predictorCache");
            return (Criteria) this;
        }

        public Criteria andPredictorCacheNotBetween(String value1, String value2) {
            addCriterion("predictor_cache not between", value1, value2, "predictorCache");
            return (Criteria) this;
        }

        public Criteria andLlmBizCodesIsNull() {
            addCriterion("llm_biz_codes is null");
            return (Criteria) this;
        }

        public Criteria andLlmBizCodesIsNotNull() {
            addCriterion("llm_biz_codes is not null");
            return (Criteria) this;
        }

        public Criteria andLlmBizCodesEqualTo(String value) {
            addCriterion("llm_biz_codes =", value, "llmBizCodes");
            return (Criteria) this;
        }

        public Criteria andLlmBizCodesNotEqualTo(String value) {
            addCriterion("llm_biz_codes <>", value, "llmBizCodes");
            return (Criteria) this;
        }

        public Criteria andLlmBizCodesGreaterThan(String value) {
            addCriterion("llm_biz_codes >", value, "llmBizCodes");
            return (Criteria) this;
        }

        public Criteria andLlmBizCodesGreaterThanOrEqualTo(String value) {
            addCriterion("llm_biz_codes >=", value, "llmBizCodes");
            return (Criteria) this;
        }

        public Criteria andLlmBizCodesLessThan(String value) {
            addCriterion("llm_biz_codes <", value, "llmBizCodes");
            return (Criteria) this;
        }

        public Criteria andLlmBizCodesLessThanOrEqualTo(String value) {
            addCriterion("llm_biz_codes <=", value, "llmBizCodes");
            return (Criteria) this;
        }

        public Criteria andLlmBizCodesLike(String value) {
            addCriterion("llm_biz_codes like", value, "llmBizCodes");
            return (Criteria) this;
        }

        public Criteria andLlmBizCodesNotLike(String value) {
            addCriterion("llm_biz_codes not like", value, "llmBizCodes");
            return (Criteria) this;
        }

        public Criteria andLlmBizCodesIn(List<String> values) {
            addCriterion("llm_biz_codes in", values, "llmBizCodes");
            return (Criteria) this;
        }

        public Criteria andLlmBizCodesNotIn(List<String> values) {
            addCriterion("llm_biz_codes not in", values, "llmBizCodes");
            return (Criteria) this;
        }

        public Criteria andLlmBizCodesBetween(String value1, String value2) {
            addCriterion("llm_biz_codes between", value1, value2, "llmBizCodes");
            return (Criteria) this;
        }

        public Criteria andLlmBizCodesNotBetween(String value1, String value2) {
            addCriterion("llm_biz_codes not between", value1, value2, "llmBizCodes");
            return (Criteria) this;
        }

        public Criteria andOctoServiceConfigIsNull() {
            addCriterion("octo_service_config is null");
            return (Criteria) this;
        }

        public Criteria andOctoServiceConfigIsNotNull() {
            addCriterion("octo_service_config is not null");
            return (Criteria) this;
        }

        public Criteria andOctoServiceConfigEqualTo(String value) {
            addCriterion("octo_service_config =", value, "octoServiceConfig");
            return (Criteria) this;
        }

        public Criteria andOctoServiceConfigNotEqualTo(String value) {
            addCriterion("octo_service_config <>", value, "octoServiceConfig");
            return (Criteria) this;
        }

        public Criteria andOctoServiceConfigGreaterThan(String value) {
            addCriterion("octo_service_config >", value, "octoServiceConfig");
            return (Criteria) this;
        }

        public Criteria andOctoServiceConfigGreaterThanOrEqualTo(String value) {
            addCriterion("octo_service_config >=", value, "octoServiceConfig");
            return (Criteria) this;
        }

        public Criteria andOctoServiceConfigLessThan(String value) {
            addCriterion("octo_service_config <", value, "octoServiceConfig");
            return (Criteria) this;
        }

        public Criteria andOctoServiceConfigLessThanOrEqualTo(String value) {
            addCriterion("octo_service_config <=", value, "octoServiceConfig");
            return (Criteria) this;
        }

        public Criteria andOctoServiceConfigLike(String value) {
            addCriterion("octo_service_config like", value, "octoServiceConfig");
            return (Criteria) this;
        }

        public Criteria andOctoServiceConfigNotLike(String value) {
            addCriterion("octo_service_config not like", value, "octoServiceConfig");
            return (Criteria) this;
        }

        public Criteria andOctoServiceConfigIn(List<String> values) {
            addCriterion("octo_service_config in", values, "octoServiceConfig");
            return (Criteria) this;
        }

        public Criteria andOctoServiceConfigNotIn(List<String> values) {
            addCriterion("octo_service_config not in", values, "octoServiceConfig");
            return (Criteria) this;
        }

        public Criteria andOctoServiceConfigBetween(String value1, String value2) {
            addCriterion("octo_service_config between", value1, value2, "octoServiceConfig");
            return (Criteria) this;
        }

        public Criteria andOctoServiceConfigNotBetween(String value1, String value2) {
            addCriterion("octo_service_config not between", value1, value2, "octoServiceConfig");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andDeployInfoIsNull() {
            addCriterion("deploy_info is null");
            return (Criteria) this;
        }

        public Criteria andDeployInfoIsNotNull() {
            addCriterion("deploy_info is not null");
            return (Criteria) this;
        }

        public Criteria andDeployInfoEqualTo(String value) {
            addCriterion("deploy_info =", value, "deployInfo");
            return (Criteria) this;
        }

        public Criteria andDeployInfoNotEqualTo(String value) {
            addCriterion("deploy_info <>", value, "deployInfo");
            return (Criteria) this;
        }

        public Criteria andDeployInfoGreaterThan(String value) {
            addCriterion("deploy_info >", value, "deployInfo");
            return (Criteria) this;
        }

        public Criteria andDeployInfoGreaterThanOrEqualTo(String value) {
            addCriterion("deploy_info >=", value, "deployInfo");
            return (Criteria) this;
        }

        public Criteria andDeployInfoLessThan(String value) {
            addCriterion("deploy_info <", value, "deployInfo");
            return (Criteria) this;
        }

        public Criteria andDeployInfoLessThanOrEqualTo(String value) {
            addCriterion("deploy_info <=", value, "deployInfo");
            return (Criteria) this;
        }

        public Criteria andDeployInfoLike(String value) {
            addCriterion("deploy_info like", value, "deployInfo");
            return (Criteria) this;
        }

        public Criteria andDeployInfoNotLike(String value) {
            addCriterion("deploy_info not like", value, "deployInfo");
            return (Criteria) this;
        }

        public Criteria andDeployInfoIn(List<String> values) {
            addCriterion("deploy_info in", values, "deployInfo");
            return (Criteria) this;
        }

        public Criteria andDeployInfoNotIn(List<String> values) {
            addCriterion("deploy_info not in", values, "deployInfo");
            return (Criteria) this;
        }

        public Criteria andDeployInfoBetween(String value1, String value2) {
            addCriterion("deploy_info between", value1, value2, "deployInfo");
            return (Criteria) this;
        }

        public Criteria andDeployInfoNotBetween(String value1, String value2) {
            addCriterion("deploy_info not between", value1, value2, "deployInfo");
            return (Criteria) this;
        }

        public Criteria andOwnerIsNull() {
            addCriterion("owner is null");
            return (Criteria) this;
        }

        public Criteria andOwnerIsNotNull() {
            addCriterion("owner is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerEqualTo(String value) {
            addCriterion("owner =", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotEqualTo(String value) {
            addCriterion("owner <>", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerGreaterThan(String value) {
            addCriterion("owner >", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerGreaterThanOrEqualTo(String value) {
            addCriterion("owner >=", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerLessThan(String value) {
            addCriterion("owner <", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerLessThanOrEqualTo(String value) {
            addCriterion("owner <=", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerLike(String value) {
            addCriterion("owner like", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotLike(String value) {
            addCriterion("owner not like", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerIn(List<String> values) {
            addCriterion("owner in", values, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotIn(List<String> values) {
            addCriterion("owner not in", values, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerBetween(String value1, String value2) {
            addCriterion("owner between", value1, value2, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotBetween(String value1, String value2) {
            addCriterion("owner not between", value1, value2, "owner");
            return (Criteria) this;
        }

        public Criteria andIsDelIsNull() {
            addCriterion("is_del is null");
            return (Criteria) this;
        }

        public Criteria andIsDelIsNotNull() {
            addCriterion("is_del is not null");
            return (Criteria) this;
        }

        public Criteria andIsDelEqualTo(Boolean value) {
            addCriterion("is_del =", value, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelNotEqualTo(Boolean value) {
            addCriterion("is_del <>", value, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelGreaterThan(Boolean value) {
            addCriterion("is_del >", value, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_del >=", value, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelLessThan(Boolean value) {
            addCriterion("is_del <", value, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelLessThanOrEqualTo(Boolean value) {
            addCriterion("is_del <=", value, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelIn(List<Boolean> values) {
            addCriterion("is_del in", values, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelNotIn(List<Boolean> values) {
            addCriterion("is_del not in", values, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelBetween(Boolean value1, Boolean value2) {
            addCriterion("is_del between", value1, value2, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_del not between", value1, value2, "isDel");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}