package com.sankuai.algoplatform.matchops.infrastructure.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ResourceGroupExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public ResourceGroupExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public ResourceGroupExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public ResourceGroupExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public ResourceGroupExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBizLineIdIsNull() {
            addCriterion("biz_line_id is null");
            return (Criteria) this;
        }

        public Criteria andBizLineIdIsNotNull() {
            addCriterion("biz_line_id is not null");
            return (Criteria) this;
        }

        public Criteria andBizLineIdEqualTo(Long value) {
            addCriterion("biz_line_id =", value, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdNotEqualTo(Long value) {
            addCriterion("biz_line_id <>", value, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdGreaterThan(Long value) {
            addCriterion("biz_line_id >", value, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdGreaterThanOrEqualTo(Long value) {
            addCriterion("biz_line_id >=", value, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdLessThan(Long value) {
            addCriterion("biz_line_id <", value, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdLessThanOrEqualTo(Long value) {
            addCriterion("biz_line_id <=", value, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdIn(List<Long> values) {
            addCriterion("biz_line_id in", values, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdNotIn(List<Long> values) {
            addCriterion("biz_line_id not in", values, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdBetween(Long value1, Long value2) {
            addCriterion("biz_line_id between", value1, value2, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdNotBetween(Long value1, Long value2) {
            addCriterion("biz_line_id not between", value1, value2, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andOctoResourcesIsNull() {
            addCriterion("octo_resources is null");
            return (Criteria) this;
        }

        public Criteria andOctoResourcesIsNotNull() {
            addCriterion("octo_resources is not null");
            return (Criteria) this;
        }

        public Criteria andOctoResourcesEqualTo(String value) {
            addCriterion("octo_resources =", value, "octoResources");
            return (Criteria) this;
        }

        public Criteria andOctoResourcesNotEqualTo(String value) {
            addCriterion("octo_resources <>", value, "octoResources");
            return (Criteria) this;
        }

        public Criteria andOctoResourcesGreaterThan(String value) {
            addCriterion("octo_resources >", value, "octoResources");
            return (Criteria) this;
        }

        public Criteria andOctoResourcesGreaterThanOrEqualTo(String value) {
            addCriterion("octo_resources >=", value, "octoResources");
            return (Criteria) this;
        }

        public Criteria andOctoResourcesLessThan(String value) {
            addCriterion("octo_resources <", value, "octoResources");
            return (Criteria) this;
        }

        public Criteria andOctoResourcesLessThanOrEqualTo(String value) {
            addCriterion("octo_resources <=", value, "octoResources");
            return (Criteria) this;
        }

        public Criteria andOctoResourcesLike(String value) {
            addCriterion("octo_resources like", value, "octoResources");
            return (Criteria) this;
        }

        public Criteria andOctoResourcesNotLike(String value) {
            addCriterion("octo_resources not like", value, "octoResources");
            return (Criteria) this;
        }

        public Criteria andOctoResourcesIn(List<String> values) {
            addCriterion("octo_resources in", values, "octoResources");
            return (Criteria) this;
        }

        public Criteria andOctoResourcesNotIn(List<String> values) {
            addCriterion("octo_resources not in", values, "octoResources");
            return (Criteria) this;
        }

        public Criteria andOctoResourcesBetween(String value1, String value2) {
            addCriterion("octo_resources between", value1, value2, "octoResources");
            return (Criteria) this;
        }

        public Criteria andOctoResourcesNotBetween(String value1, String value2) {
            addCriterion("octo_resources not between", value1, value2, "octoResources");
            return (Criteria) this;
        }

        public Criteria andMlpResourcesIsNull() {
            addCriterion("mlp_resources is null");
            return (Criteria) this;
        }

        public Criteria andMlpResourcesIsNotNull() {
            addCriterion("mlp_resources is not null");
            return (Criteria) this;
        }

        public Criteria andMlpResourcesEqualTo(String value) {
            addCriterion("mlp_resources =", value, "mlpResources");
            return (Criteria) this;
        }

        public Criteria andMlpResourcesNotEqualTo(String value) {
            addCriterion("mlp_resources <>", value, "mlpResources");
            return (Criteria) this;
        }

        public Criteria andMlpResourcesGreaterThan(String value) {
            addCriterion("mlp_resources >", value, "mlpResources");
            return (Criteria) this;
        }

        public Criteria andMlpResourcesGreaterThanOrEqualTo(String value) {
            addCriterion("mlp_resources >=", value, "mlpResources");
            return (Criteria) this;
        }

        public Criteria andMlpResourcesLessThan(String value) {
            addCriterion("mlp_resources <", value, "mlpResources");
            return (Criteria) this;
        }

        public Criteria andMlpResourcesLessThanOrEqualTo(String value) {
            addCriterion("mlp_resources <=", value, "mlpResources");
            return (Criteria) this;
        }

        public Criteria andMlpResourcesLike(String value) {
            addCriterion("mlp_resources like", value, "mlpResources");
            return (Criteria) this;
        }

        public Criteria andMlpResourcesNotLike(String value) {
            addCriterion("mlp_resources not like", value, "mlpResources");
            return (Criteria) this;
        }

        public Criteria andMlpResourcesIn(List<String> values) {
            addCriterion("mlp_resources in", values, "mlpResources");
            return (Criteria) this;
        }

        public Criteria andMlpResourcesNotIn(List<String> values) {
            addCriterion("mlp_resources not in", values, "mlpResources");
            return (Criteria) this;
        }

        public Criteria andMlpResourcesBetween(String value1, String value2) {
            addCriterion("mlp_resources between", value1, value2, "mlpResources");
            return (Criteria) this;
        }

        public Criteria andMlpResourcesNotBetween(String value1, String value2) {
            addCriterion("mlp_resources not between", value1, value2, "mlpResources");
            return (Criteria) this;
        }

        public Criteria andFridayResourcesIsNull() {
            addCriterion("friday_resources is null");
            return (Criteria) this;
        }

        public Criteria andFridayResourcesIsNotNull() {
            addCriterion("friday_resources is not null");
            return (Criteria) this;
        }

        public Criteria andFridayResourcesEqualTo(String value) {
            addCriterion("friday_resources =", value, "fridayResources");
            return (Criteria) this;
        }

        public Criteria andFridayResourcesNotEqualTo(String value) {
            addCriterion("friday_resources <>", value, "fridayResources");
            return (Criteria) this;
        }

        public Criteria andFridayResourcesGreaterThan(String value) {
            addCriterion("friday_resources >", value, "fridayResources");
            return (Criteria) this;
        }

        public Criteria andFridayResourcesGreaterThanOrEqualTo(String value) {
            addCriterion("friday_resources >=", value, "fridayResources");
            return (Criteria) this;
        }

        public Criteria andFridayResourcesLessThan(String value) {
            addCriterion("friday_resources <", value, "fridayResources");
            return (Criteria) this;
        }

        public Criteria andFridayResourcesLessThanOrEqualTo(String value) {
            addCriterion("friday_resources <=", value, "fridayResources");
            return (Criteria) this;
        }

        public Criteria andFridayResourcesLike(String value) {
            addCriterion("friday_resources like", value, "fridayResources");
            return (Criteria) this;
        }

        public Criteria andFridayResourcesNotLike(String value) {
            addCriterion("friday_resources not like", value, "fridayResources");
            return (Criteria) this;
        }

        public Criteria andFridayResourcesIn(List<String> values) {
            addCriterion("friday_resources in", values, "fridayResources");
            return (Criteria) this;
        }

        public Criteria andFridayResourcesNotIn(List<String> values) {
            addCriterion("friday_resources not in", values, "fridayResources");
            return (Criteria) this;
        }

        public Criteria andFridayResourcesBetween(String value1, String value2) {
            addCriterion("friday_resources between", value1, value2, "fridayResources");
            return (Criteria) this;
        }

        public Criteria andFridayResourcesNotBetween(String value1, String value2) {
            addCriterion("friday_resources not between", value1, value2, "fridayResources");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("creator is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("creator is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(String value) {
            addCriterion("creator =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(String value) {
            addCriterion("creator <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(String value) {
            addCriterion("creator >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(String value) {
            addCriterion("creator >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(String value) {
            addCriterion("creator <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(String value) {
            addCriterion("creator <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLike(String value) {
            addCriterion("creator like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotLike(String value) {
            addCriterion("creator not like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<String> values) {
            addCriterion("creator in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<String> values) {
            addCriterion("creator not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(String value1, String value2) {
            addCriterion("creator between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(String value1, String value2) {
            addCriterion("creator not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIsDelIsNull() {
            addCriterion("is_del is null");
            return (Criteria) this;
        }

        public Criteria andIsDelIsNotNull() {
            addCriterion("is_del is not null");
            return (Criteria) this;
        }

        public Criteria andIsDelEqualTo(Boolean value) {
            addCriterion("is_del =", value, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelNotEqualTo(Boolean value) {
            addCriterion("is_del <>", value, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelGreaterThan(Boolean value) {
            addCriterion("is_del >", value, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_del >=", value, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelLessThan(Boolean value) {
            addCriterion("is_del <", value, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelLessThanOrEqualTo(Boolean value) {
            addCriterion("is_del <=", value, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelIn(List<Boolean> values) {
            addCriterion("is_del in", values, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelNotIn(List<Boolean> values) {
            addCriterion("is_del not in", values, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelBetween(Boolean value1, Boolean value2) {
            addCriterion("is_del between", value1, value2, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_del not between", value1, value2, "isDel");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}