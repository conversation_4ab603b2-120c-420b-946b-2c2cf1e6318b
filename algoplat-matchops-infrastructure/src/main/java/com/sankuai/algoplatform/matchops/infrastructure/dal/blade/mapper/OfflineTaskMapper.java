package com.sankuai.algoplatform.matchops.infrastructure.dal.blade.mapper;


import com.sankuai.algoplatform.matchops.infrastructure.dal.blade.entity.OfflineTaskDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OfflineTaskMapper {
    List<OfflineTaskDetail> selectByCondition(@Param("record") OfflineTaskDetail condition,
                                              @Param("startId") Long startId,
                                              @Param("limit") Integer limit,
                                              @Param("industryType") String industryType,
                                              @Param("taskName") String taskName);

    long countByCondition(@Param("record") OfflineTaskDetail condition,
                          @Param("industryType") String industryType,
                          @Param("taskName") String taskName);
    List<OfflineTaskDetail> selectByRange(@Param("start") Long start,
                                          @Param("end") Long end,
                                          @Param("partitionDate") String partitionDate,
                                          @Param("industryType") String industryType,
                                          @Param("taskName") String taskName);

    int updateBatchByUniqueKey(@Param("list") List<OfflineTaskDetail> list,
                    @Param("partitionDate") String partitionDate,
                    @Param("industryType") String industryType,
                    @Param("taskName") String taskName);

    Long getMaxIdByStatus(@Param("partitionDate") String partitionDate,
                  @Param("status") Integer status,
                  @Param("resultFlag") Integer resultFlag,
                  @Param("industryType") String industryType,
                  @Param("taskName") String taskName);
    Long getMinIdByStatus(@Param("partitionDate") String partitionDate,
                  @Param("status") Integer status,
                  @Param("resultFlag") Integer resultFlag,
                  @Param("industryType") String industryType,
                  @Param("taskName") String taskName);
    String getLatestPartitionDate(@Param("industryType") String industryType,
                                  @Param("taskName") String taskName);
}