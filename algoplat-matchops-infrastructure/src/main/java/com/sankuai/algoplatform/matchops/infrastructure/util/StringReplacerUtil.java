package com.sankuai.algoplatform.matchops.infrastructure.util;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringReplacerUtil {

    /**
     * 使用Map中的键值对替换字符串中的匹配文本
     *
     * @param input        输入字符串
     * @param replacements 替换规则Map
     * @return 替换后的字符串
     */
    public static String replace(String input, Map<String, String> replacements) {
        if (input == null || replacements == null || replacements.isEmpty()) {
            return input;
        }

        // 创建正则表达式模式，匹配所有的键
        StringBuilder patternString = new StringBuilder();
        for (String key : replacements.keySet()) {
            patternString.append(Pattern.quote(key)).append("|");
        }
        // 删除最后一个多余的 '|'
        patternString.setLength(patternString.length() - 1);

        Pattern pattern = Pattern.compile(patternString.toString());
        Matcher matcher = pattern.matcher(input);

        // 使用StringBuffer来构建替换后的字符串
        StringBuffer resultString = new StringBuffer();
        while (matcher.find()) {
            String match = matcher.group();
            String replacement = replacements.get(match);
            if (replacement != null) {
                if (StringUtils.startsWith(replacement, "{") && StringUtils.endsWith(replacement, "}")) {
                    replacement = JSON.toJSONString(replacement);
                }
                matcher.appendReplacement(resultString, Matcher.quoteReplacement(replacement));
            }
        }
        matcher.appendTail(resultString);

        return resultString.toString();
    }

//    public static void main(String[] args) {
////        String info = "##Description##\\n请描述该ETL任务, 方便理解代码内容, 例如该任务产生什么数据、用于支持什么需求等\\n\\n##TaskInfo##\\ncreator = '<EMAIL>'\\n\\nsource = {\\n    'db': META['al_catering'], ## 源库对应的dsn，用于表示Extract阶段的数据源\\n}\\n\\nstream = {\\n    'format': '', ## 单引号中按序填写目标表的列名, 以逗号分割, 与Extract节点的结果顺序对应, hive2hive流程请留空\\n}\\n\\ntarget = {\\n    'db': META['al_catering'],  ## 目标库对应的dsn名\\n    'table': 'bml_dj_mt_poi_deal_realtime_match',  ## 目标表的table名\\n}\\n\\n##version='$now.timestamp'\\n##Extract##\\n## 这里填写一个能读取source库下数据sql, 读出的数据会load到target库，hive2hive流程请留空\\n\\n##Preload##\\n## 这里可以写load数据之前执行的SQL，例如清理数据、删除特定分区等，不需要请留空\\n\\n##Load##\\n## 这里填写一个能load数据的SQL，非hive2hive流程请留空\\n## 注意：注释参数请使用##，--注释仅对SQL语句生效\\nSET hive.exec.dynamic.partition.mode=nonstrict;\\nSET hive.exec.dynamic.partition=true;\\nSET hive.exec.max.dynamic.partitions=1000;\\nSET hive.default.fileformat=orc;  \\n## 触发spark合并小文件的阈值(100M)\\nSET spark.sql.mergeSmallFileSize=104857600;\\nSET spark.hadoopRDD.targetBytesInPartition=67108864;\\nSET spark.sql.adaptive.shuffle.targetPostShuffleInputSize=134217728;\\n## spark shuffle默认分区数\\nSET spark.sql.shuffle.partitions=200;\\nSET spark.hadoop.hive.exec.orc.split.strategy=ETL;\\nadd jar viewfs:///user/hadoop-udf-hub/etl-algoplat_toolkit_udf_release/algoplat_toolkit_udf_release-online.jar;\\ncreate temporary function DealMatchUdf AS 'com.sankuai.meishi.stgy.algoplatform.toolkit.udf.udf.DealMatchUdf';\\n##add jar viewfs:///user/hadoop-udf-hub/etl-algoplat-toolkit-udf/latest-test.jar;\\n##create temporary function DealMatchUdfTest AS 'com.sankuai.meishi.stgy.algoplatform.toolkit.udf.udf.DealMatchUdfTest';\\n\\nINSERT OVERWRITE TABLE `${target.table}` PARTITION (partition_date='$now.date')\\nselect \\n    dj_poi_id,\\n    mt_poi_id,\\n    -- DealMatchUdfTest(match_feature,concat(partition_date,' 03:00:00'),'test-mjf') as match_request,\\n    -- DealMatchUdf(match_feature,concat(partition_date,' 04:00:00')) as match_request,\\n    DealMatchUdf(match_feature,concat(partition_date,' 04:00:00'),'zb_dealMatching',concat('{\\\"version\\\":\\\"',version,'\\\",\\\"calculateLinkType\\\":1,\\\"seedProvider\\\":\\\"daocan_huojia_recalculate\\\",\\\"mtPoiId\\\":\\\"',nvl(mt_poi_id,''),'\\\",\\\"poiIdEnc\\\":\\\"',nvl(dj_poi_id,''),'\\\",\\\"mt_group\\\":\\\"',nvl(mt_group,''),'\\\",\\\"dj_group\\\":\\\"',nvl(dj_group,''),'\\\",\\\"request_num\\\":\\\"',nvl(request_num,''),'\\\"}')) as match_request,\\n    mt_group,\\n    dj_group,\\n    version\\nfrom\\n(\\nSELECT\\n        dj_poi_id,\\n        mt_poi_id,\\n        match_feature,\\n        partition_date,\\n        mt_group,\\n        dj_group,\\n        UNIX_TIMESTAMP() as version,\\n        max(mt_group*dj_group) OVER(PARTITION BY mt_poi_id) request_num\\nFROM\\n(\\n    select \\n    \\ta.dj_poi_id,\\n        a.mt_poi_id,\\n        a.match_feature,\\n        a.partition_date,\\n        a.mt_group,\\n        a.dj_group,\\n        a.limits\\n    from\\n    (\\n        select \\n            dj_poi_id,\\n            mt_poi_id,\\n            mt_group,\\n            dj_group,\\n            match_feature,\\n            partition_date,\\n            -- case\\n            --     when dining_type = 3 AND mt_third_cate_name IN ('西式快餐/汉堡', '西式快餐/三明治') then 0\\n            --     when ((dining_type = 1 AND mt_second_cate_name IN ('饮品','饮品店', '面包蛋糕甜品','面包甜点', '食品滋补', '生鲜蔬果','水果生鲜', '小吃快餐')) OR (dining_type = 3 AND (mt_second_cate_name IS NULL OR mt_second_cate_name != '西餐') AND (mt_third_cate_name IS NULL OR mt_third_cate_name NOT IN ('西式快餐/汉堡', '西式快餐/三明治')))) then 1 \\n            -- else 0 end as limits\\n            case\\n                when dining_type = 3 AND mt_poi_cate3_id IN (1848, 1938) then 0\\n                when ((dining_type = 1 AND mt_poi_cate2_id IN (2165,249,255,257,10)) OR (dining_type = 3 AND (mt_poi_cate2_id IS NULL OR mt_poi_cate2_id != 309) AND(mt_poi_cate3_id IS NULL OR mt_poi_cate3_id NOT IN (1848, 1938)))) then 1 \\n            else 0 end as limits\\n        from mart_zb_catering.bml_dj_mt_poi_match_features\\n        where partition_date='$now.date'\\n    )a inner join \\n    (\\n        select \\n            dj_poi_id,\\n            sum(dj_deal_cnt) as dj_deal_cnt\\n        from \\n        (\\n            select dj_poi_id,dj_group,dj_deal_cnt from mart_zb_catering.bml_dj_mt_poi_match_features\\n            where partition_date='$now.date'\\n            group by dj_poi_id,dj_group,dj_deal_cnt\\n        )t group by dj_poi_id\\n    )b on a.dj_poi_id=b.dj_poi_id\\n    inner join \\n    (\\n        select \\n            mt_poi_id,\\n            sum(mt_deal_cnt) as mt_deal_cnt\\n        from \\n        (\\n            select mt_poi_id,mt_group,mt_deal_cnt from mart_zb_catering.bml_dj_mt_poi_match_features\\n            where partition_date='$now.date'\\n            group by mt_poi_id,mt_group,mt_deal_cnt\\n        )t group by mt_poi_id\\n    )c on a.mt_poi_id=c.mt_poi_id \\n    where (limits=0 or (limits=1 and dj_deal_cnt*mt_deal_cnt<=4000)) and a.mt_poi_id not in(1225418834,1434406014,788366433,94710693)  -- 这个门店会打爆内存，第二个、第三个门店是大请求。\\n)t\\n)\\n;\\n\\n\\n##TargetDDL##\\n## 填写目标表的表结构定义，用于目标表不存在的时候根据ddl建表。请确保sql中的字段和ddl中的字段的数量、顺序一致！！\\nCREATE TABLE IF NOT EXISTS `${target.table}`\\n(\\n    `dj_poi_id`                string      comment 'DJ门店id',\\n    `mt_poi_id`                bigint      comment '美团主门店id',\\n    `match_request`            string      comment '匹配请求',\\n    `mt_group`                 bigint      comment '美团分组',\\n    `dj_group`                 bigint      comment 'DJ分组',\\n    `version`                  string      comment '版本'\\n)\\nCOMMENT 'DJ美团门店实时匹配请求表' \\nPARTITIONED BY (`partition_date` string COMMENT '分区日期，格式为yyyy-MM-dd')\\nSTORED AS ORC;";
//        String info = "##Description##\\n请描述该ETL任务, 方便理解代码内容, 例如该任务产生什么数据、用于支持什么需求等\\n\\n##TaskInfo##\\ncreator = '<EMAIL>'\\n\\nsource = {\\n    'db': META['al_catering'], ## 源库对应的dsn，用于表示Extract阶段的数据源\\n}\\n\\nstream = {\\n    'format': '', ## 单引号中按序填写目标表的列名, 以逗号分割, 与Extract节点的结果顺序对应, hive2hive流程请留空\\n}\\n\\ntarget = {\\n    'db': META['al_catering'],  ## 目标库对应的dsn名\\n    'table': 'bml_dj_mt_poi_deal_realtime_match',  ## 目标表的table名\\n}\\n\\n##version='$now.timestamp'\\n##Extract##\\n## 这里填写一个能读取source库下数据sql, 读出的数据会load到target库，hive2hive流程请留空\\n\\n##Preload##\\n## 这里可以写load数据之前执行的SQL，例如清理数据、删除特定分区等，不需要请留空\\n\\n##Load##\\n## 这里填写一个能load数据的SQL，非hive2hive流程请留空\\n## 注意：注释参数请使用##，--注释仅对SQL语句生效\\nSET hive.exec.dynamic.partition.mode=nonstrict;\\nSET hive.exec.dynamic.partition=true;\\nSET hive.exec.max.dynamic.partitions=1000;\\nSET hive.default.fileformat=orc;  \\n## 触发spark合并小文件的阈值(100M)\\nSET spark.sql.mergeSmallFileSize=104857600;\\nSET spark.hadoopRDD.targetBytesInPartition=67108864;\\nSET spark.sql.adaptive.shuffle.targetPostShuffleInputSize=134217728;\\n## spark shuffle默认分区数\\nSET spark.sql.shuffle.partitions=200;\\nSET spark.hadoop.hive.exec.orc.split.strategy=ETL;\\nadd jar viewfs:///user/hadoop-udf-hub/etl-algoplat_toolkit_udf_release/algoplat_toolkit_udf_release-online.jar;\\ncreate temporary function DealMatchUdf AS 'com.sankuai.meishi.stgy.algoplatform.toolkit.udf.udf.DealMatchUdf';\\nadd jar viewfs:///user/hadoop-udf-hub/etl-algoplat-toolkit-udf/latest-test.jar;\\ncreate temporary function DealMatchUdfTest AS 'com.sankuai.meishi.stgy.algoplatform.toolkit.udf.udf.DealMatchUdfTest';\\n\\nINSERT OVERWRITE TABLE `${target.table}` PARTITION (partition_date='$now.date')\\nselect \\n    dj_poi_id,\\n    mt_poi_id,\\n    -- DealMatchUdfTest(match_feature,concat(partition_date,' 03:00:00'),'test-mjf') as match_request,\\n    -- DealMatchUdf(match_feature,concat(partition_date,' 04:00:00')) as match_request,\\n    DealMatchUdfTest(match_feature,concat(partition_date,' 15:00:00'),'test',concat('{\\\"version\\\":\\\"',version,'\\\",\\\"calculateLinkType\\\":1,\\\"seedProvider\\\":\\\"daocan_test\\\",\\\"mtPoiId\\\":\\\"',nvl(mt_poi_id,''),'\\\",\\\"poiIdEnc\\\":\\\"',nvl(dj_poi_id,''),'\\\",\\\"mt_group\\\":\\\"',nvl(mt_group,''),'\\\",\\\"dj_group\\\":\\\"',nvl(dj_group,''),'\\\",\\\"request_num\\\":\\\"',nvl(request_num,''),'\\\"}')) as match_request,\\n    mt_group,\\n    dj_group,\\n    version\\nfrom\\n(\\nSELECT\\n        dj_poi_id,\\n        mt_poi_id,\\n        match_feature,\\n        partition_date,\\n        mt_group,\\n        dj_group,\\n        UNIX_TIMESTAMP() as version,\\n        max(mt_group*dj_group) OVER(PARTITION BY mt_poi_id) request_num\\nFROM\\n(\\n    select \\n    \\ta.dj_poi_id,\\n        a.mt_poi_id,\\n        a.match_feature,\\n        a.partition_date,\\n        a.mt_group,\\n        a.dj_group,\\n        a.limits\\n    from\\n    (\\n        select \\n            dj_poi_id,\\n            mt_poi_id,\\n            mt_group,\\n            dj_group,\\n            match_feature,\\n            partition_date,\\n            -- case\\n            --     when dining_type = 3 AND mt_third_cate_name IN ('西式快餐/汉堡', '西式快餐/三明治') then 0\\n            --     when ((dining_type = 1 AND mt_second_cate_name IN ('饮品','饮品店', '面包蛋糕甜品','面包甜点', '食品滋补', '生鲜蔬果','水果生鲜', '小吃快餐')) OR (dining_type = 3 AND (mt_second_cate_name IS NULL OR mt_second_cate_name != '西餐') AND (mt_third_cate_name IS NULL OR mt_third_cate_name NOT IN ('西式快餐/汉堡', '西式快餐/三明治')))) then 1 \\n            -- else 0 end as limits\\n            case\\n                when dining_type = 3 AND mt_poi_cate3_id IN (1848, 1938) then 0\\n                when ((dining_type = 1 AND mt_poi_cate2_id IN (2165,249,255,257,10)) OR (dining_type = 3 AND (mt_poi_cate2_id IS NULL OR mt_poi_cate2_id != 309) AND(mt_poi_cate3_id IS NULL OR mt_poi_cate3_id NOT IN (1848, 1938)))) then 1 \\n            else 0 end as limits\\n        from mart_zb_catering_test.bml_dj_mt_poi_match_features\\n        where partition_date='$now.date'\\n    )a inner join \\n    (\\n        select \\n            dj_poi_id,\\n            sum(dj_deal_cnt) as dj_deal_cnt\\n        from \\n        (\\n            select dj_poi_id,dj_group,dj_deal_cnt from mart_zb_catering_test.bml_dj_mt_poi_match_features\\n            where partition_date='$now.date'\\n            group by dj_poi_id,dj_group,dj_deal_cnt\\n        )t group by dj_poi_id\\n    )b on a.dj_poi_id=b.dj_poi_id\\n    inner join \\n    (\\n        select \\n            mt_poi_id,\\n            sum(mt_deal_cnt) as mt_deal_cnt\\n        from \\n        (\\n            select mt_poi_id,mt_group,mt_deal_cnt from mart_zb_catering_test.bml_dj_mt_poi_match_features\\n            where partition_date='$now.date'\\n            group by mt_poi_id,mt_group,mt_deal_cnt\\n        )t group by mt_poi_id\\n    )c on a.mt_poi_id=c.mt_poi_id \\n    where (limits=0 or (limits=1 and dj_deal_cnt*mt_deal_cnt<=4000)) and a.mt_poi_id not in(1225418834,1434406014,788366433,94710693)  -- 这个门店会打爆内存，第二个、第三个门店是大请求。\\n)t\\n)\\n;\\n\\n\\n##TargetDDL##\\n## 填写目标表的表结构定义，用于目标表不存在的时候根据ddl建表。请确保sql中的字段和ddl中的字段的数量、顺序一致！！\\nCREATE TABLE IF NOT EXISTS `${target.table}`\\n(\\n    `dj_poi_id`                string      comment 'DJ门店id',\\n    `mt_poi_id`                bigint      comment '美团主门店id',\\n    `match_request`            string      comment '匹配请求',\\n    `mt_group`                 bigint      comment '美团分组',\\n    `dj_group`                 bigint      comment 'DJ分组',\\n    `version`                  string      comment '版本'\\n)\\nCOMMENT 'DJ美团门店实时匹配请求表' \\nPARTITIONED BY (`partition_date` string COMMENT '分区日期，格式为yyyy-MM-dd')\\nSTORED AS ORC;";
//        String[] split = StringUtils.split(info, "\\n");
//        for (String str : split) {
//            //挑选出发起UDF调用的行
//            if (str.contains("DealMatchUdf") && str.contains("match_feature")) {
//                System.out.println(str + "lll");
//                //过滤掉所有-- DealMatchUdf
//                if (str.contains("-- DealMatchUdf")){
//                    continue;
//                }
//                //剩下的如果不是DealMatchUdfTest则告警
//                if (!str.contains("DealMatchUdfTest")){
//                    System.out.println(str + "error");
//                }
//            }
//        }
//    }
}
