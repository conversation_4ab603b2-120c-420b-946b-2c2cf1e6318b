package com.sankuai.algoplatform.matchops.infrastructure.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.LlmBizStrategy;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.LlmBizStrategyExample;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface LlmBizStrategyMapper extends MybatisBaseMapper<LlmBizStrategy, LlmBizStrategyExample, Long> {

    int batchInsert(@Param("list") List<LlmBizStrategy> list);

    @Select("SELECT * FROM llm_biz_strategy\n" +
            "WHERE JSON_CONTAINS(json_extract(abtest_config, '$.distributions'), CONCAT('{\"modelBizCode\":\"', #{modelBizCode}, '\"}') );")
    List<LlmBizStrategy> selectByModelBizCode(@Param("modelBizCode") String modelBizCode);
}