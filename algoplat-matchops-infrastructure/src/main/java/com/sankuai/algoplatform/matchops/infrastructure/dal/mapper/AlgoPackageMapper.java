package com.sankuai.algoplatform.matchops.infrastructure.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.AlgoPackage;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.AlgoPackageExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AlgoPackageMapper extends MybatisBaseMapper<AlgoPackage, AlgoPackageExample, Long> {
    int batchInsert(@Param("list") List<AlgoPackage> list);
}