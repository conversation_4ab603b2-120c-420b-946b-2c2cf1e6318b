package com.sankuai.algoplatform.matchops.infrastructure.proxy;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.algoplatform.matchops.infrastructure.config.LionConfig;
import com.sankuai.algoplatform.matchops.infrastructure.config.OAuthConfig;
import com.sankuai.algoplatform.matchops.infrastructure.dal.po.XmAddResult;
import com.sankuai.algoplatform.matchops.infrastructure.dal.po.XmContentResult;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.CitadelService;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.req.AddCollaborationContentByAppReq;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.req.GetCollaborationContentBySsoReq;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.resp.AddCollaborationContentByAppResp;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.resp.GetCollaborationContentByAppResp;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.resp.GetCollaborationContentBySsoResp;
import com.sankuai.ead.citadel.document.node.concept.Block;
import com.sankuai.ead.citadel.document.node.impl.node.Table;
import com.sankuai.ead.citadel.document.node.impl.node.TableRow;
import com.sankuai.ead.citadel.document.util.Builder;
import com.sankuai.xm.openplatform.api.service.open.CollaborationContentReq;
import com.sankuai.xm.openplatform.api.service.open.CollaborationContentResp;
import com.sankuai.xm.openplatform.api.service.open.XmOpenKmServiceI;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class XuechengService {

    /**
     * 根据content生成新文档
     *
     * @param content 文档内容
     * @return 文档ID
     */

    @Autowired
    private CitadelService citadelService;
    @Autowired
    private DxService dxService;

    @Autowired
    private OAuthConfig oAuthConfig;


    public XmAddResult addCollaborationContentByApp(String content, String title) {
        XmAddResult xmAddResult = new XmAddResult();
        xmAddResult.setCode(-1);
        xmAddResult.setMessage("error");
        xmAddResult.setXmId("");
        try {
            AddCollaborationContentByAppReq addCollaborationContentByAppReq = new AddCollaborationContentByAppReq();
            AddCollaborationContentByAppReq.Request request = new AddCollaborationContentByAppReq.Request();
            request.setParentId(Long.valueOf(LionConfig.WIKI_PARENT_ID));
            request.setTitle(title);
            request.setContent(content);
            addCollaborationContentByAppReq.setRequest(request);
            AddCollaborationContentByAppResp addCollaborationContentByAppResp = citadelService.addCollaborationContentByApp(oAuthConfig.getToken(), addCollaborationContentByAppReq);

            if(addCollaborationContentByAppResp.getStatus() != null){
                xmAddResult.setCode(addCollaborationContentByAppResp.getStatus().getCode());
                xmAddResult.setMessage(addCollaborationContentByAppResp.getStatus().getMsg());
            }
            if (addCollaborationContentByAppResp.getData() != null) {
                Long contentId = addCollaborationContentByAppResp.getData().getContentId();
                xmAddResult.setXmId(String.valueOf(Objects.isNull(contentId) ? "" : contentId));
            }
            return xmAddResult;
        } catch (Exception e) {
            log.error("调用学城生成接口失败,content is {},title is {}", content, title, e);
            xmAddResult.setMessage("调用学城接口失败");
            return xmAddResult;
        }
    }

    /**
     * 根据文档ID获取文档内容
     *
     * @param contentId 文档ID
     * @return 文档内容（json格式）
     */
    public XmContentResult getCollaborationContent(String contentId, String misId) {
        XmContentResult xmContentResult = new XmContentResult();
        xmContentResult.setCode(-1);
        xmContentResult.setMessage("error");
        xmContentResult.setContent("");
        try {
            GetCollaborationContentByAppResp resp = citadelService.getCollaborationContentByApp(oAuthConfig.getToken(), Long.valueOf(contentId));
            log.info("学城调用：{}", JSON.toJSONString(resp));

            if (resp.getStatus() != null){
                xmContentResult.setCode(resp.getStatus().getCode());
                xmContentResult.setMessage(resp.getStatus().getMsg());
            }
            if (resp.getData() != null) {
                xmContentResult.setContent(resp.getData().getContent());
            }
        } catch (Exception e) {
            log.error("调用获取学城内容接口失败,contentId is {}", contentId, e);
            xmContentResult.setMessage(e.getMessage());
            dxService.sendMsg2Users(xmContentResult.getMessage(), Lists.newArrayList(misId));
        }
        return xmContentResult;
    }

    public static Table buildTable(List<String> column, List<List<Object>> data) {
        if (CollectionUtils.isEmpty(column)) {
            return null;
        }

        Table table = Builder.table();

        List<TableRow> list = new ArrayList<>();
        TableRow tableHeader = Builder.tableRow();
        List<Block> content = new ArrayList<>();
        for (String header : column) {
            content.add(Builder.tableHeader(Builder.text(header)));
        }
        tableHeader.setContents(content);
        list.add(tableHeader);


        for (List<Object> rows : data) {
            TableRow tableRow = Builder.tableRow();
            List<Block> rowContent = new ArrayList<>();
            for (Object row : rows) {
                rowContent.add(Builder.tableCell(Builder.paragraph(row.toString())));
            }
            tableRow.setContents(rowContent);
            list.add(tableRow);
        }

        table.setContents(list);
        return table;
    }

//    public static void main(String[] args) throws DocumentParsingException {
//        List<String> column = ImmutableList.of("表头1", "表头2");
//        List<List<Object>> data = ImmutableList.of(ImmutableList.of("数据1", "数据2"), ImmutableList.of("数据3", "数据4"));
//        Table table = buildTable(column, data);
//        System.out.println(Serializer.serialize(table));
//
//        JSON.parseObject("content", Doc.class);
//    }
}
