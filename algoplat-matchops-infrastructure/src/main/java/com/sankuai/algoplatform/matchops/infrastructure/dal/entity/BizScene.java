package com.sankuai.algoplatform.matchops.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: biz_scene
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BizScene {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: biz_line_id
     *   说明: 业务线ID
     */
    private Long bizLineId;

    /**
     *   字段: biz_scene_name
     *   说明: 业务场景名称
     */
    private String bizSceneName;

    /**
     *   字段: req_post_type
     *   说明: 请求类型
     */
    private Integer reqPostType;

    /**
     *   字段: req_post_link
     *   说明: 请求链接
     */
    private String reqPostLink;

    /**
     *   字段: req_post_param
     *   说明: 请求参数
     */
    private String reqPostParam;

    /**
     *   字段: creator
     *   说明: 创建者
     */
    private String creator;

    /**
     *   字段: is_del
     *   说明: 是否删除
     */
    private Boolean isDel;

    /**
     *   字段: create_time
     *   说明: 创建时间
     */
    private Date createTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}