package com.sankuai.algoplatform.matchops.infrastructure.dal.dao;

import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.AlgoStrategy;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.BizLlmpredictConfig;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.BizStrategy;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.LlmBizStrategy;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.AlgoStrategyExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.BizLlmpredictConfigExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.LlmBizStrategyExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.BizLlmpredictConfigMapper;
import com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.LlmBizStrategyMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service

public class BizLlmPredictConfigDao {

    @Autowired
    BizLlmpredictConfigMapper bizLlmpredictConfigMapper;

    public BizLlmpredictConfig selectById(Long id) {
        return bizLlmpredictConfigMapper.selectByPrimaryKey(id);
    }

    public List<BizLlmpredictConfig> selectAllByBizLineIdAndEnv(Long bizLineId, Integer env) {
        BizLlmpredictConfigExample example = new BizLlmpredictConfigExample();
        example.createCriteria().andBizLineIdEqualTo(bizLineId).andEnvEqualTo(env).andStatusEqualTo(true);
        return bizLlmpredictConfigMapper.selectByExample(example);
    }

    public BizLlmpredictConfig selectByBizCode(String bizCode) {
        BizLlmpredictConfigExample example = new BizLlmpredictConfigExample();
        example.createCriteria().andBizCodeEqualTo(bizCode).andStatusEqualTo(true);
        List<BizLlmpredictConfig> list = bizLlmpredictConfigMapper.selectByExample(example);
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }

    public List<BizLlmpredictConfig> selectByIds(List<Long> ids) {
        BizLlmpredictConfigExample example = new BizLlmpredictConfigExample();
        example.createCriteria().andIdIn(ids);
        return bizLlmpredictConfigMapper.selectByExample(example);
    }

    public List<BizLlmpredictConfig> selectByBizCodes(List<String> bizCodes) {
        BizLlmpredictConfigExample example = new BizLlmpredictConfigExample();
        example.createCriteria().andBizCodeIn(bizCodes);
        return bizLlmpredictConfigMapper.selectByExample(example);
    }


    public int update(BizLlmpredictConfig bizLlmpredictConfig) {
        return bizLlmpredictConfigMapper.updateByPrimaryKeySelective(bizLlmpredictConfig);
    }

    public Long insert(BizLlmpredictConfig bizLlmpredictConfig) {
        bizLlmpredictConfigMapper.insertSelective(bizLlmpredictConfig);
        return bizLlmpredictConfig.getId();
    }

    public List<BizLlmpredictConfig> selectByBizLineIdAndEnv(Long bizLineId, Integer env) {
        BizLlmpredictConfigExample example = new BizLlmpredictConfigExample();
        example.createCriteria().andBizLineIdEqualTo(bizLineId).andEnvEqualTo(env).andStatusEqualTo(true);
        return bizLlmpredictConfigMapper.selectByExample(example);
    }

    public int softDelete(Long id, String mis) {
        BizLlmpredictConfig bizLlmpredictConfig = selectById(id);
        if (bizLlmpredictConfig != null) {
            bizLlmpredictConfig.setStatus(false);
            bizLlmpredictConfig.setOwner(mis);
            return update(bizLlmpredictConfig);
        }
        return 0;
    }
}


