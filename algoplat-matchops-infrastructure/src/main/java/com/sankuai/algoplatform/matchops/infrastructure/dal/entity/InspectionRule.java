package com.sankuai.algoplatform.matchops.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: inspection_rule
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InspectionRule {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: appkey
     *   说明: 服务名
     */
    private String appkey;

    /**
     *   字段: resource_type
     *   说明: 资源类型
     */
    private Integer resourceType;

    /**
     *   字段: rule
     *   说明: 规则
     */
    private String rule;

    /**
     *   字段: create_time
     *   说明: 创建时间
     */
    private Date createTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}