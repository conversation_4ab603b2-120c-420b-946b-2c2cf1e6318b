package com.sankuai.algoplatform.matchops.infrastructure.util;

import com.dianping.lion.client.Lion;
import com.meituan.mtrace.Tracer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/06/17
 */
@Slf4j
public class ContextUtil {

    /**
     * 从Tracer获取UserId
     */
    public static Long getLoginUserId() {
        try {
            String userId = Tracer.getContext("userId");
            if (userId != null) {
                return Long.parseLong(userId);
            }
            long mockUserId = Lion.getConfigRepository().getLongValue("mockUserId", 0L);
            Tracer.putContext("userId", String.valueOf(mockUserId));
            return mockUserId;
        } catch (Exception e) {
            log.error("获取userId失败:", e);
        }
        return 0L;
    }

    /**
     * 获取登录用户的mis name
     *
     * @return mis name
     */
    public static String getLoginUserMis() {
        try {
            String misName = Tracer.getContext("userMis");
            if (misName != null) {
                return misName;
            }

            String mockMis = Lion.getConfigRepository().get("mockUserMis", "");
            if (mockMis != null && mockMis.length() > 3) {
                Tracer.putContext("userMis", mockMis);
                return mockMis;
            }

        } catch (Exception e) {
            log.error("获取mis name失败:", e);
        }
        return "";
    }

    /**
     * 获取登录用户的name
     *
     * @return mis name
     */
    public static String getLoginUserName() {
        try {
            String misName = Tracer.getContext("userName");
            if (misName != null) {
                return misName;
            }

            String mockUserName = Lion.getConfigRepository().get("mockUserName", "");
            if (mockUserName != null && !mockUserName.isEmpty()) {
                return mockUserName;
            }

        } catch (Exception e) {
            log.error("获取userName失败:", e);
        }
        return "";
    }

    public static String getSsoId() {
        try {
            String getSsoId = Tracer.getContext("ssoId");
            if (StringUtils.isNotEmpty(getSsoId)) {
                return getSsoId;
            }

            String mockSsoId = Lion.getConfigRepository().get("mockSsoId", "");
            if (StringUtils.isNotEmpty(mockSsoId)) {
                return mockSsoId;
            }

        } catch (Exception e) {
            log.error("获取ssoId:", e);
        }
        return "";
    }

}
