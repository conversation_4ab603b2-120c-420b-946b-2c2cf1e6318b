package com.sankuai.algoplatform.matchops.infrastructure.model;

import lombok.Data;

@Data
public class LionConfigReq {

    private String env;         // 环境. 线上:staging/prod  线下:dev/test

    private String appkey;      // appkey

    private String key;         // 配置key

    private String value;       // 配置值

    private String set;         // set

    private String desc;        // 描述

    private String group;       // 组

    private String swimlane;    // 泳道

    private String grouptags;   // 组标签
}
