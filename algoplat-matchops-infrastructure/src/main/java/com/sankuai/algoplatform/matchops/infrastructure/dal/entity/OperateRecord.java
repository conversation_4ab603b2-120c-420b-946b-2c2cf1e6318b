package com.sankuai.algoplatform.matchops.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: operate_record
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OperateRecord {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: type
     *   说明: 操作类型
     */
    private Integer type;

    /**
     *   字段: old_value
     *   说明: 旧值
     */
    private String oldValue;

    /**
     *   字段: new_value
     *   说明: 新值
     */
    private String newValue;

    /**
     *   字段: old_status
     *   说明: 旧状态
     */
    private Integer oldStatus;

    /**
     *   字段: new_status
     *   说明: 新状态
     */
    private Integer newStatus;

    /**
     *   字段: operator
     *   说明: 操作人
     */
    private String operator;

    /**
     *   字段: create_time
     *   说明: 创建时间
     */
    private Date createTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}