package com.sankuai.algoplatform.matchops.infrastructure.config;

import com.meituan.talos.commons.exception.TalosException;
import com.sankuai.data.talos.AsyncTalosClient;
import com.sankuai.data.talos.Talos;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class TalosConfig {

    @Value("${talos.username}")
    private String username;

    public String password;

    @Bean
    public AsyncTalosClient talosClient() {
        initKeys();
        AsyncTalosClient talosClient = new AsyncTalosClient(username, password);
        try {
            talosClient.openSession(new Talos.RequestOption(60, 60, 60, TimeUnit.SECONDS));
        } catch (TalosException e) {
            log.error("实例化talos客户端失败", e);
        }
        return talosClient;
    }

    private void initKeys() {
        this.password = KmsConfigs.getTalosKey();
    }
}
