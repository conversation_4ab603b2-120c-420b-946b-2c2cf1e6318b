package com.sankuai.algoplatform.matchops.infrastructure.dal.dao;

import com.dianping.lion.client.util.CollectionUtils;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.BizScene;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.MatchStrategy;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestScene;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.MatchStrategyExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.TestSceneExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.TestSceneMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
@Service

public class TestSceneDao  {
    @Autowired
    TestSceneMapper testSceneMapper;

    public List<TestScene> selectAllNotDelete() {
        TestSceneExample example = new TestSceneExample();
        example.createCriteria().andIsDelEqualTo(false);
        return testSceneMapper.selectByExample(example);
    }

    public TestScene selectByBuAndName(String bu, String sceneName) {
        TestSceneExample example = new TestSceneExample();
        example.createCriteria().andBizLineIdEqualTo(Long.valueOf(bu)).andTestSceneNameEqualTo(sceneName).andIsDelEqualTo(false);
        List<TestScene> testScenes = testSceneMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(testScenes)) {
            return null;
        } else {
            return testScenes.get(0);
        }
    }


    public List<TestScene> selectAllByBizLineId(Long bizLineId) {
        TestSceneExample example = new TestSceneExample();
        example.createCriteria().andBizLineIdEqualTo(bizLineId).andIsDelEqualTo(false);
        return testSceneMapper.selectByExample(example);
    }

    public List<TestScene> selectAll() {
        TestSceneExample example = new TestSceneExample();
        return testSceneMapper.selectByExample(example);
    }

    public TestScene selectById(Long id) {
        TestSceneExample example = new TestSceneExample();
        example.createCriteria().andIdEqualTo(id);
        List<TestScene> testScenes = testSceneMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(testScenes)) {
            return null;
        }
        else {
            return testScenes.get(0);
        }
    }

    public TestScene selectByBizId(Long bizLineId) {
        TestSceneExample example = new TestSceneExample();
        example.createCriteria().andBizLineIdEqualTo(bizLineId);
        return testSceneMapper.selectByExample(example).get(0);
    }

    public List<TestScene> selectByBizScene(String bizSceneName) {
        TestSceneExample example = new TestSceneExample();
        example.createCriteria().andTestSceneNameEqualTo(bizSceneName);
        return testSceneMapper.selectByExample(example);
    }

    public Long insert(TestScene testScene) {
        testSceneMapper.insertSelective(testScene);
        return testScene.getId();
    }

    public int update(TestScene testScene) {
        return testSceneMapper.updateByPrimaryKeySelective(testScene);
    }

    public int softDelete(Long id, String mis) {
        TestScene testScene = selectById(id);
        if(testScene!=null){
            testScene.setIsDel(true);
            testScene.setOwner(mis);
            return update(testScene);
        }
        return 0;
    }

    public void delete(Long id) {
        testSceneMapper.deleteByPrimaryKey(id);
    }

    public List<TestScene> selectByPageAndBizLineId(int page, int pageSize, long bizLineId) {
        TestSceneExample example = new TestSceneExample();
        example.limit((page - 1) * pageSize, pageSize);
        example.createCriteria().andBizLineIdEqualTo(bizLineId).andIsDelEqualTo(false);
        return testSceneMapper.selectByExample(example);
    }

    public Long selectCountByBizLineId(long bizLineId) {
        TestSceneExample example = new TestSceneExample();
        example.createCriteria().andBizLineIdEqualTo(bizLineId).andIsDelEqualTo(false);
        return testSceneMapper.countByExample(example);
    }
}
