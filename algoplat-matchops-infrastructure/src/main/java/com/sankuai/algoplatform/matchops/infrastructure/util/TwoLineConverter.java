package com.sankuai.algoplatform.matchops.infrastructure.util;

import java.util.function.Function;

/**
 * 两行拼接转换器，将两行内容拼接成 "第一行", "第二行" 格式
 */
public class TwoLineConverter implements Function<String, String> {
    private String firstLine = null;
    private boolean isFirstLine = true;
    
    @Override
    public String apply(String line) {
        if (isFirstLine) {
            // 保存第一行
            firstLine = line;
            isFirstLine = false;
            return null; // 第一行不返回内容
        } else {
            // 第二行，进行拼接
            String result = "\"" + firstLine + "\", \"" + line + "\"";
            isFirstLine = true; // 重置状态，准备处理下一对
            firstLine = null;
            return result;
        }
    }
}