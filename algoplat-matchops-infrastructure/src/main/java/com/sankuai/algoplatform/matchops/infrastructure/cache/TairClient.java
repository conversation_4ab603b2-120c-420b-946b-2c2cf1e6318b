package com.sankuai.algoplatform.matchops.infrastructure.cache;

import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.taobao.tair3.client.Result;
import com.taobao.tair3.client.ResultMap;
import com.taobao.tair3.client.impl.MultiTairClient;
import com.taobao.tair3.client.util.ByteArray;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TairClient {

    com.taobao.tair3.client.TairClient.TairOption TAIR_OPTION = new com.taobao.tair3.client.TairClient.TairOption(500, (short) 0, 86400);

    @Resource(name = "webStorageCellar")
    private MultiTairClient tairClient;

    public String get(String key) {
        try {
            Result<byte[]> result = tairClient.get(getArea(), key.getBytes(), TAIR_OPTION);
            if (result.isOK()) {
                return new String(result.getResult());
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("tair#get error", e.getMessage(), e);
            return null;
        }
    }

    public Map<String, String> batchGet(String prefix, Collection<String> keys) {
        return batchGet(prefix, keys, "");
    }

    public Map<String, String> batchGet(String prefix, Collection<String> keys, String suffix) {
        if (CollectionUtils.isEmpty(keys)) {
            return Collections.emptyMap();
        }
        long t0 = System.currentTimeMillis();
        Map<String, String> keyMap = keys.stream().collect(Collectors.toMap(k -> prefix + k + suffix, Function.identity(), (u, v) -> v));

        ResultMap<ByteArray, Result<byte[]>> resp;
        try {
            resp = tairClient.batchGet(getArea(), keyMap.keySet().stream().map(String::getBytes).collect(Collectors.toList()), TAIR_OPTION);
        } catch (Exception e) {
            throw new RuntimeException(String.format("query tair error: %s", keyMap.keySet()), e);
        }

        Map<String, String> res = new HashMap<>(keys.size());
        for (Result<byte[]> r : resp.values()) {
            if (!r.isOK() || r.getKey() == null || r.getResult() == null || r.getResult().length == 0) {
                continue;
            }
            String originKey = keyMap.get(new String(r.getKey()));
            String value = new String(r.getResult());
            res.put(originKey, value);
        }
        log.info("batchGet: prefix:{}, batchSize:{}, suffix:{}, resultSize:{}, cost:{}", prefix, keys.size(), suffix, res.size(), System.currentTimeMillis() - t0);
        return res;
    }

    public boolean put(String key, String value, long expireTime, TimeUnit timeUnit) {
        com.taobao.tair3.client.TairClient.TairOption option = new com.taobao.tair3.client.TairClient.TairOption();
        option.setExpireTime((int) timeUnit.toSeconds(expireTime));
        option.setTimeout(500);
        Result<Void> res;
        try {
            res = tairClient.put(getArea(), key.getBytes(), value.getBytes(), option);
            return res.isOK();

        } catch (Exception e) {
            log.error("put error: key:{}, value:{}, ex", key, value, e);
            return false;
        }
    }

    public boolean expire(String key, long expireTime, TimeUnit timeUnit) {
        com.taobao.tair3.client.TairClient.TairOption option = new com.taobao.tair3.client.TairClient.TairOption();
        option.setExpireTime((int) timeUnit.toSeconds(expireTime));
        option.setTimeout(500);
        Result<Void> res;
        try {
            res = tairClient.expire(getArea(), key.getBytes(), option);
            return res.isOK();

        } catch (Exception e){
            log.error("expire error: key:{}, ex", key, e);
            return false;
        }
    }

    private short getArea() {
        return (short) (MdpContextUtils.isOnlineEnv() ? 15 : 40);
    }

    public Boolean mapPut(String pKey, String sKey, String value) {
        com.taobao.tair3.client.TairClient.TairOption option = new com.taobao.tair3.client.TairClient.TairOption();
        option.setTimeout(500);
        try {
            Result<Void> voidResult = tairClient.mapPut(getArea(), pKey.getBytes(), sKey.getBytes(), value.getBytes(), option);
            return voidResult.isOK();
        } catch (Exception e) {
            log.error("mapPut error: pKey:{}, sKey:{} , ex", pKey, sKey, e);
            return false;
        }
    }

    public Boolean mapRemove(String pKey, String sKey) {
        com.taobao.tair3.client.TairClient.TairOption option = new com.taobao.tair3.client.TairClient.TairOption();
        option.setTimeout(500);
        try {
            Result<Void> voidResult = tairClient.mapRemove(getArea(), pKey.getBytes(), sKey.getBytes(), option);
            return voidResult.isOK();
        } catch (Exception e) {
            log.error("mapRemove error: pKey:{}, sKey:{} , ex", pKey, sKey, e);
            return false;
        }
    }

    public String mapGet(String pKey, String sKey) {
        try {
            Result<byte[]> voidResult = tairClient.mapGet(getArea(), pKey.getBytes(), sKey.getBytes(), TAIR_OPTION);
            if (voidResult.isOK()) {
                return new String(voidResult.getResult());
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("mapGet error: pKey:{}, sKey:{} , ex", pKey, sKey, e);
            return null;
        }
    }
}
