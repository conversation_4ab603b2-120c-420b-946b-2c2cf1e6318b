package com.sankuai.algoplatform.matchops.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: schedule_task_config
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScheduleTaskConfig {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: task_name
     *   说明: 任务名称
     */
    private String taskName;

    /**
     *   字段: status
     *   说明: 状态（0：正常，-1禁用）
     */
    private Integer status;

    /**
     *   字段: task_type
     *   说明: 任务类型（定时、监控）
     */
    private Integer taskType;

    /**
     *   字段: action_type
     *   说明: 调度动作类型0增加，1减少
     */
    private Integer actionType;

    /**
     *   字段: rule_config
     *   说明: 规则配置
     */
    private String ruleConfig;

    /**
     *   字段: quota
     *   说明: 实例数
     */
    private Integer quota;

    /**
     *   字段: operator_mis
     *   说明: 操作人id
     */
    private String operatorMis;

    /**
     *   字段: add_time
     *   说明: 添加时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}