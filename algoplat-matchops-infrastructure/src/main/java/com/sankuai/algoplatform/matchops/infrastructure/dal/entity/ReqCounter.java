package com.sankuai.algoplatform.matchops.infrastructure.dal.entity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.LongAdder;

public class ReqCounter {
    private final LongAdder successCnt = new LongAdder();

    private final List<Long> individualResponseTimes = Collections.synchronizedList(new ArrayList<>());

    private final List<LongAdder> successTimeMetrics;

    private final LongAdder failedCnt = new LongAdder();

    public ReqCounter(int numOfTimeMetrics) {
        successTimeMetrics = new ArrayList<>();
        while (numOfTimeMetrics > 0) {
            successTimeMetrics.add(new LongAdder());
            numOfTimeMetrics--;
        }
    }

    public void updateTimeMetrics(List<Long> timeMetrics) {
        // 检查timeMetrics是否为null
        if (timeMetrics == null) {
            return;
        }

        // 记录单个请求的响应时间
        if (timeMetrics.size() > 2 && timeMetrics.get(2) != null) {
            individualResponseTimes.add(timeMetrics.get(2));
        }

        // 更新成功时间指标
        for(int i = 0; i < timeMetrics.size(); i++){
            Long value = timeMetrics.get(i);
            if (value != null) {  // 添加对null的检查
                successTimeMetrics.get(i).add(value);
            }
        }
    }
    public List<Long> getIndividualResponseTimes() {
        return new ArrayList<>(individualResponseTimes);
    }

    public void addSuccessCnt() {
        successCnt.increment();
    }

    public void addFailedCnt() {
        failedCnt.increment();
    }

    public long getSuccessCnt(){
        return successCnt.sum();
    }

    public long getFailedCnt(){
        return failedCnt.sum();
    }

    public List<Long> getSuccessTimeMetrics() {
        List<Long> res = new ArrayList<>();
        for(LongAdder adder: successTimeMetrics){
            res.add(adder.sum());
        }
        return res;
    }

}
