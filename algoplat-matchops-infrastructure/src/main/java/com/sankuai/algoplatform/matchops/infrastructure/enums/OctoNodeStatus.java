package com.sankuai.algoplatform.matchops.infrastructure.enums;

public enum OctoNodeStatus {
    DEFAULT(-1),
    NOT_START(0),
    STARTING(1),
    NORMAL(2),
    DISABLE(4);

    private int code;

    OctoNodeStatus(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static OctoNodeStatus getByCode(int value) {
        for (OctoNodeStatus status : OctoNodeStatus.values()) {
            if (status.getCode() == value) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown value: " + value);
    }
}
