package com.sankuai.algoplatform.matchops.infrastructure.model;

import lombok.Data;

import java.util.List;

/**
 * @program: algoplat-matchops
 * <AUTHOR>
 * @Date 2025/5/27
 */
@Data
public  class InstanceInfo {

    //private String ququeName;
    private List<String> ququeName;

    private String groupName;

    private String serverImage;

    private Integer rpcOutTime;

    private Boolean batching;

    private BatchingConfig batchingConfig;

    private String env;

    private String set;

    private ResourceParamConfig resourceConfig;

}