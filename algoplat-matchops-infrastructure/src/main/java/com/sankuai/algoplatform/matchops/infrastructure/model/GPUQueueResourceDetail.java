package com.sankuai.algoplatform.matchops.infrastructure.model;

import com.sankuai.algoplatform.matchops.infrastructure.enums.GPUResourceType;
import lombok.Data;

@Data
public class GPUQueueResourceDetail {
    private GPUResourceType resourceType;
    // 交付量
    private Integer quotaNum;
    //最大可调度量
    private Integer availableNum;
    //调度量
    private Integer allocatedNum;
    //Pending
    private Integer pendingNum;
    // 剩余资源量
    private Integer remainNum;
}
