package com.sankuai.algoplatform.matchops.infrastructure.dal.dao;

import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestSubTask;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.TestSubTaskExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.TestSubTaskMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class TestSubTaskDao {

    @Resource
    TestSubTaskMapper testSubTaskMapper;

    public List<TestSubTask> selectByTaskId(long taskId) {
        TestSubTaskExample example = new TestSubTaskExample();
        example.createCriteria().andTaskIdEqualTo(taskId);
        return testSubTaskMapper.selectByExample(example);
    }

    public TestSubTask selectOneByTaskIdAndStatus(long taskId, int status) {
        try {
            TestSubTaskExample example = new TestSubTaskExample();
            example.createCriteria()
                    .andTaskIdEqualTo(taskId)
                    .andStatusEqualTo(status);
            List<TestSubTask> testSubTasks = testSubTaskMapper.selectByExample(example);

            return CollectionUtils.isEmpty(testSubTasks) ? null : testSubTasks.get(0);
        } finally {
            ZebraForceMasterHelper.clearLocalContext();
        }
    }

    public List<TestSubTask> selectByTaskIdAndStatus(long taskId, List<Integer> status) {
        try {
            TestSubTaskExample example = new TestSubTaskExample();
            example.createCriteria()
                    .andTaskIdEqualTo(taskId)
                    .andStatusIn(status);
            return testSubTaskMapper.selectByExample(example);
        } finally {
            ZebraForceMasterHelper.clearLocalContext();
        }
    }


    public TestSubTask selectById(Long taskId) {
        try {
            return testSubTaskMapper.selectByPrimaryKey(taskId);
        } finally {
            ZebraForceMasterHelper.clearLocalContext();
        }
    }

    public Long insert(TestSubTask testSubTask) {
        testSubTaskMapper.insertSelective(testSubTask);
        return testSubTask.getId();
    }

    public void batchInsert(List<TestSubTask> testSubTasks) {
        testSubTasks.forEach(testSubTask -> testSubTaskMapper.insertSelective(testSubTask));
    }

    public int update(TestSubTask testSubTask) {
        return testSubTaskMapper.updateByPrimaryKeySelective(testSubTask);
    }

    public Long updateTestTaskStatus(Long testSubTaskId, int status) {
        TestSubTask testTask = new TestSubTask();
        testTask.setId(testSubTaskId);
        testTask.setStatus(status);
        testSubTaskMapper.updateByPrimaryKeySelective(testTask);
        return testTask.getId();
    }
}
