package com.sankuai.algoplatform.matchops.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: cookie_config
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CookieConfig {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: user_id
     *   说明: 用户ID
     */
    private Long userId;

    /**
     *   字段: user_mis
     *   说明: 用户MIS
     */
    private String userMis;

    /**
     *   字段: cookie
     *   说明: cookie
     */
    private String cookie;

    /**
     *   字段: create_time
     *   说明: 创建时间
     */
    private Date createTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}