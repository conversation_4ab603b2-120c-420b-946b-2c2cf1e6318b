package com.sankuai.algoplatform.matchops.infrastructure.enums;

public enum GPUResourceType {
    L40_48G("L40-48G", "gcoresl40-48g"),
    A100_80G("A100-80G", "gcores80g"),
    //A30_24G("A100-24G", "gcores24g"),
    A30_MIG_12G("A30-MIG-12G", "gcores12g"),
    A30_24G("A30-24G", "gcores24g"),
    H13D_24G("H13D-24G", "gcoresh13d-24g"),
    A100_40G("A100-40G", "gcores40g");


    private String name;
    private String mlpName;

    GPUResourceType(String name, String mlpName) {
        this.name = name;
        this.mlpName = mlpName;
    }

    public String getName() {
        return name;
    }

    public String getMlpName() {
        return mlpName;
    }

    public static GPUResourceType getByName(String name) {
        for (GPUResourceType type : GPUResourceType.values()) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static GPUResourceType getByMlpName(String mlpName) {
        for (GPUResourceType type : GPUResourceType.values()) {
            if (type.getMlpName().equals(mlpName)) {
                return type;
            }
        }
        return null;
    }
}
