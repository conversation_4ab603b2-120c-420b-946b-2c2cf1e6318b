package com.sankuai.algoplatform.matchops.infrastructure.dal.dao;

import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ServiceElasticConfig;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.ServiceElasticConfigExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.ServiceElasticConfigMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class ServiceElasticConfigDao {

    @Resource
    ServiceElasticConfigMapper serviceElasticConfigMapper;

    public ServiceElasticConfig selectAllByBizLineIdAndResourceTypeAndName(Long bizLineId, Integer resourceType, String name) {
        ServiceElasticConfigExample example = new ServiceElasticConfigExample();
        example.createCriteria().andBizLineIdEqualTo(bizLineId).andResourceTypeEqualTo(resourceType).andNameEqualTo(name);
        List<ServiceElasticConfig> configs = serviceElasticConfigMapper.selectByExample(example);
        return CollectionUtils.isEmpty(configs) ? null : configs.get(0);
    }


}
