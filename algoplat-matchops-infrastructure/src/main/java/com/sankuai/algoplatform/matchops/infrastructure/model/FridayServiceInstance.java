package com.sankuai.algoplatform.matchops.infrastructure.model;

import com.sankuai.ai.friday.model.deploy.thrift.QueryInstancesResponse;
import com.sankuai.algoplatform.matchops.infrastructure.enums.FridayInstanceStatus;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Friday服务实例
 * [
 * {
 * "createTime": "2024-10-25 16:46:43",
 * "instanceDetail": "23核/245G/L40-48G*1",
 * "instanceName": "set-hh-llm-fridayllm-qwen33333s1662a08.mt",
 * "status": "运行中"
 * }
 * ]
 */
@Data
public class FridayServiceInstance {
    // 实例名称
    private String instanceName;
    // 实例详情
    private String instanceDetail;
    // 实例创建时间
    private String createTime;
    // 实例状态
    private FridayInstanceStatus status;

    public static List<FridayServiceInstance> trans2(QueryInstancesResponse resp) {
        if (CollectionUtils.isEmpty(resp.getInstances())) {
            return Collections.emptyList();
        }
        return resp.getInstances().stream().map(FridayServiceInstance::trans2).collect(Collectors.toList());
    }

    public static FridayServiceInstance trans2(com.sankuai.ai.friday.model.deploy.thrift.FridayServiceInstance r) {
        FridayServiceInstance instance = new FridayServiceInstance();
        instance.setInstanceName(r.getInstanceName());
        instance.setInstanceDetail(r.getInstanceDetail());
        instance.setCreateTime(r.getCreateTime());
        instance.setStatus(Optional.ofNullable(FridayInstanceStatus.getByName(r.getStatus())).orElse(FridayInstanceStatus.UNKNOWN));
        return instance;
    }

}
