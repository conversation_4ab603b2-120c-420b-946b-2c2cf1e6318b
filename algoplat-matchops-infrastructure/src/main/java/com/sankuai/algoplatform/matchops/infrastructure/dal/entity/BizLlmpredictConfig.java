package com.sankuai.algoplatform.matchops.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: biz_llmpredict_config
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BizLlmpredictConfig {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: biz_code
     *   说明: 适用业务标识
     */
    private String bizCode;

    /**
     *   字段: note
     *   说明: 业务策略描述
     */
    private String note;

    /**
     *   字段: model_config
     *   说明: 模型配置，是大模型的入参配置的关键部分
     */
    private String modelConfig;

    /**
     *   字段: prompt_template_id
     *   说明: 对应prompt模板表中的ID
     */
    private Long promptTemplateId;

    /**
     *   字段: env
     *   说明: 环境，0测试/ST 1线上
     */
    private Integer env;

    /**
     *   字段: biz_line_id
     *   说明: 业务线ID
     */
    private Long bizLineId;

    /**
     *   字段: status
     *   说明: 状态，1：开启，0：关闭
     */
    private Boolean status;

    /**
     *   字段: add_time
     *   说明: 创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: owner
     *   说明: mis
     */
    private String owner;
}