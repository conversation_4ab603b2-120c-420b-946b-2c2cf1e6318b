package com.sankuai.algoplatform.matchops.infrastructure.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.sankuai.algoplatform.matchops.infrastructure.enums.OctoNodeStatus;
import com.sankuai.algoplatform.matchops.infrastructure.enums.OctoNodeStatusDeserializer;
import lombok.Data;

@Data
public class OctoNode {
    /**
     * 机器名标示（定期刷新，可能会出现不准，如果需要获取准确机器名请使用rocket接口）
     */
    private String name;
    /**
     * appkey
     */
    private String appkey;
    /**
     * sdk版本
     */
    private String version;
    /**
     * ip地址
     */
    private String ip;
    /**
     * port
     */
    private Integer port;
    /**
     * 权重 0-10
     */
    private Integer weight;
    /**
     * 0:未启动
     * 1: 启动中
     * 2:正常
     * 4:禁用
     */
    @JSONField(deserializeUsing = OctoNodeStatusDeserializer.class)
    private OctoNodeStatus status;
    /**
     * 节点启用状态：
     * 0：启用，
     * 1：禁用
     */
    private Integer enabled;
    /**
     * 角色 0：主机，1：备机
     */
    private Integer role;
    /**
     * 上次更新时间
     */
    private Long lastUpdateTime;
    /**
     * 节点类型
     * 0:thrift
     * 1:http
     */
    private Integer serverType;
    /**
     * 泳道
     */
    private String swimlane;
    /**
     * set化标记
     */
    private String cell;

}
