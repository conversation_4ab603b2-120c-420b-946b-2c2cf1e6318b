package com.sankuai.algoplatform.matchops.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: test_scene
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TestScene {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: biz_line_id
     *   说明: 业务线ID
     */
    private Long bizLineId;

    /**
     *   字段: test_scene_name
     *   说明: 测试场景名称
     */
    private String testSceneName;

    /**
     *   字段: req_post_type
     *   说明: 请求类型 0:xt 1:http
     */
    private Integer reqPostType;

    /**
     *   字段: req_post_link
     *   说明: 请求链接
     */
    private String reqPostLink;

    /**
     *   字段: extra
     *   说明: 额外信息
     */
    private String extra;

    /**
     *   字段: owner
     *   说明: 创建者
     */
    private String owner;

    /**
     *   字段: is_del
     *   说明: 是否删除
     */
    private Boolean isDel;

    /**
     *   字段: create_time
     *   说明: 创建时间
     */
    private Date createTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}