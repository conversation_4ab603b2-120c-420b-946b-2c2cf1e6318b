package com.sankuai.algoplatform.matchops.infrastructure.enums;

import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;

import java.lang.reflect.Type;

public class OctoNodeStatusDeserializer implements ObjectDeserializer {
    @Override
    public <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
        int value = parser.parseObject(Integer.class);
        return (T) OctoNodeStatus.getByCode(value);
    }

    @Override
    public int getFastMatchToken() {
        return 0;
    }

}
