package com.sankuai.algoplatform.matchops.infrastructure.proxy;

import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.WorkbookUtil;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class ExcelGenService {
    @Autowired
    private S3Service s3Service;

    private static int writeRows(SXSSFWorkbook wb, Sheet sheet, List<List<String>> values, int start) {
        CreationHelper createHelper = wb.getCreationHelper();
        CellStyle dateStyle = wb.createCellStyle();
        dateStyle.setDataFormat(createHelper.createDataFormat().getFormat("yyyy-mm-dd"));
        for (int i = 0; i < values.size(); i++) {
            List<?> cells = values.get(i);
            Row valueRow = sheet.createRow(i + start);
            for (int j = 0; j < cells.size(); j++) {
                Object value = cells.get(j);
                Cell cell = valueRow.createCell(j);
                if (value == null) {
                    cell.setCellValue((String) null);
                    continue;
                }
                if (value instanceof Number) {
//                    cell.setCellType(Cell.CELL_TYPE_NUMERIC);
                    cell.setCellValue(((Number) value).doubleValue());
                } else if (value instanceof Date) {
                    cell.setCellStyle(dateStyle);
                    cell.setCellValue((Date) value);
                } else if (value instanceof Boolean) {
                    Boolean booleanValue = (Boolean) value;
                    if (booleanValue) {
                        cell.setCellValue("是");
                    } else {
                        cell.setCellValue("否");
                    }
                } else {
                    cell.setCellValue(value.toString());
                }
            }
        }
        return start + values.size();
    }

    public void export2Stream(OutputStream os, List<String> Header, List<List<String>> rows) {
        Preconditions.checkNotNull(os);
        if (CollectionUtils.isEmpty(Header)) {
            return;
        }
//        Preconditions.checkState(CollectionUtils.isEmpty(rows)
//                        || rows.stream().noneMatch(f -> CollectionUtils.isNotEmpty(f) && f.size() != Header.size()),
//                "Header和rows内行的列数不匹配");

        try (SXSSFWorkbook wb = new SXSSFWorkbook()) {
            wb.setCompressTempFiles(true);
            //创建sheet
            String safeSheetNameName = WorkbookUtil.createSafeSheetName("sheet1");
            Sheet sheet = wb.createSheet(safeSheetNameName);
            //创建rows
            Row nameRow = sheet.createRow(0);
            for (int i = 0; i < Header.size(); i++) {
                nameRow.createCell(i).setCellValue(Header.get(i));
            }
            if (CollectionUtils.isNotEmpty(rows)) {
                writeRows(wb, sheet, rows, 1);
            }
            wb.write(os);
            os.flush();
            wb.dispose();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public String export2S3(List<String> Header, List<List<String>> rows, String fileName) {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            if (!fileName.endsWith("xlsx")) {
                fileName = fileName + ".xlsx";
            }
            // 生成Excel
            export2Stream(baos, Header, rows);
            // 上传至s3
            String originS3Url;
            try (ByteArrayInputStream inputStream = new ByteArrayInputStream(baos.toByteArray())) {
                originS3Url = s3Service.upload2S3(inputStream, fileName);
            }
//            // 加水印
//            String markedUrl = waterMarkService.addMixWaterMark(originS3Url, downloaderMis, WaterMarkService.SupportFileType.XLSX);
//            // 文件安全平台
//            String url = fileDistributeService.secureFile(markedUrl, fileName, downloaderMis);
            log.info("export2S3Securely: fileName:{}, url:{}", fileName, originS3Url);
            return originS3Url;
        } catch (IOException e) {
            throw new RuntimeException("export2S3Securely error:" + fileName, e);
        }
    }
}
