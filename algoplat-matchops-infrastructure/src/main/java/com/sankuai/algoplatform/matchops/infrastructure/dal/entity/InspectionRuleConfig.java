package com.sankuai.algoplatform.matchops.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: inspection_rule_config
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InspectionRuleConfig {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: appkey
     *   说明: 服务appkey
     */
    private String appkey;

    /**
     *   字段: cell
     *   说明: 服务set信息
     */
    private String cell;

    /**
     *   字段: resource_type
     *   说明: 资源类型。0CPU 1mlp 2friday
     */
    private Integer resourceType;

    /**
     *   字段: rule
     *   说明: 规则
     */
    private String rule;

    /**
     *   字段: is_del
     *   说明: 是否删除 0不删除 1删除
     */
    private Boolean isDel;

    /**
     *   字段: user
     *   说明: mis
     */
    private String user;

    /**
     *   字段: create_time
     *   说明: 创建时间
     */
    private Date createTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}