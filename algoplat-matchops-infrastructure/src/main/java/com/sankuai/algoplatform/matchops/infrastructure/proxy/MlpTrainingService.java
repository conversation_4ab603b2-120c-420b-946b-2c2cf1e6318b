package com.sankuai.algoplatform.matchops.infrastructure.proxy;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.sankuai.aifree.thrift.generation.MLPModelServingService;
import com.sankuai.aifree.thrift.generation.query.*;
import com.sankuai.aifree.thrift.generation.vo.*;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ModelDeployRequest;
import com.sankuai.algoplatform.matchops.infrastructure.enums.GPUResourceType;
import com.sankuai.algoplatform.matchops.infrastructure.model.GPUQueueResourceDetail;
import com.sankuai.algoplatform.matchops.infrastructure.util.RetryUtil;
import com.sankuai.mlp.kub.service.job.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MlpTrainingService {
    @Resource
    private JobServiceRPC.Iface mlpJobService;
    @Resource
    private MLPModelServingService.Iface mlpModelServingService;

    public List<GPUQueueResourceDetail> getQueueResource(String queueName) {
        QueueResourceDetailDto resp = RetryUtil.retry(
                () -> {
                    QueueResourceDetailDto detail = mlpJobService.getQueueResourceWithExplicitCores(queueName);
                    log.info("getQueueResource: queueName:{}, resp:{}", queueName, JSON.toJSONString(detail));
                    return detail;
                },
                (r, e) -> {
                    if (e != null || r.getCode() != 0) {
                        log.error("getQueueResource error:{},{}, ex:", queueName, JSON.toJSONString(r), e);
                        return true;
                    }
                    return false;
                },
                2, TimeUnit.SECONDS.toMillis(1));
        return convert(resp.getData());
    }

    private List<GPUQueueResourceDetail> convert(QueueResourceDetail detail) {
        QueueTotalResource totalResource = detail.getTotalResource();
        if (totalResource == null) {
            return Collections.emptyList();
        }
        // quota
        Map<String, Double> minResources = Optional.ofNullable(totalResource.getMinResources()).orElse(new HashMap<>());
        Map<GPUResourceType, GPUQueueResourceDetail> details = minResources.entrySet().stream()
                .map(m -> {
                    GPUResourceType resource = GPUResourceType.getByMlpName(m.getKey());
                    if (resource == null) {
                        return null;
                    }
                    GPUQueueResourceDetail det = new GPUQueueResourceDetail();
                    det.setResourceType(resource);
                    det.setQuotaNum(m.getValue().intValue());
                    return det;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(GPUQueueResourceDetail::getResourceType, Function.identity(), (a, b) -> a));
        // available
        Map<String, Double> availableResource = Optional.ofNullable(detail.getTotalResource()).map(QueueTotalResource::getMaxResources)
                .orElse(new HashMap<>());
        details.forEach((k, v) -> {
            Double available = availableResource.get(k.getMlpName());
            v.setAvailableNum(Optional.ofNullable(available).map(Number::intValue).orElse(null));
        });
        // allocated
        Map<String, Double> allocatedResource = Optional.ofNullable(detail.getUsedResource()).map(QueueUsedResource::getAllocatedResources)
                .orElse(new HashMap<>());
        details.forEach((k, v) -> {
            Double allocated = allocatedResource.get(k.getMlpName());
            v.setAllocatedNum(Optional.ofNullable(allocated).map(Number::intValue).orElse(null));
        });
        // pending
        Map<String, Double> pendingResource = Optional.ofNullable(detail.getUsedResource()).map(QueueUsedResource::getPendingResources)
                .orElse(new HashMap<>());
        details.forEach((k, v) -> {
            Double pending = pendingResource.get(k.getMlpName());
            v.setPendingNum(Optional.ofNullable(pending).map(Number::intValue).orElse(null));
        });
        // remain
        Map<String, Double> remainResource = Optional.ofNullable(detail.getRemainResource()).map(QueueRemainResource::getRemainResources)
                .orElse(new HashMap<>());
        details.forEach((k, v) -> {
            Double remain = remainResource.get(k.getMlpName());
            v.setRemainNum(Optional.ofNullable(remain).map(Number::intValue).orElse(null));
        });
        return new ArrayList<>(details.values());
    }

    public Pair<Boolean, String> addInstance(ModelDeployRequest request, int addInstanceNum, GPUResourceType gcoresType, String queueName) {
        String token = StringUtils.truncate(UUID.randomUUID().toString(), 32);
        Integer expireTime = 1;

        Preconditions.checkState(addInstanceNum > 0);
        try {
            AddInstanceQuery req = new AddInstanceQuery();
            req.setToken(token);
            req.setExpireTime(expireTime);
            req.setAppkey(request.getAppkey());
            req.setUser(request.getOptMis());
            req.setName(request.getGroup());

            InstanceQuery inst = new InstanceQuery();
            String set = request.getSet();
            if (StringUtils.isNotBlank(set) && set.contains("default")) {
                set = "";
            }
            inst.setSetName(set);
            inst.setCluster(extractCluster(queueName));
            inst.setQueue(queueName);
            inst.setWorker(addInstanceNum);
            ResourceQuery resrc = new ResourceQuery();
            inst.setResource(resrc);
            resrc.setVcores(request.getVcores());
            resrc.setMemory(request.getMemory());
            resrc.setGcores(request.getGcores());
            resrc.setGcoresType(gcoresType.getName());
            req.setInstanceQueries(Lists.newArrayList(inst));

            CommonResponseVo resp = mlpModelServingService.addInstance(req);
            log.info("addInstance: req:request.getAppkey():{},request.getGroup():{},request.getSet():{},request.getQueueName():{},addInstanceNum:{},request.getVcores():{},request.getMemory():{},request.getGcores():{},gcoresType:{}, resp:{}",
                    request.getAppkey(), request.getGroup(), request.getSet(), request.getQueueName(), addInstanceNum, request.getVcores(), request.getMemory(), request.getGcores(), gcoresType, JSON.toJSONString(resp));
            return Pair.of(resp.getCode() == 0, resp.getMessage());
        } catch (Exception e) {
            log.error("addInstance: {}, ex", request.getAppkey(), e);
            return Pair.of(false, e.getMessage());
        }
    }

    private static String extractCluster(String input) {
        int firstDotIndex = input.indexOf('.');
        int secondDotIndex = input.indexOf('.', firstDotIndex + 1);
        if (firstDotIndex != -1 && secondDotIndex != -1) {
            return input.substring(firstDotIndex + 1, secondDotIndex);
        }
        return "";
    }

    public Pair<Boolean, String> deleteInstance(String appkey,
                                                String group, List<String> hostNames, String optMis) {
        String token = StringUtils.truncate(UUID.randomUUID().toString(), 32);
        Integer expireTime = 1;
        String user = optMis;
        try {
            DeleteInstanceQuery req = new DeleteInstanceQuery();
            req.setToken(token);
            req.setExpireTime(expireTime);
            req.setAppkey(appkey);
            req.setUser(user);
            req.setName(group);
            req.setHostNames(hostNames);
            CommonResponseVo resp = mlpModelServingService.deleteInstance(req);
            log.info("deleteInstance: req:appkey:{},group:{},hostNames:{}, resp:{}",
                    appkey, group, hostNames, JSON.toJSONString(resp));
            return Pair.of(resp.getCode() == 0, resp.getMessage());
        } catch (Exception e) {
            log.error("deleteInstance: {}, ex", appkey, e);
            return Pair.of(false, e.getMessage());
        }
    }

    public Pair<Boolean, String> deleteInstance(String appkey,
                                                String group, int deleteInstanceNum, String optMis) {
        Preconditions.checkState(deleteInstanceNum > 0);
        ServingDetailDataVo servingDetail = getServingDetail(appkey, optMis);
        Objects.requireNonNull(servingDetail);
        List<GroupVo> groups = servingDetail.getGroups();
        if (CollectionUtils.isEmpty(groups)) {
            return Pair.of(false, "服务下无分组");
        }
        GroupVo groupVo = groups.stream().filter(g -> g.getName().equals(group)).findFirst().orElse(null);
        if (groupVo == null) {
            return Pair.of(false, "分组不存在");
        }
        List<InstanceVo> instances = groupVo.getInstances();
        if (CollectionUtils.isEmpty(instances)) {
            return Pair.of(false, "分组下无实例");
        }
        if (deleteInstanceNum > instances.size()) {
            return Pair.of(false, "删除实例数大于分组下实例数");
        }
        Comparator<InstanceVo> cmp = Comparator.comparing(inst -> Optional.ofNullable(inst.getStartTime()).orElse(String.valueOf(inst.getId())));
        cmp = cmp.reversed();
        List<String> hostNames = instances.stream()
                .sorted(cmp)
                .map(InstanceVo::getHostName)
                .filter(Objects::nonNull)
                .limit(deleteInstanceNum)
                .collect(Collectors.toList());
        log.info("deleteInstance: appkey:{},group:{},hostNames:{}", appkey, group, hostNames);
        return deleteInstance(appkey, group, hostNames, optMis);
    }

    public ServingDetailDataVo getServingDetail(String appkey, String optMis) {
        String user = optMis;
        try {
            ServingInfoQuery req = new ServingInfoQuery();
            req.setAppkey(appkey);
            req.setUser(user);
            ServingDetailVo resp = mlpModelServingService.getServingDetail(req);

            log.info("getServingDetail: req:appkey:{}, resp:{}",
                    appkey, JSON.toJSONString(resp));
            if (resp == null || resp.getData() == null || resp.getCode() != 0) {
                return null;
            }
            return resp.getData();
        } catch (Exception e) {
            log.error("getServingDetail: {}, ex", appkey, e);
            throw new RuntimeException(e);
        }
    }

    public Pair<Boolean, String> deleteServingGroupQuery(String appkey, String groupName, String optMis) {
        try {
            DeleteServingGroupQuery req = new DeleteServingGroupQuery();
            req.setAppkey(appkey);
            req.setName(groupName);
            req.setUser(optMis);
            CommonResponseVo resp = mlpModelServingService.deleteServingGroup(req);

            log.info("getServingDetail: req:appkey:{}, resp:{}",
                    appkey, JSON.toJSONString(resp));
            return Pair.of(resp.getCode() == 0, resp.getMessage());
        } catch (Exception e) {
            log.error("getServingDetail: {}, ex", appkey, e);
            throw new RuntimeException(e);
        }
    }
}
