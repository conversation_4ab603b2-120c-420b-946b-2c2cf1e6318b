package com.sankuai.algoplatform.matchops.infrastructure.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.BizStrategy;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.BizStrategyExample;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface BizStrategyMapper extends MybatisBaseMapper<BizStrategy, BizStrategyExample, Long> {

    int batchInsert(@Param("list") List<BizStrategy> list);

    @Select("SELECT * FROM biz_strategy " +
            "WHERE JSON_CONTAINS(json_extract(abtest_config, '$.distributions'), CONCAT('{\"strategyId\":', #{strategyId}, '}')) or " +
            "JSON_CONTAINS(json_extract(abtest_config, '$.distributions'), CONCAT('{\"strategyId\":\"', #{strategyId}, '\"}'))")
    List<BizStrategy> selectByStrategyId(@Param("strategyId") Long strategyId);
}