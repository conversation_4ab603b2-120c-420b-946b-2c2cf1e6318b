package com.sankuai.algoplatform.matchops.infrastructure.model;

import com.google.common.collect.Lists;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.AlgoPackage;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.AlgoStrategy;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/10
 */
@Data
public class AlgoStrategyPackagePo {

    /**
     * 算法策略
     */
    private AlgoStrategy algoStrategy;
    /**
     * 算法配置
     */
    private AlgoPackage algoPackage;
    /**
     * 文本转向量策略
     */
    private AlgoStrategy convertStrategy;
    /**
     * 文本转向量配置
     */
    private AlgoPackage convertPackage;

    public AlgoStrategyPackagePo(AlgoStrategy algoStrategy, AlgoPackage algoPackage) {
        this.algoStrategy = algoStrategy;
        this.algoPackage = algoPackage;
    }

    public void setConvertStrategyPackage(AlgoStrategy convertStrategy, AlgoPackage convertPackage) {
        this.convertStrategy = convertStrategy;
        this.convertPackage = convertPackage;
    }

    public boolean isValid() {
        return algoStrategy != null && algoPackage != null;
    }

    public List<AlgoStrategy> getStrategyList() {
        if (convertStrategy == null || convertPackage == null) {
            return Lists.newArrayList(algoStrategy);
        }
        return Lists.newArrayList(algoStrategy, convertStrategy);
    }

    public List<AlgoPackage> getPackageList() {
        if (convertStrategy == null || convertPackage == null) {
            return Lists.newArrayList(algoPackage);
        }
        return Lists.newArrayList(algoPackage, convertPackage);
    }
}
