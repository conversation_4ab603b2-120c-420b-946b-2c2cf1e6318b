package com.sankuai.algoplatform.matchops.infrastructure.proxy.mcm.context;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.algoplatform.matchops.api.request.algoPackage.TAddEditAlgoBizCodeRequest;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.AlgoStrategyDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.BizStrategyDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.AlgoStrategy;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.BizStrategy;
import com.sankuai.algoplatform.matchops.infrastructure.util.ContextUtil;
import com.sankuai.mcm.client.sdk.annotation.McmComponent;
import com.sankuai.mcm.client.sdk.context.eventcontext.EventContextPropertyProviderAdaptor;
import com.sankuai.mcm.client.sdk.context.eventcontext.EventContextPropertyProviderRequest;
import com.sankuai.mcm.client.sdk.dto.common.UserIdentity;
import com.sankuai.meituan.auth.vo.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@McmComponent(events = "UpdateAlgoBizCode")
public class AlgoBizCodeEventContextPropertyProvider extends EventContextPropertyProviderAdaptor {

    @Autowired
    private BizStrategyDao bizStrategyDao;

    @Autowired
    private AlgoStrategyDao algoStrategyDao;

    @Override
    public UserIdentity getUserIdentity(EventContextPropertyProviderRequest request) {
        return UserIdentity.ofUser(ContextUtil.getLoginUserMis());
    }


    @Override
    public User getSsoUser() {
        User user = new User();
        user.setLogin(ContextUtil.getLoginUserMis());
        return user;
    }

    @Override
    public Map<String, Object> getExtraInfo(EventContextPropertyProviderRequest request) {
        Map<String, Object> extraInfo = new HashMap<>();

        try {
            // 获取before信息 - 从数据库查询当前状态
            Map<String, Object> beforeInfo = getBeforeInfo(request);
            extraInfo.put("before", beforeInfo);

            // 获取after信息 - 格式化显示
            Map<String, Object> afterInfo = getAfterInfo(request);
            extraInfo.put("after", afterInfo);

        } catch (Exception e) {
            log.error("获取算法业务代码变更上下文信息失败", e);
            extraInfo.put("before", "获取变更前信息失败: " + e.getMessage());
            extraInfo.put("after", "获取变更后信息失败: " + e.getMessage());
        }

        return extraInfo;
    }

    /**
     * 获取变更前信息 - 格式化显示
     */
    private Map<String, Object> getBeforeInfo(EventContextPropertyProviderRequest request) {
        Object[] args = request.getArgs();
        if (args == null || args.length == 0) {
            throw new IllegalArgumentException("请求参数为空");
        }

        TAddEditAlgoBizCodeRequest requestObj = (TAddEditAlgoBizCodeRequest) args[0];
        if (requestObj.algoBizCodeId == null) {
            throw new IllegalArgumentException("algoBizCodeId参数为空");
        }

        BizStrategy bizStrategy = bizStrategyDao.selectById(requestObj.algoBizCodeId);
        if (bizStrategy == null) {
            throw new IllegalStateException("未找到对应的BizStrategy记录, algoBizCodeId: " + requestObj.algoBizCodeId);
        }

        Map<String, Object> beforeInfo = new HashMap<>();
        beforeInfo.put("算法代码bizCode", bizStrategy.getBizCode());
        beforeInfo.put("说明", bizStrategy.getNote());

        // 解析abtest_config获取实验名称和策略信息
        String abtestConfig = bizStrategy.getAbtestConfig();
        if (abtestConfig != null && !abtestConfig.trim().isEmpty()) {
            JSONObject abtestJson = JSON.parseObject(abtestConfig);
            beforeInfo.put("实验名称", abtestJson.getString("strategyName"));

            JSONArray distributionsArray = abtestJson.getJSONArray("distributions");
            if (distributionsArray != null && !distributionsArray.isEmpty()) {
                beforeInfo.put("算法代码策略", buildStrategiesList(distributionsArray));
            }
        }

        log.info("成功获取变更前算法业务代码信息, algoBizCodeId: {}", requestObj.algoBizCodeId);
        return beforeInfo;
    }

    /**
     * 构建策略列表
     */
    private List<Map<String, Object>> buildStrategiesList(JSONArray distributionsArray) {
        List<Map<String, Object>> strategiesList = new ArrayList<>();

        for (int i = 0; i < distributionsArray.size(); i++) {
            JSONObject distribution = distributionsArray.getJSONObject(i);
            Map<String, Object> strategyInfo = new HashMap<>();
            strategyInfo.put("流量配比", distribution.getString("quota"));
            strategyInfo.put("策略名称", getStrategyName(distribution.getString("strategyId")));
            strategiesList.add(strategyInfo);
        }

        return strategiesList;
    }

    /**
     * 根据策略ID获取策略名称
     */
    private String getStrategyName(String strategyId) {
        if (strategyId == null) {
            return "策略ID为空";
        }

        try {
            Long strategyIdLong = Long.parseLong(strategyId);
            AlgoStrategy algoStrategy = algoStrategyDao.selectById(strategyIdLong);
            return algoStrategy != null ? algoStrategy.getNote() : "未找到策略信息";
        } catch (NumberFormatException e) {
            log.warn("strategyId格式错误: {}", strategyId);
            return "策略ID格式错误";
        }
    }

    /**
     * 获取变更后信息 - 格式化显示
     */
    private Map<String, Object> getAfterInfo(EventContextPropertyProviderRequest request) {
        Map<String, Object> afterInfo = new HashMap<>();

        try {
            Object[] args = request.getArgs();
            if (args != null && args.length > 0) {
                TAddEditAlgoBizCodeRequest requestObj = (TAddEditAlgoBizCodeRequest) args[0];

                // 根据algoBizCodeId查询数据库获取真实的bizCode
                Long algoBizCodeId = requestObj.algoBizCodeId;
                String bizCode = "";

                if (algoBizCodeId != null) {
                    BizStrategy bizStrategy = bizStrategyDao.selectById(algoBizCodeId);
                    if (bizStrategy != null) {
                        bizCode = bizStrategy.getBizCode();
                    }
                }

                // 格式化基本信息
                afterInfo.put("算法代码bizCode", bizCode);
                afterInfo.put("说明", requestObj.note);

                // 处理strategy信息
                Map<String, String> strategy = requestObj.strategy;
                if (strategy != null) {
                    String strategyName = strategy.get("strategyName");
                    String distributions = strategy.get("distributions");

                    afterInfo.put("实验名称", strategyName);

                    // 解析distributions获取策略信息
                    if (distributions != null) {
                        try {
                            JSONArray distributionsArray = JSON.parseArray(distributions);
                            List<Map<String, Object>> strategiesList = new ArrayList<>();

                            for (int i = 0; i < distributionsArray.size(); i++) {
                                JSONObject distribution = distributionsArray.getJSONObject(i);
                                String quota = distribution.getString("quota");
                                String strategyId = distribution.getString("strategyId");

                                Map<String, Object> strategyInfo = new HashMap<>();
                                strategyInfo.put("流量配比", quota);

                                // 根据strategyId查询algo_strategy表获取策略名称
                                if (strategyId != null) {
                                    try {
                                        Long strategyIdLong = Long.parseLong(strategyId);
                                        AlgoStrategy algoStrategy = algoStrategyDao.selectById(strategyIdLong);
                                        if (algoStrategy != null) {
                                            strategyInfo.put("策略名称", algoStrategy.getNote());
                                        } else {
                                            strategyInfo.put("策略名称", "未找到策略信息");
                                        }
                                    } catch (NumberFormatException e) {
                                        log.warn("strategyId格式错误: {}", strategyId);
                                        strategyInfo.put("策略名称", "策略ID格式错误");
                                    }
                                } else {
                                    strategyInfo.put("策略名称", "策略ID为空");
                                }

                                strategiesList.add(strategyInfo);
                            }

                            afterInfo.put("算法代码策略", strategiesList);
                        } catch (Exception e) {
                            log.error("解析distributions失败", e);
                            afterInfo.put("算法代码策略", "解析失败");
                        }
                    }
                }

                log.info("成功格式化变更后算法业务代码信息");
                return afterInfo;
            } else {
                afterInfo.put("error", "请求参数为空");
                return afterInfo;
            }
        } catch (Exception e) {
            log.error("提取变更后信息失败", e);
            afterInfo.put("error", "提取请求参数失败: " + e.getMessage());
            return afterInfo;
        }
    }
}