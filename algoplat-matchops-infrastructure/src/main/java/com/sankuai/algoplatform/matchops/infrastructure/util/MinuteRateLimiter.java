package com.sankuai.algoplatform.matchops.infrastructure.util;


import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j
public class MinuteRateLimiter {

    private final long maxReqCount; // 每分钟请求次数的上限

    private final AtomicLong balance;  // 请求余额

    private final AtomicBoolean started = new AtomicBoolean(false);

    private final AtomicBoolean stopped = new AtomicBoolean(false);

    private final Object lock = new Object();

    private final ScheduledExecutorService timer = Executors.newSingleThreadScheduledExecutor();

    public static MinuteRateLimiter create(int maxReqCount) {
        return new MinuteRateLimiter(maxReqCount);
    }

    private MinuteRateLimiter(int maxReqCount){
        this.maxReqCount = maxReqCount;
        this.balance = new AtomicLong(maxReqCount);
        // 启动定时器, 每分钟刷新份额
        start();
    }

    // 获取一个请求份额: 直到获取成功 或 限流器停止
    public boolean acquire() throws InterruptedException {
        long prev = balance.getAndDecrement();
        if(prev > 0) return true; // 扣减成功
        synchronized (lock) {
            while (!isStopped()){
                prev = balance.getAndDecrement();
                if(prev > 0) return true;
                else lock.wait();
            }
            return false;
        }
    }

    // 启动限流器
    public void start() {
        if(started.compareAndSet(false, true)){
            timer.scheduleAtFixedRate(new Runnable() {
                @Override
                public void run() {
                    try{
                        // 每分钟刷新一次请求份额
                        balance.getAndUpdate(prev->maxReqCount);
                        synchronized (lock){
                            lock.notifyAll(); // 唤醒所有等待线程
                        }
                    }catch (Exception e){
                        log.error(e.getMessage(), e);

                    }

                }
            }, 60, 60, TimeUnit.SECONDS);
        }
    }

    // 返回值: 是否已经关闭
    public void stop() {
        if(started.get() && stopped.compareAndSet(false,true)) {
            // 进入该代码块的条件: 限流器已经启动, 并且未停止
            timer.shutdown();
            synchronized (lock){
                lock.notifyAll(); // 防止线程泄漏
            }
        }
    }

    private boolean isStopped() {
        return stopped.get();
    }

//    public static void main(String[] args) {
//        MinuteRateLimiter rateLimiter = MinuteRateLimiter.create(130);
//        rateLimiter.start();
//
//        CountDownLatch latch = new CountDownLatch(5);
//        for(int i = 0; i < 5; i++){
//            final int idx = i;
//            new Thread(()->{
//                int reqCnt = 0;
//                while (true){
//                    try {
//                        boolean success = rateLimiter.acquire();
//                        if(success){
//                            reqCnt++;
//                            Thread.sleep(500);
//                        } else break;
//                    } catch (InterruptedException e) {
//                        e.printStackTrace();
//                    }
//                }
//                System.out.println("线程" + idx + ": 请求次数" + reqCnt);
//                latch.countDown();
//            }).start();
//        }
//
//        new Thread(()->{
//            try {
//                Thread.sleep(118 * 1000);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
//            rateLimiter.stop();
//        }).start();
//
//        try {
//            latch.await();
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//
//    }
}
