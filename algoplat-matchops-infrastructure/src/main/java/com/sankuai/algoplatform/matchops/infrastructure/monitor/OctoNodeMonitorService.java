package com.sankuai.algoplatform.matchops.infrastructure.monitor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.InspectionRuleConfigDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.InspectionRuleConfig;
import com.sankuai.algoplatform.matchops.infrastructure.enums.OctoNodeStatus;
import com.sankuai.algoplatform.matchops.infrastructure.model.OctoNode;
import com.sankuai.algoplatform.matchops.infrastructure.proxy.OctoNodeService;
import com.sankuai.algoplatform.matchops.infrastructure.util.DateUtil;
import com.sankuai.inf.octo.mns.model.HostEnv;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.core.LogEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;


@Component
@Slf4j
public class OctoNodeMonitorService {

    @Autowired
    private InspectionRuleConfigDao inspectionRuleConfigDao;

    @Autowired
    private OctoNodeService octoNodeService;


    public void serverNodeMonitor() {
        List<InspectionRuleConfig> all = inspectionRuleConfigDao.getAll();
        for (InspectionRuleConfig config : all) {
            checkServerNodes(config);
        }
    }


    private void checkServerNodes(InspectionRuleConfig ruleConfig) {
        List<OctoNode> nodes = octoNodeService.getOctoNodeStatus(ruleConfig.getAppkey(), ruleConfig.getCell(), HostEnv.PROD, OctoNodeStatus.NORMAL);
        String rule = ruleConfig.getRule();
        List<Map<String, String>> list = JSONObject.parseObject(rule, new TypeReference<List<Map<String, String>>>() {
        });
        for (Map<String, String> map : list) {
            String time = map.get("time");
            String[] split = time.split("-");
            int minInstanceNums = Integer.parseInt(map.get("minInstanceNums"));
            if (checkTime(split[0], split[1])) {
                if (nodes.size() < minInstanceNums) {
                    log.info("当前服务线上机器不足，rule:{}", JSON.toJSONString(ruleConfig));
                    Cat.logEvent("InsufficientAliveNode", ruleConfig.getAppkey() + "#" + ruleConfig.getCell());
                }
            }
        }

    }

    private boolean checkTime(String startTime, String endTime) {
        String current = DateUtil.toDateTimeString_HHmm(new Date());
        return current.compareTo(startTime) >= 0 && current.compareTo(endTime) < 0;
    }

}
