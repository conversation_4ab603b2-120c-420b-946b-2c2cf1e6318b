package com.sankuai.algoplatform.matchops.infrastructure.dal.entity;

import com.sankuai.aifree.thrift.generation.query.BatchingConfigQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelDeployRequest {
    /**
     * 标识
     */
    private String appkey;
    
    /**
     * 分组名称
     */
    private String group;
    
    /**
     * 队列名称
     */
    private List<String> queueName;

    /**
     * 项目组
     */
    @Builder.Default
    private String wxProject = "diancan";


    /**
     * 模型列表
     */
    //private List<Map<String, Object>> models;
    /**
     * 批处理配置
     */
    private BatchingConfigQuery batchingConfigQuery;

    /**
     * 是否开启batch
     */
    private Boolean batching;

    /**
     * rpc超时时间
     */
    private Integer rpcTimeout;

    /**
     * 环境
     */
    private String env;

    /**
     * 版本号
     */
    private List<Long> versionNums;

    /**
     * set
     */
    private String set;
    
    /**
     * 所需实例数量
     */
    private int requireInstanceNum;
    
    /**
     * CPU核心数
     */
    private int vcores;
    
    /**
     * 服务类型
     */
    private String servingType;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 内存大小(MB)
     */
    private int memory;
    
    /**
     * GPU核心数
     */
    private int gcores;
    
    /**
     * GPU类型
     */
    private String gcoresType;
    
    /**
     * 操作人MIS
     */
    private String optMis;
    
    /**
     * 服务镜像
     */
    private String serverImage;
}