package com.sankuai.algoplatform.matchops.infrastructure.dal.dao;

import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.BizStrategy;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.LlmBizStrategy;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.LlmBizStrategyExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.LlmBizStrategyMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service

public class LlmBizStrategyDao {
    @Autowired
    LlmBizStrategyMapper llmBizStrategyMapper;

    public List<LlmBizStrategy> selectAllByBizLineIdAndEnv(Long bizLineId, Integer env) {
        LlmBizStrategyExample example = new LlmBizStrategyExample();
        example.createCriteria().andBizLineIdEqualTo(bizLineId).andEnvEqualTo(env).andStatusEqualTo(true);
        return llmBizStrategyMapper.selectByExample(example);
    }

    public List<LlmBizStrategy> selectByPageAndBizLineId(int page, int pageSize, long bizLineId) {
        LlmBizStrategyExample example = new LlmBizStrategyExample();
        example.limit((page - 1) * pageSize, pageSize);
        example.createCriteria().andBizLineIdEqualTo(bizLineId)
                .andStatusEqualTo(true);
        return llmBizStrategyMapper.selectByExample(example);
    }

    public Long selectCountByBizLineId(long bizLineId) {
        LlmBizStrategyExample example = new LlmBizStrategyExample();
        example.createCriteria().andBizLineIdEqualTo(bizLineId).andStatusEqualTo(true);
        return llmBizStrategyMapper.countByExample(example);
    }

    public LlmBizStrategy selectById(Long id) {
        return llmBizStrategyMapper.selectByPrimaryKey(id);
    }

    public List<LlmBizStrategy> selectByBizCodes(List<String> bizCodes) {
        LlmBizStrategyExample example = new LlmBizStrategyExample();
        example.createCriteria().andBizCodeIn(bizCodes);
        return llmBizStrategyMapper.selectByExample(example);
    }

    public LlmBizStrategy selectByBizCode(String bizCode) {
        LlmBizStrategyExample example = new LlmBizStrategyExample();
        example.createCriteria().andBizCodeEqualTo(bizCode);
        List<LlmBizStrategy> llmBizStrategies = llmBizStrategyMapper.selectByExample(example);
        return CollectionUtils.isNotEmpty(llmBizStrategies) ? llmBizStrategies.get(0) : null;
    }

    public int update(LlmBizStrategy llmBizStrategy) {
        return llmBizStrategyMapper.updateByPrimaryKeySelective(llmBizStrategy);
    }

    public Long insert(LlmBizStrategy llmBizStrategy) {
        llmBizStrategyMapper.insertSelective(llmBizStrategy);
        return llmBizStrategy.getId();
    }

    public int softDelete(Long id, String mis) {
        LlmBizStrategy llmBizStrategy = selectById(id);
        if (llmBizStrategy != null) {
            llmBizStrategy.setStatus(false);
            llmBizStrategy.setOwner(mis);
            return update(llmBizStrategy);
        }
        return 0;
    }

    public List<LlmBizStrategy> selectByStrategyId(String modelBizCode) {
        return llmBizStrategyMapper.selectByModelBizCode(modelBizCode);
    }
}
