package com.sankuai.algoplatform.matchops.infrastructure.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.BizLlmpredictPromptTemplate;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.BizLlmpredictPromptTemplateExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BizLlmpredictPromptTemplateMapper extends MybatisBLOBsMapper<BizLlmpredictPromptTemplate, BizLlmpredictPromptTemplateExample, Long> {
    int batchInsert(@Param("list") List<BizLlmpredictPromptTemplate> list);
}