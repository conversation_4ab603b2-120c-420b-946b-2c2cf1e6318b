package com.sankuai.algoplatform.matchops.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: task_binding_config
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TaskBindingConfig {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: schedule_service_id
     *   说明: 绑定服务id
     */
    private Long scheduleServiceId;

    /**
     *   字段: schedule_task_id
     *   说明: 绑定调度任务id
     */
    private Long scheduleTaskId;

    /**
     *   字段: resource_pool_ids
     *   说明: 绑定资源池id列表
     */
    private String resourcePoolIds;

    /**
     *   字段: status
     *   说明: 状态（0：正常，-1禁用）
     */
    private Integer status;

    /**
     *   字段: operator_mis
     *   说明: 操作人id
     */
    private String operatorMis;

    /**
     *   字段: add_time
     *   说明: 添加时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}