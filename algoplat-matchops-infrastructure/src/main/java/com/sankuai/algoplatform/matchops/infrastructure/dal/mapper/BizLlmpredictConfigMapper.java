package com.sankuai.algoplatform.matchops.infrastructure.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.BizLlmpredictConfig;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.BizLlmpredictConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BizLlmpredictConfigMapper extends MybatisBaseMapper<BizLlmpredictConfig, BizLlmpredictConfigExample, Long> {
    int batchInsert(@Param("list") List<BizLlmpredictConfig> list);
}