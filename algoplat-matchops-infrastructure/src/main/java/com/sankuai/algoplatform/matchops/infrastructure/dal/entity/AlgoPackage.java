package com.sankuai.algoplatform.matchops.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: algo_package
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AlgoPackage {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: note
     *   说明: 算法包说明
     */
    private String note;

    /**
     *   字段: owner_mis
     *   说明: 算法包负责人
     */
    private String ownerMis;

    /**
     *   字段: runtime
     *   说明: python环境
     */
    private String runtime;

    /**
     *   字段: module_path
     *   说明: 包路径
     */
    private String modulePath;

    /**
     *   字段: version
     *   说明: 算法包版本
     */
    private String version;

    /**
     *   字段: code_repo
     *   说明: 代码仓库地址
     */
    private String codeRepo;

    /**
     *   字段: status
     *   说明: 状态，0：正常，-1：删除
     */
    private Integer status;

    /**
     *   字段: add_time
     *   说明: 添加时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}