package com.sankuai.algoplatform.matchops.infrastructure.dal.dao;

import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.AlgoPackage;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.BizLlmpredictPromptTemplate;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.LlmBizStrategy;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.BizLlmpredictPromptTemplateExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.LlmBizStrategyExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.BizLlmpredictPromptTemplateMapper;
import com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.LlmBizStrategyMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service

public class BizLlmPredictPromptTemlateDao {
    @Autowired
    BizLlmpredictPromptTemplateMapper bizLlmpredictPromptTemplateMapper;

    public BizLlmpredictPromptTemplate selectById(Long id) {
        return bizLlmpredictPromptTemplateMapper.selectByPrimaryKey(id);
    }

    public List<BizLlmpredictPromptTemplate> selectByIds(List<Long> ids) {
        BizLlmpredictPromptTemplateExample example = new BizLlmpredictPromptTemplateExample();
        example.createCriteria().andIdIn(ids);
        return bizLlmpredictPromptTemplateMapper.selectByExample(example);
    }

    public int update(BizLlmpredictPromptTemplate bizLlmpredictPromptTemplate) {
        return bizLlmpredictPromptTemplateMapper.updateByPrimaryKeySelective(bizLlmpredictPromptTemplate);
    }

    public Long insert(BizLlmpredictPromptTemplate bizLlmpredictPromptTemplate) {
        bizLlmpredictPromptTemplateMapper.insertSelective(bizLlmpredictPromptTemplate);
        return bizLlmpredictPromptTemplate.getId();
    }

    public int softDelete(Long id, String mis) {
        BizLlmpredictPromptTemplate bizLlmpredictPromptTemplate = selectById(id);
        if (bizLlmpredictPromptTemplate != null) {
            bizLlmpredictPromptTemplate.setStatus(false);
            bizLlmpredictPromptTemplate.setOwner(mis);
            return update(bizLlmpredictPromptTemplate);
        }
        return 0;
    }
}
