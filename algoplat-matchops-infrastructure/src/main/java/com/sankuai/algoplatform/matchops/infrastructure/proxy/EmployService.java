package com.sankuai.algoplatform.matchops.infrastructure.proxy;

import com.dianping.cat.Cat;
import com.sankuai.meituan.org.opensdk.model.domain.Emp;
import com.sankuai.meituan.org.opensdk.service.EmpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/6/17
 */
@Service
public class EmployService {

    @Autowired
    private EmpService empService;

    public Long getEmployId(String mis) {
        try {
            Emp emp = empService.queryByMis(mis, null);
            return Long.parseLong(emp.getEmpId());
        } catch (Exception e) {
            Cat.logError(e.getMessage(), e);
            throw new RuntimeException("Error fetching employee ID", e);
        }
    }
}
