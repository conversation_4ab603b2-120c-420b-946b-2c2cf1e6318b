package com.sankuai.algoplatform.matchops.infrastructure.dal.dao;

import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ReporterTemplate;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.ReporterTemplateExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.ReporterTemplateMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
@Service

public class ReporterTemplateDao {
    @Autowired
    ReporterTemplateMapper reporterTemplateMapper;

    public List<ReporterTemplate> selectAll() {
        ReporterTemplateExample example = new ReporterTemplateExample();
        return reporterTemplateMapper.selectByExample(example);
    }

    public ReporterTemplate selectById(Long id) {
        ReporterTemplateExample example = new ReporterTemplateExample();
        example.createCriteria().andIdEqualTo(id);
        List<ReporterTemplate> list = reporterTemplateMapper.selectByExample(example);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    public List<ReporterTemplate> selectByIds(List<Long> id) {
        ReporterTemplateExample example = new ReporterTemplateExample();
        example.createCriteria().andIdIn(id);
        return reporterTemplateMapper.selectByExample(example);
    }


    public void insert(ReporterTemplate taskRunRecord) {
        reporterTemplateMapper.insertSelective(taskRunRecord);
    }

    public void update(ReporterTemplate taskRunRecord) {
        reporterTemplateMapper.updateByPrimaryKeySelective(taskRunRecord);
    }

    public void delete(Long id) {
        reporterTemplateMapper.deleteByPrimaryKey(id);
    }
}
