package com.sankuai.algoplatform.matchops.infrastructure.proxy;

import com.alibaba.fastjson.JSON;
import com.amazonaws.AmazonServiceException;
import com.amazonaws.HttpMethod;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.google.common.base.Preconditions;
import com.google.common.collect.ImmutableMap;
import com.sankuai.algoplatform.matchops.infrastructure.config.S3Properties;
import com.sankuai.algoplatform.matchops.infrastructure.util.ContextUtil;
import com.sankuai.algoplatform.matchops.infrastructure.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Date;
import java.util.Map;


@Service
@Slf4j
public class S3Service {

    @Resource
    private S3Properties s3Properties;

    @Resource
    private AmazonS3 amazonS3;

    private static final String MAC_NAME = "HmacSHA1";

    private static final String ENCODING = "UTF-8";

    public Map<String, String> getNormalUploadSign(String fileName) throws Exception {
        String bucketName = s3Properties.getBucketName();
        String accessKey = s3Properties.getAccessKey();
        String secretKey = s3Properties.getSecretKey();


        String expiration = DateUtil.getAddNumDay(30, DateUtil.uploadDateTimeFormatter);
        String policy = String.format("{\"expiration\":\"%s\",\"conditions\":[{\"bucket\":\"%s\"},[\"starts-with\",\"$key\",\"\"]]}", expiration, bucketName);
        String policyEncoded = Base64.getEncoder().encodeToString(policy.getBytes(StandardCharsets.UTF_8));

        String signature = Base64.getEncoder().encodeToString(hmacSHA1Encrypt(policyEncoded, secretKey));
        if (StringUtils.isEmpty(fileName)) {
            fileName = "match_test_tool_" + ContextUtil.getLoginUserId();
        }
        String uploadFileName = fileName + "_" + System.currentTimeMillis();
        return ImmutableMap.of("signature", signature,
                "policy", policyEncoded,
                "AWSAccessKeyId", accessKey,
                "key", uploadFileName);
    }

    public String uploadBytes(byte[] bytes, String fileName) {
        return upload2S3(new ByteArrayInputStream(bytes), fileName);
    }

    public String uploadPic(String dataURI, String fileName) {
        if (StringUtils.contains(dataURI, "data:")) {
            dataURI = StringUtils.substringAfterLast(dataURI, ",");
        }
        byte[] bytes = Base64.getDecoder().decode(dataURI);
        return upload2S3(new ByteArrayInputStream(bytes), fileName);
    }

    public String upload2S3(InputStream inputStream, String fileName) {
        Preconditions.checkNotNull(inputStream);
        Preconditions.checkNotNull(fileName);

        PutObjectRequest req = new PutObjectRequest(
                s3Properties.getBucketName(), fileName, inputStream, new ObjectMetadata());

        try {
            amazonS3.putObject(req);
        } catch (AmazonServiceException e) {
            throw new RuntimeException("S3PutObject error: " + fileName, e);
        }
        GeneratePresignedUrlRequest request =
                new GeneratePresignedUrlRequest(s3Properties.getBucketName(), fileName);
        request.setMethod(HttpMethod.GET);
        request.setExpiration(getExpiration());
        URL s3Url = amazonS3.generatePresignedUrl(request);
        log.info("upload2S3: file:{}, url:{}", fileName, JSON.toJSONString(s3Url));
        return s3Url.toString();
    }

    private static Date getExpiration() {
        long milliSeconds = System.currentTimeMillis();
        milliSeconds += 1000L * 60 * 60 * 24 * 30; //过期时间1个月
        return new Date(milliSeconds);
    }

    private byte[] hmacSHA1Encrypt(String encryptText, String encryptKey) throws Exception {
        byte[] data = encryptKey.getBytes(ENCODING);
        //根据给定的字节数组构造一个密钥，第二参数指定一个密钥算法的名称
        SecretKey secretKey = new SecretKeySpec(data, MAC_NAME);
        //生成一个指定 Mac 算法 的 Mac 对象
        Mac mac = Mac.getInstance(MAC_NAME);
        //用给定密钥初始化 Mac 对象
        mac.init(secretKey);
        byte[] text = encryptText.getBytes(ENCODING);
        //完成 Mac 操作
        return mac.doFinal(text);
    }

}
