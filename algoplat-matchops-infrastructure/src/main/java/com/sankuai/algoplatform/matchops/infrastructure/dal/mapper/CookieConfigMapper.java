package com.sankuai.algoplatform.matchops.infrastructure.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.CookieConfig;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.CookieConfigExample;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface CookieConfigMapper extends MybatisBaseMapper<CookieConfig, CookieConfigExample, Long> {

    @Insert("INSERT INTO cookie_config(user_id, user_mis, cookie) VALUES (#{record.userId}, #{record.userMis}, #{record.cookie}) " +
            "ON DUPLICATE KEY UPDATE cookie = #{record.cookie}, update_time = now();")
    int insertOrUpdate(@Param("record") CookieConfig config);

}