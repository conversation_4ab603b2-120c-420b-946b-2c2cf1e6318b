package com.sankuai.algoplatform.matchops.infrastructure.proxy;

import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.net.URL;
import java.util.List;

@Slf4j
@Service
public class ExcelReadService {

    public List<List<String>> readService(String urlStr) {
        if (StringUtils.isEmpty(urlStr)) {
            return Lists.newArrayList();
        }
        log.info("read excel start");
        long t = System.currentTimeMillis();
        List<List<String>> res = Lists.newArrayList();
        try {
            URL url = new URL(urlStr);
            String suffix = url.getPath().substring(url.getPath().lastIndexOf(".") + 1);
            Workbook workbook = createWorkbook(suffix, url.openStream());

            Sheet sheet = workbook.getSheetAt(0);

            for (int i = 0; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                // 空行过滤掉
                if (row == null) {
                    continue;
                }
                List<String> line = Lists.newArrayList();
                for (int j = 0; j < row.getLastCellNum(); j++) {
                    Cell cell = row.getCell(j);
                    String value = getCellStringVal(cell);
                    line.add(value);
                }
                res.add(line);
            }
        } catch (Exception e) {
            log.error("read excel error, urlStr:{}", urlStr, e);
        } finally {
            log.info("read excel cost: {}ms, size: {}", System.currentTimeMillis() - t, res.size());
        }
        return res;
    }

    public Workbook createWorkbook(String type, InputStream in) {
        try {
            if ("xlsx".equals(type)) {
                return new XSSFWorkbook(in);
            } else if ("xls".equals(type)) {
                return new HSSFWorkbook(in);
            }
        } catch (Exception e) {
            log.error("create workbook error", e);
        }
        return null;
    }

    public String getCellStringVal(Cell cell) {
        String retValue = null;
        if (cell != null) {
            if (cell.getCellType() != CellType.STRING) {
                cell.setCellType(CellType.STRING);
            }
            retValue = cell.getStringCellValue();
        }
        return retValue;
    }

}
