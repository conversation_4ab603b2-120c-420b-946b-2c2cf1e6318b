package com.sankuai.algoplatform.matchops.infrastructure.proxy;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.util.StringUtils;
import com.dianping.lion.client.util.CollectionUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.reflect.TypeToken;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.algoplatform.matchops.infrastructure.cache.TairClient;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.FridayModelDeployRequest;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ModelRegistRequest;
import com.sankuai.algoplatform.matchops.infrastructure.model.FridayServiceInstance;
import com.sankuai.algoplatform.matchops.infrastructure.util.HttpUtil;
import com.sankuai.algoplatform.matchops.infrastructure.util.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class FridayDeployService {

    @Resource
    private FridayService fridayService;
    @Autowired
    private TairClient tairClient;


    private static final String TENANT_ID = "434610572891277";
    private static final String FRIDAY_DEPLOY_LIST_URL = "https://friday.sankuai.com/api/friday/ml/v1/deploy/list";
    private static final String FRIDAY_GET_QUEUE_GPU_INFO = "https://aigc.sankuai.com/api/friday/ml/v1/deploy/gpus";
    private static final String FRIDAY_GET_GPU_QUEUE = "https://aigc.sankuai.com/api/friday/ml/v1/deploy/getDeployResource";
    private static final String FRIDAY_DEPLOY_LLM_MODEL = "https://aigc.sankuai.com/api/friday/ml/v1/deploy/createService";
    private static final String FRIDAY_TASK_DETAIL_URL = "https://friday.sankuai.com/api/friday/ml/v1/task/detail";
    private static final String FRIDAY_METRICS_LINK_URL = "https://friday.sankuai.com/api/friday/ml/v1/task/metricslink";
    private static final String MLP_JOB_BASIC_URL = "https://mlp.sankuai.com/mlapi/kub/run/map/job/basic";
    private static final String MLP_JOB_RANK_URL = "https://mlp.sankuai.com/mlapi/kub/series/job/rank/default";
    private static final String MLP_SERIES_SUMMARY_URL = "https://mlp.sankuai.com/mlapi/kub/series/summary";
    private static final String FRIDAY_GET_MODEL_LIST = "https://aigc.sankuai.com/api/friday/ml/v1/deploy/models";
    private static final String FRIDAY_MODEL_STATUS_URL = "https://aigc.sankuai.com/api/friday/ml/v1/deploy/serviceInfo";
    private static final String FRIDAY_MODEAL_REGIST_URL = "https://friday.sankuai.com/api/friday/ml/v1/modelregistry/create";
    private static final String FRIDAY_GET_TRAINING_LIST = "https://aigc.sankuai.com/api/friday/ml/v1/modelregistry/runList";
    private static final String FRIDAY_COOKIE_CACHE_FREFIX = "bml_match_agent_friday_cookie_";
    private static final String FRIDAY_ADD_INSTANCE_URL = "https://aigc.sankuai.com/api/friday/ml/v1/deploy/addInstance";
    private static final String FRIDAY_REMOVE_INSTANCE_URL = "https://aigc.sankuai.com/api/friday/ml/v1/deploy/removeInstance";
    private static final String FRIDAY_SERVICE_INSTANCES_URL = "https://friday.sankuai.com/api/friday/ml/v1/deploy/serviceInstances";
    private static final String FRIDAY_MODEL_LIST_URL = "https://friday.sankuai.com/api/friday/ml/v1/modelregistry/list";
    private static final List<String> DEFAULT_QUEUES = Arrays.asList(
            "root.yg_serving_cluster.hadoop-daodian.bmlserving",
            "root.hh_serving_cluster.hadoop-daodian.serving"
    );



    public boolean deployModel(FridayModelDeployRequest request) {
        try {

            int requiredGpuCount = request.getRequiredInstanceCount() * request.getGpuPerInstance();

            // 2. 查询模型是否已经部署，同时获取 serviceId
            Pair<Boolean, String> deployStatus = isModelDeployed(request.getModelDeployName(), request.getMisId());
            boolean isDeployed = deployStatus.getLeft();
            String serviceId = deployStatus.getRight();
            log.info("模型 {} 部署状态: {}", request.getModelDeployName(), isDeployed ? "已部署" : "未部署");

            //3. 模型是否部署
            if (!isDeployed) {
                // 3.1 如果未部署
                // 检查GPU资源是否满足要求
                String gpuData = getGPUCountDataForAllQueuesInProjectGroup(request.getMisId(), request.getGroupName());
                Pair<Boolean, String> gpuResourceResult = checkGpuResource(gpuData, request.getGpuType(), requiredGpuCount);
                //拿到目标队列
                String targetQueue = gpuResourceResult.getRight();
                if (!gpuResourceResult.getLeft()) {
                    log.error("GPU资源不足");
                    return false;
                }
                //3.2 模型注册
                Pair<Boolean, String> modelRegistered = isModelRegistered(request.getModelDeployName(), request.getMisId());
                if (!modelRegistered.getLeft()){

                    boolean registerSuccess = registerService(request);
                    if (!registerSuccess) {
                        log.error("模型注册失败");
                        return false;
                    }
                    log.info("模型注册成功");
                }
                log.info("模型注册过");

                // 3.3 服务部署（Friday）
                boolean deploySuccess = deployService(request, targetQueue);
                if (!deploySuccess) {
                    log.error("服务部署失败");
                    return false;
                }
                log.info("服务部署成功");
                return true;

            }
            if (MdpContextUtils.isOfflineEnv()) {
                log.info("线下环境不校验实例数，直接返回成功 {}", request.getModelDeployName());
                return true;
            }

            // 4. 检查实例数是否满足要求

            List<FridayServiceInstance> instances = fridayService.queryInstanceStatus(request.getModelDeployName());
            int currentInstanceCount = instances != null ? instances.size() : 0;

            Integer requiredInstanceCount = request.getRequiredInstanceCount();

            log.info("当前实例数: {}, 所需实例数: {}", currentInstanceCount, request.getRequiredInstanceCount());


            if (currentInstanceCount < requiredInstanceCount) {
                // 6. 如果实例数不满足，检查GPU卡资源是否满足扩容要求
                String gpuData = getGPUCountDataForAllQueuesInProjectGroup(request.getMisId(), request.getGroupName());
                Pair<Boolean, String> gpuResourceResult = checkGpuResource(gpuData, request.getGpuType(), requiredGpuCount);
                //拿到目标队列
                String targetQueue = gpuResourceResult.getRight();
                // 如果GPU资源不足，返回部署失败
                if (!gpuResourceResult.getLeft()) {
                    return false;
                }
                // 7. 扩容服务实例（Friday）
                int needToAddInstanceCount = requiredInstanceCount - currentInstanceCount;
                //Pair<Boolean, String> addResult = fridayService.addInstance(modelDeployName, needToAddInstanceCount);

                Pair<Boolean, String> addResult = addInstance(serviceId, needToAddInstanceCount, targetQueue, request.getMisId());
                if (!addResult.getLeft()) {
                    log.error("服务实例扩容失败: {}", addResult.getRight());
                    return false;
                }
                Thread.sleep(1000 * 60 * 3);
                log.info("服务实例扩容成功，新增 {} 个实例", needToAddInstanceCount);
            } else if (currentInstanceCount > requiredInstanceCount) {
                // 8. 如果实例数超过所需实例数，进行缩容操作
                int needToReduceInstanceCount = currentInstanceCount - requiredInstanceCount;
                //Pair<Boolean, String> reduceResult = fridayService.deleteInstance(modelDeployName, needToReduceInstanceCount);
                Pair<Boolean, String> reduceResult = batchRemoveInstances(serviceId, needToReduceInstanceCount, request.getMisId());
                if (!reduceResult.getLeft()) {
                    log.error("服务实例缩容失败: {}", reduceResult.getRight());
                    return false;
                }
                Thread.sleep(1000 * 60 * 3);
                log.info("服务实例缩容成功，删除 {} 个实例", needToReduceInstanceCount);
            }

            // 8. 返回部署成功结果
            log.info("模型 {} 部署成功", request.getModelDeployName());
            return true;

        } catch (Exception e) {
            // 异常处理：发送部署失败通知
            log.error("模型部署过程中发生异常", e);
            Cat.logError(e);
            return false;
        }
    }

    /**
     * 扩容服务实例
     * @param serviceId 服务ID
     * @param instanceNum 新增实例数量
     * @param queue       目标队列
     * @param misId       用户ID
     * @return Pair<Boolean, String> left: 是否成功, right: 错误信息
     */
    public Pair<Boolean, String> addInstance(String serviceId, Integer instanceNum, String queue, String misId) {
        try {
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("tenantId", TENANT_ID);
            requestBody.put("serviceId", serviceId);
            requestBody.put("instanceNum", instanceNum);
            requestBody.put("queue", queue);

            String result = HttpUtil.httpPostJson(FRIDAY_ADD_INSTANCE_URL,
                    JacksonUtil.toJsonStr(requestBody),
                    getFridayHeader(misId));

            JSONObject jsonObject = JSONObject.parseObject(result);

            // 检查响应结果
            if ("0".equals(jsonObject.getString("code"))) {
                log.info("服务实例扩容成功, serviceId: {}, instanceNum: {}", serviceId, instanceNum);
                return Pair.of(true, "扩容成功");
            } else {
                String errorMsg = jsonObject.getString("message");
                log.error("服务实例扩容失败, serviceId: {}, instanceNum: {}, error: {}", serviceId, instanceNum, errorMsg);
                return Pair.of(false, errorMsg);
            }

        } catch (Exception e) {
            String errorMsg = "服务实例扩容异常: " + e.getMessage();
            log.error(errorMsg, e);
            Cat.logError(e);
            return Pair.of(false, errorMsg);
        }
    }

    /**
     * 缩容服务实例
     *
     * @param serviceId    服务ID
     * @param instanceName 实例名称
     * @param misId        用户ID
     * @return Pair<Boolean, String> left: 是否成功, right: 错误信息
     */
    public Pair<Boolean, String> removeInstance(String serviceId, String instanceName, String misId) {
        try {
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("tenantId", TENANT_ID);
            requestBody.put("serviceId", serviceId);
            requestBody.put("instanceName", instanceName);

            String result = HttpUtil.httpPostJson(FRIDAY_REMOVE_INSTANCE_URL,
                    JacksonUtil.toJsonStr(requestBody),
                    getFridayHeader(misId));

            JSONObject jsonObject = JSONObject.parseObject(result);

            // 检查响应结果
            if ("0".equals(jsonObject.getString("code"))) {
                log.info("服务实例缩容成功, serviceId: {}, instanceName: {}", serviceId, instanceName);
                return Pair.of(true, "缩容成功");
            } else {
                String errorMsg = jsonObject.getString("message");
                log.error("服务实例缩容失败, serviceId: {}, instanceName: {}, error: {}", serviceId, instanceName, errorMsg);
                return Pair.of(false, errorMsg);
            }

        } catch (Exception e) {
            String errorMsg = "服务实例缩容异常: " + e.getMessage();
            log.error(errorMsg, e);
            Cat.logError(e);
            return Pair.of(false, errorMsg);
        }
    }

    /**
     * 批量移除服务实例
     *
     * @param serviceId     服务ID
     * @param needRemoveNum 需要移除的实例数量
     * @param misId         用户ID
     * @return Pair<Boolean, String> left: 是否成功, right: 错误信息
     */
    public Pair<Boolean, String> batchRemoveInstances(String serviceId, int needRemoveNum, String misId) {
        try {
            // 构建查询参数
            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("tenantId", TENANT_ID);
            queryParams.put("serviceId", serviceId);
            queryParams.put("pageNum", "1");
            queryParams.put("pageSize", "100");

            // 获取实例列表
            String result = HttpUtil.httpGet(FRIDAY_SERVICE_INSTANCES_URL, queryParams, getFridayHeader(misId));
            JSONObject jsonObject = JSONObject.parseObject(result);

            if (!"0".equals(jsonObject.getString("code"))) {
                String errorMsg = "获取服务实例列表失败: " + jsonObject.getString("msg");
                log.error(errorMsg);
                return Pair.of(false, errorMsg);
            }

            // 获取运行中的实例
            JSONObject data = jsonObject.getJSONObject("data");
            JSONArray instances = data.getJSONArray("instances");

            List<String> runningInstances = new ArrayList<>();
            for (int i = 0; i < instances.size(); i++) {
                JSONObject instance = instances.getJSONObject(i);
                if ("运行中".equals(instance.getString("status"))) {
                    runningInstances.add(instance.getString("name"));
                }
            }

            if (runningInstances.size() < needRemoveNum) {
                String errorMsg = String.format("运行中的实例数量(%d)小于需要移除的数量(%d)", runningInstances.size(), needRemoveNum);
                log.error(errorMsg);
                return Pair.of(false, errorMsg);
            }

            // 执行缩容操作
            List<String> failedInstances = new ArrayList<>();
            for (int i = 0; i < needRemoveNum; i++) {
                String instanceName = runningInstances.get(i);
                Pair<Boolean, String> removeResult = removeInstance(serviceId, instanceName, misId);

                if (!removeResult.getLeft()) {
                    failedInstances.add(instanceName);
                    log.error("移除实例失败: {}, 原因: {}", instanceName, removeResult.getRight());
                } else {
                    log.info("成功移除实例: {}", instanceName);
                }
            }

            // 返回执行结果
            if (failedInstances.isEmpty()) {
                return Pair.of(true, String.format("成功移除 %d 个实例", needRemoveNum));
            } else {
                String errorMsg = String.format("部分实例移除失败: %s", String.join(", ", failedInstances));
                return Pair.of(false, errorMsg);
            }

        } catch (Exception e) {
            String errorMsg = "批量移除实例异常: " + e.getMessage();
            log.error(errorMsg, e);
            Cat.logError(e);
            return Pair.of(false, errorMsg);
        }
    }

    /**
     * 检查模型是否已注册
     *
     * @param modelName 模型名称
     * @param misId     用户ID
     * @return Pair<Boolean, String> left: 是否已注册, right: 模型ID(如果已注册)
     */
    private Pair<Boolean, String> isModelRegistered(String modelName, String misId) {
        try {
            // 构建查询参数
            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("tenantId", TENANT_ID);
            queryParams.put("filterStr", "");
            queryParams.put("pageNum", "1");
            queryParams.put("pageSize", "100");
            queryParams.put("onlyMe", "false");

            // 获取模型列表
            String result = HttpUtil.httpGet(FRIDAY_MODEL_LIST_URL, queryParams, getFridayHeader(misId));
            JSONObject jsonObject = JSONObject.parseObject(result);

            if (!"0".equals(jsonObject.getString("code"))) {
                String errorMsg = "获取模型列表失败: " + jsonObject.getString("msg");
                log.error(errorMsg);
                return Pair.of(false, errorMsg);
            }

            // 获取模型列表数据
            JSONObject data = jsonObject.getJSONObject("data");
            JSONArray models = data.getJSONArray("models");

            // 遍历查找指定模型
            for (int i = 0; i < models.size(); i++) {
                JSONObject model = models.getJSONObject(i);
                if (modelName.equals(model.getString("name"))) {
                    String modelId = model.getString("id");
                    log.info("模型已注册, name: {}, id: {}", modelName, modelId);
                    return Pair.of(true, modelId);
                }
            }

            log.info("模型未注册: {}", modelName);
            return Pair.of(false, null);

        } catch (Exception e) {
            String errorMsg = "检查模型注册状态异常: " + e.getMessage();
            log.error(errorMsg, e);
            Cat.logError(e);
            return Pair.of(false, errorMsg);
        }
    }

    /**
     * 查询模型是否已经部署
     *
     * @param modelName 模型名称
     * @param misId     用户ID
     * @return Pair<Boolean, String> left: 是否已部署, right: serviceId
     */
    private Pair<Boolean, String> isModelDeployed(String modelName, String misId) {
        try {
            Map<String, String> param = new HashMap<>();
            param.put("tenantId", TENANT_ID);
            param.put("filterStr", "");
            param.put("mine", "0");
            param.put("pageNum", "1");
            param.put("pageSize", "100");

            String result = HttpUtil.httpGet(FRIDAY_DEPLOY_LIST_URL, param, getFridayHeader(misId));
            JSONObject jsonObject = JSONObject.parseObject(result);

            // 检查请求是否成功
            if (!"0".equals(jsonObject.getString("code"))) {
                log.error("查询部署列表失败: {}", result);
                throw new RuntimeException("查询部署列表失败");
            }

            // 获取服务列表
            JSONObject data = jsonObject.getJSONObject("data");
            if (data == null || data.getJSONArray("services") == null) {
                throw new RuntimeException("查询部署列表失败");
            }

            // 遍历服务列表查找模型
            List<Map<String, Object>> services = JSONObject.parseArray(data.getJSONArray("services").toJSONString())
                    .stream()
                    .map(obj -> (Map<String, Object>) obj)
                    .collect(Collectors.toList());

            for (Map<String, Object> service : services) {
                if (modelName.equals(service.get("model_name"))) {
                    String serviceId = String.valueOf(service.get("service_id"));
                    log.info("模型已部署, serviceId: {}", serviceId);
                    return Pair.of(true, serviceId);
                }
            }

            return Pair.of(false, null);
        } catch (Exception e) {
            log.error("查询模型部署状态异常", e);
            throw new RuntimeException("查询模型部署状态异常");
        }
    }

    private Map<String, String> getFridayHeader(String misId) {
        // String ssoId = SsoUtil.getSsoId();
        Map<String, String> header = new HashMap<>();
        String ssoid = null;
        if (!com.dianping.cat.util.StringUtils.isBlank(misId)) {
            Cat.logEvent("getFridayHeader", "misId");
            ssoid = "12d702aa62_ssoid=" + tairClient.get(FRIDAY_COOKIE_CACHE_FREFIX + misId);
        }
        if (StringUtils.isBlank(ssoid)) {
            throw new RuntimeException("获取ssoid失败");
        }
        header.put("Cookie", ssoid);
        return header;
    }


    /**
     * 检查GPU资源
     * @param jsonString GPU资源JSON字符串
     * @param requiredGpuType 需要的GPU类型
     * @param requiredGpuCount 需要的GPU数量
     * @return Pair<Boolean, String> left: 是否找到资源, right: 可用队列名称
     */
    private Pair<Boolean, String> checkGpuResource(String jsonString, String requiredGpuType, Integer requiredGpuCount) throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(jsonString);



        // 如果目标队列没有资源，检查默认队列列表
        for (String queue : DEFAULT_QUEUES) {

            for (JsonNode entry : rootNode) {
                String currentQueue = entry.get("queue").asText();
                if (queue.equals(currentQueue)) {
                    JsonNode gpus = entry.get("gpus");
                    if (gpus.has(requiredGpuType)) {
                        int gpuNum = gpus.get(requiredGpuType).get("num").asInt();
                        if (gpuNum >= requiredGpuCount) {
                            log.info("在默认队列中找到GPU资源: queue={}, gpuType={}, availableNum={}",
                                    queue, requiredGpuType, gpuNum);
                            return Pair.of(true, queue);
                        }
                    }
                    break;
                }
            }
        }

        log.warn("所有队列中都未找到满足要求的GPU资源: gpuType={}, requiredNum={}",
                requiredGpuType, requiredGpuCount);
        return Pair.of(false, null);
    }

    private Map<String, String> findQueue(String jsonString) throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(jsonString);

        String targetQueue = "root.yg_serving_cluster.hadoop-daodian.bmlserving";
        // 只考虑L40-48G GPU卡
        String gpuType = "L40-48G";
        Map<String, String> result = new HashMap<>();

        // 先检查目标队列是否有可用L40-48G GPU
        for (JsonNode entry : rootNode) {
            String queue = entry.get("queue").asText();
            if (targetQueue.equals(queue)) {
                JsonNode gpus = entry.get("gpus");
                int gpuNum = gpus.get(gpuType).get("num").asInt();
                if (gpuNum > 0) {
                    result.put("queue", targetQueue);
                    result.put("gpuType", gpuType);
                    return result;
                }
                break;
            }
        }

        // 在所有队列中查找拥有最多L40-48G GPU的队列
        String queueWithMostGPU = null;
        int maxGPUNum = 0;

        for (JsonNode entry : rootNode) {
            JsonNode gpus = entry.get("gpus");
            int gpuNum = gpus.get(gpuType).get("num").asInt();

            if (gpuNum > maxGPUNum) {
                maxGPUNum = gpuNum;
                queueWithMostGPU = entry.get("queue").asText();
            }
        }

        if (maxGPUNum > 0) {
            result.put("queue", queueWithMostGPU);
            result.put("gpuType", gpuType);
            return result;
        }

        // 如果没有找到可用的L40-48G GPU，抛出异常
        throw new Exception("没有找到可用的L40-48G GPU卡，请稍后再试");
    }

    private String getGPUCountDataForAllQueuesInProjectGroup(String misId, String groupName) {
        Map<String, String> param = new HashMap<>();
        param.put("mlpProject", groupName);
        String result = HttpUtil.httpGet(FRIDAY_GET_GPU_QUEUE, param, getFridayHeader(misId));
        JSONObject jsonObject = JSONObject.parseObject(result);
        Object data = jsonObject.get("data");
        if (data == null) {
            return "获取al-zb-ddpt项目组下，GPU队列详情失败！";
        }
        Type type = new TypeToken<List<Map<String, Object>>>() {
        }.getType();
        List<Map<String, Object>> queueInfoList = JSON.parseObject(data.toString(), type);
        if (CollectionUtils.isEmpty(queueInfoList)) {
            return "未找到GPU队列信息";
        }
        for (Map<String, Object> map : queueInfoList) {
            String queueName = (String) map.get("queue");
            Map<String, String> params = new HashMap<>();
            params.put("queue", queueName);
            params.put("tenantId", TENANT_ID);
            String gpuInfo = HttpUtil.httpGet(FRIDAY_GET_QUEUE_GPU_INFO, params, getFridayHeader(misId));
            if (gpuInfo == null) {
                continue;
            }
            JSONObject gpuInfoJsonObj = JSONObject.parseObject(gpuInfo);
            Object dataMap = gpuInfoJsonObj.get("data");
            if (dataMap == null) {
                return "获取GPU信息失败";
            }
            JSONObject dataJSON = JSONObject.parseObject(dataMap.toString());
            Object gpus = dataJSON.get("gpus");
            map.put("gpus", gpus);
        }
        log.info("GPU队列信息：{}", JSONObject.toJSON(queueInfoList));
        return JSONObject.toJSONString(queueInfoList);
    }

    /**
     * 在Friday平台注册模型服务
     *
     * @return 注册成功返回true，注册失败返回false
     */
    private boolean registerService(FridayModelDeployRequest request) {
        try {

            String iter = findClosestIterFromFriday(TENANT_ID, request.getTrainingTaskId(), request.getTrainMethod(),request.getMisId());

            // 执行模型注册
            String registResult = performModelRegistration(request.getModelDeployName(), request.getDescription(), request.getModelBaseName(), iter,
                    request.getTrainingTaskId(), request.getMisId());
            JSONObject registJson = JSONObject.parseObject(registResult);

            // 检查注册结果
            if (!"0".equals(registJson.getString("code")) &&
                    !"模型名称重复:该模型名已有租户占用，请重新填写;".equals(registJson.getString("message"))) {
                Cat.logError(new RuntimeException(String.format("模型注册失败,registResult:%s,modelDeployName:%s,modelBaseName:%s,trainMethod:%s,trainingTaskId:%s,descriptione:%s",
                        registResult, request.getModelDeployName(), request.getModelBaseName(), request.getTrainMethod(), request.getTrainingTaskId(), request.getDescription())));
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("registerService error", e);
            return false;
        }
    }


    private boolean deployService(FridayModelDeployRequest request, String targetQueue) {
        try {

            int maxSeqLength = Integer.parseInt(request.getModelParam().get("max_seq_length")) / 1000 + 1;

            // 执行部署
            String deployResult = deployLLMServiceOnFriday(request, maxSeqLength, targetQueue);
            JSONObject deployJson = JSONObject.parseObject(deployResult);

            // 检查部署状态
            if ("0".equals(deployJson.getString("code"))) {
                // 轮询等待服务部署完成
                int retryCount = 0;
                while (retryCount < 12) {
                    String status = queryDeploymentStatusSfLargeModel(deployJson.getString("data"), request.getMisId());
                    if ("运行失败".equals(status)) {
                        return false;
                    } else if ("服务中".equals(status)) {
                        return true;
                    }
                    log.info("服务部署中，轮询次数: {}", retryCount + 1);
                    retryCount++;
                    Thread.sleep(10 * 60 * 1000);
                }
                if (retryCount == 12) {
                    Cat.logError(new RuntimeException(String.format("服务部署未完成，轮询超时,input:%s",
                            JSONObject.toJSONString(request.getModelParam()))));
                    return false;
                }
            } else if (!"服务已存在".equals(deployJson.getString("message"))) {
                Cat.logError(new RuntimeException("服务部署失败: " + deployJson.getString("code") +
                        deployJson.getString("msg") + JSONObject.toJSONString(request.getModelParam())));
                return false;
            }

            return true;
        } catch (Exception e) {
            log.error("deployService error", e);
            return false;
        }
    }

    /**
     * 获取训练任务的appId
     *
     * @param taskId 任务ID
     * @return appId
     */
    private String getTrainingTaskAppId(String taskId, String misId) {
        try {
            Map<String, String> param = new HashMap<>();
            param.put("tenantId", TENANT_ID);
            param.put("taskID", taskId);

            String result = HttpUtil.httpGet(FRIDAY_METRICS_LINK_URL, param, getFridayHeader(misId));
            JSONObject jsonObject = JSONObject.parseObject(result);

            // 检查请求是否成功
            if (!"0".equals(jsonObject.getString("code"))) {
                log.error("获取训练任务appId失败: {}", result);
                throw new RuntimeException("获取训练任务appId失败");
            }

            // 获取data
            JSONObject data = jsonObject.getJSONObject("data");
            if (data == null) {
                throw new RuntimeException("获取训练任务appId失败");
            }

            // 获取appId
            String appId = data.getString("appID");
            if (com.dianping.cat.util.StringUtils.isBlank(appId)) {
                throw new RuntimeException("获取appId失败");
            }

            return appId;

        } catch (Exception e) {
            log.error("获取训练任务appId异常", e);
            throw new RuntimeException("获取训练任务appId异常");
        }
    }

    /**
     * 获取训练任务的mlpRunId
     *
     * @param jobIds 任务ID
     * @return mlpRunId
     */
    private String getMlpRunId(String jobIds, String misId) {
        try {
            // 构建请求参数
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("jobIds", jobIds);

            String result = HttpUtil.httpPostJson(MLP_JOB_BASIC_URL, JacksonUtil.toJsonStr(requestBody), getFridayHeader(misId));
            JSONObject jsonObject = JSONObject.parseObject(result);

            // 检查请求是否成功
            if (!"0".equals(jsonObject.getString("code"))) {
                log.error("获取训练任务mlpRunId失败: {}", result);
                throw new RuntimeException("获取训练任务mlpRunId失败");
            }

            // 获取data并直接返回第一个key
            JSONObject dataObj = jsonObject.getJSONObject("data");
            if (dataObj == null || dataObj.isEmpty()) {
                throw new RuntimeException("获取训练任务mlpRunId失败");
            }

            return dataObj.keySet().iterator().next();

        } catch (Exception e) {
            log.error("获取训练任务mlpRunId异常", e);
            throw new RuntimeException("获取训练任务mlpRunId异常");
        }
    }

    /**
     * 获取训练任务的rankId
     *
     * @param jobIds 任务ID
     * @return rankId
     */
    private Integer getRankId(String jobIds, String misId) {
        try {
            // 构建请求参数
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("jobIds", jobIds);

            String result = HttpUtil.httpPostJson(MLP_JOB_RANK_URL, JacksonUtil.toJsonStr(requestBody), getFridayHeader(misId));
            JSONObject jsonObject = JSONObject.parseObject(result);

            // 检查请求是否成功
            if (!"0".equals(jsonObject.getString("code"))) {
                log.error("获取训练任务rankId失败: {}", result);
                throw new RuntimeException("获取训练任务rankId失败");
            }

            // 获取data
            JSONArray data = jsonObject.getJSONArray("data");
            if (data == null || data.isEmpty()) {
                throw new RuntimeException("获取训练任务rankId失败");
            }

            // 获取seriesRankNameInfo
            JSONArray seriesRankNameInfo = data.getJSONObject(0).getJSONArray("seriesRankNameInfo");
            if (seriesRankNameInfo == null || seriesRankNameInfo.isEmpty()) {
                throw new RuntimeException("获取rankId失败");
            }

            // 获取rankId
            Integer rankId = seriesRankNameInfo.getJSONObject(0).getInteger("rankId");
            if (rankId == null) {
                throw new RuntimeException("获取rankId失败");
            }

            return rankId;

        } catch (Exception e) {
            log.error("获取训练任务rankId异常", e);
            throw new RuntimeException("获取训练任务rankId异常");
        }
    }

    /**
     * 获取训练任务的step
     *
     * @param instanceId 实例ID
     * @param jobId      任务ID
     * @param rankId     排名ID
     * @return step
     */
    private String getIter(String instanceId, String jobId, Integer rankId, String trainMethod, String misId) throws Exception {
        try {
            // 构建请求参数
            Map<String, Object> rankIds = new HashMap<>();
            rankIds.put(jobId, Collections.singletonList(rankId));

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("sceneId", 1);
            requestBody.put("instanceId", instanceId);
            requestBody.put("rankIds", rankIds);

            String result = HttpUtil.httpPostJson(MLP_SERIES_SUMMARY_URL, JacksonUtil.toJsonStr(requestBody), getFridayHeader(misId));
            JSONObject jsonObject = JSONObject.parseObject(result);

            // 检查请求是否成功
            if (!"0".equals(jsonObject.getString("code"))) {
                log.error("获取训练任务step失败: {}", result);
                throw new RuntimeException("获取训练任务step失败");
            }

            // 获取data
            JSONArray data = jsonObject.getJSONArray("data");
            if (data == null || data.isEmpty()) {
                throw new RuntimeException("获取训练任务step失败");
            }

            // 获取summary数组
            JSONArray summary = data.getJSONObject(0).getJSONArray("summary");
            if (summary == null || summary.isEmpty()) {
                throw new RuntimeException("获取summary失败");
            }

            // 根据trainMethod查找对应tag的数据，获取step
            for (int i = 0; i < summary.size(); i++) {
                JSONObject item = summary.getJSONObject(i);
                String tag = item.getString("tag");

                if ("sft".equals(trainMethod) && "lm loss validation".equals(tag)) {
                    // sft方法：获取min的step
                    String stepStr = item.getJSONObject("min").getString("step");
                    Integer step = parseStep(stepStr);
                    return String.format("iter_%07d", step);
                } else if ("dpo".equals(trainMethod) && "eval_accuracy".equals(tag)) {
                    // dpo方法：获取max的step
                    String stepStr = item.getJSONObject("max").getString("step");
                    Integer step = parseStep(stepStr);
                    return String.format("iter_%07d", step);
                }
            }
            return null;

        } catch (Exception e) {
            log.error("获取训练任务step异常", e);
            throw e;
        }
    }

    /**
     * 将step字符串转换为数字
     *
     * @param stepStr step字符串，可能是"1500"或"1.5K"或"1.5M"这样的格式
     * @return 转换后的数字
     */
    private Integer parseStep(String stepStr) {
        try {
            if (stepStr == null || stepStr.trim().isEmpty()) {
                throw new RuntimeException("step为空");
            }

            stepStr = stepStr.trim();
            // 检查是否是纯数字
            if (stepStr.matches("^\\d+$")) {
                return Integer.parseInt(stepStr);
            }

            // 检查是否符合带单位的格式 (数字+K或M)
            if (!stepStr.matches("^\\d*\\.?\\d+[KM]$")) {
                throw new RuntimeException("step格式错误，只支持纯数字、K或M单位格式");
            }

            double value = Double.parseDouble(stepStr.substring(0, stepStr.length() - 1));
            char unit = stepStr.charAt(stepStr.length() - 1);

            switch (unit) {
                case 'K':
                    return (int) (value * 1000);
                case 'M':
                    return (int) (value * 1000000);
                default:
                    throw new RuntimeException("不支持的单位格式");
            }
        } catch (Exception e) {
            log.error("解析step异常: {}", stepStr, e);
            throw new RuntimeException("解析step异常: " + e.getMessage());
        }
    }

    /**
     * 从Friday API获取训练任务详情并查找最接近的iter
     *
     * @param tenantId 租户ID
     * @param taskId   任务ID
     * @return 最接近的iter字符串，如果没找到则返回null
     */
    public String findClosestIterFromFriday(String tenantId, String taskId, String trainMethod, String misId) {
        try {
            String appId = getTrainingTaskAppId(taskId, misId);
            String runId = getMlpRunId(appId, misId);
            Integer rankId = getRankId(appId, misId);
            String targetIter = getIter(runId, appId, rankId, trainMethod, misId);
            // 构建请求参数
            Map<String, String> params = new HashMap<>();
            params.put("tenantId", tenantId);
            params.put("taskID", taskId);

            // 发送请求获取任务详情
            String result = HttpUtil.httpGet(FRIDAY_TASK_DETAIL_URL, params, getFridayHeader(misId));
            JSONObject jsonObject = JSONObject.parseObject(result);

            // 检查请求是否成功
            if (!"0".equals(jsonObject.getString("code"))) {
                log.error("获取训练任务详情失败: {}", result);
                throw new RuntimeException("获取训练任务详情失败");
            }

            // 获取artifacts数组
            JSONArray artifacts = jsonObject.getJSONObject("data").getJSONArray("artifacts");
            if (artifacts == null || artifacts.isEmpty()) {
                throw new RuntimeException("获取artifacts失败或artifacts为空");
            }
            if (com.dianping.cat.util.StringUtils.isBlank(targetIter)) {
                JSONObject artifact = artifacts.getJSONObject(0);
                String step = artifact.getString("step");
                Cat.logEvent("getIter", String.format("未找到符合条件的iter,instanceId:%s,jobId:%s,rankid:%s,trainMethod:%s", runId, appId, rankId, trainMethod));
                return step;
            }

            // 从targetIter中提取数字
            int targetStep = Integer.parseInt(targetIter.substring(5));

            // 如果完全匹配，直接返回
            for (int i = 0; i < artifacts.size(); i++) {
                JSONObject artifact = artifacts.getJSONObject(i);
                String step = artifact.getString("step");
                if (targetIter.equals(step)) {
                    return step;
                }
            }

            // 没有完全匹配，查找最接近的
            String closestIter = null;
            int minDiff = Integer.MAX_VALUE;

            for (int i = 0; i < artifacts.size(); i++) {
                JSONObject artifact = artifacts.getJSONObject(i);
                String step = artifact.getString("step");
                int currentStep = Integer.parseInt(step.substring(5));

                int diff = Math.abs(currentStep - targetStep);
                if (diff < minDiff) {
                    minDiff = diff;
                    closestIter = step;
                }
            }
            log.info("没有完全匹配的iter，获取closestIter: {}", closestIter);
            return closestIter;

        } catch (Exception e) {
            log.error("查找最接近的iter异常,tenantId:{},taskId:{},trainMethod:{} ", tenantId, taskId, trainMethod, e);
            return null;
        }
    }

    public String deployLLMServiceOnFriday(FridayModelDeployRequest request,Integer maxSessionLen,String targetQueue) throws Exception {
        //文本生成
        Map<String, Object> modelInfo = getModelInfo(request.getModelDeployName(),request.getMisId());
        if (MapUtils.isEmpty(modelInfo)) {
            Cat.logError(new Exception("模型信息为空"));
            throw new Exception("模型信息为空");
        }
        Map<String, Object> rateLimitConfig = new HashMap<>();
        rateLimitConfig.put("tpm", 78643200);
        rateLimitConfig.put("rpm", 19200);
        Map<String, Boolean> inferConfig = new HashMap<>();
        inferConfig.put("enableAdvancedConfig", true);
        inferConfig.put("useDefaultAdvancedConfig", true);
        Map<String, Object> chatTemplateConfig = new HashMap<>();
        chatTemplateConfig.put("useDefaultChatTemplate", true);
        chatTemplateConfig.put("chatTemplate", "");
        List<Map<String, Object>> queueInstanceCount = new ArrayList<>();



        Map<String, Object> queueMap = new HashMap<>();
        queueMap.put("queue", targetQueue);
        queueMap.put("instanceNum", request.getRequiredInstanceCount());
        queueInstanceCount.add(queueMap);

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("tenantId", TENANT_ID);
        requestBody.put("serviceName", request.getModelDeployName());
        requestBody.put("serviceType", 1);
        requestBody.put("modelId", modelInfo.get("model_id"));
        requestBody.put("modelVersionId", modelInfo.get("modelVersionId"));
        requestBody.put("modelName", request.getModelDeployName());
        requestBody.put("modelVersion", modelInfo.get("version"));
        requestBody.put("describe", request.getDescription());
        requestBody.put("defaultQueue", false);
        requestBody.put("mlpProject", request.getGroupName());
        //单实例GPU数
        requestBody.put("gpuPerInstance", request.getGpuPerInstance());
        //模型类型
        requestBody.put("gpuType", request.getGpuType());
        requestBody.put("queueInstanceCount", queueInstanceCount);
        requestBody.put("deployMode", "turboMind");
        requestBody.put("enableKvCache", true);
        requestBody.put("rateLimitConfig", rateLimitConfig);
        requestBody.put("inferConfig", inferConfig);
        requestBody.put("chatTemplateConfig", chatTemplateConfig);
        requestBody.put("maxSessionLen", maxSessionLen);
        String result = HttpUtil.httpPostJson(FRIDAY_DEPLOY_LLM_MODEL, JacksonUtil.toJsonStr(requestBody), getFridayHeader(request.getMisId()));
        return result;
    }

    public Map<String, Object> getModelInfo(String modelName, String misId) throws Exception {
        if (modelName == null) {
            throw new Exception("模型名称或模型版本为空");
        }
        String allModelsListUnderCurrentTenant = getAllModelsListUnderCurrentTenant(misId);
        JSONObject jsonObject = JSONObject.parseObject(allModelsListUnderCurrentTenant);
        if (allModelsListUnderCurrentTenant == null || !jsonObject.get("code").toString().equals("0") || jsonObject.get("data") == null) {
            Cat.logError(new Exception("获取模型列表失败"));
            throw new Exception("获取模型列表失败");
        }
        Object data = jsonObject.get("data");
        Type type = new TypeToken<List<Map<String, Object>>>() {
        }.getType();
        Map<String, Object> dataList = JSONObject.parseObject(data.toString(), Map.class);
        Object models = dataList.get("models");

        List<Map<String, Object>> modelsList = JSONObject.parseObject(models.toString(), type);
        if (modelsList == null || modelsList.size() == 0) {
            throw new Exception("模型列表为空");
        }
        Type ListMapType = new TypeToken<List<Map<String, Object>>>() {
        }.getType();
        for (Map<String, Object> model : modelsList) {
            if (modelName.equals(model.get("model_name"))) {
                List<Map<String, Object>> versions = JSONObject.parseObject(model.get("versions").toString(), ListMapType);
                if (versions == null || versions.size() == 0) {
                    throw new Exception("模型版本列表为空");
                }
                Map<String, Object> modelInfo = versions.stream()
                        .max(Comparator.comparingInt(map -> ((Number) map.get("version")).intValue()))
                        .map(maxVersionMap -> {
                            Map<String, Object> result = new HashMap<>();
                            result.putAll(model);
                            result.putAll(maxVersionMap);
                            result.remove("versions");
                            return result;
                        })
                        .orElse(new HashMap<>());
                return modelInfo;
            }
        }
        return new HashMap<>();
    }

    private String getAllModelsListUnderCurrentTenant(String misId) {
        String modelTask = "text";
        Map<String, String> param = new HashMap<>();
        param.put("tenantId", TENANT_ID);
        param.put("modelTask", modelTask);
        String result = HttpUtil.httpGet(FRIDAY_GET_MODEL_LIST, param, getFridayHeader(misId));
        return result;
    }


    public String queryDeploymentStatusSfLargeModel(String appId, String misId) {
        Map<String, String> param = new HashMap<>();
        param.put("tenantId", TENANT_ID);
        param.put("appId", appId);
        String result = HttpUtil.httpGet(FRIDAY_MODEL_STATUS_URL, param, getFridayHeader(misId));
        JSONObject jsonObject = JSONObject.parseObject(result);
        Object dataJson = jsonObject.get("data");
        Map map = JSONObject.parseObject(dataJson.toString(), Map.class);
        if (map == null || !map.containsKey("status")) {
            return "模型状态查询失败";
        }
        String status = map.get("status").toString();
        return status;
    }

    public String performModelRegistration(String modelName, String description, String modelClass, String iter, String trainingTaskId, String misId) throws Exception {
        try {

            Long artifatcsVersionId = getArtifatcsVersionId(iter, trainingTaskId, misId);
            if (artifatcsVersionId == null) {
                throw new Exception("artifatcsVersionId没有获取成功，请检查iter和trainingTaskId是否正确" + iter + "_:_" + trainingTaskId);
            }
            List<Object> artifatcsVersionList = new ArrayList<>();
            artifatcsVersionList.add(trainingTaskId);
            artifatcsVersionList.add(artifatcsVersionId);
            Integer latestVersion = 1;
            ModelRegistRequest modelRegistRequest = ModelRegistRequest.builder()
                    .name(modelName)
                    .description(description)
                    .modelClassType("text")
                    .modelClass(modelClass)
                    .latestVersion(latestVersion)
                    .artifatcsVersionId(artifatcsVersionId)
                    .artifatcsVersionList(artifatcsVersionList)
                    .tenantId(TENANT_ID)
                    .modelPrefabricatedName("")
                    .modelPath("")
                    .modelName("")
                    .build();
            String result = HttpUtil.httpPostJson(FRIDAY_MODEAL_REGIST_URL, JacksonUtil.toJsonStr(modelRegistRequest), getFridayHeader(misId));
            return result;
        } catch (Exception e) {
            log.error("performModelRegistration error", e);
            Cat.logError(e);
            throw e;
        }
    }

    private Long getArtifatcsVersionId(String iter, String trainingTaskId, String misId) throws Exception {
        String runListByTenantId = getRunListByTenantId(misId);
        JSONObject jsonObject = JSONObject.parseObject(runListByTenantId);
        if (!"0".equals(jsonObject.get("code").toString()) || jsonObject.get("data") == null) {
            Cat.logError(new Exception("获取训练列表失败"));
            throw new Exception("获取训练列表失败,请检查iter和trainingTaskId是否正确。" + iter + "_:_" + trainingTaskId);
        }
        Object data = jsonObject.get("data");
        Type type = new TypeToken<List<Map<String, Object>>>() {
        }.getType();
        List<Map<String, Object>> dataList = JSONObject.parseObject(data.toString(), type);
        if (CollectionUtils.isEmpty(dataList)) {
            Cat.logError(new Exception("获取训练列表为空"));
            throw new Exception("获取训练列表为空,请检查iter和trainingTaskId是否正确。" + iter + "_:_" + trainingTaskId);
        }
        for (Map<String, Object> map : dataList) {
            if (trainingTaskId.equals(map.get("id"))) {
                Object artifactsVersionListObj = map.get("artifactsVersionList");
                if (artifactsVersionListObj == null) {
                    Cat.logError(new Exception("获取训练列表为空"));
                    throw new Exception("获取训练列表为空,请检查iter和trainingTaskId是否正确。" + iter + "_" + trainingTaskId);
                }
                List<Map<String, Object>> artifactsVersionList = JSONObject.parseObject(artifactsVersionListObj.toString(), type);
                for (Map<String, Object> element : artifactsVersionList) {
                    if (iter.equals(element.get("step"))) {
                        return Long.valueOf(element.get("artifactsVersionId").toString());
                    }
                }
            }
        }
        return null;
    }

    private String getRunListByTenantId(String misId) {
        Map<String, String> param = new HashMap<>();
        param.put("tenantId", TENANT_ID);
        String result = HttpUtil.httpGet(FRIDAY_GET_TRAINING_LIST, param, getFridayHeader(misId));
        return result;
    }
}
