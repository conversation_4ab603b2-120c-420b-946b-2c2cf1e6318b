package com.sankuai.algoplatform.matchops.infrastructure.proxy;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.sankuai.ai.friday.model.deploy.thrift.*;
import com.sankuai.algoplatform.matchops.infrastructure.model.FridayServiceInstance;
import com.sankuai.algoplatform.matchops.infrastructure.util.RetryUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class FridayService {
    @Resource
    private FridayMLModelDeployService.Iface fridayMLModelDeployService;

    /**
     * 查询服务信息
     *
     * @param serviceName 服务名称
     * @return 服务具体信息
     */
    public List<FridayServiceInstance> queryInstanceStatus(String serviceName) {
        try {
            QueryInstancesResponse resp = RetryUtil.retry(
                    () -> {
                        QueryInstancesResponse r = fridayMLModelDeployService.queryInstancesDetail(serviceName);
                        log.info("queryInstanceStatus: modelName:{}, resp:{}", serviceName, JSON.toJSONString(r));
                        return r;
                    },
                    (r, e) -> {
                        if (e != null || r.getCode() != 0) {
                            log.error("queryInstanceStatus error:{},{}, ex:", serviceName, JSON.toJSONString(r), e);
                            return true;
                        }
                        return false;
                    },
                    2, TimeUnit.SECONDS.toMillis(1));
            return FridayServiceInstance.trans2(resp);
        } catch (Exception e) {
            log.error("FridayService.queryInstanceStatus error, serviceName:{}", serviceName, e);
            return Collections.emptyList();
        }
    }


    /**
     * 扩容实例
     *
     * @param serviceName 服务名称
     * @param instanceNum 扩容实例数
     * @return 是否成功
     */
    public Pair<Boolean, String> addInstance(String serviceName, int instanceNum) {
        Preconditions.checkState(instanceNum > 0);
        try {
            AddInstanceRequest req = new AddInstanceRequest();
            req.setInstanceNum(instanceNum);
            req.setServiceName(serviceName);
            GenericResponse resp = fridayMLModelDeployService.addInstance(req);
            log.info("addInstance: req:{}, {}, resp:{}", serviceName, instanceNum, JSON.toJSONString(resp));
            return Pair.of(resp.getCode() == 0, resp.getMsg());
        } catch (Exception e) {
            log.error("addInstance: {}, {}, ex", serviceName, instanceNum, e);
            return Pair.of(false, e.getMessage());
        }
    }

    /**
     * 缩容实例
     *
     * @param serviceName 服务名称
     * @param instanceNum 缩容实例数
     * @return 是否成功
     */
    public Pair<Boolean, String> deleteInstance(String serviceName, int instanceNum) {
        Preconditions.checkState(instanceNum > 0);
        try {
            RemoveInstanceRequest req = new RemoveInstanceRequest();
            req.setInstanceNum(instanceNum);
            req.setServiceName(serviceName);

            GenericResponse resp = fridayMLModelDeployService.removeInstance(req);
            log.info("deleteInstance: req:{}, {}, resp:{}", serviceName, instanceNum, JSON.toJSONString(resp));
            return Pair.of(resp.getCode() == 0, resp.getMsg());
        } catch (Exception e) {
            log.error("deleteInstance: {}, {}, ex", serviceName, instanceNum, e);
            return Pair.of(false, e.getMessage());
        }
    }
}
