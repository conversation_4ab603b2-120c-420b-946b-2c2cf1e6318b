package com.sankuai.algoplatform.matchops.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: task
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Task {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: requirement_id
     *   说明: 需求ID
     */
    private Long requirementId;

    /**
     *   字段: resource_group_id
     *   说明: 资源组ID
     */
    private Long resourceGroupId;

    /**
     *   字段: reporter_template_id
     *   说明: 报告模板ID
     */
    private String reporterTemplateId;

    /**
     *   字段: name
     *   说明: 任务名称
     */
    private String name;

    /**
     *   字段: status
     *   说明: 任务状态
     */
    private Integer status;

    /**
     *   字段: run_param
     *   说明: 运行参数
     */
    private String runParam;

    /**
     *   字段: owner
     *   说明: 负责人
     */
    private String owner;

    /**
     *   字段: create_time
     *   说明: 创建时间
     */
    private Date createTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}