package com.sankuai.algoplatform.matchops.infrastructure.util;

import com.dianping.zebra.group.router.ZebraForceMasterHelper;

public class ZebraForceMasterUtil {

    public static <T> T queryInMaster(QueryFunction<T> queryFunction) {
        try {
            ZebraForceMasterHelper.forceMasterInLocalContext();
            return queryFunction.query();
        } catch (Exception e) {
            if (e instanceof RuntimeException) {
                throw (RuntimeException) e;
            } else {
                throw new RuntimeException(e);
            }
        } finally {
            ZebraForceMasterHelper.clearLocalContext();
        }
    }

    @FunctionalInterface
    public interface QueryFunction<T> {
        T query() throws Exception;
    }
}
