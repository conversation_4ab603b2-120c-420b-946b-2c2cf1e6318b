package com.sankuai.algoplatform.matchops.infrastructure.proxy;


import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.aifree.thrift.generation.MLPModelServingService;
import com.sankuai.aifree.thrift.generation.query.*;
import com.sankuai.aifree.thrift.generation.vo.*;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ModelDeployRequest;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ServingDetail;
import com.sankuai.algoplatform.matchops.infrastructure.enums.GPUResourceType;
import com.sankuai.algoplatform.matchops.infrastructure.model.GPUQueueResourceDetail;

import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;

import com.sun.crypto.provider.SunJCE;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.thrift.TException;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.Security;

import org.apache.commons.codec.binary.Base64;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MLPDeployService {

    @Resource
    private MlpTrainingService mlpTrainingService;

    @Resource
    private MLPModelServingService.Iface mlpModelServingService;

    /**
     * 部署模型并管理实例
     */
    public Pair<Boolean, String> deployModel(ModelDeployRequest modelDeployRequest) {
        try {
            //set不传入，默认主干道
            if (modelDeployRequest.getSet() == null) {
                modelDeployRequest.setSet("");
            }

            // 1. 查询模型是否已经部署
            ServingDetailDataVo servingDetail = mlpTrainingService.getServingDetail(modelDeployRequest.getAppkey(), modelDeployRequest.getOptMis());
            ServingDetail deployInfo = getServingDetail(servingDetail, modelDeployRequest.getGroup(), modelDeployRequest.getModelName());
            boolean groupExist = deployInfo.getGroupExist();
            boolean modelDeployed = deployInfo.getModelDeployed();
            int existInstanceNum = deployInfo.getInstanceCount();

            // 使用 buildModelsFromModelInfo 构建 models
            List<Map<String, Object>> models = buildModelsFromModelInfo(
                    modelDeployRequest.getWxProject(),
                    modelDeployRequest.getModelName(),
                    modelDeployRequest.getVersionNums()
            );

            // 分组存在
            if (groupExist) {
                //实例数不够，需要扩容
                if (existInstanceNum < modelDeployRequest.getRequireInstanceNum()) {

                    int requireGpuNum = modelDeployRequest.getRequireInstanceNum() - existInstanceNum;
                    // 转换gpuType格式
                    GPUResourceType gpuResourceType = GPUResourceType.getByName(modelDeployRequest.getGcoresType());
                    if (gpuResourceType == null) {
                        log.error("无效的GPU资源类型: {}", modelDeployRequest.getGcoresType());
                        return Pair.of(false, "无效的GPU资源类型: " + modelDeployRequest.getGcoresType());
                    }
                    // 查询卡资源是否满足要求
                    Map<String, Integer> remainOfQueue = getRemainOfQueue(modelDeployRequest.getQueueName(), gpuResourceType);
                    Collection<Integer> values = remainOfQueue.values();
                    int remainGpu = values.stream().mapToInt(Integer::intValue).sum();
                    if (remainGpu < requireGpuNum) {
                        log.error("部署前检查：队列资源不足: queueName={}, 可用GPU数量={}, 需要GPU数量={}",
                                modelDeployRequest.getQueueName(), remainGpu, requireGpuNum);
                        Cat.logError(new RuntimeException("队列资源不足,modelDeployRequest:" + JSONObject.toJSONString(modelDeployRequest)));
                        return Pair.of(false, "队列资源不足，无法部署");
                    }
                    Map<String, Integer> gpuCountMap = allocateCards(requireGpuNum, remainOfQueue);

                    for (Map.Entry<String, Integer> queueInfo : gpuCountMap.entrySet()) {
                        Integer num = queueInfo.getValue();
                        String queueName = queueInfo.getKey();
                        // 实例扩容
                        Pair<Boolean, String> addResult = mlpTrainingService.addInstance(modelDeployRequest, num, gpuResourceType, queueName);
                        if (!addResult.getLeft()) {
                            log.error("实例扩容失败: appkey={}, group={}, needToAddCount={}, reason={}", modelDeployRequest.getAppkey(), modelDeployRequest.getGroup(), requireGpuNum, addResult.getRight());
                            return Pair.of(false, "实例扩容失败: " + addResult.getRight());
                        }
                        log.info("实例扩容成功: appkey={}, group={}, addedCount={}", modelDeployRequest.getAppkey(), modelDeployRequest.getGroup(), num);
                    }


                    // 实例缩容
                }
//                else if (existInstanceNum > modelDeployRequest.getRequireInstanceNum()) {
//
//                    int deleteInstanceNum = existInstanceNum - modelDeployRequest.getRequireInstanceNum();
//
//                    Pair<Boolean, String> deleteResult = mlpTrainingService.deleteInstance(modelDeployRequest.getAppkey(), modelDeployRequest.getGroup(), deleteInstanceNum, modelDeployRequest.getOptMis());
//                    if (!deleteResult.getLeft()) {
//                        log.error("实例缩容失败: appkey={}, group={}, needToDeleteCount={}, reason={}", modelDeployRequest.getAppkey(), modelDeployRequest.getGroup(), deleteInstanceNum, deleteResult.getRight());
//                        return Pair.of(false, "实例缩容失败: " + deleteResult.getRight());
//                    }
//                    log.info("实例缩容成功: appkey={}, group={}, deletedCount={}", modelDeployRequest.getAppkey(), modelDeployRequest.getGroup(), deleteInstanceNum);
//                }
                //模型未注册，加模型
                if (!modelDeployed) {
                    // 添加模型到指定的分组中
                    Map<String, Object> resp = loadModel(deployInfo.getServingId(), deployInfo.getGroupId(), modelDeployRequest.getOptMis(), modelDeployRequest.getWxProject(), models);

                    log.info("模型添加结果: {}", resp);
                    if ((int) resp.get("code") != 0) {
                        return Pair.of(false, "模型添加失败");
                    }
                    return Pair.of(true, "模型部署成功");
                }

                return Pair.of(true, "模型已部署");
            }
            // 转换gpuType格式
            GPUResourceType gpuResourceType = GPUResourceType.getByName(modelDeployRequest.getGcoresType());
            if (gpuResourceType == null) {
                log.error("无效的GPU资源类型: {}", modelDeployRequest.getGcoresType());
                return Pair.of(false, "无效的GPU资源类型: " + modelDeployRequest.getGcoresType());
            }
            // 查询卡资源是否满足要求
            Map<String, Integer> remainOfQueue = getRemainOfQueue(modelDeployRequest.getQueueName(), gpuResourceType);
            int remainGpu = remainOfQueue.values().stream().mapToInt(Integer::intValue).sum();
            if (remainGpu < modelDeployRequest.getRequireInstanceNum()) {
                log.error("部署前检查：队列资源不足: queueName={}, 可用GPU数量={}, 需要GPU数量={}",
                        modelDeployRequest.getQueueName(), remainGpu, modelDeployRequest.getRequireInstanceNum());
                return Pair.of(false, "队列资源不足，无法部署");
            }
            //创建分组并部署模型和实例
            Boolean creatResult = creatGroupAndModelDeploy(modelDeployRequest, models, gpuResourceType, remainOfQueue);
            if (!creatResult) {
                return Pair.of(false, "分组及模型部署失败");
            }

            return Pair.of(true, "模型部署成功");
        } catch (Exception e) {
            log.error("模型部署过程发生异常: appkey={}",
                    modelDeployRequest.getAppkey(), e);
            return Pair.of(false, "部署失败: " + e.getMessage());
        }
    }

    /**
     * 均衡分配各队列中的卡片用于扩容
     *
     * @param remainOfQueue 需要扩容的实例数量
     * @param queueCards    各队列中可用于扩容的卡片数量
     * @return 分配结果，key为队列名，value为从该队列分配的卡片数量
     * @throws IllegalArgumentException 当卡片总数不足以满足扩容需求时抛出
     */
    public static Map<String, Integer> allocateCards(int remainOfQueue, Map<String, Integer> queueCards) {
        // 检查参数有效性
        if (remainOfQueue <= 0) {
            throw new IllegalArgumentException("扩容实例数量必须为正数");
        }

        if (queueCards == null || queueCards.isEmpty()) {
            throw new IllegalArgumentException("队列卡片信息不能为空");
        }

        // 计算总可用卡片数
        int totalAvailableCards = queueCards.values().stream().mapToInt(Integer::intValue).sum();

        // 检查卡片总数是否足够
        if (totalAvailableCards < remainOfQueue) {
            throw new IllegalArgumentException("可用卡片总数(" + totalAvailableCards +
                    ")不足以满足扩容需求(" + remainOfQueue + ")");
        }

        // 创建结果Map
        Map<String, Integer> result = new HashMap<>();

        // 使用优先队列进行均衡分配
        // 按照剩余卡片数量从大到小排序
        PriorityQueue<QueueInfo> pq = new PriorityQueue<>((a, b) ->
                Integer.compare(b.remainingCards, a.remainingCards));

        // 初始化优先队列
        for (Map.Entry<String, Integer> entry : queueCards.entrySet()) {
            if (entry.getValue() > 0) {
                pq.offer(new QueueInfo(entry.getKey(), entry.getValue()));
            }
        }

        // 分配卡片
        int remaining = remainOfQueue;
        while (remaining > 0) {
            // 从剩余卡片最多的队列中取卡
            QueueInfo queue = pq.poll();
            if (queue == null) break;

            // 计算当前队列应分配的卡片数
            int toAllocate = Math.min(queue.remainingCards,
                    (int) Math.ceil((double) remaining / (pq.size() + 1)));
            toAllocate = Math.min(toAllocate, remaining);

            // 更新分配结果
            if (toAllocate > 0) {
                result.put(queue.queueName, toAllocate);
            }
            remaining -= toAllocate;

            // 如果队列还有剩余卡片，重新加入优先队列
            if (queue.remainingCards - toAllocate > 0) {
                pq.offer(new QueueInfo(queue.queueName, queue.remainingCards - toAllocate));
            }
        }

        return result;
    }

    // 队列信息辅助类
    private static class QueueInfo {
        String queueName;
        int remainingCards;

        public QueueInfo(String queueName, int remainingCards) {
            this.queueName = queueName;
            this.remainingCards = remainingCards;
        }
    }

    private Boolean creatGroupAndModelDeploy(ModelDeployRequest modelDeployRequest, List<Map<String, Object>> models, GPUResourceType gpuResourceType, Map<String, Integer> remainOfQueue) {
        try {
            // 模型部署
            ModelQuery modelQuery = new ModelQuery();
            modelQuery.setModelName(modelDeployRequest.getModelName());
//            // 将 versionNums 字符串转换为 List<Long>
//                    List<Long> versionNumsList = new ArrayList<>();
//                    if (StringUtils.isNotBlank(versionNums)) {
//                        String[] versions = versionNums.split(",");
//                        for (String version : versions) {
//                            try {
//                                versionNumsList.add(Long.parseLong(version.trim()));
//                            } catch (NumberFormatException e) {
//                                log.warn("无法解析版本号: {}", version, e);
//                            }
//                        }
//                    }
            List<Long> versionNumsList = models.stream()
                    .flatMap(model -> ((List<Integer>) model.get("modelVersions")).stream())
                    .map(Long::valueOf)
                    .collect(Collectors.toList());

            modelQuery.setVersionNums(versionNumsList);
            List<ModelQuery> modelQueries = new ArrayList<>();
            modelQueries.add(modelQuery);


            String token = StringUtils.truncate(UUID.randomUUID().toString(), 32);

            CreateServingQuery query = new CreateServingQuery();
            query.setToken(token);
            query.setAppkey(modelDeployRequest.getAppkey());
            query.setWxProject(modelDeployRequest.getWxProject());
            query.setUser(modelDeployRequest.getOptMis());
            query.setName(modelDeployRequest.getGroup());
            query.setServingType(modelDeployRequest.getServingType());

            query.setEnv(modelDeployRequest.getEnv());
            query.setServerImage(modelDeployRequest.getServerImage());
            query.setAutoUpdateBatch(1);
            query.setThriftTimeout(modelDeployRequest.getRpcTimeout());
            query.setBatching(modelDeployRequest.getBatching());
            query.setBatchingConfig(modelDeployRequest.getBatchingConfigQuery());
            query.setModelQueries(modelQueries);
            query.setExtra("");
            ArrayList<InstanceQuery> instanceQueries = new ArrayList<>();

            //部署实例
            for (Map.Entry<String, Integer> queueInfo : remainOfQueue.entrySet()) {
                String queueName = queueInfo.getKey();
                Integer requireInstanceNum = queueInfo.getValue();
                InstanceQuery instanceQuery = new InstanceQuery();
                instanceQuery.setSetName(modelDeployRequest.getSet());
                instanceQuery.setCluster(extractCluster(queueName));
                instanceQuery.setQueue(queueName);
                instanceQuery.setWorker(requireInstanceNum);

                //实例资源
                ResourceQuery resourceQuery = new ResourceQuery();
                resourceQuery.setVcores(modelDeployRequest.getVcores());
                resourceQuery.setMemory(modelDeployRequest.getMemory());
                resourceQuery.setGcores(modelDeployRequest.getGcores());
                resourceQuery.setGcoresType(gpuResourceType.getName());
                instanceQuery.setResource(resourceQuery);
                instanceQueries.add(instanceQuery);
            }
            //模型部署和实例部署
            query.setInstanceQueries(instanceQueries);
            // 调用创建服务
            CommonResponseVo service = mlpModelServingService.createService(query);
            if (!Objects.equals(service.getMessage(), "请求成功")) {
                log.error("模型创建失败: code={}, message={}", service.getCode(), service.getMessage());
                return false;
            }
            log.info("分组及模型创建成功: group={}, modelName={}", modelDeployRequest.getGroup(), modelDeployRequest.getModelName());
            return true;
        } catch (TException e) {
            log.error("模型部署失败: appkey={}, modelName={}, error={}", modelDeployRequest.getAppkey(), modelDeployRequest.getModelName(), e);
            throw new RuntimeException(e);
        }
    }


    public Map<String, Integer> getRemainOfQueue(List<String> queueName, GPUResourceType gcoresType) {

        Map<String, Integer> queueGpuNum = new HashMap<>();
        //队列剩余GPU数量
        for (String queue : queueName) {
            int queueRestNum = 0;
            List<GPUQueueResourceDetail> resourceList = mlpTrainingService.getQueueResource(queue);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(resourceList)) {
                Optional<GPUQueueResourceDetail> que = resourceList.stream().filter(g -> gcoresType == g.getResourceType()).findFirst();
                if (que.isPresent()) {
                    GPUQueueResourceDetail gpuQueueResourceDetail = que.get();
                    queueRestNum = gpuQueueResourceDetail.getRemainNum();
                }
            }
            queueGpuNum.put(queue, queueRestNum);
        }
        return queueGpuNum;
    }


    /**
     * 检查指定分组中是否已经部署了指定模型
     *
     * @param servingDetail 服务详情
     * @param groupName     分组名称
     * @param modelName     要检查的模型名称
     * @return <分组是否存在，模型是否部署，当前分组已有实例>
     */
    private ServingDetail getServingDetail(ServingDetailDataVo servingDetail,
                                           String groupName,
                                           String modelName) {
        if (servingDetail == null || CollectionUtils.isEmpty(servingDetail.getGroups())) {
            return ServingDetail.builder()
                    .groupExist(false)
                    .modelDeployed(false)
                    .instanceCount(0)
                    .groupId(0)
                    .servingId(0)
                    .build();
        }

        Optional<GroupVo> targetGroup = servingDetail.getGroups().stream()
                .filter(group -> groupName.equals(group.getName()))
                .findFirst();

        if (!targetGroup.isPresent()) {
            return ServingDetail.builder()
                    .groupExist(false)
                    .modelDeployed(false)
                    .instanceCount(0)
                    .groupId(0)
                    .servingId(servingDetail.getId())
                    .build();
        }

        GroupVo group = targetGroup.get();
        ServingModelsVo servingModels = group.getModels();
        if (servingModels != null && !CollectionUtils.isEmpty(servingModels.getModels())) {
            for (ServingModelInfoVo modelInfo : servingModels.getModels()) {
                if (modelName.equals(modelInfo.getModelName())) {
                    return ServingDetail.builder()
                            .groupExist(true)
                            .modelDeployed(true)
                            .instanceCount(group.getInstances().size())
                            .groupId(group.getId())
                            .servingId(servingDetail.getId())
                            .build();
                }
            }
        }

        return ServingDetail.builder()
                .groupExist(true)
                .modelDeployed(false)
                .instanceCount(0)
                .groupId(group.getId())
                .servingId(servingDetail.getId())
                .build();
    }

    /**
     * 模型上线函数
     *
     * @param servingId 服务ID
     * @param groupId   服务分组ID
     * @param models    模型列表
     * @return 接口返回结果
     */
    public Map<String, Object> loadModel(
            int servingId,
            int groupId,
            String optMis,
            String wxProjectName,
            List<Map<String, Object>> models) {
        try {
            // 基础URL
            String baseUrl = "https://mlp.sankuai.com/";

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();

            // 处理模型列表
            List<Map<String, Object>> processedModels = new ArrayList<>();
            for (Map<String, Object> model : models) {
                Map<String, Object> processedModel = new HashMap<>();
                processedModel.put("modelId", model.get("modelId"));
                processedModel.put("autoUpgrade", false);
                processedModel.put("modelName", model.get("modelName"));
                processedModel.put("modelVersionIds", model.get("modelVersionIds"));
                processedModel.put("modelVersions", model.get("modelVersions"));
                processedModels.add(processedModel);
            }
            requestBody.put("models", processedModels);
            requestBody.put("instances", new int[]{}); // 空列表，代表全量发布

            // 构建URL和URI
            String uri = "/mlapi/msp/serving/" + servingId + "/group/" + groupId + "/load-model";
            String url = baseUrl + uri;

            // 生成认证头
            String method = "POST";
            SimpleDateFormat df = new SimpleDateFormat("EEE, d MMM yyyy HH:mm:ss z", Locale.ENGLISH);
            df.setTimeZone(TimeZone.getTimeZone("GMT"));
            String date = df.format(new Date());

            String clientId = "com.sankuai.algoplatform.matchops";
            String secret = "AjLGqNnMfCNOc4OtMXpteMsnYx6bC2tC";

            String stringToSign = method + " " + uri + "\n" + date;
            String signature = getSignature(stringToSign, secret);
            String authorization = "MWS " + clientId + ":" + signature;

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            headers.setAccept(Lists.newArrayList(new MediaType[]{MediaType.APPLICATION_JSON_UTF8}));
            headers.add("Date", date);
            headers.add("Authorization", authorization);
            headers.add("AIFree-User", optMis);
            headers.add("AIFree-WXProject", wxProjectName);

            // 创建HTTP实体
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

            // 发送POST请求
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<Map> response = restTemplate.postForEntity(url, requestEntity, Map.class);

            return response.getBody();

        } catch (Exception e) {
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "模型上线失败: " + e.getMessage());
            errorResult.put("error", e.toString());
            log.error("模型上线失败: ", e);
            return errorResult;
        }
    }

    private String getSignature(String data, String secret) throws NoSuchAlgorithmException, InvalidKeyException, UnsupportedEncodingException {
        Security.addProvider(new SunJCE());
        SecretKeySpec signingKey = new SecretKeySpec(secret.getBytes(), "HmacSHA1");
        Mac mac = Mac.getInstance("HmacSHA1");
        mac.init(signingKey);
        byte[] rawHmac = mac.doFinal(data.getBytes("utf-8"));
        return Base64.encodeBase64String(rawHmac);

    }

    private String extractCluster(String input) {
        int firstDotIndex = input.indexOf('.');
        int secondDotIndex = input.indexOf('.', firstDotIndex + 1);
        if (firstDotIndex != -1 && secondDotIndex != -1) {
            return input.substring(firstDotIndex + 1, secondDotIndex);
        }
        return "";
    }

    /**
     * 根据模型名称和项目获取模型信息，构建 models 参数
     *
     * @param wxProject   项目名称
     * @param modelName   模型名称
     * @param versionNums 需要匹配的版本号列表
     * @return 构建好的 models 列表，包含 modelName, modelVersionIds, modelVersions
     */
    public List<Map<String, Object>> buildModelsFromModelInfo(String wxProject, String modelName, List<Long> versionNums) {
        try {
            // 构建查询参数
            GetModelInfoQuery query = new GetModelInfoQuery();
            query.setWxProject(wxProject);
            query.setModelName(modelName);

            // 调用接口获取模型信息
            GetModelInfoVo modelInfo = mlpModelServingService.getModelInfo(query);

            // 检查返回结果
            if (modelInfo == null || modelInfo.getData() == null) {
                log.error("获取模型信息失败: wxProject={}, modelName={}", wxProject, modelName);
                return Collections.emptyList();
            }

            GetModelInfoDataVo data = modelInfo.getData();
            List<ModelVersionInfoVo> versions = data.getModelVersions(); // 获取版本列表
            if (CollectionUtils.isEmpty(versions)) {
                log.error("模型版本信息为空: wxProject={}, modelName={}", wxProject, modelName);
                return Collections.emptyList();
            }

            // 构建版本号到版本信息的映射
            Map<Long, ModelVersionInfoVo> versionMap = versions.stream()
                    .collect(Collectors.toMap(
                            ModelVersionInfoVo::getVersionNum,
                            version -> version,
                            (v1, v2) -> v1
                    ));

            // 匹配请求的版本号
            List<Integer> matchedVersionIds = new ArrayList<>();
            List<Integer> matchedVersionNums = new ArrayList<>();
            Integer modelId = null;
            for (Long requestedVersion : versionNums) {
                ModelVersionInfoVo versionInfo = versionMap.get(requestedVersion);
                if (versionInfo != null) {
                    matchedVersionIds.add(versionInfo.getId());
                    matchedVersionNums.add(Integer.valueOf(requestedVersion.toString()));
                    // 获取 modelId，因为所有版本的 modelId 都是一样的，所以取第一个就可以
                    if (modelId == null) {
                        modelId = versionInfo.getModelId();
                    }
                } else {
                    log.warn("未找到匹配的版本信息: modelName={}, versionNum={}", modelName, requestedVersion);
                }
            }

            if (matchedVersionIds.isEmpty()) {
                log.error("没有找到任何匹配的版本: modelName={}, requestedVersions={}", modelName, versionNums);
                return Collections.emptyList();
            }

            // 构建返回结果
            List<Map<String, Object>> models = new ArrayList<>();
            Map<String, Object> modelMap = new HashMap<>();

            // 设置模型信息，确保返回字段符合要求
            modelMap.put("modelName", modelName);
            modelMap.put("modelId", modelId);
            modelMap.put("modelVersionIds", matchedVersionIds);
            modelMap.put("modelVersions", matchedVersionNums);

            models.add(modelMap);
            return models;

        } catch (Exception e) {
            log.error("构建模型信息异常: wxProject={}, modelName={}, versionNums={}",
                    wxProject, modelName, versionNums, e);
            throw new RuntimeException(e);
        }
    }
}
