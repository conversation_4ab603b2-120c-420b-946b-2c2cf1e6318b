package com.sankuai.algoplatform.matchops.infrastructure.proxy.mcm.context;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.algoplatform.matchops.api.request.prompt.TAddEditPromptStrategyRequest;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.BizLlmPredictConfigDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.BizLlmPredictPromptTemlateDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.dao.LlmBizStrategyDao;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.BizLlmpredictConfig;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.BizLlmpredictPromptTemplate;
import com.sankuai.algoplatform.matchops.infrastructure.util.ContextUtil;
import com.sankuai.mcm.client.sdk.annotation.McmComponent;
import com.sankuai.mcm.client.sdk.context.eventcontext.EventContextPropertyProviderAdaptor;
import com.sankuai.mcm.client.sdk.context.eventcontext.EventContextPropertyProviderRequest;
import com.sankuai.mcm.client.sdk.dto.common.UserIdentity;
import com.sankuai.meituan.auth.vo.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@McmComponent(events = "UpdatePromptStrategy")
public class PromptEventContextPropertyProvider extends EventContextPropertyProviderAdaptor {

    @Autowired
    private BizLlmPredictConfigDao bizLlmPredictConfigDao;

    @Autowired
    private BizLlmPredictPromptTemlateDao bizLlmPredictPromptTemlateDao;

    @Override
    public UserIdentity getUserIdentity(EventContextPropertyProviderRequest request) {
        return UserIdentity.ofUser(ContextUtil.getLoginUserMis());
    }


    @Override
    public User getSsoUser() {
        User user = new User();
        user.setLogin(ContextUtil.getLoginUserMis());
        return user;
    }

    @Override
    public Map<String, Object> getExtraInfo(EventContextPropertyProviderRequest request) {
        Map<String, Object> extraInfo = new HashMap<>();

        try {
            // 获取before信息 - 从数据库查询当前状态
            Map<String, Object> beforeInfo = getBeforeInfo(request);
            extraInfo.put("before", beforeInfo);

            // 获取after信息 - 从request的args中提取
            Map<String, Object> afterInfo = getAfterInfo(request);
            extraInfo.put("after", afterInfo);

        } catch (Exception e) {
            log.error("获取Prompt策略变更上下文信息失败", e);
            extraInfo.put("before", "获取变更前信息失败: " + e.getMessage());
            extraInfo.put("after", "获取变更后信息失败: " + e.getMessage());
        }

        return extraInfo;
    }

    /**
     * 获取变更前信息 - 根据llmStrategyBizCode查询数据库，格式化显示
     */
    private Map<String, Object> getBeforeInfo(EventContextPropertyProviderRequest request) {
        Map<String, Object> beforeInfo = new HashMap<>();

        // 从请求参数中提取llmStrategyBizCode
        Object[] args = request.getArgs();
        if (args == null || args.length == 0) {
            throw new RuntimeException("请求参数为空");
        }

        // 直接转换为TAddEditPromptStrategyRequest对象
        TAddEditPromptStrategyRequest requestObj = (TAddEditPromptStrategyRequest) args[0];
        String llmStrategyBizCode = requestObj.llmStrategyBizCode;

        if (llmStrategyBizCode == null || llmStrategyBizCode.isEmpty()) {
            throw new RuntimeException("无法提取llmStrategyBizCode参数");
        }

        // 根据llmStrategyBizCode查询完整信息
        Map<String, Object> promptInfo = getPromptInfoByBizCode(llmStrategyBizCode);
        if (promptInfo.containsKey("error")) {
            throw new RuntimeException((String) promptInfo.get("error"));
        }

        // 格式化显示字段
        beforeInfo.put("bizCode", promptInfo.get("llmStrategyBizCode"));
        beforeInfo.put("策略名称", promptInfo.get("llmStrategyName"));
        beforeInfo.put("模型名称", promptInfo.get("llmModelName"));
        beforeInfo.put("服务名称", promptInfo.get("llmServiceName"));
        beforeInfo.put("batchSize", promptInfo.get("batchSize"));
        beforeInfo.put("使用模版", promptInfo.get("isTemplate"));
        beforeInfo.put("appId", promptInfo.get("appId"));
        beforeInfo.put("debug模式", promptInfo.get("isDebug"));
        beforeInfo.put("prompt模版", promptInfo.get("prompt"));
        beforeInfo.put("大模型参数topP", promptInfo.get("topP"));
        beforeInfo.put("大模型参数topK", promptInfo.get("topK"));
        beforeInfo.put("大模型参数temperature", promptInfo.get("temperature"));
        beforeInfo.put("大模型参数maxTokens", promptInfo.get("maxTokens"));
        beforeInfo.put("说明", promptInfo.get("note"));

        log.info("成功获取变更前Prompt策略信息, llmStrategyBizCode: {}", llmStrategyBizCode);
        return beforeInfo;
    }



    private Map<String, Object> getPromptInfoByBizCode(String llmStrategyBizCode) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 直接根据bizCode查询biz_llmpredict_config表
            BizLlmpredictConfig bizLlmpredictConfig = bizLlmPredictConfigDao.selectByBizCode(llmStrategyBizCode);
            if (bizLlmpredictConfig == null) {
                result.put("error", "未找到对应的BizLlmpredictConfig记录, bizCode: " + llmStrategyBizCode);
                return result;
            }

            // 设置基本配置信息
            result.put("llmStrategyId", bizLlmpredictConfig.getId());
            result.put("llmStrategyName", bizLlmpredictConfig.getNote());
            result.put("llmStrategyBizCode", bizLlmpredictConfig.getBizCode());

            // 解析model_config
            String modelConfigStr = bizLlmpredictConfig.getModelConfig();
            if (modelConfigStr == null || modelConfigStr.trim().isEmpty()) {
                result.put("error", "model_config为空");
                return result;
            }

            JSONObject modelConfig = JSON.parseObject(modelConfigStr);

            // 设置模型配置信息
            result.put("llmModelName", modelConfig.getString("modelName"));
            result.put("llmServiceName", modelConfig.getString("modelServiceName"));
            result.put("batchSize", modelConfig.getInteger("batchSize"));
            result.put("isTemplate", modelConfig.getInteger("useTemplate"));
            result.put("appId", modelConfig.getString("appId"));
            result.put("isDebug", modelConfig.getBoolean("debug") ? 1 : 0);

            // 解析modelParms
            JSONObject modelParms = modelConfig.getJSONObject("modelParms");
            if (modelParms != null) {
                result.put("maxTokens", modelParms.getInteger("maxTokens"));
                result.put("temperature", modelParms.getDouble("temperature"));
                result.put("topK", modelParms.getDouble("top_k"));
                result.put("topP", modelParms.getDouble("top_p"));
            }

            // 根据prompt_template_id查询prompt模板
            Long promptTemplateId = bizLlmpredictConfig.getPromptTemplateId();
            if (promptTemplateId != null) {
                BizLlmpredictPromptTemplate promptTemplate = bizLlmPredictPromptTemlateDao.selectById(promptTemplateId);
                if (promptTemplate != null) {
                    result.put("prompt", promptTemplate.getPromptTemplate());
                    result.put("note", promptTemplate.getNote());
                }
            }

            log.info("成功获取Prompt信息, bizCode: {}, configId: {}, promptTemplateId: {}",
                    llmStrategyBizCode, bizLlmpredictConfig.getId(), promptTemplateId);

        } catch (Exception e) {
            log.error("根据bizCode获取Prompt信息失败, bizCode: {}", llmStrategyBizCode, e);
            result.put("error", "查询失败: " + e.getMessage());
        }

        return result;
    }
    /**
     * 获取变更后信息 - 从TAddEditPromptStrategyRequest中提取，格式化显示
     */
    private Map<String, Object> getAfterInfo(EventContextPropertyProviderRequest request) {
        Map<String, Object> afterInfo = new HashMap<>();

        Object[] args = request.getArgs();
        if (args == null || args.length == 0) {
            throw new RuntimeException("请求参数为空");
        }

        // 直接转换为TAddEditPromptStrategyRequest对象
        TAddEditPromptStrategyRequest requestObj = (TAddEditPromptStrategyRequest) args[0];

        // 格式化显示字段 - 直接从请求对象获取
        afterInfo.put("bizCode", requestObj.llmStrategyBizCode);
        afterInfo.put("策略名称", requestObj.llmStrategyName);
        afterInfo.put("模型名称", requestObj.llmModelName);
        afterInfo.put("服务名称", requestObj.llmServiceName);
        afterInfo.put("batchSize", requestObj.batchSize);
        afterInfo.put("使用模版", requestObj.isTemplate);
        afterInfo.put("appId", requestObj.appId);
        afterInfo.put("debug模式", requestObj.isDebug);
        afterInfo.put("prompt模版", requestObj.prompt);
        afterInfo.put("大模型参数topP", requestObj.topP);
        afterInfo.put("大模型参数topK", requestObj.topK);
        afterInfo.put("大模型参数temperature", requestObj.temperature);
        afterInfo.put("大模型参数maxTokens", requestObj.maxTokens);
        afterInfo.put("说明", requestObj.note);

        log.info("成功提取变更后Prompt策略信息, llmStrategyId: {}, llmStrategyName: {}",
                requestObj.llmStrategyId, requestObj.llmStrategyName);
        return afterInfo;
    }
}