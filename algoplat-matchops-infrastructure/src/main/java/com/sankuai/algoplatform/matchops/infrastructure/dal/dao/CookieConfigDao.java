package com.sankuai.algoplatform.matchops.infrastructure.dal.dao;

import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.AlgoPackage;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.CookieConfig;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.AlgoPackageExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.CookieConfigExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.AlgoPackageMapper;
import com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.CookieConfigMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CookieConfigDao {

    @Autowired
    private CookieConfigMapper cookieConfigMapper;

    public CookieConfig selectByMis(String mis) {
        CookieConfigExample example = new CookieConfigExample();
        example.createCriteria().andUserMisEqualTo(mis);
        List<CookieConfig> cookieConfigs = cookieConfigMapper.selectByExample(example);
        return CollectionUtils.isNotEmpty(cookieConfigs) ? cookieConfigs.get(0) : null;
    }

    public int insertOrUpdate(CookieConfig config) {
        return cookieConfigMapper.insertOrUpdate(config);
    }
}
