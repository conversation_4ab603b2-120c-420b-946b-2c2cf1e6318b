package com.sankuai.algoplatform.matchops.infrastructure.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TestTaskExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public TestTaskExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public TestTaskExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public TestTaskExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public TestTaskExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andBizLineIdIsNull() {
            addCriterion("biz_line_id is null");
            return (Criteria) this;
        }

        public Criteria andBizLineIdIsNotNull() {
            addCriterion("biz_line_id is not null");
            return (Criteria) this;
        }

        public Criteria andBizLineIdEqualTo(Long value) {
            addCriterion("biz_line_id =", value, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdNotEqualTo(Long value) {
            addCriterion("biz_line_id <>", value, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdGreaterThan(Long value) {
            addCriterion("biz_line_id >", value, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdGreaterThanOrEqualTo(Long value) {
            addCriterion("biz_line_id >=", value, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdLessThan(Long value) {
            addCriterion("biz_line_id <", value, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdLessThanOrEqualTo(Long value) {
            addCriterion("biz_line_id <=", value, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdIn(List<Long> values) {
            addCriterion("biz_line_id in", values, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdNotIn(List<Long> values) {
            addCriterion("biz_line_id not in", values, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdBetween(Long value1, Long value2) {
            addCriterion("biz_line_id between", value1, value2, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdNotBetween(Long value1, Long value2) {
            addCriterion("biz_line_id not between", value1, value2, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andMatchStrategyIdIsNull() {
            addCriterion("match_strategy_id is null");
            return (Criteria) this;
        }

        public Criteria andMatchStrategyIdIsNotNull() {
            addCriterion("match_strategy_id is not null");
            return (Criteria) this;
        }

        public Criteria andMatchStrategyIdEqualTo(Long value) {
            addCriterion("match_strategy_id =", value, "matchStrategyId");
            return (Criteria) this;
        }

        public Criteria andMatchStrategyIdNotEqualTo(Long value) {
            addCriterion("match_strategy_id <>", value, "matchStrategyId");
            return (Criteria) this;
        }

        public Criteria andMatchStrategyIdGreaterThan(Long value) {
            addCriterion("match_strategy_id >", value, "matchStrategyId");
            return (Criteria) this;
        }

        public Criteria andMatchStrategyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("match_strategy_id >=", value, "matchStrategyId");
            return (Criteria) this;
        }

        public Criteria andMatchStrategyIdLessThan(Long value) {
            addCriterion("match_strategy_id <", value, "matchStrategyId");
            return (Criteria) this;
        }

        public Criteria andMatchStrategyIdLessThanOrEqualTo(Long value) {
            addCriterion("match_strategy_id <=", value, "matchStrategyId");
            return (Criteria) this;
        }

        public Criteria andMatchStrategyIdIn(List<Long> values) {
            addCriterion("match_strategy_id in", values, "matchStrategyId");
            return (Criteria) this;
        }

        public Criteria andMatchStrategyIdNotIn(List<Long> values) {
            addCriterion("match_strategy_id not in", values, "matchStrategyId");
            return (Criteria) this;
        }

        public Criteria andMatchStrategyIdBetween(Long value1, Long value2) {
            addCriterion("match_strategy_id between", value1, value2, "matchStrategyId");
            return (Criteria) this;
        }

        public Criteria andMatchStrategyIdNotBetween(Long value1, Long value2) {
            addCriterion("match_strategy_id not between", value1, value2, "matchStrategyId");
            return (Criteria) this;
        }

        public Criteria andResourceGroupIdIsNull() {
            addCriterion("resource_group_id is null");
            return (Criteria) this;
        }

        public Criteria andResourceGroupIdIsNotNull() {
            addCriterion("resource_group_id is not null");
            return (Criteria) this;
        }

        public Criteria andResourceGroupIdEqualTo(Long value) {
            addCriterion("resource_group_id =", value, "resourceGroupId");
            return (Criteria) this;
        }

        public Criteria andResourceGroupIdNotEqualTo(Long value) {
            addCriterion("resource_group_id <>", value, "resourceGroupId");
            return (Criteria) this;
        }

        public Criteria andResourceGroupIdGreaterThan(Long value) {
            addCriterion("resource_group_id >", value, "resourceGroupId");
            return (Criteria) this;
        }

        public Criteria andResourceGroupIdGreaterThanOrEqualTo(Long value) {
            addCriterion("resource_group_id >=", value, "resourceGroupId");
            return (Criteria) this;
        }

        public Criteria andResourceGroupIdLessThan(Long value) {
            addCriterion("resource_group_id <", value, "resourceGroupId");
            return (Criteria) this;
        }

        public Criteria andResourceGroupIdLessThanOrEqualTo(Long value) {
            addCriterion("resource_group_id <=", value, "resourceGroupId");
            return (Criteria) this;
        }

        public Criteria andResourceGroupIdIn(List<Long> values) {
            addCriterion("resource_group_id in", values, "resourceGroupId");
            return (Criteria) this;
        }

        public Criteria andResourceGroupIdNotIn(List<Long> values) {
            addCriterion("resource_group_id not in", values, "resourceGroupId");
            return (Criteria) this;
        }

        public Criteria andResourceGroupIdBetween(Long value1, Long value2) {
            addCriterion("resource_group_id between", value1, value2, "resourceGroupId");
            return (Criteria) this;
        }

        public Criteria andResourceGroupIdNotBetween(Long value1, Long value2) {
            addCriterion("resource_group_id not between", value1, value2, "resourceGroupId");
            return (Criteria) this;
        }

        public Criteria andReporterTemplateIdIsNull() {
            addCriterion("reporter_template_id is null");
            return (Criteria) this;
        }

        public Criteria andReporterTemplateIdIsNotNull() {
            addCriterion("reporter_template_id is not null");
            return (Criteria) this;
        }

        public Criteria andReporterTemplateIdEqualTo(String value) {
            addCriterion("reporter_template_id =", value, "reporterTemplateId");
            return (Criteria) this;
        }

        public Criteria andReporterTemplateIdNotEqualTo(String value) {
            addCriterion("reporter_template_id <>", value, "reporterTemplateId");
            return (Criteria) this;
        }

        public Criteria andReporterTemplateIdGreaterThan(String value) {
            addCriterion("reporter_template_id >", value, "reporterTemplateId");
            return (Criteria) this;
        }

        public Criteria andReporterTemplateIdGreaterThanOrEqualTo(String value) {
            addCriterion("reporter_template_id >=", value, "reporterTemplateId");
            return (Criteria) this;
        }

        public Criteria andReporterTemplateIdLessThan(String value) {
            addCriterion("reporter_template_id <", value, "reporterTemplateId");
            return (Criteria) this;
        }

        public Criteria andReporterTemplateIdLessThanOrEqualTo(String value) {
            addCriterion("reporter_template_id <=", value, "reporterTemplateId");
            return (Criteria) this;
        }

        public Criteria andReporterTemplateIdLike(String value) {
            addCriterion("reporter_template_id like", value, "reporterTemplateId");
            return (Criteria) this;
        }

        public Criteria andReporterTemplateIdNotLike(String value) {
            addCriterion("reporter_template_id not like", value, "reporterTemplateId");
            return (Criteria) this;
        }

        public Criteria andReporterTemplateIdIn(List<String> values) {
            addCriterion("reporter_template_id in", values, "reporterTemplateId");
            return (Criteria) this;
        }

        public Criteria andReporterTemplateIdNotIn(List<String> values) {
            addCriterion("reporter_template_id not in", values, "reporterTemplateId");
            return (Criteria) this;
        }

        public Criteria andReporterTemplateIdBetween(String value1, String value2) {
            addCriterion("reporter_template_id between", value1, value2, "reporterTemplateId");
            return (Criteria) this;
        }

        public Criteria andReporterTemplateIdNotBetween(String value1, String value2) {
            addCriterion("reporter_template_id not between", value1, value2, "reporterTemplateId");
            return (Criteria) this;
        }

        public Criteria andRunParamIsNull() {
            addCriterion("run_param is null");
            return (Criteria) this;
        }

        public Criteria andRunParamIsNotNull() {
            addCriterion("run_param is not null");
            return (Criteria) this;
        }

        public Criteria andRunParamEqualTo(String value) {
            addCriterion("run_param =", value, "runParam");
            return (Criteria) this;
        }

        public Criteria andRunParamNotEqualTo(String value) {
            addCriterion("run_param <>", value, "runParam");
            return (Criteria) this;
        }

        public Criteria andRunParamGreaterThan(String value) {
            addCriterion("run_param >", value, "runParam");
            return (Criteria) this;
        }

        public Criteria andRunParamGreaterThanOrEqualTo(String value) {
            addCriterion("run_param >=", value, "runParam");
            return (Criteria) this;
        }

        public Criteria andRunParamLessThan(String value) {
            addCriterion("run_param <", value, "runParam");
            return (Criteria) this;
        }

        public Criteria andRunParamLessThanOrEqualTo(String value) {
            addCriterion("run_param <=", value, "runParam");
            return (Criteria) this;
        }

        public Criteria andRunParamLike(String value) {
            addCriterion("run_param like", value, "runParam");
            return (Criteria) this;
        }

        public Criteria andRunParamNotLike(String value) {
            addCriterion("run_param not like", value, "runParam");
            return (Criteria) this;
        }

        public Criteria andRunParamIn(List<String> values) {
            addCriterion("run_param in", values, "runParam");
            return (Criteria) this;
        }

        public Criteria andRunParamNotIn(List<String> values) {
            addCriterion("run_param not in", values, "runParam");
            return (Criteria) this;
        }

        public Criteria andRunParamBetween(String value1, String value2) {
            addCriterion("run_param between", value1, value2, "runParam");
            return (Criteria) this;
        }

        public Criteria andRunParamNotBetween(String value1, String value2) {
            addCriterion("run_param not between", value1, value2, "runParam");
            return (Criteria) this;
        }

        public Criteria andResourcePrepareInfoIsNull() {
            addCriterion("resource_prepare_info is null");
            return (Criteria) this;
        }

        public Criteria andResourcePrepareInfoIsNotNull() {
            addCriterion("resource_prepare_info is not null");
            return (Criteria) this;
        }

        public Criteria andResourcePrepareInfoEqualTo(String value) {
            addCriterion("resource_prepare_info =", value, "resourcePrepareInfo");
            return (Criteria) this;
        }

        public Criteria andResourcePrepareInfoNotEqualTo(String value) {
            addCriterion("resource_prepare_info <>", value, "resourcePrepareInfo");
            return (Criteria) this;
        }

        public Criteria andResourcePrepareInfoGreaterThan(String value) {
            addCriterion("resource_prepare_info >", value, "resourcePrepareInfo");
            return (Criteria) this;
        }

        public Criteria andResourcePrepareInfoGreaterThanOrEqualTo(String value) {
            addCriterion("resource_prepare_info >=", value, "resourcePrepareInfo");
            return (Criteria) this;
        }

        public Criteria andResourcePrepareInfoLessThan(String value) {
            addCriterion("resource_prepare_info <", value, "resourcePrepareInfo");
            return (Criteria) this;
        }

        public Criteria andResourcePrepareInfoLessThanOrEqualTo(String value) {
            addCriterion("resource_prepare_info <=", value, "resourcePrepareInfo");
            return (Criteria) this;
        }

        public Criteria andResourcePrepareInfoLike(String value) {
            addCriterion("resource_prepare_info like", value, "resourcePrepareInfo");
            return (Criteria) this;
        }

        public Criteria andResourcePrepareInfoNotLike(String value) {
            addCriterion("resource_prepare_info not like", value, "resourcePrepareInfo");
            return (Criteria) this;
        }

        public Criteria andResourcePrepareInfoIn(List<String> values) {
            addCriterion("resource_prepare_info in", values, "resourcePrepareInfo");
            return (Criteria) this;
        }

        public Criteria andResourcePrepareInfoNotIn(List<String> values) {
            addCriterion("resource_prepare_info not in", values, "resourcePrepareInfo");
            return (Criteria) this;
        }

        public Criteria andResourcePrepareInfoBetween(String value1, String value2) {
            addCriterion("resource_prepare_info between", value1, value2, "resourcePrepareInfo");
            return (Criteria) this;
        }

        public Criteria andResourcePrepareInfoNotBetween(String value1, String value2) {
            addCriterion("resource_prepare_info not between", value1, value2, "resourcePrepareInfo");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andOwnerIsNull() {
            addCriterion("owner is null");
            return (Criteria) this;
        }

        public Criteria andOwnerIsNotNull() {
            addCriterion("owner is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerEqualTo(String value) {
            addCriterion("owner =", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotEqualTo(String value) {
            addCriterion("owner <>", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerGreaterThan(String value) {
            addCriterion("owner >", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerGreaterThanOrEqualTo(String value) {
            addCriterion("owner >=", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerLessThan(String value) {
            addCriterion("owner <", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerLessThanOrEqualTo(String value) {
            addCriterion("owner <=", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerLike(String value) {
            addCriterion("owner like", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotLike(String value) {
            addCriterion("owner not like", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerIn(List<String> values) {
            addCriterion("owner in", values, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotIn(List<String> values) {
            addCriterion("owner not in", values, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerBetween(String value1, String value2) {
            addCriterion("owner between", value1, value2, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotBetween(String value1, String value2) {
            addCriterion("owner not between", value1, value2, "owner");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}