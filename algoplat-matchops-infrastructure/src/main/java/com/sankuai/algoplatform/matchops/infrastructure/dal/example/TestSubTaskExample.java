package com.sankuai.algoplatform.matchops.infrastructure.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TestSubTaskExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public TestSubTaskExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNull() {
            addCriterion("task_id is null");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNotNull() {
            addCriterion("task_id is not null");
            return (Criteria) this;
        }

        public Criteria andTaskIdEqualTo(Long value) {
            addCriterion("task_id =", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotEqualTo(Long value) {
            addCriterion("task_id <>", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThan(Long value) {
            addCriterion("task_id >", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThanOrEqualTo(Long value) {
            addCriterion("task_id >=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThan(Long value) {
            addCriterion("task_id <", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThanOrEqualTo(Long value) {
            addCriterion("task_id <=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdIn(List<Long> values) {
            addCriterion("task_id in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotIn(List<Long> values) {
            addCriterion("task_id not in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdBetween(Long value1, Long value2) {
            addCriterion("task_id between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotBetween(Long value1, Long value2) {
            addCriterion("task_id not between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andRunParamIsNull() {
            addCriterion("run_param is null");
            return (Criteria) this;
        }

        public Criteria andRunParamIsNotNull() {
            addCriterion("run_param is not null");
            return (Criteria) this;
        }

        public Criteria andRunParamEqualTo(String value) {
            addCriterion("run_param =", value, "runParam");
            return (Criteria) this;
        }

        public Criteria andRunParamNotEqualTo(String value) {
            addCriterion("run_param <>", value, "runParam");
            return (Criteria) this;
        }

        public Criteria andRunParamGreaterThan(String value) {
            addCriterion("run_param >", value, "runParam");
            return (Criteria) this;
        }

        public Criteria andRunParamGreaterThanOrEqualTo(String value) {
            addCriterion("run_param >=", value, "runParam");
            return (Criteria) this;
        }

        public Criteria andRunParamLessThan(String value) {
            addCriterion("run_param <", value, "runParam");
            return (Criteria) this;
        }

        public Criteria andRunParamLessThanOrEqualTo(String value) {
            addCriterion("run_param <=", value, "runParam");
            return (Criteria) this;
        }

        public Criteria andRunParamLike(String value) {
            addCriterion("run_param like", value, "runParam");
            return (Criteria) this;
        }

        public Criteria andRunParamNotLike(String value) {
            addCriterion("run_param not like", value, "runParam");
            return (Criteria) this;
        }

        public Criteria andRunParamIn(List<String> values) {
            addCriterion("run_param in", values, "runParam");
            return (Criteria) this;
        }

        public Criteria andRunParamNotIn(List<String> values) {
            addCriterion("run_param not in", values, "runParam");
            return (Criteria) this;
        }

        public Criteria andRunParamBetween(String value1, String value2) {
            addCriterion("run_param between", value1, value2, "runParam");
            return (Criteria) this;
        }

        public Criteria andRunParamNotBetween(String value1, String value2) {
            addCriterion("run_param not between", value1, value2, "runParam");
            return (Criteria) this;
        }

        public Criteria andAlgoCodeInfoIsNull() {
            addCriterion("algo_code_info is null");
            return (Criteria) this;
        }

        public Criteria andAlgoCodeInfoIsNotNull() {
            addCriterion("algo_code_info is not null");
            return (Criteria) this;
        }

        public Criteria andAlgoCodeInfoEqualTo(String value) {
            addCriterion("algo_code_info =", value, "algoCodeInfo");
            return (Criteria) this;
        }

        public Criteria andAlgoCodeInfoNotEqualTo(String value) {
            addCriterion("algo_code_info <>", value, "algoCodeInfo");
            return (Criteria) this;
        }

        public Criteria andAlgoCodeInfoGreaterThan(String value) {
            addCriterion("algo_code_info >", value, "algoCodeInfo");
            return (Criteria) this;
        }

        public Criteria andAlgoCodeInfoGreaterThanOrEqualTo(String value) {
            addCriterion("algo_code_info >=", value, "algoCodeInfo");
            return (Criteria) this;
        }

        public Criteria andAlgoCodeInfoLessThan(String value) {
            addCriterion("algo_code_info <", value, "algoCodeInfo");
            return (Criteria) this;
        }

        public Criteria andAlgoCodeInfoLessThanOrEqualTo(String value) {
            addCriterion("algo_code_info <=", value, "algoCodeInfo");
            return (Criteria) this;
        }

        public Criteria andAlgoCodeInfoLike(String value) {
            addCriterion("algo_code_info like", value, "algoCodeInfo");
            return (Criteria) this;
        }

        public Criteria andAlgoCodeInfoNotLike(String value) {
            addCriterion("algo_code_info not like", value, "algoCodeInfo");
            return (Criteria) this;
        }

        public Criteria andAlgoCodeInfoIn(List<String> values) {
            addCriterion("algo_code_info in", values, "algoCodeInfo");
            return (Criteria) this;
        }

        public Criteria andAlgoCodeInfoNotIn(List<String> values) {
            addCriterion("algo_code_info not in", values, "algoCodeInfo");
            return (Criteria) this;
        }

        public Criteria andAlgoCodeInfoBetween(String value1, String value2) {
            addCriterion("algo_code_info between", value1, value2, "algoCodeInfo");
            return (Criteria) this;
        }

        public Criteria andAlgoCodeInfoNotBetween(String value1, String value2) {
            addCriterion("algo_code_info not between", value1, value2, "algoCodeInfo");
            return (Criteria) this;
        }

        public Criteria andPredictorCacheInfoIsNull() {
            addCriterion("predictor_cache_info is null");
            return (Criteria) this;
        }

        public Criteria andPredictorCacheInfoIsNotNull() {
            addCriterion("predictor_cache_info is not null");
            return (Criteria) this;
        }

        public Criteria andPredictorCacheInfoEqualTo(String value) {
            addCriterion("predictor_cache_info =", value, "predictorCacheInfo");
            return (Criteria) this;
        }

        public Criteria andPredictorCacheInfoNotEqualTo(String value) {
            addCriterion("predictor_cache_info <>", value, "predictorCacheInfo");
            return (Criteria) this;
        }

        public Criteria andPredictorCacheInfoGreaterThan(String value) {
            addCriterion("predictor_cache_info >", value, "predictorCacheInfo");
            return (Criteria) this;
        }

        public Criteria andPredictorCacheInfoGreaterThanOrEqualTo(String value) {
            addCriterion("predictor_cache_info >=", value, "predictorCacheInfo");
            return (Criteria) this;
        }

        public Criteria andPredictorCacheInfoLessThan(String value) {
            addCriterion("predictor_cache_info <", value, "predictorCacheInfo");
            return (Criteria) this;
        }

        public Criteria andPredictorCacheInfoLessThanOrEqualTo(String value) {
            addCriterion("predictor_cache_info <=", value, "predictorCacheInfo");
            return (Criteria) this;
        }

        public Criteria andPredictorCacheInfoLike(String value) {
            addCriterion("predictor_cache_info like", value, "predictorCacheInfo");
            return (Criteria) this;
        }

        public Criteria andPredictorCacheInfoNotLike(String value) {
            addCriterion("predictor_cache_info not like", value, "predictorCacheInfo");
            return (Criteria) this;
        }

        public Criteria andPredictorCacheInfoIn(List<String> values) {
            addCriterion("predictor_cache_info in", values, "predictorCacheInfo");
            return (Criteria) this;
        }

        public Criteria andPredictorCacheInfoNotIn(List<String> values) {
            addCriterion("predictor_cache_info not in", values, "predictorCacheInfo");
            return (Criteria) this;
        }

        public Criteria andPredictorCacheInfoBetween(String value1, String value2) {
            addCriterion("predictor_cache_info between", value1, value2, "predictorCacheInfo");
            return (Criteria) this;
        }

        public Criteria andPredictorCacheInfoNotBetween(String value1, String value2) {
            addCriterion("predictor_cache_info not between", value1, value2, "predictorCacheInfo");
            return (Criteria) this;
        }

        public Criteria andLlmInfoIsNull() {
            addCriterion("llm_info is null");
            return (Criteria) this;
        }

        public Criteria andLlmInfoIsNotNull() {
            addCriterion("llm_info is not null");
            return (Criteria) this;
        }

        public Criteria andLlmInfoEqualTo(String value) {
            addCriterion("llm_info =", value, "llmInfo");
            return (Criteria) this;
        }

        public Criteria andLlmInfoNotEqualTo(String value) {
            addCriterion("llm_info <>", value, "llmInfo");
            return (Criteria) this;
        }

        public Criteria andLlmInfoGreaterThan(String value) {
            addCriterion("llm_info >", value, "llmInfo");
            return (Criteria) this;
        }

        public Criteria andLlmInfoGreaterThanOrEqualTo(String value) {
            addCriterion("llm_info >=", value, "llmInfo");
            return (Criteria) this;
        }

        public Criteria andLlmInfoLessThan(String value) {
            addCriterion("llm_info <", value, "llmInfo");
            return (Criteria) this;
        }

        public Criteria andLlmInfoLessThanOrEqualTo(String value) {
            addCriterion("llm_info <=", value, "llmInfo");
            return (Criteria) this;
        }

        public Criteria andLlmInfoLike(String value) {
            addCriterion("llm_info like", value, "llmInfo");
            return (Criteria) this;
        }

        public Criteria andLlmInfoNotLike(String value) {
            addCriterion("llm_info not like", value, "llmInfo");
            return (Criteria) this;
        }

        public Criteria andLlmInfoIn(List<String> values) {
            addCriterion("llm_info in", values, "llmInfo");
            return (Criteria) this;
        }

        public Criteria andLlmInfoNotIn(List<String> values) {
            addCriterion("llm_info not in", values, "llmInfo");
            return (Criteria) this;
        }

        public Criteria andLlmInfoBetween(String value1, String value2) {
            addCriterion("llm_info between", value1, value2, "llmInfo");
            return (Criteria) this;
        }

        public Criteria andLlmInfoNotBetween(String value1, String value2) {
            addCriterion("llm_info not between", value1, value2, "llmInfo");
            return (Criteria) this;
        }

        public Criteria andOctoInfoIsNull() {
            addCriterion("octo_info is null");
            return (Criteria) this;
        }

        public Criteria andOctoInfoIsNotNull() {
            addCriterion("octo_info is not null");
            return (Criteria) this;
        }

        public Criteria andOctoInfoEqualTo(String value) {
            addCriterion("octo_info =", value, "octoInfo");
            return (Criteria) this;
        }

        public Criteria andOctoInfoNotEqualTo(String value) {
            addCriterion("octo_info <>", value, "octoInfo");
            return (Criteria) this;
        }

        public Criteria andOctoInfoGreaterThan(String value) {
            addCriterion("octo_info >", value, "octoInfo");
            return (Criteria) this;
        }

        public Criteria andOctoInfoGreaterThanOrEqualTo(String value) {
            addCriterion("octo_info >=", value, "octoInfo");
            return (Criteria) this;
        }

        public Criteria andOctoInfoLessThan(String value) {
            addCriterion("octo_info <", value, "octoInfo");
            return (Criteria) this;
        }

        public Criteria andOctoInfoLessThanOrEqualTo(String value) {
            addCriterion("octo_info <=", value, "octoInfo");
            return (Criteria) this;
        }

        public Criteria andOctoInfoLike(String value) {
            addCriterion("octo_info like", value, "octoInfo");
            return (Criteria) this;
        }

        public Criteria andOctoInfoNotLike(String value) {
            addCriterion("octo_info not like", value, "octoInfo");
            return (Criteria) this;
        }

        public Criteria andOctoInfoIn(List<String> values) {
            addCriterion("octo_info in", values, "octoInfo");
            return (Criteria) this;
        }

        public Criteria andOctoInfoNotIn(List<String> values) {
            addCriterion("octo_info not in", values, "octoInfo");
            return (Criteria) this;
        }

        public Criteria andOctoInfoBetween(String value1, String value2) {
            addCriterion("octo_info between", value1, value2, "octoInfo");
            return (Criteria) this;
        }

        public Criteria andOctoInfoNotBetween(String value1, String value2) {
            addCriterion("octo_info not between", value1, value2, "octoInfo");
            return (Criteria) this;
        }

        public Criteria andOctoResourcesIsNull() {
            addCriterion("octo_resources is null");
            return (Criteria) this;
        }

        public Criteria andOctoResourcesIsNotNull() {
            addCriterion("octo_resources is not null");
            return (Criteria) this;
        }

        public Criteria andOctoResourcesEqualTo(String value) {
            addCriterion("octo_resources =", value, "octoResources");
            return (Criteria) this;
        }

        public Criteria andOctoResourcesNotEqualTo(String value) {
            addCriterion("octo_resources <>", value, "octoResources");
            return (Criteria) this;
        }

        public Criteria andOctoResourcesGreaterThan(String value) {
            addCriterion("octo_resources >", value, "octoResources");
            return (Criteria) this;
        }

        public Criteria andOctoResourcesGreaterThanOrEqualTo(String value) {
            addCriterion("octo_resources >=", value, "octoResources");
            return (Criteria) this;
        }

        public Criteria andOctoResourcesLessThan(String value) {
            addCriterion("octo_resources <", value, "octoResources");
            return (Criteria) this;
        }

        public Criteria andOctoResourcesLessThanOrEqualTo(String value) {
            addCriterion("octo_resources <=", value, "octoResources");
            return (Criteria) this;
        }

        public Criteria andOctoResourcesLike(String value) {
            addCriterion("octo_resources like", value, "octoResources");
            return (Criteria) this;
        }

        public Criteria andOctoResourcesNotLike(String value) {
            addCriterion("octo_resources not like", value, "octoResources");
            return (Criteria) this;
        }

        public Criteria andOctoResourcesIn(List<String> values) {
            addCriterion("octo_resources in", values, "octoResources");
            return (Criteria) this;
        }

        public Criteria andOctoResourcesNotIn(List<String> values) {
            addCriterion("octo_resources not in", values, "octoResources");
            return (Criteria) this;
        }

        public Criteria andOctoResourcesBetween(String value1, String value2) {
            addCriterion("octo_resources between", value1, value2, "octoResources");
            return (Criteria) this;
        }

        public Criteria andOctoResourcesNotBetween(String value1, String value2) {
            addCriterion("octo_resources not between", value1, value2, "octoResources");
            return (Criteria) this;
        }

        public Criteria andMlpModelResourcesIsNull() {
            addCriterion("mlp_model_resources is null");
            return (Criteria) this;
        }

        public Criteria andMlpModelResourcesIsNotNull() {
            addCriterion("mlp_model_resources is not null");
            return (Criteria) this;
        }

        public Criteria andMlpModelResourcesEqualTo(String value) {
            addCriterion("mlp_model_resources =", value, "mlpModelResources");
            return (Criteria) this;
        }

        public Criteria andMlpModelResourcesNotEqualTo(String value) {
            addCriterion("mlp_model_resources <>", value, "mlpModelResources");
            return (Criteria) this;
        }

        public Criteria andMlpModelResourcesGreaterThan(String value) {
            addCriterion("mlp_model_resources >", value, "mlpModelResources");
            return (Criteria) this;
        }

        public Criteria andMlpModelResourcesGreaterThanOrEqualTo(String value) {
            addCriterion("mlp_model_resources >=", value, "mlpModelResources");
            return (Criteria) this;
        }

        public Criteria andMlpModelResourcesLessThan(String value) {
            addCriterion("mlp_model_resources <", value, "mlpModelResources");
            return (Criteria) this;
        }

        public Criteria andMlpModelResourcesLessThanOrEqualTo(String value) {
            addCriterion("mlp_model_resources <=", value, "mlpModelResources");
            return (Criteria) this;
        }

        public Criteria andMlpModelResourcesLike(String value) {
            addCriterion("mlp_model_resources like", value, "mlpModelResources");
            return (Criteria) this;
        }

        public Criteria andMlpModelResourcesNotLike(String value) {
            addCriterion("mlp_model_resources not like", value, "mlpModelResources");
            return (Criteria) this;
        }

        public Criteria andMlpModelResourcesIn(List<String> values) {
            addCriterion("mlp_model_resources in", values, "mlpModelResources");
            return (Criteria) this;
        }

        public Criteria andMlpModelResourcesNotIn(List<String> values) {
            addCriterion("mlp_model_resources not in", values, "mlpModelResources");
            return (Criteria) this;
        }

        public Criteria andMlpModelResourcesBetween(String value1, String value2) {
            addCriterion("mlp_model_resources between", value1, value2, "mlpModelResources");
            return (Criteria) this;
        }

        public Criteria andMlpModelResourcesNotBetween(String value1, String value2) {
            addCriterion("mlp_model_resources not between", value1, value2, "mlpModelResources");
            return (Criteria) this;
        }

        public Criteria andFridayModelResourcesIsNull() {
            addCriterion("friday_model_resources is null");
            return (Criteria) this;
        }

        public Criteria andFridayModelResourcesIsNotNull() {
            addCriterion("friday_model_resources is not null");
            return (Criteria) this;
        }

        public Criteria andFridayModelResourcesEqualTo(String value) {
            addCriterion("friday_model_resources =", value, "fridayModelResources");
            return (Criteria) this;
        }

        public Criteria andFridayModelResourcesNotEqualTo(String value) {
            addCriterion("friday_model_resources <>", value, "fridayModelResources");
            return (Criteria) this;
        }

        public Criteria andFridayModelResourcesGreaterThan(String value) {
            addCriterion("friday_model_resources >", value, "fridayModelResources");
            return (Criteria) this;
        }

        public Criteria andFridayModelResourcesGreaterThanOrEqualTo(String value) {
            addCriterion("friday_model_resources >=", value, "fridayModelResources");
            return (Criteria) this;
        }

        public Criteria andFridayModelResourcesLessThan(String value) {
            addCriterion("friday_model_resources <", value, "fridayModelResources");
            return (Criteria) this;
        }

        public Criteria andFridayModelResourcesLessThanOrEqualTo(String value) {
            addCriterion("friday_model_resources <=", value, "fridayModelResources");
            return (Criteria) this;
        }

        public Criteria andFridayModelResourcesLike(String value) {
            addCriterion("friday_model_resources like", value, "fridayModelResources");
            return (Criteria) this;
        }

        public Criteria andFridayModelResourcesNotLike(String value) {
            addCriterion("friday_model_resources not like", value, "fridayModelResources");
            return (Criteria) this;
        }

        public Criteria andFridayModelResourcesIn(List<String> values) {
            addCriterion("friday_model_resources in", values, "fridayModelResources");
            return (Criteria) this;
        }

        public Criteria andFridayModelResourcesNotIn(List<String> values) {
            addCriterion("friday_model_resources not in", values, "fridayModelResources");
            return (Criteria) this;
        }

        public Criteria andFridayModelResourcesBetween(String value1, String value2) {
            addCriterion("friday_model_resources between", value1, value2, "fridayModelResources");
            return (Criteria) this;
        }

        public Criteria andFridayModelResourcesNotBetween(String value1, String value2) {
            addCriterion("friday_model_resources not between", value1, value2, "fridayModelResources");
            return (Criteria) this;
        }

        public Criteria andReporterInfoIsNull() {
            addCriterion("reporter_info is null");
            return (Criteria) this;
        }

        public Criteria andReporterInfoIsNotNull() {
            addCriterion("reporter_info is not null");
            return (Criteria) this;
        }

        public Criteria andReporterInfoEqualTo(String value) {
            addCriterion("reporter_info =", value, "reporterInfo");
            return (Criteria) this;
        }

        public Criteria andReporterInfoNotEqualTo(String value) {
            addCriterion("reporter_info <>", value, "reporterInfo");
            return (Criteria) this;
        }

        public Criteria andReporterInfoGreaterThan(String value) {
            addCriterion("reporter_info >", value, "reporterInfo");
            return (Criteria) this;
        }

        public Criteria andReporterInfoGreaterThanOrEqualTo(String value) {
            addCriterion("reporter_info >=", value, "reporterInfo");
            return (Criteria) this;
        }

        public Criteria andReporterInfoLessThan(String value) {
            addCriterion("reporter_info <", value, "reporterInfo");
            return (Criteria) this;
        }

        public Criteria andReporterInfoLessThanOrEqualTo(String value) {
            addCriterion("reporter_info <=", value, "reporterInfo");
            return (Criteria) this;
        }

        public Criteria andReporterInfoLike(String value) {
            addCriterion("reporter_info like", value, "reporterInfo");
            return (Criteria) this;
        }

        public Criteria andReporterInfoNotLike(String value) {
            addCriterion("reporter_info not like", value, "reporterInfo");
            return (Criteria) this;
        }

        public Criteria andReporterInfoIn(List<String> values) {
            addCriterion("reporter_info in", values, "reporterInfo");
            return (Criteria) this;
        }

        public Criteria andReporterInfoNotIn(List<String> values) {
            addCriterion("reporter_info not in", values, "reporterInfo");
            return (Criteria) this;
        }

        public Criteria andReporterInfoBetween(String value1, String value2) {
            addCriterion("reporter_info between", value1, value2, "reporterInfo");
            return (Criteria) this;
        }

        public Criteria andReporterInfoNotBetween(String value1, String value2) {
            addCriterion("reporter_info not between", value1, value2, "reporterInfo");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andRunTimeIsNull() {
            addCriterion("run_time is null");
            return (Criteria) this;
        }

        public Criteria andRunTimeIsNotNull() {
            addCriterion("run_time is not null");
            return (Criteria) this;
        }

        public Criteria andRunTimeEqualTo(String value) {
            addCriterion("run_time =", value, "runTime");
            return (Criteria) this;
        }

        public Criteria andRunTimeNotEqualTo(String value) {
            addCriterion("run_time <>", value, "runTime");
            return (Criteria) this;
        }

        public Criteria andRunTimeGreaterThan(String value) {
            addCriterion("run_time >", value, "runTime");
            return (Criteria) this;
        }

        public Criteria andRunTimeGreaterThanOrEqualTo(String value) {
            addCriterion("run_time >=", value, "runTime");
            return (Criteria) this;
        }

        public Criteria andRunTimeLessThan(String value) {
            addCriterion("run_time <", value, "runTime");
            return (Criteria) this;
        }

        public Criteria andRunTimeLessThanOrEqualTo(String value) {
            addCriterion("run_time <=", value, "runTime");
            return (Criteria) this;
        }

        public Criteria andRunTimeLike(String value) {
            addCriterion("run_time like", value, "runTime");
            return (Criteria) this;
        }

        public Criteria andRunTimeNotLike(String value) {
            addCriterion("run_time not like", value, "runTime");
            return (Criteria) this;
        }

        public Criteria andRunTimeIn(List<String> values) {
            addCriterion("run_time in", values, "runTime");
            return (Criteria) this;
        }

        public Criteria andRunTimeNotIn(List<String> values) {
            addCriterion("run_time not in", values, "runTime");
            return (Criteria) this;
        }

        public Criteria andRunTimeBetween(String value1, String value2) {
            addCriterion("run_time between", value1, value2, "runTime");
            return (Criteria) this;
        }

        public Criteria andRunTimeNotBetween(String value1, String value2) {
            addCriterion("run_time not between", value1, value2, "runTime");
            return (Criteria) this;
        }

        public Criteria andExtraInfoIsNull() {
            addCriterion("extra_info is null");
            return (Criteria) this;
        }

        public Criteria andExtraInfoIsNotNull() {
            addCriterion("extra_info is not null");
            return (Criteria) this;
        }

        public Criteria andExtraInfoEqualTo(String value) {
            addCriterion("extra_info =", value, "extraInfo");
            return (Criteria) this;
        }

        public Criteria andExtraInfoNotEqualTo(String value) {
            addCriterion("extra_info <>", value, "extraInfo");
            return (Criteria) this;
        }

        public Criteria andExtraInfoGreaterThan(String value) {
            addCriterion("extra_info >", value, "extraInfo");
            return (Criteria) this;
        }

        public Criteria andExtraInfoGreaterThanOrEqualTo(String value) {
            addCriterion("extra_info >=", value, "extraInfo");
            return (Criteria) this;
        }

        public Criteria andExtraInfoLessThan(String value) {
            addCriterion("extra_info <", value, "extraInfo");
            return (Criteria) this;
        }

        public Criteria andExtraInfoLessThanOrEqualTo(String value) {
            addCriterion("extra_info <=", value, "extraInfo");
            return (Criteria) this;
        }

        public Criteria andExtraInfoLike(String value) {
            addCriterion("extra_info like", value, "extraInfo");
            return (Criteria) this;
        }

        public Criteria andExtraInfoNotLike(String value) {
            addCriterion("extra_info not like", value, "extraInfo");
            return (Criteria) this;
        }

        public Criteria andExtraInfoIn(List<String> values) {
            addCriterion("extra_info in", values, "extraInfo");
            return (Criteria) this;
        }

        public Criteria andExtraInfoNotIn(List<String> values) {
            addCriterion("extra_info not in", values, "extraInfo");
            return (Criteria) this;
        }

        public Criteria andExtraInfoBetween(String value1, String value2) {
            addCriterion("extra_info between", value1, value2, "extraInfo");
            return (Criteria) this;
        }

        public Criteria andExtraInfoNotBetween(String value1, String value2) {
            addCriterion("extra_info not between", value1, value2, "extraInfo");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("creator is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("creator is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(String value) {
            addCriterion("creator =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(String value) {
            addCriterion("creator <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(String value) {
            addCriterion("creator >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(String value) {
            addCriterion("creator >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(String value) {
            addCriterion("creator <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(String value) {
            addCriterion("creator <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLike(String value) {
            addCriterion("creator like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotLike(String value) {
            addCriterion("creator not like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<String> values) {
            addCriterion("creator in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<String> values) {
            addCriterion("creator not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(String value1, String value2) {
            addCriterion("creator between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(String value1, String value2) {
            addCriterion("creator not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}