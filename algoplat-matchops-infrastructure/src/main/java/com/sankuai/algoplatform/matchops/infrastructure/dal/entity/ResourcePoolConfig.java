package com.sankuai.algoplatform.matchops.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: resource_pool_config
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResourcePoolConfig {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: tenant
     *   说明: 租户
     */
    private String tenant;

    /**
     *   字段: project_name
     *   说明: 项目名称
     */
    private String projectName;

    /**
     *   字段: queue
     *   说明: 队列
     */
    private String queue;

    /**
     *   字段: resource_type
     *   说明: 资源类型（L40,A100）
     */
    private String resourceType;

    /**
     *   字段: quota_limit
     *   说明: 配额上限
     */
    private Integer quotaLimit;

    /**
     *   字段: operator_mis
     *   说明: 操作人id
     */
    private String operatorMis;

    /**
     *   字段: status
     *   说明: 状态（0：正常，-1禁用）
     */
    private Integer status;

    /**
     *   字段: add_time
     *   说明: 添加时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}