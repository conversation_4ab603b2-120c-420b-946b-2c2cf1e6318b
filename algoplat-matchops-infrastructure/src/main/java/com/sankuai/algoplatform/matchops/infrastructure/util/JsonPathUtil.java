package com.sankuai.algoplatform.matchops.infrastructure.util;

import com.alibaba.fastjson.JSON;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.PathNotFoundException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class JsonPathUtil {

    /**
     * 从JSON字符串中提取指定路径的字符串值
     *
     * @param json JSON字符串
     * @param path JSONPath表达式
     * @return 提取的字符串值，若路径不存在则返回空字符串
     */
    public static String extractString(String json, String path) {
        try {
            if (StringUtils.equals("*", path)) {
                return json;//如果为*，则返回全部
            }
            return JsonPath.parse(json).read(path, String.class);
        } catch (PathNotFoundException e) {
            log.error("JsonPathUtil.extractString error, path: {} not found in json", path, e);
            return "";
        }
    }

//    public static void main(String[] args) {
//        System.out.println(extractString("{\"a\": \"b\"}", "$.a"));
//        System.out.println(extractString("{\n" +
//                "    \"code\": 0,\n" +
//                "    \"column\": [\n" +
//                "        \"total\",\n" +
//                "        \"cache_cnt\"\n" +
//                "    ],\n" +
//                "    \"data\": [\n" +
//                "        [\n" +
//                "            \"1153256\",\n" +
//                "            \"655371\"\n" +
//                "        ]\n" +
//                "    ],\n" +
//                "    \"message\": \"success\"\n" +
//                "}\n", "$.data[0][1]"));
//
//        System.out.println(extractString("[\n" +
//                "    {\n" +
//                "        \"algoBizCode\": \"test\",\n" +
//                "        \"algoStrategyAbtest\": {\n" +
//                "            \"strategyName\": \"deal_match\",\n" +
//                "            \"distributions\": [\n" +
//                "                {\n" +
//                "                    \"quota\": 100,\n" +
//                "                    \"algoPackage\": {\n" +
//                "                        \"runtime\": \"PYTHON37\",\n" +
//                "                        \"version\": \"c6a6c6ae690\",\n" +
//                "                        \"codeRepo\": \"ssh://*******************/dcgstgy/algoplat-package.git\",\n" +
//                "                        \"modulePath\": \"barbecue_realtime_code_muti_kinds\"\n" +
//                "                    },\n" +
//                "                    \"entrancePath\": \"deal_match_main_lanconic_multi_version_online\",\n" +
//                "                    \"entranceMethod\": \"calculate\"\n" +
//                "                }\n" +
//                "            ]\n" +
//                "        }\n" +
//                "    }\n" +
//                "]", "$[0].algoStrategyAbtest.distributions[0].quota"));
//
//        Map<String, Triple<Double, Double, Double>> map = new HashMap<>();
//        map.put("aaa", Triple.of(12.2, 12.2, 123.9));
//        System.out.println(JSON.toJSONString(map));
//        System.out.println(DateUtil.toDateTimeString_yyyyMMddHHmm(new Date()));
//    }
}
