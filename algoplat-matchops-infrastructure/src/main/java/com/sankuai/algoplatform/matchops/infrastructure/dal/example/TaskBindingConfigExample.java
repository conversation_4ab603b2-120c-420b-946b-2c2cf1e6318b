package com.sankuai.algoplatform.matchops.infrastructure.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TaskBindingConfigExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public TaskBindingConfigExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andScheduleServiceIdIsNull() {
            addCriterion("schedule_service_id is null");
            return (Criteria) this;
        }

        public Criteria andScheduleServiceIdIsNotNull() {
            addCriterion("schedule_service_id is not null");
            return (Criteria) this;
        }

        public Criteria andScheduleServiceIdEqualTo(Long value) {
            addCriterion("schedule_service_id =", value, "scheduleServiceId");
            return (Criteria) this;
        }

        public Criteria andScheduleServiceIdNotEqualTo(Long value) {
            addCriterion("schedule_service_id <>", value, "scheduleServiceId");
            return (Criteria) this;
        }

        public Criteria andScheduleServiceIdGreaterThan(Long value) {
            addCriterion("schedule_service_id >", value, "scheduleServiceId");
            return (Criteria) this;
        }

        public Criteria andScheduleServiceIdGreaterThanOrEqualTo(Long value) {
            addCriterion("schedule_service_id >=", value, "scheduleServiceId");
            return (Criteria) this;
        }

        public Criteria andScheduleServiceIdLessThan(Long value) {
            addCriterion("schedule_service_id <", value, "scheduleServiceId");
            return (Criteria) this;
        }

        public Criteria andScheduleServiceIdLessThanOrEqualTo(Long value) {
            addCriterion("schedule_service_id <=", value, "scheduleServiceId");
            return (Criteria) this;
        }

        public Criteria andScheduleServiceIdIn(List<Long> values) {
            addCriterion("schedule_service_id in", values, "scheduleServiceId");
            return (Criteria) this;
        }

        public Criteria andScheduleServiceIdNotIn(List<Long> values) {
            addCriterion("schedule_service_id not in", values, "scheduleServiceId");
            return (Criteria) this;
        }

        public Criteria andScheduleServiceIdBetween(Long value1, Long value2) {
            addCriterion("schedule_service_id between", value1, value2, "scheduleServiceId");
            return (Criteria) this;
        }

        public Criteria andScheduleServiceIdNotBetween(Long value1, Long value2) {
            addCriterion("schedule_service_id not between", value1, value2, "scheduleServiceId");
            return (Criteria) this;
        }

        public Criteria andScheduleTaskIdIsNull() {
            addCriterion("schedule_task_id is null");
            return (Criteria) this;
        }

        public Criteria andScheduleTaskIdIsNotNull() {
            addCriterion("schedule_task_id is not null");
            return (Criteria) this;
        }

        public Criteria andScheduleTaskIdEqualTo(Long value) {
            addCriterion("schedule_task_id =", value, "scheduleTaskId");
            return (Criteria) this;
        }

        public Criteria andScheduleTaskIdNotEqualTo(Long value) {
            addCriterion("schedule_task_id <>", value, "scheduleTaskId");
            return (Criteria) this;
        }

        public Criteria andScheduleTaskIdGreaterThan(Long value) {
            addCriterion("schedule_task_id >", value, "scheduleTaskId");
            return (Criteria) this;
        }

        public Criteria andScheduleTaskIdGreaterThanOrEqualTo(Long value) {
            addCriterion("schedule_task_id >=", value, "scheduleTaskId");
            return (Criteria) this;
        }

        public Criteria andScheduleTaskIdLessThan(Long value) {
            addCriterion("schedule_task_id <", value, "scheduleTaskId");
            return (Criteria) this;
        }

        public Criteria andScheduleTaskIdLessThanOrEqualTo(Long value) {
            addCriterion("schedule_task_id <=", value, "scheduleTaskId");
            return (Criteria) this;
        }

        public Criteria andScheduleTaskIdIn(List<Long> values) {
            addCriterion("schedule_task_id in", values, "scheduleTaskId");
            return (Criteria) this;
        }

        public Criteria andScheduleTaskIdNotIn(List<Long> values) {
            addCriterion("schedule_task_id not in", values, "scheduleTaskId");
            return (Criteria) this;
        }

        public Criteria andScheduleTaskIdBetween(Long value1, Long value2) {
            addCriterion("schedule_task_id between", value1, value2, "scheduleTaskId");
            return (Criteria) this;
        }

        public Criteria andScheduleTaskIdNotBetween(Long value1, Long value2) {
            addCriterion("schedule_task_id not between", value1, value2, "scheduleTaskId");
            return (Criteria) this;
        }

        public Criteria andResourcePoolIdsIsNull() {
            addCriterion("resource_pool_ids is null");
            return (Criteria) this;
        }

        public Criteria andResourcePoolIdsIsNotNull() {
            addCriterion("resource_pool_ids is not null");
            return (Criteria) this;
        }

        public Criteria andResourcePoolIdsEqualTo(String value) {
            addCriterion("resource_pool_ids =", value, "resourcePoolIds");
            return (Criteria) this;
        }

        public Criteria andResourcePoolIdsNotEqualTo(String value) {
            addCriterion("resource_pool_ids <>", value, "resourcePoolIds");
            return (Criteria) this;
        }

        public Criteria andResourcePoolIdsGreaterThan(String value) {
            addCriterion("resource_pool_ids >", value, "resourcePoolIds");
            return (Criteria) this;
        }

        public Criteria andResourcePoolIdsGreaterThanOrEqualTo(String value) {
            addCriterion("resource_pool_ids >=", value, "resourcePoolIds");
            return (Criteria) this;
        }

        public Criteria andResourcePoolIdsLessThan(String value) {
            addCriterion("resource_pool_ids <", value, "resourcePoolIds");
            return (Criteria) this;
        }

        public Criteria andResourcePoolIdsLessThanOrEqualTo(String value) {
            addCriterion("resource_pool_ids <=", value, "resourcePoolIds");
            return (Criteria) this;
        }

        public Criteria andResourcePoolIdsLike(String value) {
            addCriterion("resource_pool_ids like", value, "resourcePoolIds");
            return (Criteria) this;
        }

        public Criteria andResourcePoolIdsNotLike(String value) {
            addCriterion("resource_pool_ids not like", value, "resourcePoolIds");
            return (Criteria) this;
        }

        public Criteria andResourcePoolIdsIn(List<String> values) {
            addCriterion("resource_pool_ids in", values, "resourcePoolIds");
            return (Criteria) this;
        }

        public Criteria andResourcePoolIdsNotIn(List<String> values) {
            addCriterion("resource_pool_ids not in", values, "resourcePoolIds");
            return (Criteria) this;
        }

        public Criteria andResourcePoolIdsBetween(String value1, String value2) {
            addCriterion("resource_pool_ids between", value1, value2, "resourcePoolIds");
            return (Criteria) this;
        }

        public Criteria andResourcePoolIdsNotBetween(String value1, String value2) {
            addCriterion("resource_pool_ids not between", value1, value2, "resourcePoolIds");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andOperatorMisIsNull() {
            addCriterion("operator_mis is null");
            return (Criteria) this;
        }

        public Criteria andOperatorMisIsNotNull() {
            addCriterion("operator_mis is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorMisEqualTo(String value) {
            addCriterion("operator_mis =", value, "operatorMis");
            return (Criteria) this;
        }

        public Criteria andOperatorMisNotEqualTo(String value) {
            addCriterion("operator_mis <>", value, "operatorMis");
            return (Criteria) this;
        }

        public Criteria andOperatorMisGreaterThan(String value) {
            addCriterion("operator_mis >", value, "operatorMis");
            return (Criteria) this;
        }

        public Criteria andOperatorMisGreaterThanOrEqualTo(String value) {
            addCriterion("operator_mis >=", value, "operatorMis");
            return (Criteria) this;
        }

        public Criteria andOperatorMisLessThan(String value) {
            addCriterion("operator_mis <", value, "operatorMis");
            return (Criteria) this;
        }

        public Criteria andOperatorMisLessThanOrEqualTo(String value) {
            addCriterion("operator_mis <=", value, "operatorMis");
            return (Criteria) this;
        }

        public Criteria andOperatorMisLike(String value) {
            addCriterion("operator_mis like", value, "operatorMis");
            return (Criteria) this;
        }

        public Criteria andOperatorMisNotLike(String value) {
            addCriterion("operator_mis not like", value, "operatorMis");
            return (Criteria) this;
        }

        public Criteria andOperatorMisIn(List<String> values) {
            addCriterion("operator_mis in", values, "operatorMis");
            return (Criteria) this;
        }

        public Criteria andOperatorMisNotIn(List<String> values) {
            addCriterion("operator_mis not in", values, "operatorMis");
            return (Criteria) this;
        }

        public Criteria andOperatorMisBetween(String value1, String value2) {
            addCriterion("operator_mis between", value1, value2, "operatorMis");
            return (Criteria) this;
        }

        public Criteria andOperatorMisNotBetween(String value1, String value2) {
            addCriterion("operator_mis not between", value1, value2, "operatorMis");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}