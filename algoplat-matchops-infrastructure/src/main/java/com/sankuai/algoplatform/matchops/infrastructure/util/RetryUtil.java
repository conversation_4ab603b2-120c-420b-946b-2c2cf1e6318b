package com.sankuai.algoplatform.matchops.infrastructure.util;

import java.util.function.BiFunction;

public class RetryUtil {

    public static <R> R retry(Supplier<R> supplier, BiFunction<R, Exception, Boolean> getNeedRetry,
                              int maxAttempts, long delay) {
        Exception ex = null;
        R res = null;
        for (int i = 0; i < maxAttempts; i++) {
            // 执行
            try {
                res = supplier.get();
                ex = null;
            } catch (Exception e) {
                ex = e;
                res = null;
            }
            // 是否需要重试
            boolean needRetry = getNeedRetry.apply(res, ex);
            if (!needRetry) {
                return res;
            }
            // 重试时长(最后一次不等待)
            if (i < maxAttempts - 1) {
                try {
                    Thread.sleep(delay);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
        if (ex != null) {
            throw new RuntimeException(ex);
        }
        return res;
    }

    @FunctionalInterface
    public interface Supplier<T> {
        T get() throws Exception;
    }
}
