package com.sankuai.algoplatform.matchops.infrastructure.proxy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.util.AuthUtil;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.algoplatform.matchops.infrastructure.config.KmsConfigs;
import com.sankuai.algoplatform.matchops.infrastructure.model.LionConfigReq;
import com.sankuai.algoplatform.matchops.infrastructure.model.LionConfigResp;
import com.sankuai.algoplatform.matchops.infrastructure.util.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 参考：https://km.sankuai.com/collabpage/383601114
 * API：https://sc.sankuai.com/#/wheel/appDocs/lion-api/com.dianping.lion.api.controller.ConfigAPIV3Controller_restful/com.dianping.lion.api.controller.ConfigAPIV3Controller.setKey(java.lang.String%252C%2520java.lang.String%252C%2520com.dianping.lion.api.model.form.ConfigSetForm)?id=4210&timestamp=1732424650204
 */
@Slf4j
public class LionConfigService {

    final static String HOST_ONLINE = "http://lion.vip.sankuai.com";

    final static String HOST_OFFLINE = "http://lion-api.inf.test.sankuai.com";

    final static String queryKeyFmt = "/v3/configs/envs/%s/appkeys/%s/keys/%s";

    final static String updateKeyFmt = "/v3/configs/envs/%s/appkeys/%s/key";

    final static String USERNAME = "bml_match_test_tool";

    final static String PASSWORD = KmsConfigs.getLionPassword();


    public static String queryLionConfig(LionConfigReq configSet) {
        handleEnv(configSet);
        String uri = String.format(queryKeyFmt, configSet.getEnv(), configSet.getAppkey(), configSet.getKey());
        Map<String, String> header = buildHeader(uri, "GET");

        String url = buildUrl(configSet.getEnv(), uri);
        String respStr = HttpUtil.httpGet(url, null, header);
        LionConfigResp resp = JSONObject.parseObject(respStr, LionConfigResp.class);
        if (!resp.success()) {
            log.error("lion set fail. req:{}, resp:{}", JSON.toJSONString(configSet), JSON.toJSONString(resp));
            throw new RuntimeException("lion set fail." + resp.getMessage());
        }
        return resp.getResult().get("value");
    }


    public static void setLionConfig(LionConfigReq configSet) {
        handleEnv(configSet);
        String uri = String.format(updateKeyFmt, configSet.getEnv(), configSet.getAppkey());
        Map<String, String> header = buildHeader(uri, "POST");

        String url = buildUrl(configSet.getEnv(), uri);
        String respStr = HttpUtil.httpPostJson(url, JSON.toJSONString(configSet), header);
        LionConfigResp resp = JSONObject.parseObject(respStr, LionConfigResp.class);
        if (!resp.success()) {
            log.error("lion set fail. req:{}, resp:{}", JSON.toJSONString(configSet), JSON.toJSONString(resp));
            throw new RuntimeException("lion set fail." + resp.getMessage());
        }
    }


    private static String buildUrl(String env, String url) {
        if (StringUtils.equals(env, "prod") || StringUtils.equals(env, "staging")) {
            url = HOST_ONLINE + url;
        } else {
            url = HOST_OFFLINE + url;
        }
        return url;
    }

    private static Map<String, String> buildHeader(String uri, String method) {
        String date = AuthUtil.getAuthDate(new Date());
        String auth = AuthUtil.getAuthorization(uri, method, date, USERNAME, PASSWORD);
        Map<String, String> header = new HashMap<>();
        header.put("Authorization", auth);
        header.put("Date", date);
        return header;
    }

    private static void handleEnv(LionConfigReq configSet) {
        if (MdpContextUtils.isOfflineEnv()) {
            configSet.setSet(null);
        } else {
            configSet.setSwimlane(null);
        }
    }

}
