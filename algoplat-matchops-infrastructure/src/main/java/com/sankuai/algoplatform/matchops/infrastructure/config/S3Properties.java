package com.sankuai.algoplatform.matchops.infrastructure.config;

import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.inf.octo.mns.model.HostEnv;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

@Data
@Slf4j
@Configuration
public class S3Properties {
    private String accessKey;

    private String secretKey;


    @Value("${s3.bucketName}")
    private String bucketName;

    @Value("${s3.hostname}")
    private String hostname;


    private static final String KMS_S3PLUS_SERVICE_ACCESS_KEY = "s3plus_service_access_key";
    private static final String KMS_S3PLUS_SERVICE_SECRET_KEY = "s3plus_service_secret_key";


    @PostConstruct
    private void initKeys() throws KmsResultNullException {
        this.accessKey = Kms.getByName(MdpContextUtils.getAppKey(), KMS_S3PLUS_SERVICE_ACCESS_KEY);
        this.secretKey = Kms.getByName(MdpContextUtils.getAppKey(), KMS_S3PLUS_SERVICE_SECRET_KEY);
    }

}
