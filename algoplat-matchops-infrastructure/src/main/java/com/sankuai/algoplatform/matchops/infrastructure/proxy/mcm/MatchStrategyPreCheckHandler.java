//package com.sankuai.algoplatform.matchops.infrastructure.proxy.mcm;
//
//import com.sankuai.mcm.client.sdk.annotation.McmComponent;
//import com.sankuai.mcm.client.sdk.context.handler.PreCheckHandler;
//import com.sankuai.mcm.client.sdk.context.handler.PreCheckHandlerItem;
//import com.sankuai.mcm.client.sdk.context.handler.PreCheckHandlerResult;
//import com.sankuai.mcm.client.sdk.context.handler.PreCheckRequest;
//import lombok.extern.slf4j.Slf4j;
//
//import java.util.ArrayList;
//import java.util.List;
//
//
//@McmComponent(events = {"UpdateAlgoCodePackage"})
//@Slf4j
//public class MatchStrategyPreCheckHandler implements PreCheckHandler {
//
//    @Override
//    public PreCheckHandlerResult preCheck(PreCheckRequest preCheckRequest) {
//        PreCheckHandlerResult result = new PreCheckHandlerResult();
//        PreCheckHandlerItem item;
//        List<PreCheckHandlerItem> list = new ArrayList<>();
//
////        TTrafficRequest req = (TTrafficRequest) request.getArgs()[0];
////        if (sceneService.getSceneById(req.getSceneId()) == null || promptService.getPromptPoById(req.getPromptId()) == null) {
////            list.add(new PreCheckHandlerItem("场景Id或promptId错误"));
////            return new PreCheckHandlerResult(false, list, Decision.REJECT, request.getEventContext());
////        }
////        if (promptService.getAuditingPrompt(req.getSceneId()).size() != 0) {
////            list.add(new PreCheckHandlerItem("存在正在进行的变更"));
////            return new PreCheckHandlerResult(false, list, Decision.REJECT, request.getEventContext());
////        }
//
//        result.setItems(list);
//        return null;
//    }
//}
