package com.sankuai.algoplatform.matchops.infrastructure.util;

import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.sankuai.algoplatform.matchops.infrastructure.monitor.RaptorTrack;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

public class ThreadPoolFactory {
    private static final int CORE_SIZE = Runtime.getRuntime().availableProcessors();
    private ThreadPoolFactory() {
    }

    static class CallerRunsPolicy implements RejectedExecutionHandler {
        private String name;

        public CallerRunsPolicy(String name) {
            this.name = name;
        }

        public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
            if (!e.isShutdown()) {
                RaptorTrack.Sys_ThreadPoolRejectNum.report("ThreadPoolFactory." + this.name + ".CallerRunsPolicy.run");
                r.run();
            }
        }
    }

    private static ThreadPool invokeTaskThreadPool = Rhino.newThreadPool("invoke_task_thread_pool",
            DefaultThreadPoolProperties.Setter()
                    .withCoreSize(8).withMaxSize(128).withMaxQueueSize(16)
                    .withRejectHandler(new CallerRunsPolicy("excelReportThreadPool")));

    private static ThreadPool taskToolTaskEnvThreadPool = Rhino.newThreadPool("task_tool_task_env_thread_pool",
            DefaultThreadPoolProperties.Setter()
                    .withCoreSize(8).withMaxSize(128).withMaxQueueSize(16)
                    .withRejectHandler(new CallerRunsPolicy("taskToolTaskEnvThreadPool")));

    private static ThreadPool taskToolTaskRunThreadPool = Rhino.newThreadPool("task_tool_task_run_thread_pool",
            DefaultThreadPoolProperties.Setter()
                    .withCoreSize(8).withMaxSize(128).withMaxQueueSize(16)
                    .withRejectHandler(new CallerRunsPolicy("taskToolTaskRunThreadPool")));

    private static ThreadPool templateQueryThreadPool = Rhino.newThreadPool("task_tool_template_query_thread_pool",
            DefaultThreadPoolProperties.Setter()
                    .withCoreSize(8).withMaxSize(128).withMaxQueueSize(16)
                    .withRejectHandler(new CallerRunsPolicy("templateQueryThreadPool")));

    private static ThreadPool generateReporterThreadPool = Rhino.newThreadPool("task_tool_generate_reporter_thread_pool",
            DefaultThreadPoolProperties.Setter()
                    .withCoreSize(8).withMaxSize(128).withMaxQueueSize(16)
                    .withRejectHandler(new CallerRunsPolicy("generateReporterThreadPool")));

    private static ThreadPool runTestThreadPool = Rhino.newThreadPool("task_tool_run_test_thread_pool",
            DefaultThreadPoolProperties.Setter()
                    .withCoreSize(8).withMaxSize(128).withMaxQueueSize(16)
                    .withRejectHandler(new CallerRunsPolicy("runTestThreadPool")));

    private static ThreadPool offlineTaskIncrDataHandleThreadPool = Rhino.newThreadPool("offline_task_incr_data_handle_thread_pool",
            DefaultThreadPoolProperties.Setter()
                    .withCoreSize(CORE_SIZE).withMaxSize(CORE_SIZE + 1)
                    .withMaxQueueSize(16)
                    .withRejectHandler(new CallerRunsPolicy("offlineTaskIncrDataHandleThreadPool")));

    private static ThreadPool offlineTaskIncrDataStatisticThreadPool = Rhino.newThreadPool("offline_task_incr_data_statistic_thread_pool",
            DefaultThreadPoolProperties.Setter()
                    .withCoreSize(CORE_SIZE).withMaxSize(CORE_SIZE + 1)
                    .withMaxQueueSize(16)
                    .withRejectHandler(new CallerRunsPolicy("offlineTaskIncrDataStatisticThreadPool")));

    private static ThreadPool offlineTaskFullDataScanThreadPool = Rhino.newThreadPool("offline_task_full_data_scan_thread_pool",
            DefaultThreadPoolProperties.Setter()
                    .withCoreSize(CORE_SIZE * 2).withMaxSize(CORE_SIZE * 2 + 1)
                    .withMaxQueueSize(16)
                    .withRejectHandler(new CallerRunsPolicy("offlineTaskFullDataScanThreadPool")));
    public static ThreadPoolExecutor getInvokeTaskThreadPool() {
        return invokeTaskThreadPool.getExecutor();
    }
    public static ThreadPoolExecutor getRunTestThreadPool() {
        return runTestThreadPool.getExecutor();
    }

    public static ThreadPoolExecutor getTaskEnvThreadPool() {
        return taskToolTaskEnvThreadPool.getExecutor();
    }


    public static ThreadPoolExecutor getTaskRunThreadPool() {
        return taskToolTaskRunThreadPool.getExecutor();
    }

    public static ThreadPoolExecutor getTemplateQueryThreadPool() {
        return templateQueryThreadPool.getExecutor();
    }

    public static ThreadPoolExecutor getGenerateReporterThreadPool() {
        return generateReporterThreadPool.getExecutor();
    }
    public static ThreadPoolExecutor getOfflineTaskIncrDataHandleThreadPool() {
        return offlineTaskIncrDataHandleThreadPool.getExecutor();
    }
    public static ThreadPoolExecutor getOfflineTaskIncrDataStatisticThreadPool() {
        return offlineTaskIncrDataStatisticThreadPool.getExecutor();
    }
    public static ThreadPoolExecutor getOfflineTaskFullDataScanThreadPool() {
        return offlineTaskFullDataScanThreadPool.getExecutor();
    }
}
