package com.sankuai.algoplatform.matchops.infrastructure.proxy.mcm.custom;


import com.google.common.collect.Lists;
import com.sankuai.algoplatform.matchops.infrastructure.config.LionConfig;
import com.sankuai.mcm.client.sdk.annotation.McmComponent;
import com.sankuai.mcm.client.sdk.context.customconfig.CustomConfigProviderAdaptor;
import com.sankuai.mcm.client.sdk.context.customconfig.CustomConfigProviderRequest;
import com.sankuai.mcm.client.sdk.context.handler.AuditConfig;
import com.sankuai.mcm.client.sdk.context.handler.AuditNode;
import com.sankuai.mcm.client.sdk.dto.common.*;
import com.sankuai.mcm.client.sdk.enums.AuditApproveType;
import com.sankuai.mcm.client.sdk.enums.AuditorType;
import com.sankuai.mcm.client.sdk.enums.OnEvent;

import java.util.ArrayList;
import java.util.List;

/**
 * MCM自定义配置提供者
 */
@McmComponent(events = "CreateNotice")
public class CreateNoticeCustomConfigProvider extends CustomConfigProviderAdaptor {
    @Override
    public AuditConfig getAuditConfig(CustomConfigProviderRequest request) {
        // 审核节点，可配置多个
        AuditNode node = new AuditNode();
        node.setName("上线周知审核");
        node.setApproveType(AuditApproveType.OR);
        // 通过审核人类型来设置审核人
        List<AuditorType> auditorTypes = Lists.newArrayList(AuditorType.USER_LEADER);
        node.setCustomAuditors(auditorTypes);
        // 当审核人为空时的备份审核人
        node.setBackupAuditors(LionConfig.DEFAULT_BACKUP_AUDITORS);

        // 审核链路
        List<AuditNode> configs = Lists.newArrayList();
        configs.add(node);

        AuditConfig auditConfig = new AuditConfig();
        auditConfig.setConfigs(configs);

        //配置审核链路排除审核发起人，在发起审核构建审核链路时就会过滤发起人
        auditConfig.setExcludeCreator(true);

        return auditConfig;
    }
    //自定义审核周知
    @Override
    public NoticeConfig getAuditNoticeConfig(CustomConfigProviderRequest request) {
        List<NoticeMsgItemConfig> items = new ArrayList<>();
        NoticeMsgItemConfig msgItemConfig = NoticeMsgItemConfig.builder()
                // 自定义key
                .title(request.getEventContext().getEventName())
                // 自定义value
                .content("上线周知")
                .build();
        items.add(msgItemConfig);

        NoticeMsgConfig noticeMsgConfig = NoticeMsgConfig.builder()
                .content("消息自定义周知内容")
                .eventName("匹配策略上线")
                .accountName("匹配测试工具")
                .items(items)
                .build();

        // item可以有多个，不同的时机，周知不同的对象
        NoticeConfigItem item = new NoticeConfigItem();
        // 周知时机
        item.setOn(Lists.newArrayList(OnEvent.AFTER_AUDIT_ACCEPT));

        return NoticeConfig.builder()
                // 自定义周知消息
                .msgConfig(noticeMsgConfig)
                .configs(Lists.newArrayList(item))
                .build();
    }
    //自定义变更内容
    @Override
    public ChangeConfig getChangeConfig(CustomConfigProviderRequest request) {
        ChangeConfig changeConfig = new ChangeConfig();
        // 使用HTML格式来实现换行和格式化
        StringBuilder content = new StringBuilder();
        content.append("<div style='font-family: Arial, sans-serif; line-height: 1.6;'>");
        content.append("<h2 style='color: #1890ff; margin-bottom: 20px;'>美团安全生产准则 - 六要两不要</h2>");

        // 六要部分
        content.append("<h3 style='color: #52c41a; margin-top: 20px; margin-bottom: 15px;'>六要</h3>");
        content.append("<div style='margin-left: 20px;'>");
        content.append("<p><strong>1. 要测试</strong><br/>变更前要测试，场景要完备</p>");
        content.append("<p><strong>2. 要周知</strong><br/>变更前要周知，范围要全面</p>");
        content.append("<p><strong>3. 要审核</strong><br/>变更前要审核，方案要全面</p>");
        content.append("<p><strong>4. 要灰度</strong><br/>变更要灰度，影响范围要可控</p>");
        content.append("<p><strong>5. 要观测</strong><br/>变更结果要可观测，期间及时查看监控和日志</p>");
        content.append("<p><strong>6. 要可回滚</strong><br/>变更要有可快速执行的回滚方案</p>");
        content.append("</div>");

        // 两不要部分
        content.append("<h3 style='color: #ff4d4f; margin-top: 20px; margin-bottom: 15px;'>两不要</h3>");
        content.append("<div style='margin-left: 20px;'>");
        content.append("<p><strong>1. 不要瞒报故障</strong></p>");
        content.append("<p><strong>2. 不要违规变更线上数据</strong></p>");
        content.append("</div>");

        content.append("</div>");

        changeConfig.setContent(content.toString());
        return changeConfig;
    }
}