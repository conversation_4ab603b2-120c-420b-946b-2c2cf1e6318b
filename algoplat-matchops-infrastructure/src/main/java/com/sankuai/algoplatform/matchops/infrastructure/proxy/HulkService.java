package com.sankuai.algoplatform.matchops.infrastructure.proxy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.sankuai.algoplatform.matchops.infrastructure.util.HttpUtil;
import com.sankuai.inf.octo.mns.model.HostEnv;
import com.sankuai.inf.octo.mns.util.ProcessInfoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Map;

@Slf4j
@Service
public class HulkService {
    @Value("${hulk.auth-token}")
    private String authToken;
    @Value("${hulk.operatorId}")
    private String operatorId;

    /**
     * 增加弹性实例
     *
     * @param groupId     弹性组ID
     * @param instanceNum 扩容实例数
     * @param retainHour  保留时长，单位小时
     * @return 扩容任务是否提交成功
     */
    public Pair<Boolean, String> addInstance(String groupId, int instanceNum, int retainHour) {
        Pair<Boolean, String> res = null;
        try {
            res = doManualInitialize(groupId, instanceNum, retainHour);
            if (res != null) {
                return res;
            }
            res = doManualAdd(groupId, instanceNum, retainHour);
        } finally {
            log.info("扩容任务提交: groupId:{}, instanceNum:{}, retainHour:{}, res:{}", groupId, instanceNum, retainHour, JSON.toJSONString(res));
        }
        return res;
    }

    private Pair<Boolean, String> doManualInitialize(String groupId, Integer instanceNum, int retainHour) {
        String url = String.format("%s/bannerapi/strategy/manual/group/%s", getApiUrl(ProcessInfoUtil.getHostEnv()), groupId);
        Map<String, String> header = ImmutableMap.of("auth-token", authToken,
                "operatorId", operatorId,
                "Accept", "application/json");

        String resp = HttpUtil.httpGet(url, Collections.emptyMap(), header);
        JSONObject json = JSON.parseObject(resp);
        if (json.getInteger("code") != 0 || json.getJSONObject("data") == null
                || !StringUtils.equals(json.getString("message"), "success")) {
            return Pair.of(false, String.format("扩容任务提交失败，返回：%s,%s", json.getInteger("code"), json.getString("message")));
        }
        Map<String, Object> initParams = json.getObject("data", new TypeReference<Map<String, Object>>() {
        });
        if (instanceNum > ((Number) initParams.get("quota")).intValue()) {
            return Pair.of(false, String.format("扩容任务提交失败，扩容实例数：%s，超出弹性组配额：%s",
                    instanceNum, ((Number) initParams.get("quota")).intValue()));
        }
        if (instanceNum > ((Number) initParams.get("maxScaleOutNum")).intValue()) {
            return Pair.of(false, String.format("扩容任务提交失败，扩容实例数：%s，超出可扩至机器数：%s",
                    instanceNum, ((Number) initParams.get("quota")).intValue()));
        }
        if (retainHour > ((Number) initParams.get("maxRetainTime")).intValue()) {
            return Pair.of(false, String.format("扩容任务提交失败，保留时长：%s，超出最大保留时长：%s",
                    retainHour, ((Number) initParams.get("maxRetainTime")).intValue()));
        }
        return null;
    }

    private Pair<Boolean, String> doManualAdd(String groupId, Integer instanceNum, int retainHour) {
        String url = String.format("%s/bannerapi/strategy/manual/add", getApiUrl(ProcessInfoUtil.getHostEnv()));
        Map<String, String> header = ImmutableMap.of("auth-token", authToken,
                "operatorId", operatorId,
                "Accept", "application/json");
        Map<String, Object> body = ImmutableMap.of(
                "groupId", groupId,
                "expectNum", instanceNum,
                "scaleIn", true,
                "retainTime", retainHour,
                "reason", String.format("提交扩容任务申请，扩容实例数：%s，保留时长：%s", instanceNum, retainHour));
        String resp = HttpUtil.httpPostJson(url, JSON.toJSONString(body), header);
        JSONObject json = JSON.parseObject(resp);
        if (json.getInteger("code") != 0 || json.getJSONObject("data") == null
                || !StringUtils.equals(json.getString("message"), "success")) {
            return Pair.of(false, String.format("扩容任务提交失败，返回：%s,%s", json.getInteger("code"), json.getString("message")));
        }
        return Pair.of(true, String.format("扩容任务提交成功，扩容实例数：%s，保留时长：%s:%s",
                json.getInteger("optNum"), json.getString("strategyStartTime"), json.getString("strategyEndTime")));
    }

    private String getApiUrl(HostEnv env) {
        switch (env) {
            case PROD:
            case STAGING:
                return "http://bannerapi.inf.vip.sankuai.com";
            case TEST:
                return "http://bannerapi.inf.test.sankuai.com";
            case DEV:
                return "http://bannerapi.inf.dev.sankuai.com";
            default:
                throw new IllegalArgumentException("invalid environment: " + env);
        }
    }
}
