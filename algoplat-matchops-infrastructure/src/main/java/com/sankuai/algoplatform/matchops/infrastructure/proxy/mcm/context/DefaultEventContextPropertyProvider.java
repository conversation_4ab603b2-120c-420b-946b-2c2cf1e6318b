package com.sankuai.algoplatform.matchops.infrastructure.proxy.mcm.context;

import com.sankuai.algoplatform.matchops.infrastructure.util.ContextUtil;
import com.sankuai.mcm.client.sdk.annotation.McmComponent;
import com.sankuai.mcm.client.sdk.context.eventcontext.EventContextPropertyProviderAdaptor;
import com.sankuai.mcm.client.sdk.context.eventcontext.EventContextPropertyProviderRequest;
import com.sankuai.mcm.client.sdk.dto.common.UserIdentity;

import java.util.HashMap;
import java.util.Map;

@McmComponent
public class DefaultEventContextPropertyProvider extends EventContextPropertyProviderAdaptor {
    @Override
    public UserIdentity getUserIdentity(EventContextPropertyProviderRequest request) {
        return UserIdentity.ofUser(ContextUtil.getLoginUserMis());
    }

    @Override
    public Map<String, Object> getExtraInfo(EventContextPropertyProviderRequest request) {
        Map<String, Object> extraInfo = new HashMap<>();
        extraInfo.put("before", "before");
        extraInfo.put("after", request);
        return extraInfo;
    }
}