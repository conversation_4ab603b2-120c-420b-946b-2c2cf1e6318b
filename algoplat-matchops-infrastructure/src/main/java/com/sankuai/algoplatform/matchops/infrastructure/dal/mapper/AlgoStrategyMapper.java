package com.sankuai.algoplatform.matchops.infrastructure.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.AlgoStrategy;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.AlgoStrategyExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AlgoStrategyMapper extends MybatisBaseMapper<AlgoStrategy, AlgoStrategyExample, Long> {
    int batchInsert(@Param("list") List<AlgoStrategy> list);
}