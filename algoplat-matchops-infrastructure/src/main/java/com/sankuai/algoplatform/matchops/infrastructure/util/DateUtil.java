package com.sankuai.algoplatform.matchops.infrastructure.util;

import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

public class DateUtil {

    public static final DateTimeFormatter uploadDateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'");

    public static Date parseDate(String dateStr) {
        try {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(dateStr);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    public static Long parseDate2Stamp(String dateStr) {
        try {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(dateStr).getTime();
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    public static String toDateTimeString(Date date) {
        if (date == null) {
            return "";
        }
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date);
    }

    public static String toDateTimeShortString(Date date) {
        return new SimpleDateFormat("yyyyMMddHHmmss").format(date);
    }

    public static String toDateTimeString_yyyyMMddHH(Date date) {
        return new SimpleDateFormat("yyyyMMddHH").format(date);
    }

    public static String toDateTimeString_yyyyMMddHHmm(Date date) {
        return new SimpleDateFormat("yyyyMMddHHmm").format(date);
    }

    public static String toDateTimeString_HHmm(Date date) {
        return new SimpleDateFormat("HH:mm").format(date);
    }

    public static String toDateTimeString_yyyyMMdd(Date date) {
        return new SimpleDateFormat("yyyyMMdd").format(date);
    }

    public static Date parseDateByShortStr(String dateStr) {
        try {
            return new SimpleDateFormat("yyyyMMddHHmmss").parse(dateStr);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    public static Date parseDate_yyyyMMddHH(String dateStr) {
        try {
            return new SimpleDateFormat("yyyyMMddHH").parse(dateStr);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    public static String toDateString(Date date) {
        return new SimpleDateFormat("yyyy-MM-dd").format(date);
    }


    public static List<String> getDateRangeString(Date beforeTime, Date afterTime) {
        List<String> dates = new ArrayList<>();
        Date currentTime = DateUtils.truncate(beforeTime, Calendar.DAY_OF_MONTH);
        while (!currentTime.after(DateUtils.truncate(afterTime, Calendar.DAY_OF_MONTH))) {
            dates.add(DateUtil.toDateString(currentTime));
            currentTime = DateUtils.addDays(currentTime, 1);
        }
        return dates;
    }

    public static LocalDateTime convertToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        return LocalDateTime.ofInstant(instant, zoneId);
    }

    public static Date convertToDate(LocalDateTime localDateTime) {
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zonedDateTime = localDateTime.atZone(zoneId);
        Instant instant = zonedDateTime.toInstant();
        return Date.from(instant);
    }

    public static String convertLocalDateTimeToDateTimeString(LocalDateTime localDateTime) {
        Date date = convertToDate(localDateTime);
        return toDateTimeString(date);
    }

    public static long calMinuteDiff(LocalDateTime startTime, LocalDateTime endTime) {
        return Duration.between(startTime, endTime).toMinutes();
    }

    public static long calMillSecondDiff(LocalDateTime startTime, LocalDateTime endTime) {
        return Duration.between(startTime, endTime).toMillis();
    }

    public static long calSecondDiff(LocalDateTime startTime, LocalDateTime endTime) {
        long millis = Duration.between(startTime, endTime).toMillis();
        return millis / 1000;
    }

    public static List<Pair<Date, Date>> splitTimeByHour(Date startDate, Date endDate) {

        // 解析输入时间
        LocalDateTime startTime = convertToLocalDateTime(startDate);
        LocalDateTime endTime = convertToLocalDateTime(endDate);

        List<Pair<Date, Date>> timeIntervals = new ArrayList<>();

        // 计算每个小时的区间
        while (!startTime.isAfter(endTime)) {
            LocalDateTime intervalEndTime = startTime.withMinute(59).withSecond(59);

            // 如果当前区间的结束时间超过了实际的结束时间，调整为实际的结束时间
            if (intervalEndTime.isAfter(endTime)) {
                intervalEndTime = endTime;
            }

            // 格式化并添加当前小时区间
            startTime = startTime.withMinute(0).withSecond(0);
            timeIntervals.add(Pair.of(convertToDate(startTime), convertToDate(intervalEndTime)));

            // 增加一个小时，设置分钟和秒为0
            startTime = startTime.plusHours(1).withMinute(0).withSecond(0);
        }

        return timeIntervals;
    }

    public static long calMillSecondDiff(Date startDate, Date endDate) {
        return calMillSecondDiff(convertToLocalDateTime(startDate), convertToLocalDateTime(endDate));
    }

    public static long calSecondDiff(Date startDate, Date endDate) {
        return calSecondDiff(convertToLocalDateTime(startDate), convertToLocalDateTime(endDate));
    }

    public static LocalDateTime getPlusDay(int day) {
        return LocalDateTime.now().plusDays(day);
    }

    public static String getAddNumDay(int num, DateTimeFormatter formatter) {
        LocalDateTime dateTime = getPlusDay(num);
        return dateTime.format(formatter);
    }
}
