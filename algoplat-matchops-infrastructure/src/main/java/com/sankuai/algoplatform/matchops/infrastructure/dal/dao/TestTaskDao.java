package com.sankuai.algoplatform.matchops.infrastructure.dal.dao;

import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestSubTask;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestTask;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.TestSubTaskExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.TestTaskExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.TestTaskMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class TestTaskDao {

    @Resource
    TestTaskMapper testTaskMapper;

    public List<TestTask> selectByPageAndBizLineId(int page, int pageSize, long bizLineId) {
        TestTaskExample example = new TestTaskExample();
        example.limit((page - 1) * pageSize, pageSize);
        example.createCriteria()
                .andBizLineIdEqualTo(bizLineId);
        return testTaskMapper.selectByExample(example);
    }

    public Long selectCountByBizLineId(long bizLineId) {
        TestTaskExample example = new TestTaskExample();
        example.createCriteria().andBizLineIdEqualTo(bizLineId);
        return testTaskMapper.countByExample(example);
    }

    public TestTask selectById(long id) {
        try {
            return testTaskMapper.selectByPrimaryKey(id);
        } finally {
            ZebraForceMasterHelper.clearLocalContext();
        }
    }

    public TestTask selectByName(String taskName, long bizLineId) {
        try {
            TestTaskExample example = new TestTaskExample();
            example.createCriteria().andBizLineIdEqualTo(bizLineId).andNameEqualTo(taskName);
            List<TestTask> testTasks = testTaskMapper.selectByExample(example);
            return CollectionUtils.isEmpty(testTasks) ? null : testTasks.get(0);
        } finally {
            ZebraForceMasterHelper.clearLocalContext();
        }
    }

    public List<TestTask> selectByStatus(int status) {
        TestTaskExample example = new TestTaskExample();
        example.createCriteria().andStatusEqualTo(status);
        return testTaskMapper.selectByExample(example);
    }


    public Long insert(TestTask testTask) {
        testTaskMapper.insertSelective(testTask);
        return testTask.getId();
    }

    public Long update(TestTask testTask) {
        testTaskMapper.updateByPrimaryKeySelective(testTask);
        return testTask.getId();
    }

    public Long updateTestTaskStatus(Long testTaskId, int status) {
        TestTask testTask = new TestTask();
        testTask.setId(testTaskId);
        testTask.setStatus(status);
        testTaskMapper.updateByPrimaryKeySelective(testTask);
        return testTask.getId();
    }

    public List<TestTask> selectByMatchStrategyIds(List<Long> matchStrategyIds) {
        TestTaskExample example = new TestTaskExample();
        example.createCriteria().andMatchStrategyIdIn(matchStrategyIds);
        return testTaskMapper.selectByExample(example);
    }

}
