package com.sankuai.algoplatform.matchops.infrastructure.dal.dao;

import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.AlgoStrategy;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.BizStrategy;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.AlgoStrategyExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.BizStrategyExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.AlgoStrategyMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service

public class AlgoStrategyDao {
    @Autowired
    AlgoStrategyMapper algoStrategyMapper;

    public AlgoStrategy selectById(Long id) {
        return algoStrategyMapper.selectByPrimaryKey(id);
    }

    public List<AlgoStrategy> selectByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        AlgoStrategyExample example = new AlgoStrategyExample();
        example.createCriteria().andIdIn(ids);
        return algoStrategyMapper.selectByExample(example);
    }

    public int update(AlgoStrategy algoStrategy) {
        return algoStrategyMapper.updateByPrimaryKeySelective(algoStrategy);
    }

    public Long insert(AlgoStrategy algoStrategy) {
        algoStrategyMapper.insertSelective(algoStrategy);
        return algoStrategy.getId();
    }

    public List<AlgoStrategy> selectByBizLineIdAndEnv(Long bizLineId, Integer env) {
        AlgoStrategyExample example = new AlgoStrategyExample();
        example.createCriteria().andBizLineIdEqualTo(bizLineId)
                .andEnvEqualTo(env)
                .andStatusEqualTo(0);
        return algoStrategyMapper.selectByExample(example);
    }

    public int softDelete(Long id, String mis) {
        AlgoStrategy algoStrategy = selectById(id);
        if (algoStrategy != null) {
            algoStrategy.setStatus(-1);
            algoStrategy.setOwner(mis);
            return update(algoStrategy);
        }
        return 0;
    }
}
