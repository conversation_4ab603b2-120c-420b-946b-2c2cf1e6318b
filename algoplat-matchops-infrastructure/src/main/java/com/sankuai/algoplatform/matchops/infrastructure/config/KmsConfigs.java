package com.sankuai.algoplatform.matchops.infrastructure.config;

import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

@Slf4j
public class KmsConfigs {

    private static final String appKey = "com.sankuai.algoplatform.matchops";

    private static final String TALOS_KEY = "talos.password";

    private static final String XM_SECRET = "xm.secret";

    private static final String LION_PASSWORD = "lion.password";

    public static String getTalosKey() {
        return getKmsValueByKey(TALOS_KEY);
    }

    public static String getXmSecret() {
        return getKmsValueByKey(XM_SECRET);
    }

    public static String getLionPassword() {
        return getKmsValueByKey(LION_PASSWORD);
    }

    public static String getKmsValueByKey(String key) {
        try {
            return Kms.getByName(appKey, key);
        } catch (KmsResultNullException e) {
            log.error("获取KMS秘钥失败", e);
        }
        return "";
    }
}
