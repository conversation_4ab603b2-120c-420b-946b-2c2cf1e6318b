package com.sankuai.algoplatform.matchops.infrastructure.monitor;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.Map;
import java.util.function.Supplier;

public class RaptorTrack {
    /**
     * 异常逻辑
     */
    public static Item Sys_UnexpectedVisitNum = params -> logEvent("Sys_UnexpectedVisitNum", params);

    /**
     * 内部错误
     */
    public static Item Sys_InternalErrorNum = params -> logEvent("Sys_InternalErrorNum", params);

    /**
     * 线程池任务溢出
     */
    public static Item Sys_ThreadPoolRejectNum = params -> logEvent("Sys_ThreadPoolRejectNum", params);


    /**
     * GPU调度成功Event打点
     */
    public static Item Sys_GpuSchedule_SuccessEvent = params -> logEvent("Sys_GpuSchedule_SuccessEvent", params);

    /**
     * GPU调度失败Event打点
     */
    public static Item Sys_GpuSchedule_FailEvent = params -> logEvent("Sys_GpuSchedule_FailEvent", params);

    /**
     * 资源池不足
     */
    public static Item Sys_GpuSchedule_InsufficientResourcePool = params -> logEvent("Sys_GpuSchedule_InsufficientResourcePool", params);

    public static Logger logger = LoggerFactory.getLogger(RaptorTrack.class);


    private static void logEvent(String type, Object... params) {
        switch (params.length) {
            case 1: {
                Cat.logEvent(type, String.valueOf(params[0]));
                return;
            }
            case 2: {
                for (int i = 0; i < ((Number) params[1]).intValue(); i++) {
                    Cat.logEvent(type, String.valueOf(params[0]));
                }
                return;
            }
            case 3: {
                Cat.logEvent(type, String.valueOf(params[0]), String.valueOf(params[1]), null);
                return;
            }
            default: {
            }
        }
    }

    public static <T> T take(String type, String name, Supplier<T> metric) {
        return take(type, name, Collections.emptyMap(), metric);
    }

    public static <T> T take(String type, String name, Map<String, Object> appends, Supplier<T> metric) {
        Transaction t = Cat.newTransaction(type, name);
        if (MapUtils.isNotEmpty(appends)) {
            appends.forEach(t::addData);
        }
        try {
            T r = metric.get();
            t.setSuccessStatus();
            return r;
        } catch (Exception e) {
            t.setStatus(e);
            throw e;
        } finally {
            t.complete();
        }
    }


    @FunctionalInterface
    public interface Item {
        void report(Object... params);
    }

}
