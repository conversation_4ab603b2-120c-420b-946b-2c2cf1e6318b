package com.sankuai.algoplatform.matchops.infrastructure.dal.blade.dao;

import com.google.common.collect.Lists;
import com.sankuai.algoplatform.matchops.infrastructure.dal.blade.entity.OfflineTaskDetail;
import com.sankuai.algoplatform.matchops.infrastructure.dal.blade.mapper.OfflineTaskMapper;
import com.sankuai.algoplatform.matchops.infrastructure.monitor.RaptorTrack;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class OfflineTaskDao {
    @Autowired
    private OfflineTaskMapper offlineTaskMapper;

    public List<OfflineTaskDetail> getTaskByRange(Long start, Long end, String partitionDate, String industryType, String taskName) {
        return offlineTaskMapper.selectByRange(start, end, partitionDate,  industryType, taskName);
    }
    public Long getMaxIdByStatus(String partitionDate, OfflineTaskDetail.TaskState state, String industryType, String taskName) {
        if(state == null){
            return offlineTaskMapper.getMaxIdByStatus(partitionDate, null, null, industryType, taskName);
        }
        return offlineTaskMapper.getMaxIdByStatus(partitionDate, state.getStatus(), state.getResultFlag(), industryType, taskName);
    }
    public Long getMinIdByStatus(String partitionDate, OfflineTaskDetail.TaskState state, String industryType, String taskName) {
        if(state == null){
            return offlineTaskMapper.getMinIdByStatus(partitionDate, null, null, industryType, taskName);
        }
        return offlineTaskMapper.getMinIdByStatus(partitionDate, state.getStatus(), state.getResultFlag(), industryType, taskName);
    }
    public String getLatestPartitionDate(String industryType, String taskName){
        return offlineTaskMapper.getLatestPartitionDate(industryType, taskName);
    }
    public void updateBatchByUniqueKey(List<OfflineTaskDetail> list, String partitionDate, String industryType, String taskName) {
        offlineTaskMapper.updateBatchByUniqueKey(list, partitionDate, industryType, taskName);
    }
    public long getCountByCondition(OfflineTaskDetail taskDetail, String industryType, String taskName){
        return offlineTaskMapper.countByCondition(taskDetail, industryType, taskName);
    }
    public List<OfflineTaskDetail> selectByCondition(OfflineTaskDetail taskDetail, Long startId, Integer limit, String industryType, String taskName){
        return offlineTaskMapper.selectByCondition(taskDetail, startId, limit, industryType, taskName);
    }
}
