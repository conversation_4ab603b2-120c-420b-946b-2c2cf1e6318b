package com.sankuai.algoplatform.matchops.infrastructure.util;



import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class CommonUtils {

    public static long msToMin(long timeMs) {
        return timeMs / (1000 * 60);
    }

    public static List<String> splitString(String source, String delim){
        List<String> list = new ArrayList<>();
        if(StringUtils.isEmpty(source)) return list;

        String[] splitRes = source.split(delim);
        for(String seg: splitRes){
            if(StringUtils.isEmpty(seg)) continue;
            list.add(seg);
        }
        return list;
    }

}
