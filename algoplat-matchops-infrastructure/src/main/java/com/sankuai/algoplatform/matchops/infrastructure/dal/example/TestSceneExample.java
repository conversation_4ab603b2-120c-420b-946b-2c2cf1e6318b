package com.sankuai.algoplatform.matchops.infrastructure.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TestSceneExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public TestSceneExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public TestSceneExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public TestSceneExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public TestSceneExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBizLineIdIsNull() {
            addCriterion("biz_line_id is null");
            return (Criteria) this;
        }

        public Criteria andBizLineIdIsNotNull() {
            addCriterion("biz_line_id is not null");
            return (Criteria) this;
        }

        public Criteria andBizLineIdEqualTo(Long value) {
            addCriterion("biz_line_id =", value, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdNotEqualTo(Long value) {
            addCriterion("biz_line_id <>", value, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdGreaterThan(Long value) {
            addCriterion("biz_line_id >", value, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdGreaterThanOrEqualTo(Long value) {
            addCriterion("biz_line_id >=", value, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdLessThan(Long value) {
            addCriterion("biz_line_id <", value, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdLessThanOrEqualTo(Long value) {
            addCriterion("biz_line_id <=", value, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdIn(List<Long> values) {
            addCriterion("biz_line_id in", values, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdNotIn(List<Long> values) {
            addCriterion("biz_line_id not in", values, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdBetween(Long value1, Long value2) {
            addCriterion("biz_line_id between", value1, value2, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andBizLineIdNotBetween(Long value1, Long value2) {
            addCriterion("biz_line_id not between", value1, value2, "bizLineId");
            return (Criteria) this;
        }

        public Criteria andTestSceneNameIsNull() {
            addCriterion("test_scene_name is null");
            return (Criteria) this;
        }

        public Criteria andTestSceneNameIsNotNull() {
            addCriterion("test_scene_name is not null");
            return (Criteria) this;
        }

        public Criteria andTestSceneNameEqualTo(String value) {
            addCriterion("test_scene_name =", value, "testSceneName");
            return (Criteria) this;
        }

        public Criteria andTestSceneNameNotEqualTo(String value) {
            addCriterion("test_scene_name <>", value, "testSceneName");
            return (Criteria) this;
        }

        public Criteria andTestSceneNameGreaterThan(String value) {
            addCriterion("test_scene_name >", value, "testSceneName");
            return (Criteria) this;
        }

        public Criteria andTestSceneNameGreaterThanOrEqualTo(String value) {
            addCriterion("test_scene_name >=", value, "testSceneName");
            return (Criteria) this;
        }

        public Criteria andTestSceneNameLessThan(String value) {
            addCriterion("test_scene_name <", value, "testSceneName");
            return (Criteria) this;
        }

        public Criteria andTestSceneNameLessThanOrEqualTo(String value) {
            addCriterion("test_scene_name <=", value, "testSceneName");
            return (Criteria) this;
        }

        public Criteria andTestSceneNameLike(String value) {
            addCriterion("test_scene_name like", value, "testSceneName");
            return (Criteria) this;
        }

        public Criteria andTestSceneNameNotLike(String value) {
            addCriterion("test_scene_name not like", value, "testSceneName");
            return (Criteria) this;
        }

        public Criteria andTestSceneNameIn(List<String> values) {
            addCriterion("test_scene_name in", values, "testSceneName");
            return (Criteria) this;
        }

        public Criteria andTestSceneNameNotIn(List<String> values) {
            addCriterion("test_scene_name not in", values, "testSceneName");
            return (Criteria) this;
        }

        public Criteria andTestSceneNameBetween(String value1, String value2) {
            addCriterion("test_scene_name between", value1, value2, "testSceneName");
            return (Criteria) this;
        }

        public Criteria andTestSceneNameNotBetween(String value1, String value2) {
            addCriterion("test_scene_name not between", value1, value2, "testSceneName");
            return (Criteria) this;
        }

        public Criteria andReqPostTypeIsNull() {
            addCriterion("req_post_type is null");
            return (Criteria) this;
        }

        public Criteria andReqPostTypeIsNotNull() {
            addCriterion("req_post_type is not null");
            return (Criteria) this;
        }

        public Criteria andReqPostTypeEqualTo(Integer value) {
            addCriterion("req_post_type =", value, "reqPostType");
            return (Criteria) this;
        }

        public Criteria andReqPostTypeNotEqualTo(Integer value) {
            addCriterion("req_post_type <>", value, "reqPostType");
            return (Criteria) this;
        }

        public Criteria andReqPostTypeGreaterThan(Integer value) {
            addCriterion("req_post_type >", value, "reqPostType");
            return (Criteria) this;
        }

        public Criteria andReqPostTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("req_post_type >=", value, "reqPostType");
            return (Criteria) this;
        }

        public Criteria andReqPostTypeLessThan(Integer value) {
            addCriterion("req_post_type <", value, "reqPostType");
            return (Criteria) this;
        }

        public Criteria andReqPostTypeLessThanOrEqualTo(Integer value) {
            addCriterion("req_post_type <=", value, "reqPostType");
            return (Criteria) this;
        }

        public Criteria andReqPostTypeIn(List<Integer> values) {
            addCriterion("req_post_type in", values, "reqPostType");
            return (Criteria) this;
        }

        public Criteria andReqPostTypeNotIn(List<Integer> values) {
            addCriterion("req_post_type not in", values, "reqPostType");
            return (Criteria) this;
        }

        public Criteria andReqPostTypeBetween(Integer value1, Integer value2) {
            addCriterion("req_post_type between", value1, value2, "reqPostType");
            return (Criteria) this;
        }

        public Criteria andReqPostTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("req_post_type not between", value1, value2, "reqPostType");
            return (Criteria) this;
        }

        public Criteria andReqPostLinkIsNull() {
            addCriterion("req_post_link is null");
            return (Criteria) this;
        }

        public Criteria andReqPostLinkIsNotNull() {
            addCriterion("req_post_link is not null");
            return (Criteria) this;
        }

        public Criteria andReqPostLinkEqualTo(String value) {
            addCriterion("req_post_link =", value, "reqPostLink");
            return (Criteria) this;
        }

        public Criteria andReqPostLinkNotEqualTo(String value) {
            addCriterion("req_post_link <>", value, "reqPostLink");
            return (Criteria) this;
        }

        public Criteria andReqPostLinkGreaterThan(String value) {
            addCriterion("req_post_link >", value, "reqPostLink");
            return (Criteria) this;
        }

        public Criteria andReqPostLinkGreaterThanOrEqualTo(String value) {
            addCriterion("req_post_link >=", value, "reqPostLink");
            return (Criteria) this;
        }

        public Criteria andReqPostLinkLessThan(String value) {
            addCriterion("req_post_link <", value, "reqPostLink");
            return (Criteria) this;
        }

        public Criteria andReqPostLinkLessThanOrEqualTo(String value) {
            addCriterion("req_post_link <=", value, "reqPostLink");
            return (Criteria) this;
        }

        public Criteria andReqPostLinkLike(String value) {
            addCriterion("req_post_link like", value, "reqPostLink");
            return (Criteria) this;
        }

        public Criteria andReqPostLinkNotLike(String value) {
            addCriterion("req_post_link not like", value, "reqPostLink");
            return (Criteria) this;
        }

        public Criteria andReqPostLinkIn(List<String> values) {
            addCriterion("req_post_link in", values, "reqPostLink");
            return (Criteria) this;
        }

        public Criteria andReqPostLinkNotIn(List<String> values) {
            addCriterion("req_post_link not in", values, "reqPostLink");
            return (Criteria) this;
        }

        public Criteria andReqPostLinkBetween(String value1, String value2) {
            addCriterion("req_post_link between", value1, value2, "reqPostLink");
            return (Criteria) this;
        }

        public Criteria andReqPostLinkNotBetween(String value1, String value2) {
            addCriterion("req_post_link not between", value1, value2, "reqPostLink");
            return (Criteria) this;
        }

        public Criteria andExtraIsNull() {
            addCriterion("extra is null");
            return (Criteria) this;
        }

        public Criteria andExtraIsNotNull() {
            addCriterion("extra is not null");
            return (Criteria) this;
        }

        public Criteria andExtraEqualTo(String value) {
            addCriterion("extra =", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraNotEqualTo(String value) {
            addCriterion("extra <>", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraGreaterThan(String value) {
            addCriterion("extra >", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraGreaterThanOrEqualTo(String value) {
            addCriterion("extra >=", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraLessThan(String value) {
            addCriterion("extra <", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraLessThanOrEqualTo(String value) {
            addCriterion("extra <=", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraLike(String value) {
            addCriterion("extra like", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraNotLike(String value) {
            addCriterion("extra not like", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraIn(List<String> values) {
            addCriterion("extra in", values, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraNotIn(List<String> values) {
            addCriterion("extra not in", values, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraBetween(String value1, String value2) {
            addCriterion("extra between", value1, value2, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraNotBetween(String value1, String value2) {
            addCriterion("extra not between", value1, value2, "extra");
            return (Criteria) this;
        }

        public Criteria andOwnerIsNull() {
            addCriterion("owner is null");
            return (Criteria) this;
        }

        public Criteria andOwnerIsNotNull() {
            addCriterion("owner is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerEqualTo(String value) {
            addCriterion("owner =", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotEqualTo(String value) {
            addCriterion("owner <>", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerGreaterThan(String value) {
            addCriterion("owner >", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerGreaterThanOrEqualTo(String value) {
            addCriterion("owner >=", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerLessThan(String value) {
            addCriterion("owner <", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerLessThanOrEqualTo(String value) {
            addCriterion("owner <=", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerLike(String value) {
            addCriterion("owner like", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotLike(String value) {
            addCriterion("owner not like", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerIn(List<String> values) {
            addCriterion("owner in", values, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotIn(List<String> values) {
            addCriterion("owner not in", values, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerBetween(String value1, String value2) {
            addCriterion("owner between", value1, value2, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotBetween(String value1, String value2) {
            addCriterion("owner not between", value1, value2, "owner");
            return (Criteria) this;
        }

        public Criteria andIsDelIsNull() {
            addCriterion("is_del is null");
            return (Criteria) this;
        }

        public Criteria andIsDelIsNotNull() {
            addCriterion("is_del is not null");
            return (Criteria) this;
        }

        public Criteria andIsDelEqualTo(Boolean value) {
            addCriterion("is_del =", value, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelNotEqualTo(Boolean value) {
            addCriterion("is_del <>", value, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelGreaterThan(Boolean value) {
            addCriterion("is_del >", value, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_del >=", value, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelLessThan(Boolean value) {
            addCriterion("is_del <", value, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelLessThanOrEqualTo(Boolean value) {
            addCriterion("is_del <=", value, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelIn(List<Boolean> values) {
            addCriterion("is_del in", values, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelNotIn(List<Boolean> values) {
            addCriterion("is_del not in", values, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelBetween(Boolean value1, Boolean value2) {
            addCriterion("is_del between", value1, value2, "isDel");
            return (Criteria) this;
        }

        public Criteria andIsDelNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_del not between", value1, value2, "isDel");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}