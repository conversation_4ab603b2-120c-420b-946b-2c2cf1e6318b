package com.sankuai.algoplatform.matchops.infrastructure.dal.dao;

import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.MatchStrategy;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ResourceGroup;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ResourceGroup;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.MatchStrategyExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.ResourceGroupExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.ResourceGroupExample;
import com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.ResourceGroupMapper;
import com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.ResourceGroupMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service

public class ResourceGroupDao {
    @Autowired
    ResourceGroupMapper resourceGroupMapper;

    public List<ResourceGroup> selectAllNotDelete() {
        ResourceGroupExample example = new ResourceGroupExample();
        example.createCriteria().andIsDelEqualTo(false);
        return resourceGroupMapper.selectByExample(example);
    }

    public List<ResourceGroup> selectAllByBizLineId(Long bizLineId) {
        ResourceGroupExample example = new ResourceGroupExample();
        example.createCriteria().andBizLineIdEqualTo(bizLineId).andIsDelEqualTo(false);
        return resourceGroupMapper.selectByExample(example);
    }

    public ResourceGroup selectById(Long id) {
        return resourceGroupMapper.selectByPrimaryKey(id);
    }

    public List<ResourceGroup> selectByIds(List<Long> ids) {
        ResourceGroupExample example = new ResourceGroupExample();
        example.createCriteria().andIdIn(ids);
        return resourceGroupMapper.selectByExample(example);
    }

    public ResourceGroup selectByBizId(Long bizLineId) {
        ResourceGroupExample example = new ResourceGroupExample();
        example.createCriteria().andBizLineIdEqualTo(bizLineId);
        return resourceGroupMapper.selectByExample(example).get(0);
    }

    public Long insert(ResourceGroup resourceGroup) {
        resourceGroupMapper.insertSelective(resourceGroup);
        return resourceGroup.getId();
    }

    public int update(ResourceGroup resourceGroup) {
        return resourceGroupMapper.updateByPrimaryKeySelective(resourceGroup);
    }

    public int softDelete(Long id, String mis) {
        ResourceGroup resourceGroup = selectById(id);
        if (resourceGroup != null) {
            resourceGroup.setIsDel(true);
            resourceGroup.setCreator(mis);
            return update(resourceGroup);
        }
        return 0;
    }

    public void delete(Long id) {
        resourceGroupMapper.deleteByPrimaryKey(id);
    }

    public List<ResourceGroup> selectByPageAndBizLineId(int page, int pageSize, long bizLineId) {
        ResourceGroupExample example = new ResourceGroupExample();
        example.limit((page - 1) * pageSize, pageSize);
        example.createCriteria().andBizLineIdEqualTo(bizLineId).andIsDelEqualTo(false);
        return resourceGroupMapper.selectByExample(example);
    }
    public List<ResourceGroup> selectByResourceGroupNameAndBizLineId(Long bizLineId, Long resourceGroupId) {
        ResourceGroupExample example = new ResourceGroupExample();
        example.createCriteria().andBizLineIdEqualTo(bizLineId).andIdEqualTo(resourceGroupId).andIsDelEqualTo(false);
        return resourceGroupMapper.selectByExample(example);
    }

    public Long selectCountByBizLineId(long bizLineId) {
        ResourceGroupExample example = new ResourceGroupExample();
        example.createCriteria().andBizLineIdEqualTo(bizLineId).andIsDelEqualTo(false);
        return resourceGroupMapper.countByExample(example);
    }

}
