package com.sankuai.algoplatform.matchops.infrastructure.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.message.Trace;
import com.google.common.collect.ImmutableMap;
import com.meituan.mtrace.Tracer;
import com.sankuai.oceanus.http.client.okhttp.OceanusInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections4.MapUtils;
import org.apache.http.client.utils.URIBuilder;
import org.apache.logging.log4j.core.tools.picocli.CommandLine;
import org.jetbrains.annotations.Nullable;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
public class HttpUtil {
    private static String OCTO_APP_KEY = "com.sankuai.octo.openapi";
    private static OkHttpClient client;
    private static OkHttpClient octoClient;

    static {
        client = new OkHttpClient.Builder().build();
        octoClient = new OkHttpClient.Builder()
                .addInterceptor(new OceanusInterceptor(OCTO_APP_KEY))
                .build();
    }

    /**
     * http get请求
     */
    public static String httpGet(String url, Map<String, String> param, Map<String, String> headerMap) {
        return doHttpGet(url, param, headerMap, client);
    }

    /**
     * http post请求
     */
    public static String httpPost(String url, Map<String, String> param, Map<String, String> headerMap) {
        return doHttpPost(url, param, headerMap, client);
    }

    private static String doHttpPost(String url, Map<String, String> param, Map<String, String> headerMap, OkHttpClient client) {
        long t = System.currentTimeMillis();
        String resStr = null;
        try {
            okhttp3.FormBody.Builder formBuilder = new okhttp3.FormBody.Builder();
            if (!MapUtils.isEmpty(param)) {
                param.forEach(formBuilder::add);
            }
            okhttp3.RequestBody requestBody = formBuilder.build();

            Request.Builder builder = new Request.Builder().url(url).post(requestBody);
            if (!MapUtils.isEmpty(headerMap)) {
                headerMap.forEach(builder::addHeader);
            }
            Request request = builder.build();
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                resStr = Objects.requireNonNull(response.body()).string();
            }
        } catch (Exception e) {
            log.error("HttpUtil.httpPost error ", e);
        } finally {
            log.info("HttpUtil.httpPost url:{}, resp:{}, cost:{}", url, resStr, System.currentTimeMillis() - t);
        }
        return resStr;
    }

    public static String httpPostJson(String url, String json, Map<String, String> headerMap) {
        long t = System.currentTimeMillis();
        String resStr = null;
        try {
            okhttp3.RequestBody requestBody = okhttp3.RequestBody.create(json, okhttp3.MediaType.parse("application/json; charset=utf-8"));

            Request.Builder builder = new Request.Builder().url(url).post(requestBody);
            if (!MapUtils.isEmpty(headerMap)) {
                headerMap.forEach(builder::addHeader);
            }
            Request request = builder.build();

            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                resStr = Objects.requireNonNull(response.body()).string();
            }
        } catch (Exception e) {
            log.error("HttpUtil.httpPostJson error ", e);
        } finally {
            log.info("HttpUtil.httpPostJson url:{}, cost:{}, resp:{}", url, System.currentTimeMillis() - t, resStr);
        }
        return resStr;
    }

    /**
     * http get请求
     */
    public static String octoHttpGet(String url, Map<String, String> param, Map<String, String> headerMap) {
        return doHttpGet(url, param, headerMap, octoClient);
    }


    @Nullable
    private static String doHttpGet(String url, Map<String, String> param, Map<String, String> headerMap, OkHttpClient client) {
        long t = System.currentTimeMillis();
        String resStr = null;
        try {
            if (!MapUtils.isEmpty(param)) {
                URIBuilder builder = new URIBuilder(url);
                param.forEach(builder::setParameter);
                url = builder.build().toString();
            }

            Request.Builder builder = new Request.Builder().url(url);
            if (!MapUtils.isEmpty(headerMap)) {
                headerMap.forEach(builder::addHeader);
            }
            Request request = builder.build();
            Response response = client.newCall(request).execute();
            resStr = Objects.requireNonNull(response.body()).string();
        } catch (Exception e) {
            log.error("HttpUtil.httpGet error ", e);
        } finally {
            log.info("HttpUtil.httpGet url:{}, resp:{}, cost:{}", url, resStr, System.currentTimeMillis() - t);
        }
        return resStr;
    }

    public static String httpPostWithTimeout(String url, String param, int timeOutSecond, Map<String, String> header) {
        long t = System.currentTimeMillis();
        String resStr = "";
        try {
            HttpUrl.Builder urlBuilder = Objects.requireNonNull(HttpUrl.parse(url)).newBuilder();
            Request request = new Request.Builder()
                    .url(urlBuilder.build())
                    .headers(Headers.of(header))
                    .post(RequestBody.create(MediaType.parse("application/json; charset=utf-8"), param))
                    .build();

            Response response = new OkHttpClient.Builder()
                    //.connectTimeout(timeOutSecond, TimeUnit.SECONDS)
                    .readTimeout(timeOutSecond , TimeUnit.SECONDS)
                    .build()
                    .newCall(request)
                    .execute();
            log.info("HttpUtil.httpPostWithHeader url:{} param:{}, responseHeader：{}",
                    url, param,
                    JSONObject.toJSONString(response.headers()));
            resStr = Objects.requireNonNull(response.body()).string();
        } catch (Exception e) {
            log.error("HttpUtil.httpPostWithHeader error: url:{}, ex:", url, e);
        } finally {
            log.info("HttpUtil.httpPostWithHeader req:{}, cost:{}, res:{}", JSONObject.toJSONString(param),
                    System.currentTimeMillis() - t, resStr);
        }
        return resStr;
    }
}