package com.sankuai.algoplatform.matchops.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: llm_biz_strategy
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LlmBizStrategy {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: biz_code
     *   说明: 适用业务标识
     */
    private String bizCode;

    /**
     *   字段: note
     *   说明: 业务策略说明
     */
    private String note;

    /**
     *   字段: abtest_config
     *   说明: 大模型配置说明
     */
    private String abtestConfig;

    /**
     *   字段: env
     *   说明: 环境，0测试/ST 1线上
     */
    private Integer env;

    /**
     *   字段: biz_line_id
     *   说明: 业务线ID
     */
    private Long bizLineId;

    /**
     *   字段: status
     *   说明: 状态，1：正常，0：删除
     */
    private Boolean status;

    /**
     *   字段: add_time
     *   说明: 添加时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: owner
     *   说明: owner
     */
    private String owner;
}