package com.sankuai.algoplatform.matchops.infrastructure.config;

import com.alibaba.fastjson.JSONObject;
import com.dianping.zebra.util.StringUtils;
import com.sankuai.sso.oidc.client.OAuthClient;
import com.sankuai.sso.oidc.client.OAuthClientConfig;
import com.sankuai.sso.oidc.client.OAuthClientConfigBuilder;
import com.sankuai.sso.oidc.client.OAuthClientFactory;
import com.sankuai.sso.oidc.enums.ClientAuthMethodEnum;
import com.sankuai.sso.oidc.enums.GrantTypeEnum;
import com.sankuai.sso.oidc.request.OAuthTokenRequest;
import com.sankuai.sso.oidc.request.OAuthTokenRequestBuilder;
import com.sankuai.sso.oidc.response.OAuthTokenResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import javax.annotation.PostConstruct;
import java.util.Objects;

/**
 * OAuth客户端配置类
 *
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/8/23
 */
@Configuration
@Slf4j
public class OAuthConfig {

    @Value("${xuecheng.appkey}")
    private String clientId;

    /**
     * 初始化OAuthClientFactory
     * 使用自适应环境模式，默认内存缓存，最大缓存25000张票据
     */
    @PostConstruct
    public void initializeOAuthFactory() {
        try {
            OAuthClientFactory.initialize();
            log.info("OAuthClientFactory initialized successfully with adaptive environment");
        } catch (Exception e) {
            log.error("Failed to initialize OAuthClientFactory", e);
            throw new RuntimeException("OAuth factory initialization failed", e);
        }
    }

    /**
     * 获取OAuth客户端实例的便捷方法
     * 如果需要在运行时动态创建不同配置的客户端，可以使用此方法
     *
     * @param clientId     客户端ID
     * @param clientSecret 客户端密钥
     * @return OAuthClient实例
     */
    public OAuthClient createOAuthClient(String clientId, String clientSecret) {
        OAuthClientConfig config = OAuthClientConfigBuilder.create()
                .clientId(clientId)
                .clientSecret(clientSecret)
                .clientAuthMethod(ClientAuthMethodEnum.CLIENT_SECRET_JWT)
                .build();
        OAuthClient client = OAuthClientFactory.getOrCreateClient(config);
        if (client == null) {
            client = OAuthClientFactory.createClient(config);
        }
        return client;
    }

    public String getToken() {
        String clientSecret = KmsConfigs.getXmSecret();
        OAuthClient oAuthClient = createOAuthClient(clientId, clientSecret);
        OAuthTokenRequest request = OAuthTokenRequestBuilder.create()
                .grantType(GrantTypeEnum.ClientCredentials)
                .audience(new String[]{"com.sankuai.it.ead.citadel"})
                .build();
        OAuthTokenResponse response = oAuthClient.requestToken(request);
        if (Objects.isNull(response) || StringUtils.isBlank(response.getAccessToken())) {
            log.error("getToken failed,resp:{}", JSONObject.toJSONString(response));
            return null;
        }
        return response.getAccessToken();

    }
}
