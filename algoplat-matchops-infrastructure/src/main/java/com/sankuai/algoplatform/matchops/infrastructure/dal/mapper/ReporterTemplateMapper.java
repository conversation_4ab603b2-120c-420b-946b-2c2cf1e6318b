package com.sankuai.algoplatform.matchops.infrastructure.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ReporterTemplate;
import com.sankuai.algoplatform.matchops.infrastructure.dal.example.ReporterTemplateExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ReporterTemplateMapper extends MybatisBaseMapper<ReporterTemplate, ReporterTemplateExample, Long> {
    int batchInsert(@Param("list") List<ReporterTemplate> list);
}