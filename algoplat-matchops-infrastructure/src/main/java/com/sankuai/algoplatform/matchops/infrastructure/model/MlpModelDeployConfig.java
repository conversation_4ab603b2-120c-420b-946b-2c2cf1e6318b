//package com.sankuai.algoplatform.matchops.infrastructure.model;
//
//import com.alibaba.fastjson.annotation.JSONField;
//import lombok.AllArgsConstructor;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//
//import java.util.List;
//
///**
// * @program: algoplat-matchops
// * <AUTHOR>
// * @Date 2025/5/26
// */
//@Data
//@NoArgsConstructor
//@AllArgsConstructor
//public class MlpModelDeployConfig {
//
//    private String bu;
//
//    private String industryType;
//
//    private String matchStandard;
//
//    private String wxProject;
//
//    private String appkey;
//
//
//    private String modelType;
//
//
//    private String env;
//
//
//    private List<GroupConfig> group;
//
//    @Data
//    public static class GroupConfig {
//
//        private String groupName;
//
//
//        private String serverImage;
//
//
//        private int rpcOutTime;
//
//
//        private boolean batching;
//
//
//        private BatchingConfig batchingConfig;
//
//
//        private String set;
//
//
//        private ResourceConfig resourceConfig;
//    }
//
//    @Data
//    public static class BatchingConfig {
//
//        private int maxBatchSize;
//
//
//        private int batchTimeoutMicros;
//
//
//        private String batchingExtra;
//    }
//
//    @Data
//    public static class ResourceConfig {
//
//        private int vcores;
//
//
//        private int memory;
//
//
//        private int gcores;
//
//
//        private String gcoresType;
//    }
//}