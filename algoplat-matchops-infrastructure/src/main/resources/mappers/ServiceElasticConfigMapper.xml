<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.ServiceElasticConfigMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ServiceElasticConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_line_id" jdbcType="BIGINT" property="bizLineId" />
    <result column="resource_type" jdbcType="INTEGER" property="resourceType" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="deploy_config" jdbcType="CHAR" property="deployConfig" />
    <result column="elastic_rule" jdbcType="CHAR" property="fridayElasticRule" />
    <result column="owner" jdbcType="VARCHAR" property="owner" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, biz_line_id, resource_type, name, deploy_config, elastic_rule, owner, create_time, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.ServiceElasticConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from service_elastic_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from service_elastic_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from service_elastic_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.ServiceElasticConfigExample">
    delete from service_elastic_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ServiceElasticConfig" useGeneratedKeys="true">
    insert into service_elastic_config (biz_line_id, resource_type, name, 
      deploy_config, elastic_rule, owner, 
      create_time, update_time)
    values (#{bizLineId,jdbcType=BIGINT}, #{resourceType,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, 
      #{deployConfig,jdbcType=CHAR}, #{fridayElasticRule,jdbcType=CHAR}, #{owner,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ServiceElasticConfig" useGeneratedKeys="true">
    insert into service_elastic_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bizLineId != null">
        biz_line_id,
      </if>
      <if test="resourceType != null">
        resource_type,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="deployConfig != null">
        deploy_config,
      </if>
      <if test="fridayElasticRule != null">
        elastic_rule,
      </if>
      <if test="owner != null">
        owner,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bizLineId != null">
        #{bizLineId,jdbcType=BIGINT},
      </if>
      <if test="resourceType != null">
        #{resourceType,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="deployConfig != null">
        #{deployConfig,jdbcType=CHAR},
      </if>
      <if test="fridayElasticRule != null">
        #{fridayElasticRule,jdbcType=CHAR},
      </if>
      <if test="owner != null">
        #{owner,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.ServiceElasticConfigExample" resultType="java.lang.Long">
    select count(*) from service_elastic_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update service_elastic_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.bizLineId != null">
        biz_line_id = #{record.bizLineId,jdbcType=BIGINT},
      </if>
      <if test="record.resourceType != null">
        resource_type = #{record.resourceType,jdbcType=INTEGER},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.deployConfig != null">
        deploy_config = #{record.deployConfig,jdbcType=CHAR},
      </if>
      <if test="record.fridayElasticRule != null">
        elastic_rule = #{record.fridayElasticRule,jdbcType=CHAR},
      </if>
      <if test="record.owner != null">
        owner = #{record.owner,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update service_elastic_config
    set id = #{record.id,jdbcType=BIGINT},
      biz_line_id = #{record.bizLineId,jdbcType=BIGINT},
      resource_type = #{record.resourceType,jdbcType=INTEGER},
      name = #{record.name,jdbcType=VARCHAR},
      deploy_config = #{record.deployConfig,jdbcType=CHAR},
      elastic_rule = #{record.fridayElasticRule,jdbcType=CHAR},
      owner = #{record.owner,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ServiceElasticConfig">
    update service_elastic_config
    <set>
      <if test="bizLineId != null">
        biz_line_id = #{bizLineId,jdbcType=BIGINT},
      </if>
      <if test="resourceType != null">
        resource_type = #{resourceType,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="deployConfig != null">
        deploy_config = #{deployConfig,jdbcType=CHAR},
      </if>
      <if test="fridayElasticRule != null">
        elastic_rule = #{fridayElasticRule,jdbcType=CHAR},
      </if>
      <if test="owner != null">
        owner = #{owner,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ServiceElasticConfig">
    update service_elastic_config
    set biz_line_id = #{bizLineId,jdbcType=BIGINT},
      resource_type = #{resourceType,jdbcType=INTEGER},
      name = #{name,jdbcType=VARCHAR},
      deploy_config = #{deployConfig,jdbcType=CHAR},
      elastic_rule = #{fridayElasticRule,jdbcType=CHAR},
      owner = #{owner,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into service_elastic_config
    (biz_line_id, resource_type, name, deploy_config, elastic_rule, owner, create_time, 
      update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.bizLineId,jdbcType=BIGINT}, #{item.resourceType,jdbcType=INTEGER}, #{item.name,jdbcType=VARCHAR}, 
        #{item.deployConfig,jdbcType=CHAR}, #{item.fridayElasticRule,jdbcType=CHAR}, #{item.owner,jdbcType=VARCHAR},
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>