<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.ResourcePoolConfigMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ResourcePoolConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant" jdbcType="VARCHAR" property="tenant" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="queue" jdbcType="VARCHAR" property="queue" />
    <result column="resource_type" jdbcType="VARCHAR" property="resourceType" />
    <result column="quota_limit" jdbcType="INTEGER" property="quotaLimit" />
    <result column="operator_mis" jdbcType="VARCHAR" property="operatorMis" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, tenant, project_name, queue, resource_type, quota_limit, operator_mis, status, 
    add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.ResourcePoolConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from resource_pool_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from resource_pool_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from resource_pool_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.ResourcePoolConfigExample">
    delete from resource_pool_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ResourcePoolConfig">
    insert into resource_pool_config (id, tenant, project_name, 
      queue, resource_type, quota_limit, 
      operator_mis, status, add_time, 
      update_time)
    values (#{id,jdbcType=BIGINT}, #{tenant,jdbcType=VARCHAR}, #{projectName,jdbcType=VARCHAR}, 
      #{queue,jdbcType=VARCHAR}, #{resourceType,jdbcType=VARCHAR}, #{quotaLimit,jdbcType=INTEGER}, 
      #{operatorMis,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ResourcePoolConfig">
    insert into resource_pool_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="tenant != null">
        tenant,
      </if>
      <if test="projectName != null">
        project_name,
      </if>
      <if test="queue != null">
        queue,
      </if>
      <if test="resourceType != null">
        resource_type,
      </if>
      <if test="quotaLimit != null">
        quota_limit,
      </if>
      <if test="operatorMis != null">
        operator_mis,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="tenant != null">
        #{tenant,jdbcType=VARCHAR},
      </if>
      <if test="projectName != null">
        #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="queue != null">
        #{queue,jdbcType=VARCHAR},
      </if>
      <if test="resourceType != null">
        #{resourceType,jdbcType=VARCHAR},
      </if>
      <if test="quotaLimit != null">
        #{quotaLimit,jdbcType=INTEGER},
      </if>
      <if test="operatorMis != null">
        #{operatorMis,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.ResourcePoolConfigExample" resultType="java.lang.Long">
    select count(*) from resource_pool_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update resource_pool_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.tenant != null">
        tenant = #{record.tenant,jdbcType=VARCHAR},
      </if>
      <if test="record.projectName != null">
        project_name = #{record.projectName,jdbcType=VARCHAR},
      </if>
      <if test="record.queue != null">
        queue = #{record.queue,jdbcType=VARCHAR},
      </if>
      <if test="record.resourceType != null">
        resource_type = #{record.resourceType,jdbcType=VARCHAR},
      </if>
      <if test="record.quotaLimit != null">
        quota_limit = #{record.quotaLimit,jdbcType=INTEGER},
      </if>
      <if test="record.operatorMis != null">
        operator_mis = #{record.operatorMis,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update resource_pool_config
    set id = #{record.id,jdbcType=BIGINT},
      tenant = #{record.tenant,jdbcType=VARCHAR},
      project_name = #{record.projectName,jdbcType=VARCHAR},
      queue = #{record.queue,jdbcType=VARCHAR},
      resource_type = #{record.resourceType,jdbcType=VARCHAR},
      quota_limit = #{record.quotaLimit,jdbcType=INTEGER},
      operator_mis = #{record.operatorMis,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ResourcePoolConfig">
    update resource_pool_config
    <set>
      <if test="tenant != null">
        tenant = #{tenant,jdbcType=VARCHAR},
      </if>
      <if test="projectName != null">
        project_name = #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="queue != null">
        queue = #{queue,jdbcType=VARCHAR},
      </if>
      <if test="resourceType != null">
        resource_type = #{resourceType,jdbcType=VARCHAR},
      </if>
      <if test="quotaLimit != null">
        quota_limit = #{quotaLimit,jdbcType=INTEGER},
      </if>
      <if test="operatorMis != null">
        operator_mis = #{operatorMis,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ResourcePoolConfig">
    update resource_pool_config
    set tenant = #{tenant,jdbcType=VARCHAR},
      project_name = #{projectName,jdbcType=VARCHAR},
      queue = #{queue,jdbcType=VARCHAR},
      resource_type = #{resourceType,jdbcType=VARCHAR},
      quota_limit = #{quotaLimit,jdbcType=INTEGER},
      operator_mis = #{operatorMis,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>