<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.TestTaskMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestTask">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="biz_line_id" jdbcType="BIGINT" property="bizLineId" />
    <result column="match_strategy_id" jdbcType="BIGINT" property="matchStrategyId" />
    <result column="resource_group_id" jdbcType="BIGINT" property="resourceGroupId" />
    <result column="reporter_template_id" jdbcType="VARCHAR" property="reporterTemplateId" />
    <result column="run_param" jdbcType="VARCHAR" property="runParam" />
    <result column="resource_prepare_info" jdbcType="CHAR" property="resourcePrepareInfo" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="owner" jdbcType="VARCHAR" property="owner" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, biz_line_id, match_strategy_id, resource_group_id, reporter_template_id,
    run_param, resource_prepare_info, status, owner, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.TestTaskExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from test_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from test_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from test_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.TestTaskExample">
    delete from test_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestTask">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into test_task (name, biz_line_id, match_strategy_id,
    resource_group_id, reporter_template_id, run_param,
    resource_prepare_info, status, owner,
    create_time, update_time)
    values (#{name,jdbcType=VARCHAR}, #{bizLineId,jdbcType=BIGINT}, #{matchStrategyId,jdbcType=BIGINT},
    #{resourceGroupId,jdbcType=BIGINT}, #{reporterTemplateId,jdbcType=VARCHAR}, #{runParam,jdbcType=VARCHAR},
    #{resourcePrepareInfo,jdbcType=CHAR}, #{status,jdbcType=INTEGER}, #{owner,jdbcType=VARCHAR},
    #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestTask">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into test_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="bizLineId != null">
        biz_line_id,
      </if>
      <if test="matchStrategyId != null">
        match_strategy_id,
      </if>
      <if test="resourceGroupId != null">
        resource_group_id,
      </if>
      <if test="reporterTemplateId != null">
        reporter_template_id,
      </if>
      <if test="runParam != null">
        run_param,
      </if>
      <if test="resourcePrepareInfo != null">
        resource_prepare_info,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="owner != null">
        owner,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="bizLineId != null">
        #{bizLineId,jdbcType=BIGINT},
      </if>
      <if test="matchStrategyId != null">
        #{matchStrategyId,jdbcType=BIGINT},
      </if>
      <if test="resourceGroupId != null">
        #{resourceGroupId,jdbcType=BIGINT},
      </if>
      <if test="reporterTemplateId != null">
        #{reporterTemplateId,jdbcType=VARCHAR},
      </if>
      <if test="runParam != null">
        #{runParam,jdbcType=VARCHAR},
      </if>
      <if test="resourcePrepareInfo != null">
        #{resourcePrepareInfo,jdbcType=CHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="owner != null">
        #{owner,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.TestTaskExample" resultType="java.lang.Long">
    select count(*) from test_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update test_task
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.name != null">
        name = #{row.name,jdbcType=VARCHAR},
      </if>
      <if test="row.bizLineId != null">
        biz_line_id = #{row.bizLineId,jdbcType=BIGINT},
      </if>
      <if test="row.matchStrategyId != null">
        match_strategy_id = #{row.matchStrategyId,jdbcType=BIGINT},
      </if>
      <if test="row.resourceGroupId != null">
        resource_group_id = #{row.resourceGroupId,jdbcType=BIGINT},
      </if>
      <if test="row.reporterTemplateId != null">
        reporter_template_id = #{row.reporterTemplateId,jdbcType=VARCHAR},
      </if>
      <if test="row.runParam != null">
        run_param = #{row.runParam,jdbcType=VARCHAR},
      </if>
      <if test="row.resourcePrepareInfo != null">
        resource_prepare_info = #{row.resourcePrepareInfo,jdbcType=CHAR},
      </if>
      <if test="row.status != null">
        status = #{row.status,jdbcType=INTEGER},
      </if>
      <if test="row.owner != null">
        owner = #{row.owner,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update test_task
    set id = #{row.id,jdbcType=BIGINT},
    name = #{row.name,jdbcType=VARCHAR},
    biz_line_id = #{row.bizLineId,jdbcType=BIGINT},
    match_strategy_id = #{row.matchStrategyId,jdbcType=BIGINT},
    resource_group_id = #{row.resourceGroupId,jdbcType=BIGINT},
    reporter_template_id = #{row.reporterTemplateId,jdbcType=VARCHAR},
    run_param = #{row.runParam,jdbcType=VARCHAR},
    resource_prepare_info = #{row.resourcePrepareInfo,jdbcType=CHAR},
    status = #{row.status,jdbcType=INTEGER},
    owner = #{row.owner,jdbcType=VARCHAR},
    create_time = #{row.createTime,jdbcType=TIMESTAMP},
    update_time = #{row.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestTask">
    update test_task
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="bizLineId != null">
        biz_line_id = #{bizLineId,jdbcType=BIGINT},
      </if>
      <if test="matchStrategyId != null">
        match_strategy_id = #{matchStrategyId,jdbcType=BIGINT},
      </if>
      <if test="resourceGroupId != null">
        resource_group_id = #{resourceGroupId,jdbcType=BIGINT},
      </if>
      <if test="reporterTemplateId != null">
        reporter_template_id = #{reporterTemplateId,jdbcType=VARCHAR},
      </if>
      <if test="runParam != null">
        run_param = #{runParam,jdbcType=VARCHAR},
      </if>
      <if test="resourcePrepareInfo != null">
        resource_prepare_info = #{resourcePrepareInfo,jdbcType=CHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="owner != null">
        owner = #{owner,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestTask">
    update test_task
    set name = #{name,jdbcType=VARCHAR},
        biz_line_id = #{bizLineId,jdbcType=BIGINT},
        match_strategy_id = #{matchStrategyId,jdbcType=BIGINT},
        resource_group_id = #{resourceGroupId,jdbcType=BIGINT},
        reporter_template_id = #{reporterTemplateId,jdbcType=VARCHAR},
        run_param = #{runParam,jdbcType=VARCHAR},
        resource_prepare_info = #{resourcePrepareInfo,jdbcType=CHAR},
        status = #{status,jdbcType=INTEGER},
        owner = #{owner,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>