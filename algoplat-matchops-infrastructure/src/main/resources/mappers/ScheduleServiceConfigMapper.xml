<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.ScheduleServiceConfigMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ScheduleServiceConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="appkey" jdbcType="VARCHAR" property="appkey" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="schedule_type" jdbcType="VARCHAR" property="scheduleType" />
    <result column="gpu_usage_num" jdbcType="INTEGER" property="gpuUsageNum" />
    <result column="inst_max_num" jdbcType="INTEGER" property="instMaxNum" />
    <result column="inst_min_num" jdbcType="INTEGER" property="instMinNum" />
    <result column="operator_mis" jdbcType="VARCHAR" property="operatorMis" />
    <result column="extra" jdbcType="VARCHAR" property="extra" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, appkey, group_name, schedule_type, gpu_usage_num, inst_max_num, inst_min_num,
    operator_mis, extra, status, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.ScheduleServiceConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from schedule_service_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from schedule_service_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from schedule_service_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.ScheduleServiceConfigExample">
    delete from schedule_service_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ScheduleServiceConfig">
    insert into schedule_service_config (id, appkey, group_name,
      schedule_type, gpu_usage_num, inst_max_num,
      inst_min_num, operator_mis, status,
      add_time, update_time)
    values (#{id,jdbcType=BIGINT}, #{appkey,jdbcType=VARCHAR}, #{groupName,jdbcType=VARCHAR},
      #{scheduleType,jdbcType=VARCHAR}, #{gpuUsageNum,jdbcType=INTEGER}, #{instMaxNum,jdbcType=INTEGER},
      #{instMinNum,jdbcType=INTEGER}, #{operatorMis,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER},
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ScheduleServiceConfig">
    insert into schedule_service_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="appkey != null">
        appkey,
      </if>
      <if test="groupName != null">
        group_name,
      </if>
      <if test="scheduleType != null">
        schedule_type,
      </if>
      <if test="gpuUsageNum != null">
        gpu_usage_num,
      </if>
      <if test="instMaxNum != null">
        inst_max_num,
      </if>
      <if test="instMinNum != null">
        inst_min_num,
      </if>
      <if test="operatorMis != null">
        operator_mis,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="appkey != null">
        #{appkey,jdbcType=VARCHAR},
      </if>
      <if test="groupName != null">
        #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="scheduleType != null">
        #{scheduleType,jdbcType=VARCHAR},
      </if>
      <if test="gpuUsageNum != null">
        #{gpuUsageNum,jdbcType=INTEGER},
      </if>
      <if test="instMaxNum != null">
        #{instMaxNum,jdbcType=INTEGER},
      </if>
      <if test="instMinNum != null">
        #{instMinNum,jdbcType=INTEGER},
      </if>
      <if test="operatorMis != null">
        #{operatorMis,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.ScheduleServiceConfigExample" resultType="java.lang.Long">
    select count(*) from schedule_service_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update schedule_service_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.appkey != null">
        appkey = #{record.appkey,jdbcType=VARCHAR},
      </if>
      <if test="record.groupName != null">
        group_name = #{record.groupName,jdbcType=VARCHAR},
      </if>
      <if test="record.scheduleType != null">
        schedule_type = #{record.scheduleType,jdbcType=VARCHAR},
      </if>
      <if test="record.gpuUsageNum != null">
        gpu_usage_num = #{record.gpuUsageNum,jdbcType=INTEGER},
      </if>
      <if test="record.instMaxNum != null">
        inst_max_num = #{record.instMaxNum,jdbcType=INTEGER},
      </if>
      <if test="record.instMinNum != null">
        inst_min_num = #{record.instMinNum,jdbcType=INTEGER},
      </if>
      <if test="record.operatorMis != null">
        operator_mis = #{record.operatorMis,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update schedule_service_config
    set id = #{record.id,jdbcType=BIGINT},
      appkey = #{record.appkey,jdbcType=VARCHAR},
      group_name = #{record.groupName,jdbcType=VARCHAR},
      schedule_type = #{record.scheduleType,jdbcType=VARCHAR},
      gpu_usage_num = #{record.gpuUsageNum,jdbcType=INTEGER},
      inst_max_num = #{record.instMaxNum,jdbcType=INTEGER},
      inst_min_num = #{record.instMinNum,jdbcType=INTEGER},
      operator_mis = #{record.operatorMis,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ScheduleServiceConfig">
    update schedule_service_config
    <set>
      <if test="appkey != null">
        appkey = #{appkey,jdbcType=VARCHAR},
      </if>
      <if test="groupName != null">
        group_name = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="scheduleType != null">
        schedule_type = #{scheduleType,jdbcType=VARCHAR},
      </if>
      <if test="gpuUsageNum != null">
        gpu_usage_num = #{gpuUsageNum,jdbcType=INTEGER},
      </if>
      <if test="instMaxNum != null">
        inst_max_num = #{instMaxNum,jdbcType=INTEGER},
      </if>
      <if test="instMinNum != null">
        inst_min_num = #{instMinNum,jdbcType=INTEGER},
      </if>
      <if test="operatorMis != null">
        operator_mis = #{operatorMis,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ScheduleServiceConfig">
    update schedule_service_config
    set appkey = #{appkey,jdbcType=VARCHAR},
      group_name = #{groupName,jdbcType=VARCHAR},
      schedule_type = #{scheduleType,jdbcType=VARCHAR},
      gpu_usage_num = #{gpuUsageNum,jdbcType=INTEGER},
      inst_max_num = #{instMaxNum,jdbcType=INTEGER},
      inst_min_num = #{instMinNum,jdbcType=INTEGER},
      operator_mis = #{operatorMis,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>