<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.BizLlmpredictConfigMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.BizLlmpredictConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_code" jdbcType="VARCHAR" property="bizCode" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="model_config" jdbcType="VARCHAR" property="modelConfig" />
    <result column="prompt_template_id" jdbcType="BIGINT" property="promptTemplateId" />
    <result column="env" jdbcType="INTEGER" property="env" />
    <result column="biz_line_id" jdbcType="BIGINT" property="bizLineId" />
    <result column="status" jdbcType="BIT" property="status" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="owner" jdbcType="VARCHAR" property="owner" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, biz_code, note, model_config, prompt_template_id, env, biz_line_id, status, add_time, 
    update_time, owner
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.BizLlmpredictConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from biz_llmpredict_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from biz_llmpredict_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from biz_llmpredict_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.BizLlmpredictConfigExample">
    delete from biz_llmpredict_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" useGeneratedKeys="true" keyProperty="id" keyColumn="id" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.BizLlmpredictConfig">
    insert into biz_llmpredict_config (id, biz_code, note, 
      model_config, prompt_template_id, env, 
      biz_line_id, status, add_time, 
      update_time, owner)
    values (#{id,jdbcType=BIGINT}, #{bizCode,jdbcType=VARCHAR}, #{note,jdbcType=VARCHAR}, 
      #{modelConfig,jdbcType=VARCHAR}, #{promptTemplateId,jdbcType=BIGINT}, #{env,jdbcType=INTEGER}, 
      #{bizLineId,jdbcType=BIGINT}, #{status,jdbcType=BIT}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{owner,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" keyColumn="id" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.BizLlmpredictConfig">
    insert into biz_llmpredict_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="bizCode != null">
        biz_code,
      </if>
      <if test="note != null">
        note,
      </if>
      <if test="modelConfig != null">
        model_config,
      </if>
      <if test="promptTemplateId != null">
        prompt_template_id,
      </if>
      <if test="env != null">
        env,
      </if>
      <if test="bizLineId != null">
        biz_line_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="owner != null">
        owner,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="bizCode != null">
        #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="note != null">
        #{note,jdbcType=VARCHAR},
      </if>
      <if test="modelConfig != null">
        #{modelConfig,jdbcType=VARCHAR},
      </if>
      <if test="promptTemplateId != null">
        #{promptTemplateId,jdbcType=BIGINT},
      </if>
      <if test="env != null">
        #{env,jdbcType=INTEGER},
      </if>
      <if test="bizLineId != null">
        #{bizLineId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=BIT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="owner != null">
        #{owner,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.BizLlmpredictConfigExample" resultType="java.lang.Long">
    select count(*) from biz_llmpredict_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update biz_llmpredict_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.bizCode != null">
        biz_code = #{record.bizCode,jdbcType=VARCHAR},
      </if>
      <if test="record.note != null">
        note = #{record.note,jdbcType=VARCHAR},
      </if>
      <if test="record.modelConfig != null">
        model_config = #{record.modelConfig,jdbcType=VARCHAR},
      </if>
      <if test="record.promptTemplateId != null">
        prompt_template_id = #{record.promptTemplateId,jdbcType=BIGINT},
      </if>
      <if test="record.env != null">
        env = #{record.env,jdbcType=INTEGER},
      </if>
      <if test="record.bizLineId != null">
        biz_line_id = #{record.bizLineId,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=BIT},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.owner != null">
        owner = #{record.owner,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update biz_llmpredict_config
    set id = #{record.id,jdbcType=BIGINT},
      biz_code = #{record.bizCode,jdbcType=VARCHAR},
      note = #{record.note,jdbcType=VARCHAR},
      model_config = #{record.modelConfig,jdbcType=VARCHAR},
      prompt_template_id = #{record.promptTemplateId,jdbcType=BIGINT},
      env = #{record.env,jdbcType=INTEGER},
      biz_line_id = #{record.bizLineId,jdbcType=BIGINT},
      status = #{record.status,jdbcType=BIT},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      owner = #{record.owner,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.BizLlmpredictConfig">
    update biz_llmpredict_config
    <set>
      <if test="bizCode != null">
        biz_code = #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="note != null">
        note = #{note,jdbcType=VARCHAR},
      </if>
      <if test="modelConfig != null">
        model_config = #{modelConfig,jdbcType=VARCHAR},
      </if>
      <if test="promptTemplateId != null">
        prompt_template_id = #{promptTemplateId,jdbcType=BIGINT},
      </if>
      <if test="env != null">
        env = #{env,jdbcType=INTEGER},
      </if>
      <if test="bizLineId != null">
        biz_line_id = #{bizLineId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=BIT},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="owner != null">
        owner = #{owner,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.BizLlmpredictConfig">
    update biz_llmpredict_config
    set biz_code = #{bizCode,jdbcType=VARCHAR},
      note = #{note,jdbcType=VARCHAR},
      model_config = #{modelConfig,jdbcType=VARCHAR},
      prompt_template_id = #{promptTemplateId,jdbcType=BIGINT},
      env = #{env,jdbcType=INTEGER},
      biz_line_id = #{bizLineId,jdbcType=BIGINT},
      status = #{status,jdbcType=BIT},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      owner = #{owner,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into biz_llmpredict_config
    (id, biz_code, note, model_config, prompt_template_id, env, biz_line_id, status, 
      add_time, update_time, owner)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.bizCode,jdbcType=VARCHAR}, #{item.note,jdbcType=VARCHAR}, 
        #{item.modelConfig,jdbcType=VARCHAR}, #{item.promptTemplateId,jdbcType=BIGINT}, 
        #{item.env,jdbcType=INTEGER}, #{item.bizLineId,jdbcType=BIGINT}, #{item.status,jdbcType=BIT}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.owner,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
</mapper>