<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.ResourceGroupMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ResourceGroup">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_line_id" jdbcType="BIGINT" property="bizLineId" />
    <result column="octo_resources" jdbcType="CHAR" property="octoResources" />
    <result column="mlp_resources" jdbcType="CHAR" property="mlpResources" />
    <result column="friday_resources" jdbcType="CHAR" property="fridayResources" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_del" jdbcType="BIT" property="isDel" />
    <result column="name" jdbcType="VARCHAR" property="name" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, biz_line_id, octo_resources, mlp_resources, friday_resources, creator, create_time, 
    update_time, is_del, name
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.ResourceGroupExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from resource_group
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from resource_group
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from resource_group
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.ResourceGroupExample">
    delete from resource_group
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" useGeneratedKeys="true" keyProperty="id" keyColumn="id" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ResourceGroup">
    insert into resource_group (id, biz_line_id, octo_resources,
                                mlp_resources, friday_resources, creator,
                                create_time, update_time, is_del,
                                name)
    values (#{id,jdbcType=BIGINT}, #{bizLineId,jdbcType=BIGINT}, #{octoResources,jdbcType=CHAR},
            #{mlpResources,jdbcType=CHAR}, #{fridayResources,jdbcType=CHAR}, #{creator,jdbcType=VARCHAR},
            #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{isDel,jdbcType=BIT},
            #{name,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" keyColumn="id" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ResourceGroup">
    insert into resource_group
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="bizLineId != null">
        biz_line_id,
      </if>
      <if test="octoResources != null">
        octo_resources,
      </if>
      <if test="mlpResources != null">
        mlp_resources,
      </if>
      <if test="fridayResources != null">
        friday_resources,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDel != null">
        is_del,
      </if>
      <if test="name != null">
        name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="bizLineId != null">
        #{bizLineId,jdbcType=BIGINT},
      </if>
      <if test="octoResources != null">
        #{octoResources,jdbcType=CHAR},
      </if>
      <if test="mlpResources != null">
        #{mlpResources,jdbcType=CHAR},
      </if>
      <if test="fridayResources != null">
        #{fridayResources,jdbcType=CHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=BIT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.ResourceGroupExample" resultType="java.lang.Long">
    select count(*) from resource_group
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update resource_group
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.bizLineId != null">
        biz_line_id = #{record.bizLineId,jdbcType=BIGINT},
      </if>
      <if test="record.octoResources != null">
        octo_resources = #{record.octoResources,jdbcType=CHAR},
      </if>
      <if test="record.mlpResources != null">
        mlp_resources = #{record.mlpResources,jdbcType=CHAR},
      </if>
      <if test="record.fridayResources != null">
        friday_resources = #{record.fridayResources,jdbcType=CHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDel != null">
        is_del = #{record.isDel,jdbcType=BIT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update resource_group
    set id = #{record.id,jdbcType=BIGINT},
    biz_line_id = #{record.bizLineId,jdbcType=BIGINT},
    octo_resources = #{record.octoResources,jdbcType=CHAR},
    mlp_resources = #{record.mlpResources,jdbcType=CHAR},
    friday_resources = #{record.fridayResources,jdbcType=CHAR},
    creator = #{record.creator,jdbcType=VARCHAR},
    create_time = #{record.createTime,jdbcType=TIMESTAMP},
    update_time = #{record.updateTime,jdbcType=TIMESTAMP},
    is_del = #{record.isDel,jdbcType=BIT},
    name = #{record.name,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ResourceGroup">
    update resource_group
    <set>
      <if test="bizLineId != null">
        biz_line_id = #{bizLineId,jdbcType=BIGINT},
      </if>
      <if test="octoResources != null">
        octo_resources = #{octoResources,jdbcType=CHAR},
      </if>
      <if test="mlpResources != null">
        mlp_resources = #{mlpResources,jdbcType=CHAR},
      </if>
      <if test="fridayResources != null">
        friday_resources = #{fridayResources,jdbcType=CHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDel != null">
        is_del = #{isDel,jdbcType=BIT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ResourceGroup">
    update resource_group
    set biz_line_id = #{bizLineId,jdbcType=BIGINT},
        octo_resources = #{octoResources,jdbcType=CHAR},
        mlp_resources = #{mlpResources,jdbcType=CHAR},
        friday_resources = #{fridayResources,jdbcType=CHAR},
        creator = #{creator,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_del = #{isDel,jdbcType=BIT},
        name = #{name,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into resource_group
    (id, biz_line_id, octo_resources, mlp_resources, friday_resources, creator, create_time,
    update_time, is_del, name)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.bizLineId,jdbcType=BIGINT}, #{item.octoResources,jdbcType=CHAR},
      #{item.mlpResources,jdbcType=CHAR}, #{item.fridayResources,jdbcType=CHAR}, #{item.creator,jdbcType=VARCHAR},
      #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.isDel,jdbcType=BIT},
      #{item.name,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>