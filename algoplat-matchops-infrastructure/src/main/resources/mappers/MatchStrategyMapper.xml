<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.MatchStrategyMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.MatchStrategy">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="biz_line_id" jdbcType="BIGINT" property="bizLineId" />
    <result column="test_scene_id" jdbcType="BIGINT" property="testSceneId" />
    <result column="algo_biz_codes" jdbcType="CHAR" property="algoBizCodes" />
    <result column="predictor_cache" jdbcType="CHAR" property="predictorCache" />
    <result column="llm_biz_codes" jdbcType="VARCHAR" property="llmBizCodes" />
    <result column="octo_service_config" jdbcType="CHAR" property="octoServiceConfig" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="deploy_info" jdbcType="CHAR" property="deployInfo" />
    <result column="owner" jdbcType="VARCHAR" property="owner" />
    <result column="is_del" jdbcType="BIT" property="isDel" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, biz_line_id, test_scene_id, algo_biz_codes, predictor_cache, llm_biz_codes, 
    octo_service_config, status, deploy_info, owner, is_del, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.MatchStrategyExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from match_strategy
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from match_strategy
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from match_strategy
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.MatchStrategyExample">
    delete from match_strategy
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.MatchStrategy">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into match_strategy (name, biz_line_id, test_scene_id,
    algo_biz_codes, predictor_cache, llm_biz_codes,
    octo_service_config, status, deploy_info,
    owner, is_del, create_time,
    update_time)
    values (#{name,jdbcType=VARCHAR}, #{bizLineId,jdbcType=BIGINT}, #{testSceneId,jdbcType=BIGINT},
    #{algoBizCodes,jdbcType=CHAR}, #{predictorCache,jdbcType=CHAR}, #{llmBizCodes,jdbcType=VARCHAR},
    #{octoServiceConfig,jdbcType=CHAR}, #{status,jdbcType=INTEGER}, #{deployInfo,jdbcType=CHAR},
    #{owner,jdbcType=VARCHAR}, #{isDel,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP},
    #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.MatchStrategy">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into match_strategy
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="bizLineId != null">
        biz_line_id,
      </if>
      <if test="testSceneId != null">
        test_scene_id,
      </if>
      <if test="algoBizCodes != null">
        algo_biz_codes,
      </if>
      <if test="predictorCache != null">
        predictor_cache,
      </if>
      <if test="llmBizCodes != null">
        llm_biz_codes,
      </if>
      <if test="octoServiceConfig != null">
        octo_service_config,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="deployInfo != null">
        deploy_info,
      </if>
      <if test="owner != null">
        owner,
      </if>
      <if test="isDel != null">
        is_del,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="bizLineId != null">
        #{bizLineId,jdbcType=BIGINT},
      </if>
      <if test="testSceneId != null">
        #{testSceneId,jdbcType=BIGINT},
      </if>
      <if test="algoBizCodes != null">
        #{algoBizCodes,jdbcType=CHAR},
      </if>
      <if test="predictorCache != null">
        #{predictorCache,jdbcType=CHAR},
      </if>
      <if test="llmBizCodes != null">
        #{llmBizCodes,jdbcType=VARCHAR},
      </if>
      <if test="octoServiceConfig != null">
        #{octoServiceConfig,jdbcType=CHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="deployInfo != null">
        #{deployInfo,jdbcType=CHAR},
      </if>
      <if test="owner != null">
        #{owner,jdbcType=VARCHAR},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.MatchStrategyExample" resultType="java.lang.Long">
    select count(*) from match_strategy
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update match_strategy
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.bizLineId != null">
        biz_line_id = #{record.bizLineId,jdbcType=BIGINT},
      </if>
      <if test="record.testSceneId != null">
        test_scene_id = #{record.testSceneId,jdbcType=BIGINT},
      </if>
      <if test="record.algoBizCodes != null">
        algo_biz_codes = #{record.algoBizCodes,jdbcType=CHAR},
      </if>
      <if test="record.predictorCache != null">
        predictor_cache = #{record.predictorCache,jdbcType=CHAR},
      </if>
      <if test="record.llmBizCodes != null">
        llm_biz_codes = #{record.llmBizCodes,jdbcType=VARCHAR},
      </if>
      <if test="record.octoServiceConfig != null">
        octo_service_config = #{record.octoServiceConfig,jdbcType=CHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.deployInfo != null">
        deploy_info = #{record.deployInfo,jdbcType=CHAR},
      </if>
      <if test="record.owner != null">
        owner = #{record.owner,jdbcType=VARCHAR},
      </if>
      <if test="record.isDel != null">
        is_del = #{record.isDel,jdbcType=BIT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update match_strategy
    set id = #{record.id,jdbcType=BIGINT},
    name = #{record.name,jdbcType=VARCHAR},
    biz_line_id = #{record.bizLineId,jdbcType=BIGINT},
    test_scene_id = #{record.testSceneId,jdbcType=BIGINT},
    algo_biz_codes = #{record.algoBizCodes,jdbcType=CHAR},
    predictor_cache = #{record.predictorCache,jdbcType=CHAR},
    llm_biz_codes = #{record.llmBizCodes,jdbcType=VARCHAR},
    octo_service_config = #{record.octoServiceConfig,jdbcType=CHAR},
    status = #{record.status,jdbcType=INTEGER},
    deploy_info = #{record.deployInfo,jdbcType=CHAR},
    owner = #{record.owner,jdbcType=VARCHAR},
    is_del = #{record.isDel,jdbcType=BIT},
    create_time = #{record.createTime,jdbcType=TIMESTAMP},
    update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.MatchStrategy">
    update match_strategy
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="bizLineId != null">
        biz_line_id = #{bizLineId,jdbcType=BIGINT},
      </if>
      <if test="testSceneId != null">
        test_scene_id = #{testSceneId,jdbcType=BIGINT},
      </if>
      <if test="algoBizCodes != null">
        algo_biz_codes = #{algoBizCodes,jdbcType=CHAR},
      </if>
      <if test="predictorCache != null">
        predictor_cache = #{predictorCache,jdbcType=CHAR},
      </if>
      <if test="llmBizCodes != null">
        llm_biz_codes = #{llmBizCodes,jdbcType=VARCHAR},
      </if>
      <if test="octoServiceConfig != null">
        octo_service_config = #{octoServiceConfig,jdbcType=CHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="deployInfo != null">
        deploy_info = #{deployInfo,jdbcType=CHAR},
      </if>
      <if test="owner != null">
        owner = #{owner,jdbcType=VARCHAR},
      </if>
      <if test="isDel != null">
        is_del = #{isDel,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.MatchStrategy">
    update match_strategy
    set name = #{name,jdbcType=VARCHAR},
        biz_line_id = #{bizLineId,jdbcType=BIGINT},
        test_scene_id = #{testSceneId,jdbcType=BIGINT},
        algo_biz_codes = #{algoBizCodes,jdbcType=CHAR},
        predictor_cache = #{predictorCache,jdbcType=CHAR},
        llm_biz_codes = #{llmBizCodes,jdbcType=VARCHAR},
        octo_service_config = #{octoServiceConfig,jdbcType=CHAR},
        status = #{status,jdbcType=INTEGER},
        deploy_info = #{deployInfo,jdbcType=CHAR},
        owner = #{owner,jdbcType=VARCHAR},
        is_del = #{isDel,jdbcType=BIT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>