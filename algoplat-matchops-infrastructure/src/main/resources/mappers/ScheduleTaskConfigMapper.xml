<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.ScheduleTaskConfigMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ScheduleTaskConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="task_type" jdbcType="INTEGER" property="taskType" />
    <result column="action_type" jdbcType="INTEGER" property="actionType" />
    <result column="rule_config" jdbcType="CHAR" property="ruleConfig" />
    <result column="quota" jdbcType="INTEGER" property="quota" />
    <result column="operator_mis" jdbcType="VARCHAR" property="operatorMis" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_name, status, task_type, action_type, rule_config, quota, operator_mis, 
    add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.ScheduleTaskConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from schedule_task_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from schedule_task_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from schedule_task_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.ScheduleTaskConfigExample">
    delete from schedule_task_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ScheduleTaskConfig">
    insert into schedule_task_config (id, task_name, status, 
      task_type, action_type, rule_config, 
      quota, operator_mis, add_time, 
      update_time)
    values (#{id,jdbcType=BIGINT}, #{taskName,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{taskType,jdbcType=INTEGER}, #{actionType,jdbcType=INTEGER}, #{ruleConfig,jdbcType=CHAR}, 
      #{quota,jdbcType=INTEGER}, #{operatorMis,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ScheduleTaskConfig">
    insert into schedule_task_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="taskName != null">
        task_name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="taskType != null">
        task_type,
      </if>
      <if test="actionType != null">
        action_type,
      </if>
      <if test="ruleConfig != null">
        rule_config,
      </if>
      <if test="quota != null">
        quota,
      </if>
      <if test="operatorMis != null">
        operator_mis,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="taskName != null">
        #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="taskType != null">
        #{taskType,jdbcType=INTEGER},
      </if>
      <if test="actionType != null">
        #{actionType,jdbcType=INTEGER},
      </if>
      <if test="ruleConfig != null">
        #{ruleConfig,jdbcType=CHAR},
      </if>
      <if test="quota != null">
        #{quota,jdbcType=INTEGER},
      </if>
      <if test="operatorMis != null">
        #{operatorMis,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.ScheduleTaskConfigExample" resultType="java.lang.Long">
    select count(*) from schedule_task_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update schedule_task_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskName != null">
        task_name = #{record.taskName,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.taskType != null">
        task_type = #{record.taskType,jdbcType=INTEGER},
      </if>
      <if test="record.actionType != null">
        action_type = #{record.actionType,jdbcType=INTEGER},
      </if>
      <if test="record.ruleConfig != null">
        rule_config = #{record.ruleConfig,jdbcType=CHAR},
      </if>
      <if test="record.quota != null">
        quota = #{record.quota,jdbcType=INTEGER},
      </if>
      <if test="record.operatorMis != null">
        operator_mis = #{record.operatorMis,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update schedule_task_config
    set id = #{record.id,jdbcType=BIGINT},
      task_name = #{record.taskName,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      task_type = #{record.taskType,jdbcType=INTEGER},
      action_type = #{record.actionType,jdbcType=INTEGER},
      rule_config = #{record.ruleConfig,jdbcType=CHAR},
      quota = #{record.quota,jdbcType=INTEGER},
      operator_mis = #{record.operatorMis,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ScheduleTaskConfig">
    update schedule_task_config
    <set>
      <if test="taskName != null">
        task_name = #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="taskType != null">
        task_type = #{taskType,jdbcType=INTEGER},
      </if>
      <if test="actionType != null">
        action_type = #{actionType,jdbcType=INTEGER},
      </if>
      <if test="ruleConfig != null">
        rule_config = #{ruleConfig,jdbcType=CHAR},
      </if>
      <if test="quota != null">
        quota = #{quota,jdbcType=INTEGER},
      </if>
      <if test="operatorMis != null">
        operator_mis = #{operatorMis,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.ScheduleTaskConfig">
    update schedule_task_config
    set task_name = #{taskName,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      task_type = #{taskType,jdbcType=INTEGER},
      action_type = #{actionType,jdbcType=INTEGER},
      rule_config = #{ruleConfig,jdbcType=CHAR},
      quota = #{quota,jdbcType=INTEGER},
      operator_mis = #{operatorMis,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>