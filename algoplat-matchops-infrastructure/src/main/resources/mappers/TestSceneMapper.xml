<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.TestSceneMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestScene">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_line_id" jdbcType="BIGINT" property="bizLineId" />
    <result column="test_scene_name" jdbcType="VARCHAR" property="testSceneName" />
    <result column="req_post_type" jdbcType="INTEGER" property="reqPostType" />
    <result column="req_post_link" jdbcType="VARCHAR" property="reqPostLink" />
    <result column="extra" jdbcType="CHAR" property="extra" />
    <result column="owner" jdbcType="VARCHAR" property="owner" />
    <result column="is_del" jdbcType="BIT" property="isDel" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, biz_line_id, test_scene_name, req_post_type, req_post_link, extra, owner, is_del, 
    create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.TestSceneExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from test_scene
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from test_scene
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from test_scene
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.TestSceneExample">
    delete from test_scene
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestScene">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into test_scene (biz_line_id, test_scene_name, req_post_type,
    req_post_link, extra, owner,
    is_del, create_time, update_time
    )
    values (#{bizLineId,jdbcType=BIGINT}, #{testSceneName,jdbcType=VARCHAR}, #{reqPostType,jdbcType=INTEGER},
    #{reqPostLink,jdbcType=VARCHAR}, #{extra,jdbcType=CHAR}, #{owner,jdbcType=VARCHAR},
    #{isDel,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
    )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestScene">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into test_scene
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bizLineId != null">
        biz_line_id,
      </if>
      <if test="testSceneName != null">
        test_scene_name,
      </if>
      <if test="reqPostType != null">
        req_post_type,
      </if>
      <if test="reqPostLink != null">
        req_post_link,
      </if>
      <if test="extra != null">
        extra,
      </if>
      <if test="owner != null">
        owner,
      </if>
      <if test="isDel != null">
        is_del,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bizLineId != null">
        #{bizLineId,jdbcType=BIGINT},
      </if>
      <if test="testSceneName != null">
        #{testSceneName,jdbcType=VARCHAR},
      </if>
      <if test="reqPostType != null">
        #{reqPostType,jdbcType=INTEGER},
      </if>
      <if test="reqPostLink != null">
        #{reqPostLink,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=CHAR},
      </if>
      <if test="owner != null">
        #{owner,jdbcType=VARCHAR},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.TestSceneExample" resultType="java.lang.Long">
    select count(*) from test_scene
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update test_scene
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.bizLineId != null">
        biz_line_id = #{record.bizLineId,jdbcType=BIGINT},
      </if>
      <if test="record.testSceneName != null">
        test_scene_name = #{record.testSceneName,jdbcType=VARCHAR},
      </if>
      <if test="record.reqPostType != null">
        req_post_type = #{record.reqPostType,jdbcType=INTEGER},
      </if>
      <if test="record.reqPostLink != null">
        req_post_link = #{record.reqPostLink,jdbcType=VARCHAR},
      </if>
      <if test="record.extra != null">
        extra = #{record.extra,jdbcType=CHAR},
      </if>
      <if test="record.owner != null">
        owner = #{record.owner,jdbcType=VARCHAR},
      </if>
      <if test="record.isDel != null">
        is_del = #{record.isDel,jdbcType=BIT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update test_scene
    set id = #{record.id,jdbcType=BIGINT},
    biz_line_id = #{record.bizLineId,jdbcType=BIGINT},
    test_scene_name = #{record.testSceneName,jdbcType=VARCHAR},
    req_post_type = #{record.reqPostType,jdbcType=INTEGER},
    req_post_link = #{record.reqPostLink,jdbcType=VARCHAR},
    extra = #{record.extra,jdbcType=CHAR},
    owner = #{record.owner,jdbcType=VARCHAR},
    is_del = #{record.isDel,jdbcType=BIT},
    create_time = #{record.createTime,jdbcType=TIMESTAMP},
    update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestScene">
    update test_scene
    <set>
      <if test="bizLineId != null">
        biz_line_id = #{bizLineId,jdbcType=BIGINT},
      </if>
      <if test="testSceneName != null">
        test_scene_name = #{testSceneName,jdbcType=VARCHAR},
      </if>
      <if test="reqPostType != null">
        req_post_type = #{reqPostType,jdbcType=INTEGER},
      </if>
      <if test="reqPostLink != null">
        req_post_link = #{reqPostLink,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=CHAR},
      </if>
      <if test="owner != null">
        owner = #{owner,jdbcType=VARCHAR},
      </if>
      <if test="isDel != null">
        is_del = #{isDel,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestScene">
    update test_scene
    set biz_line_id = #{bizLineId,jdbcType=BIGINT},
        test_scene_name = #{testSceneName,jdbcType=VARCHAR},
        req_post_type = #{reqPostType,jdbcType=INTEGER},
        req_post_link = #{reqPostLink,jdbcType=VARCHAR},
        extra = #{extra,jdbcType=CHAR},
        owner = #{owner,jdbcType=VARCHAR},
        is_del = #{isDel,jdbcType=BIT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>