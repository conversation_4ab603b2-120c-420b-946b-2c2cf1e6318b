<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.algoplatform.matchops.infrastructure.dal.blade.mapper.OfflineTaskMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.algoplatform.matchops.infrastructure.dal.blade.entity.OfflineTaskDetail">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="partition_date" jdbcType="VARCHAR" property="partitionDate" />
    <result column="cache_key" jdbcType="VARCHAR" property="cacheKey" />
    <result column="unique_key" jdbcType="VARCHAR" property="uniqueKey" />
    <result column="request" jdbcType="LONGVARCHAR" property="request" />
    <result column="result" jdbcType="LONGVARCHAR" property="result" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="result_flag" jdbcType="INTEGER" property="resultFlag" />
  </resultMap>
  <!-- 基础列名 -->
  <sql id="Base_Column_List">
    id, partition_date, cache_key, unique_key, request, result, status, result_flag
  </sql>
  <select id="selectByCondition" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.blade.entity.OfflineTaskDetail" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM offline_task_${industryType}_${taskName}
    WHERE 1 = 1
    <if test="record.id != null and record.id != ''">
      AND id = #{record.id, jdbcType=BIGINT}
    </if>
    <if test="record.partitionDate != null and record.partitionDate != ''">
      AND partition_date = #{record.partitionDate, jdbcType=VARCHAR}
    </if>
    <if test="record.cacheKey != null and record.cacheKey != ''">
      AND cache_key = #{record.cacheKey, jdbcType=VARCHAR}
    </if>
    <if test="record.uniqueKey != null and record.uniqueKey != ''">
      AND unique_key = #{record.uniqueKey, jdbcType=VARCHAR}
    </if>
    <if test="record.request != null and record.request != ''">
      AND request = #{record.request, jdbcType=LONGVARCHAR}
    </if>
    <if test="record.result != null and record.result != ''">
      AND result = #{record.result, jdbcType=LONGVARCHAR}
    </if>
    <if test="record.resultFlag != null">
      AND result_flag = #{record.resultFlag, jdbcType=INTEGER}
    </if>
    <if test="record.status != null">
      AND status = #{record.status, jdbcType=INTEGER}
    </if>
    <if test="startId != null">
      AND id >= #{startId}
    </if>
    ORDER BY id ASC
    <if test="limit != null">
      LIMIT #{limit}
    </if>
  </select>

  <update id="updateBatchByUniqueKey">
    UPDATE offline_task_${industryType}_${taskName}
    SET
    status = CASE id
    <foreach collection="list" item="item">
      WHEN #{item.id} THEN #{item.status}
    </foreach>
    ELSE status END,
    result = CASE id
    <foreach collection="list" item="item">
      WHEN #{item.id} THEN #{item.result}
    </foreach>
    ELSE result END,
    result_flag = CASE id
    <foreach collection="list" item="item">
      WHEN #{item.id} THEN #{item.resultFlag}
    </foreach>
    ELSE result_flag END
    WHERE partition_date = #{partitionDate}
    AND id IN
    <foreach collection="list" item="item" open="(" separator="," close=")">
      #{item.id}
    </foreach>
  </update>



  <select id="getMaxIdByStatus" resultType="java.lang.Long">
    SELECT MAX(id) FROM offline_task_${industryType}_${taskName}
    WHERE partition_date = #{partitionDate}
    <if test="status != null">
      AND status = #{status}
    </if>
    <if test="resultFlag != null">
      AND result_flag = #{resultFlag}
    </if>
  </select>

  <select id="getMinIdByStatus" resultType="java.lang.Long">
    SELECT MIN(id) FROM offline_task_${industryType}_${taskName}
    WHERE partition_date = #{partitionDate}
    <if test="status != null">
      AND status = #{status}
    </if>
    <if test="resultFlag != null">
      AND result_flag = #{resultFlag}
    </if>
  </select>

  <select id="getLatestPartitionDate" resultType="java.lang.String">
    SELECT MAX(partition_date) FROM offline_task_${industryType}_${taskName}
  </select>

  <select id="countByCondition" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.blade.entity.OfflineTaskDetail" resultType="java.lang.Long">
    SELECT COUNT(1)
    FROM offline_task_${industryType}_${taskName}
    WHERE 1 = 1
    <if test="record.id != null and record.id != ''">
      AND id = #{record.id, jdbcType=BIGINT}
    </if>
    <if test="record.partitionDate != null and record.partitionDate != ''">
      AND partition_date = #{record.partitionDate, jdbcType=VARCHAR}
    </if>
    <if test="record.cacheKey != null and record.cacheKey != ''">
      AND cache_key = #{record.cacheKey, jdbcType=VARCHAR}
    </if>
    <if test="record.uniqueKey != null and record.uniqueKey != ''">
      AND unique_key = #{record.uniqueKey, jdbcType=VARCHAR}
    </if>
    <if test="record.request != null and record.request != ''">
      AND request = #{record.request, jdbcType=LONGVARCHAR}
    </if>
    <if test="record.result != null and record.result != ''">
      AND result = #{record.result, jdbcType=LONGVARCHAR}
    </if>
    <if test="record.status != null">
      AND status = #{record.status, jdbcType=INTEGER}
    </if>
    <if test="record.resultFlag != null">
      AND result_flag = #{record.resultFlag, jdbcType=INTEGER}
    </if>
  </select>

  <select id="selectByRange" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM offline_task_${industryType}_${taskName}
    WHERE partition_date = #{partitionDate}
    AND id BETWEEN #{start} AND #{end}
  </select>

</mapper>