<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.TaskBindingConfigMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TaskBindingConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="schedule_service_id" jdbcType="BIGINT" property="scheduleServiceId" />
    <result column="schedule_task_id" jdbcType="BIGINT" property="scheduleTaskId" />
    <result column="resource_pool_ids" jdbcType="CHAR" property="resourcePoolIds" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="operator_mis" jdbcType="VARCHAR" property="operatorMis" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, schedule_service_id, schedule_task_id, resource_pool_ids, status, operator_mis, 
    add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.TaskBindingConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from task_binding_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from task_binding_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from task_binding_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.TaskBindingConfigExample">
    delete from task_binding_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TaskBindingConfig">
    insert into task_binding_config (id, schedule_service_id, schedule_task_id, 
      resource_pool_ids, status, operator_mis, 
      add_time, update_time)
    values (#{id,jdbcType=BIGINT}, #{scheduleServiceId,jdbcType=BIGINT}, #{scheduleTaskId,jdbcType=BIGINT}, 
      #{resourcePoolIds,jdbcType=CHAR}, #{status,jdbcType=INTEGER}, #{operatorMis,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TaskBindingConfig">
    insert into task_binding_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="scheduleServiceId != null">
        schedule_service_id,
      </if>
      <if test="scheduleTaskId != null">
        schedule_task_id,
      </if>
      <if test="resourcePoolIds != null">
        resource_pool_ids,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="operatorMis != null">
        operator_mis,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="scheduleServiceId != null">
        #{scheduleServiceId,jdbcType=BIGINT},
      </if>
      <if test="scheduleTaskId != null">
        #{scheduleTaskId,jdbcType=BIGINT},
      </if>
      <if test="resourcePoolIds != null">
        #{resourcePoolIds,jdbcType=CHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="operatorMis != null">
        #{operatorMis,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.TaskBindingConfigExample" resultType="java.lang.Long">
    select count(*) from task_binding_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update task_binding_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.scheduleServiceId != null">
        schedule_service_id = #{record.scheduleServiceId,jdbcType=BIGINT},
      </if>
      <if test="record.scheduleTaskId != null">
        schedule_task_id = #{record.scheduleTaskId,jdbcType=BIGINT},
      </if>
      <if test="record.resourcePoolIds != null">
        resource_pool_ids = #{record.resourcePoolIds,jdbcType=CHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.operatorMis != null">
        operator_mis = #{record.operatorMis,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update task_binding_config
    set id = #{record.id,jdbcType=BIGINT},
      schedule_service_id = #{record.scheduleServiceId,jdbcType=BIGINT},
      schedule_task_id = #{record.scheduleTaskId,jdbcType=BIGINT},
      resource_pool_ids = #{record.resourcePoolIds,jdbcType=CHAR},
      status = #{record.status,jdbcType=INTEGER},
      operator_mis = #{record.operatorMis,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TaskBindingConfig">
    update task_binding_config
    <set>
      <if test="scheduleServiceId != null">
        schedule_service_id = #{scheduleServiceId,jdbcType=BIGINT},
      </if>
      <if test="scheduleTaskId != null">
        schedule_task_id = #{scheduleTaskId,jdbcType=BIGINT},
      </if>
      <if test="resourcePoolIds != null">
        resource_pool_ids = #{resourcePoolIds,jdbcType=CHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="operatorMis != null">
        operator_mis = #{operatorMis,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TaskBindingConfig">
    update task_binding_config
    set schedule_service_id = #{scheduleServiceId,jdbcType=BIGINT},
      schedule_task_id = #{scheduleTaskId,jdbcType=BIGINT},
      resource_pool_ids = #{resourcePoolIds,jdbcType=CHAR},
      status = #{status,jdbcType=INTEGER},
      operator_mis = #{operatorMis,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>