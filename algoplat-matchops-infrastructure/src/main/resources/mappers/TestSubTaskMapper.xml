<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.TestSubTaskMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestSubTask">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="run_param" jdbcType="VARCHAR" property="runParam" />
    <result column="algo_code_info" jdbcType="CHAR" property="algoCodeInfo" />
    <result column="predictor_cache_info" jdbcType="CHAR" property="predictorCacheInfo" />
    <result column="llm_info" jdbcType="CHAR" property="llmInfo" />
    <result column="octo_info" jdbcType="CHAR" property="octoInfo" />
    <result column="octo_resources" jdbcType="CHAR" property="octoResources" />
    <result column="mlp_model_resources" jdbcType="CHAR" property="mlpModelResources" />
    <result column="friday_model_resources" jdbcType="CHAR" property="fridayModelResources" />
    <result column="reporter_info" jdbcType="CHAR" property="reporterInfo" />
    <result column="task_result" jdbcType="VARCHAR" property="taskResult" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="run_time" jdbcType="CHAR" property="runTime" />
    <result column="extra_info" jdbcType="CHAR" property="extraInfo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />

    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestSubTask">
    <result column="task_result" jdbcType="LONGVARCHAR" property="taskResult" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_id, run_param, algo_code_info, predictor_cache_info, llm_info, octo_info, 
    octo_resources, mlp_model_resources, friday_model_resources, reporter_info,task_result, status,
    run_time, extra_info, creator, create_time, update_time
  </sql>
  <sql id="Blob_Column_List">
    task_result
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.TestSubTaskExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from test_sub_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.TestSubTaskExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from test_sub_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from test_sub_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from test_sub_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.TestSubTaskExample">
    delete from test_sub_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestSubTask">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into test_sub_task (task_id, run_param, algo_code_info, 
      predictor_cache_info, llm_info, octo_info, 
      octo_resources, mlp_model_resources, friday_model_resources, 
      reporter_info, status, run_time, 
      extra_info, creator, create_time, 
      update_time, task_result)
    values (#{taskId,jdbcType=BIGINT}, #{runParam,jdbcType=VARCHAR}, #{algoCodeInfo,jdbcType=CHAR}, 
      #{predictorCacheInfo,jdbcType=CHAR}, #{llmInfo,jdbcType=CHAR}, #{octoInfo,jdbcType=CHAR}, 
      #{octoResources,jdbcType=CHAR}, #{mlpModelResources,jdbcType=CHAR}, #{fridayModelResources,jdbcType=CHAR}, 
      #{reporterInfo,jdbcType=CHAR}, #{status,jdbcType=INTEGER}, #{runTime,jdbcType=CHAR}, 
      #{extraInfo,jdbcType=CHAR}, #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{taskResult,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestSubTask">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into test_sub_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        task_id,
      </if>
      <if test="runParam != null">
        run_param,
      </if>
      <if test="algoCodeInfo != null">
        algo_code_info,
      </if>
      <if test="predictorCacheInfo != null">
        predictor_cache_info,
      </if>
      <if test="llmInfo != null">
        llm_info,
      </if>
      <if test="octoInfo != null">
        octo_info,
      </if>
      <if test="octoResources != null">
        octo_resources,
      </if>
      <if test="mlpModelResources != null">
        mlp_model_resources,
      </if>
      <if test="fridayModelResources != null">
        friday_model_resources,
      </if>
      <if test="reporterInfo != null">
        reporter_info,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="runTime != null">
        run_time,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="taskResult != null">
        task_result,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="runParam != null">
        #{runParam,jdbcType=VARCHAR},
      </if>
      <if test="algoCodeInfo != null">
        #{algoCodeInfo,jdbcType=CHAR},
      </if>
      <if test="predictorCacheInfo != null">
        #{predictorCacheInfo,jdbcType=CHAR},
      </if>
      <if test="llmInfo != null">
        #{llmInfo,jdbcType=CHAR},
      </if>
      <if test="octoInfo != null">
        #{octoInfo,jdbcType=CHAR},
      </if>
      <if test="octoResources != null">
        #{octoResources,jdbcType=CHAR},
      </if>
      <if test="mlpModelResources != null">
        #{mlpModelResources,jdbcType=CHAR},
      </if>
      <if test="fridayModelResources != null">
        #{fridayModelResources,jdbcType=CHAR},
      </if>
      <if test="reporterInfo != null">
        #{reporterInfo,jdbcType=CHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="runTime != null">
        #{runTime,jdbcType=CHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=CHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskResult != null">
        #{taskResult,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.TestSubTaskExample" resultType="java.lang.Long">
    select count(*) from test_sub_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update test_sub_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.runParam != null">
        run_param = #{record.runParam,jdbcType=VARCHAR},
      </if>
      <if test="record.algoCodeInfo != null">
        algo_code_info = #{record.algoCodeInfo,jdbcType=CHAR},
      </if>
      <if test="record.predictorCacheInfo != null">
        predictor_cache_info = #{record.predictorCacheInfo,jdbcType=CHAR},
      </if>
      <if test="record.llmInfo != null">
        llm_info = #{record.llmInfo,jdbcType=CHAR},
      </if>
      <if test="record.octoInfo != null">
        octo_info = #{record.octoInfo,jdbcType=CHAR},
      </if>
      <if test="record.octoResources != null">
        octo_resources = #{record.octoResources,jdbcType=CHAR},
      </if>
      <if test="record.mlpModelResources != null">
        mlp_model_resources = #{record.mlpModelResources,jdbcType=CHAR},
      </if>
      <if test="record.fridayModelResources != null">
        friday_model_resources = #{record.fridayModelResources,jdbcType=CHAR},
      </if>
      <if test="record.reporterInfo != null">
        reporter_info = #{record.reporterInfo,jdbcType=CHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.runTime != null">
        run_time = #{record.runTime,jdbcType=CHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=CHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.taskResult != null">
        task_result = #{record.taskResult,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update test_sub_task
    set id = #{record.id,jdbcType=BIGINT},
      task_id = #{record.taskId,jdbcType=BIGINT},
      run_param = #{record.runParam,jdbcType=VARCHAR},
      algo_code_info = #{record.algoCodeInfo,jdbcType=CHAR},
      predictor_cache_info = #{record.predictorCacheInfo,jdbcType=CHAR},
      llm_info = #{record.llmInfo,jdbcType=CHAR},
      octo_info = #{record.octoInfo,jdbcType=CHAR},
      octo_resources = #{record.octoResources,jdbcType=CHAR},
      mlp_model_resources = #{record.mlpModelResources,jdbcType=CHAR},
      friday_model_resources = #{record.fridayModelResources,jdbcType=CHAR},
      reporter_info = #{record.reporterInfo,jdbcType=CHAR},
      status = #{record.status,jdbcType=INTEGER},
      run_time = #{record.runTime,jdbcType=CHAR},
      extra_info = #{record.extraInfo,jdbcType=CHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      task_result = #{record.taskResult,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update test_sub_task
    set id = #{record.id,jdbcType=BIGINT},
      task_id = #{record.taskId,jdbcType=BIGINT},
      run_param = #{record.runParam,jdbcType=VARCHAR},
      algo_code_info = #{record.algoCodeInfo,jdbcType=CHAR},
      predictor_cache_info = #{record.predictorCacheInfo,jdbcType=CHAR},
      llm_info = #{record.llmInfo,jdbcType=CHAR},
      octo_info = #{record.octoInfo,jdbcType=CHAR},
      octo_resources = #{record.octoResources,jdbcType=CHAR},
      mlp_model_resources = #{record.mlpModelResources,jdbcType=CHAR},
      friday_model_resources = #{record.fridayModelResources,jdbcType=CHAR},
      reporter_info = #{record.reporterInfo,jdbcType=CHAR},
      status = #{record.status,jdbcType=INTEGER},
      run_time = #{record.runTime,jdbcType=CHAR},
      extra_info = #{record.extraInfo,jdbcType=CHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestSubTask">
    update test_sub_task
    <set>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="runParam != null">
        run_param = #{runParam,jdbcType=VARCHAR},
      </if>
      <if test="algoCodeInfo != null">
        algo_code_info = #{algoCodeInfo,jdbcType=CHAR},
      </if>
      <if test="predictorCacheInfo != null">
        predictor_cache_info = #{predictorCacheInfo,jdbcType=CHAR},
      </if>
      <if test="llmInfo != null">
        llm_info = #{llmInfo,jdbcType=CHAR},
      </if>
      <if test="octoInfo != null">
        octo_info = #{octoInfo,jdbcType=CHAR},
      </if>
      <if test="octoResources != null">
        octo_resources = #{octoResources,jdbcType=CHAR},
      </if>
      <if test="mlpModelResources != null">
        mlp_model_resources = #{mlpModelResources,jdbcType=CHAR},
      </if>
      <if test="fridayModelResources != null">
        friday_model_resources = #{fridayModelResources,jdbcType=CHAR},
      </if>
      <if test="reporterInfo != null">
        reporter_info = #{reporterInfo,jdbcType=CHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="runTime != null">
        run_time = #{runTime,jdbcType=CHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=CHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskResult != null">
        task_result = #{taskResult,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestSubTask">
    update test_sub_task
    set task_id = #{taskId,jdbcType=BIGINT},
      run_param = #{runParam,jdbcType=VARCHAR},
      algo_code_info = #{algoCodeInfo,jdbcType=CHAR},
      predictor_cache_info = #{predictorCacheInfo,jdbcType=CHAR},
      llm_info = #{llmInfo,jdbcType=CHAR},
      octo_info = #{octoInfo,jdbcType=CHAR},
      octo_resources = #{octoResources,jdbcType=CHAR},
      mlp_model_resources = #{mlpModelResources,jdbcType=CHAR},
      friday_model_resources = #{fridayModelResources,jdbcType=CHAR},
      reporter_info = #{reporterInfo,jdbcType=CHAR},
      status = #{status,jdbcType=INTEGER},
      run_time = #{runTime,jdbcType=CHAR},
      extra_info = #{extraInfo,jdbcType=CHAR},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      task_result = #{taskResult,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.TestSubTask">
    update test_sub_task
    set task_id = #{taskId,jdbcType=BIGINT},
      run_param = #{runParam,jdbcType=VARCHAR},
      algo_code_info = #{algoCodeInfo,jdbcType=CHAR},
      predictor_cache_info = #{predictorCacheInfo,jdbcType=CHAR},
      llm_info = #{llmInfo,jdbcType=CHAR},
      octo_info = #{octoInfo,jdbcType=CHAR},
      octo_resources = #{octoResources,jdbcType=CHAR},
      mlp_model_resources = #{mlpModelResources,jdbcType=CHAR},
      friday_model_resources = #{fridayModelResources,jdbcType=CHAR},
      reporter_info = #{reporterInfo,jdbcType=CHAR},
      status = #{status,jdbcType=INTEGER},
      run_time = #{runTime,jdbcType=CHAR},
      extra_info = #{extraInfo,jdbcType=CHAR},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>