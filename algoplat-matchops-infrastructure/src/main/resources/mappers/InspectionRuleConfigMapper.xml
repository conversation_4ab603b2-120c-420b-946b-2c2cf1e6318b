<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.algoplatform.matchops.infrastructure.dal.mapper.InspectionRuleConfigMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.InspectionRuleConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="appkey" jdbcType="VARCHAR" property="appkey" />
    <result column="cell" jdbcType="VARCHAR" property="cell" />
    <result column="resource_type" jdbcType="INTEGER" property="resourceType" />
    <result column="rule" jdbcType="CHAR" property="rule" />
    <result column="is_del" jdbcType="BIT" property="isDel" />
    <result column="user" jdbcType="VARCHAR" property="user" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, appkey, cell, resource_type, rule, is_del, user, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.InspectionRuleConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from inspection_rule_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from inspection_rule_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from inspection_rule_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.InspectionRuleConfigExample">
    delete from inspection_rule_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.InspectionRuleConfig">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into inspection_rule_config (appkey, cell, resource_type, 
      rule, is_del, user, create_time, 
      update_time)
    values (#{appkey,jdbcType=VARCHAR}, #{cell,jdbcType=VARCHAR}, #{resourceType,jdbcType=INTEGER}, 
      #{rule,jdbcType=CHAR}, #{isDel,jdbcType=BIT}, #{user,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.InspectionRuleConfig">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into inspection_rule_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appkey != null">
        appkey,
      </if>
      <if test="cell != null">
        cell,
      </if>
      <if test="resourceType != null">
        resource_type,
      </if>
      <if test="rule != null">
        rule,
      </if>
      <if test="isDel != null">
        is_del,
      </if>
      <if test="user != null">
        user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appkey != null">
        #{appkey,jdbcType=VARCHAR},
      </if>
      <if test="cell != null">
        #{cell,jdbcType=VARCHAR},
      </if>
      <if test="resourceType != null">
        #{resourceType,jdbcType=INTEGER},
      </if>
      <if test="rule != null">
        #{rule,jdbcType=CHAR},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=BIT},
      </if>
      <if test="user != null">
        #{user,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.example.InspectionRuleConfigExample" resultType="java.lang.Long">
    select count(*) from inspection_rule_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update inspection_rule_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.appkey != null">
        appkey = #{record.appkey,jdbcType=VARCHAR},
      </if>
      <if test="record.cell != null">
        cell = #{record.cell,jdbcType=VARCHAR},
      </if>
      <if test="record.resourceType != null">
        resource_type = #{record.resourceType,jdbcType=INTEGER},
      </if>
      <if test="record.rule != null">
        rule = #{record.rule,jdbcType=CHAR},
      </if>
      <if test="record.isDel != null">
        is_del = #{record.isDel,jdbcType=BIT},
      </if>
      <if test="record.user != null">
        user = #{record.user,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update inspection_rule_config
    set id = #{record.id,jdbcType=BIGINT},
      appkey = #{record.appkey,jdbcType=VARCHAR},
      cell = #{record.cell,jdbcType=VARCHAR},
      resource_type = #{record.resourceType,jdbcType=INTEGER},
      rule = #{record.rule,jdbcType=CHAR},
      is_del = #{record.isDel,jdbcType=BIT},
      user = #{record.user,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.InspectionRuleConfig">
    update inspection_rule_config
    <set>
      <if test="appkey != null">
        appkey = #{appkey,jdbcType=VARCHAR},
      </if>
      <if test="cell != null">
        cell = #{cell,jdbcType=VARCHAR},
      </if>
      <if test="resourceType != null">
        resource_type = #{resourceType,jdbcType=INTEGER},
      </if>
      <if test="rule != null">
        rule = #{rule,jdbcType=CHAR},
      </if>
      <if test="isDel != null">
        is_del = #{isDel,jdbcType=BIT},
      </if>
      <if test="user != null">
        user = #{user,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.algoplatform.matchops.infrastructure.dal.entity.InspectionRuleConfig">
    update inspection_rule_config
    set appkey = #{appkey,jdbcType=VARCHAR},
      cell = #{cell,jdbcType=VARCHAR},
      resource_type = #{resourceType,jdbcType=INTEGER},
      rule = #{rule,jdbcType=CHAR},
      is_del = #{isDel,jdbcType=BIT},
      user = #{user,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>