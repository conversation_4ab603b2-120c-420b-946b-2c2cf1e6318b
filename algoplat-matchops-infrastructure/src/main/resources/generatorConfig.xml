<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>

    <context id="goods" targetRuntime="MyBatis3">
        <!-- 生成的entity实体引入lombok注解 Getter,Setter,Builder -->
        <plugin type="com.meituan.mdp.mybatis.generator.plugins.LombokPlugin"/>
        <!--生成通用sql方法类，包含通用方法。共MdpMapperPlugin、MdpSimpleMapperPlugin、MdpMixedMapperPlugin
        3个插件，根据诉求决定使用哪个插件，具体区别见 https://km.sankuai.com/custom/onecloud/page/424829078 -->
        <plugin type="com.meituan.mdp.mybatis.generator.plugins.MdpMapperPlugin"/>
        <!--<plugin type="com.meituan.mdp.mybatis.generator.plugins.MdpSimpleMapperPlugin"/>-->
        <!--<plugin type="com.meituan.mdp.mybatis.generator.plugins.MdpMixedMapperPlugin"/>-->
        <!-- 每次执行插件生成的 xml 时通用的方法会覆盖的 -->
        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin"/>
        <!-- 生成批量插入方法插件，默认不需要，需要时配置此插件。使用条件：
        Context targetRuntime="Mybatis3" ; javaClientGenerator type="XMLMAPPER、MIXEDMAPPER"-->
        <!--<plugin type="com.meituan.mdp.mybatis.generator.plugins.BatchInsertPlugin"/>-->
        <!--分页插件，默认不开启。使用条件：Context targetRuntime="Mybatis3" ; javaClientGenerator
        type="XMLMAPPER、MIXEDMAPPER"-->
        <plugin type="com.meituan.mdp.mybatis.generator.plugins.LimitPlugin"/>
        <!-- targetRuntime="Mybatis3"时需要，Example类存储路径 -->
        <plugin type="com.meituan.mdp.mybatis.generator.plugins.ExampleTargetPlugin">
            <property name="targetPackage" value="com.sankuai.algoplatform.matchops.infrastructure.dal.example"/>
        </plugin>
        <!-- 从数据库中的字段的comment做为生成entity的属性注释 -->
        <commentGenerator type="com.meituan.mdp.mybatis.generator.internal.RemarksCommentGenerator">
            <property name="suppressAllComments" value="true"/>
            <property name="suppressDate" value="true"/>
            <property name="addRemarkComments" value="true"/>
        </commentGenerator>

        <!--使用前替换数据库名,账号密码-->
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="******************************************************************************************************************"
                        userId="rds_q3boy"
                        password="^3xrWO99vZd0(I">
        </jdbcConnection>

        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <javaModelGenerator targetPackage="com.sankuai.algoplatform.matchops.infrastructure.dal.entity" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <sqlMapGenerator targetPackage="mappers" targetProject="src/main/resources">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <javaClientGenerator type="XMLMAPPER" targetPackage="com.sankuai.algoplatform.matchops.infrastructure.dal.mapper" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

        <!--配置需要生成的表-->
<!--        <table tableName="resource_pool_config" domainObjectName="ResourcePoolConfig"/>-->
<!--        <table tableName="schedule_service_config" domainObjectName="ScheduleServiceConfig"/>-->
<!--        <table tableName="schedule_task_config" domainObjectName="ScheduleTaskConfig"/>-->
<!--        <table tableName="task_binding_config" domainObjectName="TaskBindingConfig"/>-->
        <table tableName="test_task" domainObjectName="TestTask">
            <!-- 将BLOB字段处理为VARCHAR，避免拆分 -->
            <columnOverride column="run_param" jdbcType="VARCHAR" />
            <!-- 可以添加其他需要处理的BLOB/TEXT字段 -->
        </table>
        <table tableName="resource_group" domainObjectName="ResourceGroup"/>
        <table tableName="reporter_template" domainObjectName="ReporterTemplate"/>
        <table tableName="test_scene" domainObjectName="TestScene">
            <generatedKey column="id" sqlStatement="MYSQL" identity="true"/>
        </table>
        <table tableName="llm_biz_strategy" domainObjectName="LlmBizStrategy"/>
        <table tableName="algo_package" domainObjectName="AlgoPackage"/>
        <table tableName="match_strategy" domainObjectName="MatchStrategy"/>
<!--        <table tableName="cookie_config" domainObjectName="CookieConfig">-->
<!--            <generatedKey column="id" sqlStatement="MYSQL" identity="true"/>-->
<!--        </table>-->

    </context>

</generatorConfiguration>