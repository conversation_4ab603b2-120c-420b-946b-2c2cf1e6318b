<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sankuai.algoplatform</groupId>
        <artifactId>algoplat-matchops</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>algoplat-matchops-infrastructure</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>
    <name>algoplat-matchops-infrastructure</name>

    <dependencies>
        <dependency>
            <groupId>com.sankuai.algoplatform</groupId>
            <artifactId>algoplat-matchops-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.it.sso</groupId>
            <artifactId>sso-java-sdk</artifactId>
            <version>2.6.5</version>
        </dependency>

        <!-- apache poi-->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-scratchpad</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.4</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.13</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>1.19</version>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlbeans</groupId>
            <artifactId>xmlbeans</artifactId>
            <version>3.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>ooxml-schemas</artifactId>
            <version>1.4</version>
        </dependency>
        <!-- apache poi-->

        <dependency>
            <groupId>com.sankuai.mcm</groupId>
            <artifactId>mcm-client-sdk</artifactId>
            <version>0.3.9</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.ead</groupId>
            <artifactId>citadel-client</artifactId>
            <version>3.0.55</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-cache</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-cellar</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-mafka</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-thrift</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-web-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.xm</groupId>
            <artifactId>openplatform-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-zebra</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.ai</groupId>
            <artifactId>friday-llm-training-registry-thrift</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.talostwo</groupId>
            <artifactId>talostwo-sdk-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>mss-java-sdk-s3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mlp.ml</groupId>
            <artifactId>k8sweb-thrift-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.xm</groupId>
            <artifactId>xm-pub-api-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.xm</groupId>
            <artifactId>udb-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>aifree-thrift</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.thrift</groupId>
            <artifactId>libthrift</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.ai</groupId>
            <artifactId>friday-java-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meishi.stgy.algoplatform</groupId>
            <artifactId>predictor-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dxenterprise.open.gateway</groupId>
            <artifactId>open-sdk</artifactId>
            <version>1.1.5-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.org</groupId>
            <artifactId>open-sdk</artifactId>
        </dependency>
        <!-- 添加Apache Commons CLI依赖 -->
        <dependency>
            <groupId>commons-cli</groupId>
            <artifactId>commons-cli</artifactId>
            <version>1.4</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.meituan.mdp.maven.plugins</groupId>
                <artifactId>mdp-mybatis-generator-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
